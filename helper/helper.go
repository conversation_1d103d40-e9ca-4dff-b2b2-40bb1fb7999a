package helper

import (
	"fmt"
	"io"
	"net/http"
	"net/url"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/auth"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type (
	Param     = string
	ParamDesc = string
	ParamType = string
)

const (
	InnerUserCtx string = "__user_ctx__" // InnerUserCtx 用于完成用户验证后，将用户信息存入 HTTP Request 中

	PathRoleID        Param     = "role-id"
	PathRoleIDDesc    ParamDesc = "角色唯一标识符"
	PathUserID        Param     = "user-id"
	PathUserIDDesc    ParamDesc = "用户唯一标识符"
	PathTokenID       Param     = "token-id"
	PathTokenIDDesc   ParamDesc = "用户 TOKEN 唯一标识符"
	QueryUsername     Param     = "username"
	QueryUsernameDesc ParamDesc = "用户名"
	QueryRoleName     Param     = "role-name"
	QueryRoleNameDesc ParamDesc = "角色名称"
	QueryToken        Param     = "token"
	QueryTokenDesc    ParamDesc = "免密登录时使用的 TOKEN"
	QueryTicket       Param     = "ticket"
	QueryTicketDesc   ParamDesc = "基于 TGT 生成的单次登录凭证"
	QueryService      Param     = "service"
	QueryServiceDesc  ParamDesc = "登录（或登出）前浏览器地址栏中的路径，登录（或登出）成功后会自动重定向到该路径"
	QueryRedirect     Param     = "redirect"
	QueryRedirectDesc ParamDesc = "登录（或登出）前浏览器地址栏中的路径，登录（或登出）成功后会自动重定向到该路径"
	FormUsername      Param     = "username"
	FormUsernameDesc  ParamDesc = "用户名"
	FormPassword      Param     = "password"
	FormPasswordDesc  ParamDesc = "密码"

	PathProjectID     Param     = "pid"
	PathProjectIDDesc ParamDesc = "空间唯一标识符"

	PathMemberID     Param     = "mid"
	PathMemberIDDesc ParamDesc = "空间成员唯一标识"

	PathRbacRoleID     Param     = "rid"
	PathRbacRoleIDDesc ParamDesc = "角色唯一标识符"

	PathRbacUserID     Param     = "uid"
	PathRbacUserIDDesc ParamDesc = "用户唯一标识符"

	PathRbacGroupID     Param     = "gid"
	PathRbacGroupIDDesc ParamDesc = "用户组唯一标识符"

	PathRbacGroupName     Param     = "groupName"
	PathRbacGroupNameDesc ParamDesc = "用户组名称"

	PathFlowInstanceID Param = "instance-id"
	QueryOn            Param = "on"

	QueryType     Param     = "type"
	QueryTypeDesc ParamDesc = "角色类型，type=platform，表示获取所有平台角色，type=project，表示获取所有空间角色，type不传表示获取所有角色"

	MemberType     Param     = "member_type"
	MemberTypeDesc ParamDesc = "成员类型，member_type=user, 成员类型为用户，member_type=user_group，成员类型为用户组"

	ParamTypeString ParamType = "string"
	ParamTypeBool   ParamType = "boolean"
	ParamTypeInt    ParamType = "integer"
	ParamTypeFloat  ParamType = "float"
	ParamTypeFile   ParamType = "file"

	ParamProjectId Param = "project_id"

	ParamPermissionCode   Param = "permission_code"
	ParamPermissionAction Param = "permission_action"

	TenantId     Param = "tenant_uid"
	TenantIdDesc Param = "租户id"

	GroupIDs     Param = "group_ids"
	GroupIDsDesc Param = "gpu 分组id"
)

// WriteFileResponse 将文件写入到Http的响应体附件中, 触发浏览器的下载事件
func WriteFileResponse(response *restful.Response, fileName string, src io.Reader) error {
	WriteFileHeader(response, fileName)
	if _, err := io.Copy(response.ResponseWriter, src); err != nil {
		return err
	}
	return nil
}

// WriteFileHeader 设置返回附件文件的响应头
func WriteFileHeader(response *restful.Response, fileName string) {
	response.Header().Add("Content-Type", "application/octet-stream")
	response.Header().Add("Content-Disposition", fmt.Sprintf(`attachment; filename=%s`, url.QueryEscape(fileName)))
	response.Header().Add("File-Name", url.QueryEscape(fileName))
}

func SuccessResponse(response *restful.Response, value interface{}) {
	_ = response.WriteHeaderAndEntity(http.StatusOK, value)
}
func SuccessStringResponse(response *restful.Response, value interface{}) {
	if err := response.WriteHeaderAndEntity(http.StatusOK, value); err != nil {
		stdlog.WithError(err).Error("write response")
	}
}

func HandleErr(response *restful.Response, err error) {
	if err != nil {
		ErrorResponse(response, err)
		return
	}
}
func ErrorResponse(response *restful.Response, e error) {
	httpCode, ge := BuildErrorResponse(e)
	if ge != nil {
		stdlog.WithError(ge).Errorf("Internal Error")
	}
	if ge.Code <= http.StatusNetworkAuthenticationRequired {
		httpCode = int(ge.Code)
	}
	_ = response.WriteError(httpCode, ge)
}
func BuildErrorResponse(e error) (int, *stderr.GatewayError) {
	se := stderr.Unwrap(e)
	return stderr.GetCode(se.Code).HttpCode, &stderr.GatewayError{
		Tag:    se.Tag,
		Code:   se.Code,
		Msg:    se.Message(),
		Detail: se.Message() + "\n" + se.Stack(),
	}
}

func Redirect(request *restful.Request, response *restful.Response, redirect string) {
	http.Redirect(response.ResponseWriter, request.Request, redirect, http.StatusFound)
}

type EmptyRsp struct{}

func ParseUsername(r *restful.Request) string {
	var username string
	user := auth.GetAuthContext(r)
	if user == nil || user.GetUsername() == "" {
		username = "-"
	} else {
		username = user.GetUsername()
	}
	return username
}
