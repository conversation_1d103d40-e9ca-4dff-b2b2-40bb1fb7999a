package helper

import (
	"encoding/json"
	"strings"
)

func InterfaceToString(v interface{}) (string, error) {
	if stringValue, ok := v.(string); ok {
		return stringValue, nil
	} else {
		if bytes, err := json.Marshal(v); err != nil {
			return "", err
		} else {
			return string(bytes), nil
		}
	}
}

func StringToMap(str string) (map[string]string, error) {
	res := make(map[string]string, 0)
	if str == "" {
		return res, nil
	}
	if err := json.Unmarshal([]byte(str), &res); err != nil {
		return nil, err
	}
	return res, nil

}

func StringToInterface(str string, target any) error {
	if str == "" {
		return nil
	}
	if err := json.Unmarshal([]byte(str), target); err != nil {
		return err
	}
	return nil
}

func StringToEnumSlice[T ~int32](str string, mp map[string]int32) []T {
	var ret []T
	for _, s := range strings.Split(str, ",") {
		if enumVal, ok := mp[s]; ok {
			ret = append(ret, T(enumVal))
		}
	}
	return ret
}

func StringToBoolSlice(str string) (ret []bool) {
	for _, s := range strings.Split(str, ",") {
		if strings.ToLower(s) == "true" {
			ret = append(ret, true)
		} else if strings.ToLower(s) == "false" {
			ret = append(ret, false)
		}
	}
	return
}

func StringToEnum[T ~int32](str string, mp map[string]int32) T {
	return T(mp[str])
}
