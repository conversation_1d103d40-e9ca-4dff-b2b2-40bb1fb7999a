package helper

import (
	"fmt"
	"net/url"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/aip/llmops-common/pb/serving"
)

const (
	PathSwagger = "/apidocs"

	PathV1             = "/api/v1"
	PathUser           = PathV1 + "/users"
	PathAuth           = PathV1 + "/auth"
	PathAsset          = PathV1 + "/assetmgr"
	PathTenant         = PathV1 + "/tenants"
	PathAuthLoginCheck = PathAuth + "/loginCheck"
	PathAuthLogin      = PathAuth + "/login"
	PathAuthLogout     = PathAuth + "/logout"
	PathAuthCAS        = PathAuth + "/cas"
	PathAuthCASLogin   = PathAuthCAS + "/login"
	PathAuthCASLogout  = PathAuthCAS + "/logout"
	PathExamineFlow    = PathV1 + "/examine"
	PathCustom         = PathV1 + "/custom"
	PathAlertHistory   = PathV1 + "/alerting/alerts"
	PathAlertRules     = PathV1 + "/alerting/rules"
	PathNotice         = PathV1 + "/notice"
)

func ParseServiceFromRequest(request *restful.Request) string {
	service := request.QueryParameter(QueryService)
	if service == "" {
		service = ParseForwardedForRequest(request)
	}
	service, _ = url.PathUnescape(service)
	return service
}
func ParseRedirectFromRequest(request *restful.Request) string {
	redirect := request.QueryParameter(QueryRedirect)
	if redirect == "" {
		redirect = ParseForwardedForRequest(request)
	}
	redirect, _ = url.PathUnescape(redirect)
	return redirect
}
func ParseForwardedForRequest(request *restful.Request) string {
	proto := request.HeaderParameter("X-Forwarded-Proto")
	if proto == "" {
		proto = "http"
	}
	host := request.HeaderParameter("X-Forwarded-Host")
	if host == "" {
		host = request.Request.Host
	}
	return fmt.Sprintf("%s://%s", proto, host)
}

func ParseSvcSourceTypeFromRequest(request *restful.Request) (ret serving.SourceType, ok bool) {
	stStr := request.QueryParameter("service_source_type")
	if stStr == "" {
		return serving.SourceType_SOURCE_TYPE_UNKNOW, false
	}
	if _, ok := serving.SourceType_value[stStr]; !ok {
		return serving.SourceType_SOURCE_TYPE_UNKNOW, false
	}
	return serving.SourceType(serving.SourceType_value[stStr]), true
}

func ParseDashboardTabTypeFromRequest(request *restful.Request) (ret serving.DashboardTabType, ok bool) {
	str := request.QueryParameter("tab")
	if str == "" {
		return serving.DashboardTabType_DASHBOARD_TAB_TYPE_INVOKE, true
	}
	if _, ok := serving.DashboardTabType_value[str]; !ok {
		return serving.DashboardTabType_DASHBOARD_TAB_TYPE_INVOKE, false
	}
	return serving.DashboardTabType(serving.DashboardTabType_value[str]), true
}
