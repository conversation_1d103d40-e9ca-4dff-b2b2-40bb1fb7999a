package helper

import (
	"context"
	"google.golang.org/grpc/metadata"


	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"

	"github.com/emicklei/go-restful/v3"

	"transwarp.io/applied-ai/aiot/vision-std/auth"
)

const (
	tokenKey                    = "Ctx-Token"
	QueryParamProjectId         = "project_id"
	QueryParamTenantId          = "tenantId"
	QueryParamWithRemoteService = "with_remote_service"
	QueryParamWithAsset         = "with_asset"
	QueryParamOnlyRunning       = "only_running"
	QueryValueTrue              = "true"
	QueryValueFalse             = "false"
	ProjectIdAssets             = "assets"
	UsernameKey                 = "username"
	AUTH_HEADER                 = "Authorization"
)

var (
	UsernameQP = stdsrv.NewQueryParam(QueryUsername, QueryUsernameDesc)
)

func GenNewCtx(req *restful.Request) context.Context {
	ctx := req.Request.Context()
	MD := make(map[string]string)
	cookies := req.Request.Cookies()
	for _, cookie := range cookies {
		MD[cookie.Name] = cookie.Value
	}
	if len(MD) != 0 {
		ctx = metadata.NewIncomingContext(ctx, metadata.New(MD))
	}
	jwtToken := auth.GetAuthContext(req)
	token := jwtToken.Token()
	projectID := req.QueryParameter(QueryParamProjectId)
	if projectID != "" {
		ctx = SetProjectID(ctx, projectID)
	}
	if tenantId := req.QueryParameter(QueryParamTenantId); tenantId != "" {
		ctx = SetTenantID(ctx, tenantId)
	}
	username := jwtToken.GetUsername()
	if username != "" {
		ctx = SetUsername(ctx, username)
	}
	ctx = SetToken(ctx, token)
	return ctx
}

func SetToken(ctx context.Context, token string) context.Context {
	MD, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		MD = metadata.New(make(map[string]string))
	}
	MD.Set(AUTH_HEADER, token)
	ctx = metadata.NewIncomingContext(ctx, MD)
	return context.WithValue(ctx, tokenKey, token)
}

func GetToken(ctx context.Context) (string, error) {
	if v := ctx.Value(tokenKey); v == nil {
		return "", stderr.Error("no token in context")
	} else if r, ok := v.(string); !ok {
		return "", stderr.Error("no token in context")
	} else if r == "" {
		return "", stderr.Error("no token in context")
	} else {
		return r, nil
	}
}

func SetProjectID(ctx context.Context, projectID string) context.Context {
	return context.WithValue(ctx, QueryParamProjectId, projectID)
}

func GetProjectID(ctx context.Context) string {
	if v := ctx.Value(QueryParamProjectId); v == nil {
		return ""
	} else if r, ok := v.(string); !ok {
		return ""
	} else if r == "" {
		return ""
	} else {
		return r
	}
}

func SetTenantID(ctx context.Context, tenantID string) context.Context {
	return context.WithValue(ctx, QueryParamTenantId, tenantID)
}

func GetTenantID(ctx context.Context) string {
	if v := ctx.Value(QueryParamTenantId); v == nil {
		return ""
	} else if r, ok := v.(string); !ok {
		return ""
	} else if r == "" {
		return ""
	} else {
		return r
	}
}

func SetUsername(ctx context.Context, username string) context.Context {
	return context.WithValue(ctx, UsernameKey, username)
}

func GetUsername(ctx context.Context) string {
	if v := ctx.Value(UsernameKey); v == nil {
		return ""
	} else if r, ok := v.(string); !ok {
		return ""
	} else if r == "" {
		return ""
	} else {
		return r
	}
}
