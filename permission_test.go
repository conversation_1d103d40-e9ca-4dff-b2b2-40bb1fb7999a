package main

import (
	"gopkg.in/yaml.v3"
	"os"
	"testing"
)

// 修改permission.yaml文件后生成所有的permission code，方便修改role.json文件
func TestPermission(t *testing.T) {
	file, err := os.ReadFile("etc/permission.yaml")
	if err != nil {
		panic(err)
	}
	var datas []map[string]string
	err = yaml.Unmarshal(file, &datas)
	if err != nil {
		panic(err)
	}

	println("platform permissions: ")
	println("")
	for _, data := range datas {
		if data["action"] == "access" || data["type"] != "platform" {
			continue
		}
		println("\"" + data["code"] + ":" + data["action"] + "\",")
	}

	println("")
	println("")

	println("project permissions: ")
	println("")
	for _, data := range datas {
		if data["action"] == "access" || data["type"] != "project" {
			continue
		}
		println("\"" + data["code"] + ":" + data["action"] + "\",")
	}

}
