package clients

import (
	"fmt"
	"github.com/prometheus/client_golang/api"
	v1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"os"
	"strings"

	"transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/client"

	stdcli "transwarp.io/applied-ai/aiot/vision-std/clients"
	stdConf "transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/database/influxdb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/central-auth-service/conf"
)

var (
	InfluxdbCli  *influxdb.InfluxClient // 用于查询时序数据库中的数据
	InfluxWriter influxdb.InfluxWriter  // 高并发的时序数据库写入客户端

	RedisCli   stdcli.RedisClient
	ExpenseCli *client.ExpenseClient
	ServingCli serving.MLOpsServiceServiceClient
	PromQueryClient v1.API

	initializers = []cliInitializer{
		initPromQueryClient,
		initGrafanaCli,
		initInfluxDBCli,
		initRedisCli,
		initExpenseCli,
		initServingGrpcClients,
	}
)

type cliInitializer func() error

func Init() {
	if err := startCommonClients(); err != nil {
		if conf.IsDevMode() {
			stdlog.Warnf("!!!! skipping deps (clients) initiation in development mode")
			return
		}
		panic(any(fmt.Errorf("failed to start common clients: %+v", err)))
	}
}

func startCommonClients() error {
	return initClients(initializers)
}

func initClients(initializers []cliInitializer) error {
	for _, initializer := range initializers {
		name := strings.TrimPrefix(toolkit.GetFunctionName(initializer), "init")
		if err := initializer(); err != nil {
			stdlog.WithError(err).Errorf("failed to initialize %s", name)
			return err
		}
		stdlog.Infof("succeed to initialize %s", name)
	}
	return nil
}

func initRedisCli() (err error) {
	RedisCli, err = stdcli.NewRedisClient(*conf.C.Redis)
	return
}

func initInfluxDBCli() (err error) {
	if conf.C.Server.LiteMode { // 轻量模式不包含influxdb
		return nil
	}
	c := conf.C.InfluxDB
	InfluxdbCli, err = influxdb.NewInfluxClient(c)
	if err != nil {
		return stderr.Wrap(err, "influxdb's config maybe illegal: %+v", c)
	}
	if err = InfluxdbCli.CreateDB(); err != nil {
		return stderr.Wrap(err, "fail to create database by: %+v", c)
	}

	InfluxWriter, err = influxdb.NewInfluxWriter(influxdb.InfluxWriterConfig{
		Cfg:           c,
		MaxBatch:      1000,
		MaxConcurrent: 20,
	}, nil)
	if err != nil {
		return stderr.Wrap(err, "failed to init influxdb writer")
	}
	return nil
}

func initExpenseCli() error {
	url := os.Getenv("EXPENSE_ADDR")
	ExpenseCli = client.NewExpenseClient(url)
	return nil
}

func initServingGrpcClients() error {
	url := os.Getenv("SERVING_ADDR")
	port := os.Getenv("SERVING_GRPC_PORT")
	if len(url) == 0 {
		url = "llmops-sophon-serving"
	}
	if len(port) == 0 {
		port = ":8752"
	}
	conf := stdConf.GrpcConfig{
		ServerHost:   url,
		ServerPort:   port,
		Registered:   true,
		MaxMessageMB: 1024,
	}
	conn, err := stdcli.InitGrpcConnect(&conf)
	if err != nil {
		return stderr.Wrap(err, "failed to init template client grpc connection")
	}
	ServingCli = serving.NewMLOpsServiceServiceClient(conn)
	return nil
}

func initPromQueryClient() error {
	client, err := api.NewClient(api.Config{
		Address: conf.C.Prometheus.Address, // Prometheus地址
	})
	if err != nil {
		return stderr.Wrap(err, "failed to init prom client")
	}
	PromQueryClient = v1.NewAPI(client)
	return nil
}
