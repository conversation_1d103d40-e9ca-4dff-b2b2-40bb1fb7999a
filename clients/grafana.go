package clients

import (
	"crypto/tls"
	"net/url"
	"strings"

	"github.com/go-openapi/strfmt"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	gfapi "transwarp.io/applied-ai/grafana-openapi-client-go/client"
	"transwarp.io/applied-ai/grafana-openapi-client-go/client/folders"
	"transwarp.io/applied-ai/grafana-openapi-client-go/models"
)

var (
	GrafanaCli    *gfapi.GrafanaHTTPAPI
	GrafanaFolder       = "llmops-maintained"
	GrafanaOrgID  int64 = 1
)

func initGrafanaCli() error {
	if conf.C.Server.LiteMode || conf.IsDevMode() { // 轻量模式不包含grafana
		stdlog.Warnf("!!!! skipping deps (grafana) initiation in development mode")
		return nil
	}
	cfg := &gfapi.TransportConfig{
		Debug:     conf.C.Grafana.Debug,
		Host:      conf.C.<PERSON>.Host,
		BasicAuth: url.UserPassword(conf.C.Grafana.Username, conf.C.Grafana.Password),
		BasePath:  "/api",
		Schemes:   []string{"http"},
		// OrgID provides an optional organization ID.
		// OrgID is only supported with BasicAuth since API keys are already org-scoped.
		OrgID:     1,
		TLSConfig: &tls.Config{},
		// NumRetries contains the optional number of attempted retries
		NumRetries: 3,
		// RetryTimeout sets an optional time to wait before retrying a request
		RetryTimeout: 0,
		// RetryStatusCodes contains the optional list of status codes to retry
		// Use "x" as a wildcard for a single digit (default: [429, 5xx])
		RetryStatusCodes: []string{"420", "5xx"},
		// HTTPHeaders contains an optional map of HTTP headers to add to each request
		HTTPHeaders: map[string]string{},
	}

	cli := gfapi.NewHTTPClientWithConfig(strfmt.Default, cfg)
	health, err := cli.Health.GetHealth()
	if err != nil {
		return stderr.Wrap(err, "grafana cli GetHealth")
	}
	if health.Payload.Database != "ok" {
		return stderr.Errorf("grafana cli GetHealth: database not ok")
	}
	stdlog.Infof("grafana cli GetHealth: %s", health.Payload)

	// create folder llmops-mantained
	_, err = cli.Folders.GetFolderByUID(GrafanaFolder)
	if err != nil {
		if strings.Contains(err.Error(), "folder not found") {
			_, err := cli.Folders.CreateFolderWithParams(&folders.CreateFolderParams{Body: &models.CreateFolderCommand{
				UID:   GrafanaFolder,
				Title: GrafanaFolder,
			}})
			if err != nil {
				return stderr.Wrap(err, "grafana cli CreateFolder")
			}
		} else {
			return stderr.Wrap(err, "grafana cli GetFolderByUID %s", GrafanaFolder)
		}
	}

	GrafanaCli = cli
	return nil
}
