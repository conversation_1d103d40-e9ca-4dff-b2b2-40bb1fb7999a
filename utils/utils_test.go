package utils

import (
	"net"
	"net/http"
	"testing"

	"github.com/emicklei/go-restful/v3"
	"github.com/stretchr/testify/assert"
)

func Test_ClientIP(t *testing.T) {
	tests := []struct {
		name string
		r    *restful.Request
		want string
	}{
		{
			name: "xff",
			r: restful.NewRequest(&http.Request{
				Header: http.Header{http.CanonicalHeaderKey(HeaderXFF): []string{"***********"}},
			}),
			want: "",
		},
		{
			name: "xrip",
			r: restful.NewRequest(&http.Request{
				Header: http.Header{http.CanonicalHeaderKey(HeaderXRIP): []string{"***********"}},
			}),
			want: "***********",
		},
		{
			name: "remote",
			r: restful.NewRequest(&http.Request{
				RemoteAddr: "***********:1111",
			}),
			want: "",
		},
		{
			name: "xff-xrip",
			r: restful.NewRequest(&http.Request{
				Header: http.Header{
					http.Canonical<PERSON><PERSON><PERSON><PERSON><PERSON>(HeaderXFF):  []string{"***********"},
					http.CanonicalHeader<PERSON>ey(HeaderXRIP): []string{"***********"}},
			}),
			want: "***********",
		},
		{
			name: "all",
			r: restful.NewRequest(&http.Request{
				Header: http.Header{
					http.CanonicalHeaderKey(HeaderXFF):  []string{"***********"},
					http.CanonicalHeaderKey(HeaderXRIP): []string{"***********"}},
				RemoteAddr: "***********:1111",
			}),
			want: "***********",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			want := net.ParseIP(tt.want)
			ac := net.ParseIP(ClientIP(tt.r))
			// 同一请求获取多次
			ip := ClientIP(tt.r)
			t.Log(ip)
			assert.Equal(t, true, want.Equal(ac))
		})
	}
}
