package client

import (
	"io"
	"net/http"
	"net/url"

	"google.golang.org/protobuf/encoding/protojson"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/conf"
)

const (
	ProjectId = "project_id"
	KEY       = "key"
)

// bodyParams暂时不使用
func GetModuleData(realUrl string, query *pb.AssetReq, bodyParams map[string]string) (response *pb.Group, err error) {
	realUrl = addQueryParams(realUrl, query)

	req, _ := http.NewRequest(http.MethodGet, realUrl, nil)

	req.Header.Add("Authorization", "Bearer "+conf.C.Auth.Token)

	// 发送请求
	client := http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)

	if err != nil {
		return nil, stderr.IllegalDataFormat.Error("response body")
	}

	groups := new(pb.Group)
	err = protojson.Unmarshal(body, groups)
	if err != nil {
		return nil, err
	}

	return groups, nil

}

// addQueryParams 将查询参数添加到 URL 中
func addQueryParams(apiURL string, queryParams *pb.AssetReq) string {
	u, err := url.Parse(apiURL)
	if err != nil {
		panic(err)
	}

	// 添加查询参数
	q := u.Query()
	if queryParams.ProjectId != "" {
		q.Add(ProjectId, queryParams.ProjectId)
	}
	if queryParams.Key != "" {
		q.Add(KEY, queryParams.Key)
	}

	u.RawQuery = q.Encode()

	return u.String()
}
