package auth

import (
	"testing"
)

func TestNewCASHandler(t *testing.T) {
	type args struct {
		casVersion   string
		casServerUrl string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "",
			args: args{
				casVersion:   "",
				casServerUrl: "https://172.26.0.128:8393/cas",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// got, err := NewCASHandler(tt.args.casVersion, tt.args.casServerUrl)
			// if (err != nil) != tt.wantErr {
			// 	t.<PERSON>rrorf("NewCASHandler() error = %v, wantErr %v", err, tt.wantErr)
			// 	return
			// }
			// u,err := url.Parse("https://172.18.128.37:30443/gateway/cas/api/login?redirect=https://172.18.128.37:30443/intro/data")
			// if err != nil {
			// 	t.Fatal(err)
			// }
			// rsp,err := got.c.C().ValidateTicket(u, "ST-2691-0Ee2QyOWQbCvdb1VTavIF171i8Q-tw-node128", cas.V3)
			// if err != nil {
			// 	t.Fatal(err)
			// }
			// t.Logf("%+v",rsp)
		})
	}
}
