package auth

import (
	"net/http"
	"time"
)

const (
	TokenMaxAge    = 3 * 24 * time.Hour
	TokenExpireAge = -TokenMaxAge
)

func NewCookie(name string, value string) *http.Cookie {
	return &http.Cookie{
		Name:     name,
		Value:    value,
		Path:     "/",
		Expires:  time.Now().Add(TokenMaxAge),
		MaxAge:   int(TokenMaxAge.Seconds()),
		Secure:   true,
		HttpOnly: true,
		SameSite: http.SameSiteNoneMode,
	}
}

func NewExpireCookie(name string) *http.Cookie {
	return &http.Cookie{
		Name:     name,
		Value:    "",
		Path:     "/",
		Expires:  time.Now().Add(TokenExpireAge),
		MaxAge:   int(TokenExpireAge.Seconds()),
		Secure:   true,
		HttpOnly: true,
		SameSite: http.SameSiteNoneMode,
	}
}
