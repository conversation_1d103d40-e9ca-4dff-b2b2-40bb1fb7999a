package auth

import (
	"fmt"
	"net/http"

	"transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
)

type (
	SessionStoreType string

	OptionName string
	Options    map[OptionName]string
)

const (
	Memory SessionStoreType = "memory"
	Redis  SessionStoreType = "redis"

	FinalRedirectServiceQueryParamKey = "service"
)

var _h *handler

func initDefaultInnerHandler(c *conf.AuthConfig, ss SessionStore) *handler {
	if _h == nil {
		_h = &handler{
			loginCheckPath: helper.PathAuthLoginCheck,
			cookieName:     fmt.Sprintf("%s", c.<PERSON>),
			secret:         []byte{'s', 'i', 'm', 'p', 'l', 'e', 'S', 'e', 'c', 'r', 'e', 't'}, // simpleSecret
			sessions:       ss,
			getUserByName: func(username string) (*dao.User, error) {
				return dao.GetUserByName(dao.GetDB(), username)
			},
			allowMultiClientLoginAtSameTime: c.AllowMultiClientLoginAtSameTime,
		}
	}
	return _h
}

// NewAuthHandler 返回不同类型的登录鉴权管理实现(目前支持 CAS/OAUTH2)
func NewAuthHandler(c *conf.AuthConfig, sc *conf.SessionStoreConfig) (Handler, error) {
	if c == nil || sc == nil {
		return nil, stderr.InvalidParam.Errorf("new auth handler with nil auth or session config")
	}
	ss, err := NewSessionStore(conf.C.SessionStore)
	if err != nil {
		return nil, stderr.Wrap(err, "new default session store")
	}
	initDefaultInnerHandler(c, ss)

	switch c.Mode {
	case conf.AuthModeCAS, conf.AuthModeMixed:
		return NewCASHandler(c.CAS)
	case conf.AuthModeOAuth2:
		return NewOAuth2Handler(c.OAuth2)
	default:
		return nil, stderr.InvalidParam.Errorf("invalid auth mode : %s, available modes are: [%+v]", c.Mode,
			[]conf.AuthMode{conf.AuthModeCAS, conf.AuthModeOAuth2})
	}
}

type Handler interface {
	// IsAuthenticated 检查当前请求是否已经通过认证。
	// 参数:
	//   - w: 用于写入HTTP响应的ResponseWriter。
	//   - r: 包含HTTP请求信息的Request对象。
	// 返回值:
	//   - bool: 如果用户已认证则返回true，否则返回false。
	IsAuthenticated(w http.ResponseWriter, r *http.Request) bool

	// GetAuthenticationUser 从请求中获取已认证的用户信息。
	// 参数:
	//   - r: 包含HTTP请求信息的Request对象。
	// 返回值:
	//   - *dao.User: 返回已认证的用户对象。
	//   - error: 如果获取用户信息失败，则返回错误。
	GetAuthenticationUser(r *http.Request) (*dao.User, error)

	// RedirectBeforeLoginForRequest 在用户登录前根据请求和服务的不同生成重定向URL。
	// 参数:
	//   - r: 包含HTTP请求信息的Request对象。
	//   - service: 服务名称，用于确定重定向的目标。
	// 返回值:
	//   - string: 返回生成的重定向URL。
	//   - error: 如果生成URL失败，则返回错误。
	RedirectBeforeLoginForRequest(r *http.Request, service string) (string, error)

	// RedirectAfterLogoutForRequest 在用户登出后根据请求和服务的不同生成重定向URL。
	// 参数:
	//   - r: 包含HTTP请求信息的Request对象。
	//   - service: 服务名称，用于确定重定向的目标。
	// 返回值:
	//   - string: 返回生成的重定向URL。
	//   - error: 如果生成URL失败，则返回错误。
	RedirectAfterLogoutForRequest(r *http.Request, service string) (string, error)

	// SetSessionStore 设置用于存储会话的SessionStore。
	// 参数:
	//   - store: 用于存储会话的SessionStore对象。
	SetSessionStore(store SessionStore)

	// GetSessionStore 获取当前用于存储会话的SessionStore。
	// 返回值:
	//   - SessionStore: 返回当前使用的SessionStore对象。
	GetSessionStore() SessionStore

	// SaveSession 将用户会话信息保存到会话存储中。
	// 1. 生成用户对应的 SOPHONID -> JWT 并存储(Redis / Memory)
	// 2. 将生成的用户的 SOPHONID 写会 http response header
	// 参数:
	//   - w: 用于写入HTTP响应的ResponseWriter。
	//   - r: 包含HTTP请求信息的Request对象。
	//   - user: 需要保存的用户对象。
	// 返回值:
	//   - error: 如果保存会话失败，则返回错误。
	SaveSession(w http.ResponseWriter, r *http.Request, user *dao.User) error

	// LoginWithToken 使用令牌进行用户登录，并可选地设置Cookie。
	// 参数:
	//   - w: 用于写入HTTP响应的ResponseWriter。
	//   - r: 包含HTTP请求信息的Request对象。
	//   - token: 用于登录的令牌。
	//   - cookie: 是否设置Cookie。
	// 返回值:
	//   - error: 如果登录失败，则返回错误。
	LoginWithToken(w http.ResponseWriter, r *http.Request, token string, cookie bool) error

	// ClearSession 清除当前请求的会话信息。
	// 参数:
	//   - w: 用于写入HTTP响应的ResponseWriter。
	//   - r: 包含HTTP请求信息的Request对象。
	// 返回值:
	//   - error: 如果清除会话失败，则返回错误。
	ClearSession(w http.ResponseWriter, r *http.Request) error

	// ClearSessionByUsername 通过用户名清除该用户的会话信息。
	// 参数:
	//   - uname: 需要清除会话的用户名。
	// 返回值:
	//   - error: 如果清除会话失败，则返回错误。
	ClearSessionByUsername(uname string) error
}

type handler struct {
	loginCheckPath string
	loginPath      string
	logoutPath     string

	cookieName string
	secret     []byte
	// string(JWT's md5) -> string(JWT) [ -> parse username from the JWT and get UserProfile by username from MetaStore ]
	sessions      SessionStore
	getUserByName func(username string) (*dao.User, error)

	allowMultiClientLoginAtSameTime bool
}

func (h *handler) ClearSessionByUsername(uname string) error {
	m, err := h.sessions.GetAll()
	if err != nil {
		return err
	}
	for s, t := range m {
		jwt, err := auth.ParseToken(t)
		if err != nil {
			stdlog.WithError(err).Errorf("Delete session: parse token [%s] error.", t)
			continue
		}
		if jwt.GetUsername() == uname {
			err = h.sessions.Delete(s)
			if err != nil {
				stdlog.WithError(err).Errorf("Delete session: delete session [%s] error.", s)
				continue
			}
			return nil
		}
	}
	return nil
}

// IsAuthenticated 简单根据请求头中的 Authorization 中的 JWT 判断用户是否登录以及是否合法
func (h *handler) IsAuthenticated(w http.ResponseWriter, r *http.Request) bool {
	_, err := h.GetAuthenticationUser(r)
	if err != nil {
		stdlog.Errorf("GetAuthenticationUser: %v", err)
		return false
	}
	return true
}

// GetAuthenticationUser 根据请求头中的 Authorization 中的 JWT 获取用户信息
func (h *handler) GetAuthenticationUser(r *http.Request) (*dao.User, error) {
	token, err := auth.ParseTokenFromRequest(r)
	if err != nil {
		return nil, err
	}

	username := token.GetUsername()
	roles := make([]*dao.Role, len(token.GetRoles()))
	for i, role := range token.GetRoles() {
		roles[i] = &dao.Role{
			Name: role,
		}
	}
	return &dao.User{
		Name:  username,
		Roles: roles,
	}, nil
}

func (h *handler) RedirectBeforeLoginForRequest(r *http.Request, service string) (string, error) {
	return service, nil
}

func (h *handler) RedirectAfterLogoutForRequest(r *http.Request, service string) (string, error) {
	return service, nil
}

func (h *handler) SaveSession(w http.ResponseWriter, r *http.Request, user *dao.User) error {
	roles := make([]string, len(user.Roles))
	for i, role := range user.Roles {
		roles[i] = role.Name
	}
	token := auth.JWTokenBuilder{}.
		Username(user.Name).Roles(roles...).Iss(auth.DefaultIss).Sub(auth.DefaultSub).TimeToLive(TokenMaxAge).Build().Token()
	if token == "" {
		return fmt.Errorf("failed to generate token for user: %+v", user)
	}
	return h.saveSession(w, token)
}

func (h *handler) LoginWithToken(w http.ResponseWriter, r *http.Request, token string, setCookie bool) error {
	jt, err := auth.ParseToken(token)
	if err != nil {
		return err
	}
	token = jt.InternalToken()
	if !setCookie {
		return nil
	}
	return h.saveSession(w, token)
}

func (h *handler) saveSession(w http.ResponseWriter, token string) error {
	jt, err := auth.ParseToken(token)
	if err != nil {
		return err
	}
	if !h.allowMultiClientLoginAtSameTime {
		if err = h.sessions.CleanUserLoginSessionID(jt.GetUsername()); err != nil {
			stdlog.WithError(err).Warnf("CleanUserLoginSessionID %s", jt.GetUsername())
		}
	}

	sessionID := toolkit.MD5Bytes([]byte(token))
	if err := h.sessions.Set(sessionID, token); err != nil {
		return err
	}
	if !h.allowMultiClientLoginAtSameTime {
		if err = h.sessions.RecordUserLoginSessionID(sessionID, jt.GetUsername()); err != nil {
			stdlog.WithError(err).Warnf("RecordUserLoginSessionID %s", jt.GetUsername())
		}
	}

	http.SetCookie(w, NewCookie(h.cookieName, sessionID))
	return nil
}

func (h *handler) ClearSession(w http.ResponseWriter, r *http.Request) error {
	for _, cookie := range r.Cookies() {
		if cookie.Name == h.cookieName {
			_ = h.sessions.Delete(cookie.Value)
		}
		http.SetCookie(w, NewExpireCookie(cookie.Name))
	}

	return nil
}

func (h *handler) SetSessionStore(store SessionStore) {
	h.sessions = store
}

func (h *handler) GetSessionStore() SessionStore {
	return h.sessions
}
