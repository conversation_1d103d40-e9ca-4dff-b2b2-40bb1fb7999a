package auth

import (
	"net/http"
)

func NewMixedHandler(inner, cas, oauth2 Handler) (*MixedHandler, error) {
	return &MixedHandler{
		handler: _h,
		// c:       config,
	}, nil
}

// MixedHandler handles Mixed Protocol HTTP requests
type MixedHandler struct {
	*handler
	// c *conf.MixedConfig
}

//
// func (ch *MixedHandler) IsAuthenticated(w http.ResponseWriter, r *http.Request) bool {
// 	if ch.handler.IsAuthenticated(w, r) {
// 		// 请求头包含 JWT, 登录验证通过
// 		return true
// 	}
//
// 	return false
// }
//
// func (ch *MixedHandler) GetAuthenticationUser(r *http.Request) (*dao.User, error) {
// 	user, err := ch.handler.GetAuthenticationUser(r)
// 	if err != nil {
// 		return nil, stderr.Wrap(err, "GetAuthenticationUser")
// 	}
// 	return user, err
// 	//
// 	// ar := client.GetAuthenticationResponse(r)
// 	// if ar == nil {
// 	// 	return nil, fmt.Errorf("fail to get the user passing the authentication")
// 	// }
//
// 	// roles := make([]*dao.Role, 0)
// 	// for attribute, values := range ar.Attributes {
// 	// 	if attribute == "roles" {
// 	// 		for _, value := range values {
// 	// 			roles = append(roles, &dao.Role{Name: value})
// 	// 		}
// 	// 		break
// 	// 	}
// 	// }
// 	//
// 	// return &dao.User{
// 	// 	Name: user,
// 	// 	// Roles: roles,
// 	// }, nil
// }

// func (ch *MixedHandler) LoginUrl(r *http.Request) (string, error) {
// 	// loginUrl, err := ch.c.LoginUrlForRequest(r)
// 	// if err != nil {
// 	// 	return "", err
// 	// }
// 	//
// 	// login, err := url.Parse(loginUrl)
// 	// if err != nil {
// 	// 	return "", err
// 	// }
// 	// q := login.Query()
// 	// q.Set("service", r.FormValue("service"))
// 	// login.RawQuery = q.Encode()
// 	// return login.String(), nil
// }
// func (ch *MixedHandler) LogoutUrl(r *http.Request) (string, error) {
// 	// logoutUrl, err := ch.c.LogoutUrlForRequest(r)
// 	// if err != nil {
// 	// 	return "", err
// 	// }
// 	// logout, err := url.Parse(logoutUrl)
// 	// if err != nil {
// 	// 	return "", err
// 	// }
// 	// q := logout.Query()
// 	// q.Set("service", r.FormValue("service"))
// 	// logout.RawQuery = q.Encode()
// 	// return logout.String(), nil
// }
// func (ch *MixedHandler) ServiceValidateUrl(r *http.Request) (string, error) {
// 	serviceValidateUrl, err := ch.c.ServiceValidateUrlForRequest("", r)
// 	if err != nil {
// 		return "", err
// 	}
// 	serviceValidate, err := url.Parse(serviceValidateUrl)
// 	if err != nil {
// 		return "", err
// 	}
// 	q := serviceValidate.Query()
// 	q.Set("service", r.FormValue("service"))
// 	q.Set("ticket", r.FormValue("ticket"))
// 	serviceValidate.RawQuery = q.Encode()
// 	return serviceValidate.String(), nil
// }

func (ch *MixedHandler) RedirectBeforeLoginForRequest(r *http.Request, service string) (string, error) {
	// return ch.c.WithRedirectParam(FinalRedirectServiceQueryParamKey, service).AuthCodeURL(""), nil
	return "", nil
}

// isSingleLogoutRequest determines if the http.Request is a Mixed Single Logout Request.
//
// The rules for a SLO request are, HTTP POST urlencoded form with a logoutRequest parameter.
// func isSingleLogoutRequest(r *http.Request) bool {
// 	if r.Method != "POST" {
// 		return false
// 	}
//
// 	contentType := r.Header.Get("Content-Type")
// 	if contentType != "application/x-www-form-urlencoded" {
// 		return false
// 	}
//
// 	if v := r.FormValue("logoutRequest"); v == "" {
// 		return false
// 	}
//
// 	return true
// }
