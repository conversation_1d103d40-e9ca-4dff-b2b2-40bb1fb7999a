# auth 认证使用说明

因为边缘计算组存在多个产品应用，并且对于每种产品应用都提供自有认证（基于用户名密码的简单认证方式）和 Guardian CAS 认证两种方式，
后期可能会支持更多的认证方式。如果将核心认证逻辑散落在每个应用内部，则不便于管理并且开发维护成本比较高， 所以需要将核心认证逻辑封装到 thinger-std 空间中作为基础构建为上层应用提供认证支持。

1. 在 api/usermgr 包下创建 auth.Handler 实例：

```go

var AuthHandler auth.Handler

...

{
    if conf.Config.CAS.Enabled {
        mode = auth.ModeCAS
        options = map[auth.OptionName]string{
            auth.OptionNameCASProtocolVersion: cas.V3,
            auth.OptionNameCASServerUrl:       conf.Config.CAS.ServerUrl,
        }
    } else {
        mode = auth.ModeRAW
    }
    AuthHandler, err = auth.NewHandler("sophon_edge", mode, options, dao.GetUserByName)
    if err != nil {
        panic(err)
    }
}
```

> dao

```go
func (d *UserDao) GetUserByName(name string) (*auth.User, error) {
	u, ok := d.usersCache.Load(name)
	if !ok {
		return nil, fmt.Errorf("the user[%s] not found", name)
	}
	user := u.(*User)
	role := d.GetRole(user.RoleID)
	user.UserRole = role

	return &auth.User{
		Id:    user.Name,
		Name:  user.Name,
		SK:    user.secretKey,
		Roles: []string{user.RoleID},
	}, nil
}
```

2. 处理 Login 请求：

```go
func Login(request *restful.Request, response *restful.Response) {
	req := new(LoginReq)
	if err := request.ReadEntity(req); err != nil {
		helper.ErrorResponse(response, stderr.InvalidParam.Cause(err, "用户信息获取失败"))
		return
	}
	if req.UserName == "" || req.Password == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("用户名或密码不能为空"))
		return
	}

	if err := AuthHandler.LoginForRequest(response.ResponseWriter, request.Request, &auth.User{
		Name:     req.UserName,
		Password: req.Password,
	}); err != nil {
		helper.ErrorResponse(response, stderr.Unauthenticated.Error(err.Error()))
	} else {
		helper.SuccessResponse(response, LoginRsp{
			Code: LoginSuccessCode,
			User: &auth.User{Name: req.UserName},
		})
	}
	return
}
```

3. 处理 Logout 请求：

```go
func Logout(request *restful.Request, response *restful.Response) {
	if err := AuthHandler.LogoutForRequest(response.ResponseWriter, request.Request); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "fail to logout"))
		return
	} else {
		redirect, err := AuthHandler.LogoutUrlForRequest(request.Request)
		if err != nil {
			helper.ErrorResponse(response, stderr.Internal.Cause(err, "fail to get the redirect url"))
			return
		}
		helper.SuccessResponse(response, LoginRsp{Code: RedirectCode, Redirect: redirect})
	}
}
```

4. 处理 Check 请求：

```go
func Check(request *restful.Request, response *restful.Response) {
	if !AuthHandler.IsAuthenticated(response.ResponseWriter, request.Request) {
		redirect, err := AuthHandler.LoginUrlForRequest(request.Request)
		if err != nil {
			helper.ErrorResponse(response, stderr.Internal.Cause(err, "fail to get the login url"))
			return
		}
		helper.ErrorResponse(response, stderr.Unauthenticated.RedirectWithError(redirect,
			"fail to check the validity of the request"))
		return
	}

	if AuthHandler.RedirectedFrom3rdParty(request.Request) {
		helper.Redirect(response.ResponseWriter, request.Request,
			AuthHandler.IndexUrlForRequest(request.Request), http.StatusFound) // redirect to the index
		return
	} else { // return the information about the user passing the authentication
		user, err := AuthHandler.GetAuthenticationUser(request.Request)
		if err != nil {
			helper.ErrorResponse(response, stderr.Unauthenticated.Error("fail to get the user passing the authentication"))
			return
		}
		helper.SuccessResponse(response, LoginRsp{
			Code: LoginSuccessCode,
			User: user,
		})
	}
}
```

5. 审计日志记录用户名：

```go
func FormatLogger(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
	username := parseUsername(req.Request)

	now := time.Now()
	//适配tdc的ingress跳转
	spaBasePath := req.HeaderParameter(conf.Config.SpaBasePath)
	if spaBasePath != "" {
		cookie := http.Cookie{
			Name:  "basename",
			Value: spaBasePath,
			Path:  "/",
		}
		resp.AddHeader("Set-Cookie", cookie.String())
	}
	chain.ProcessFilter(req, resp)

	realRemoteAddr := req.Request.Header.Get("X-Remote-Addr")
	if realRemoteAddr == "" {
		realRemoteAddr = req.Request.RemoteAddr
	}
	stdlog.Infof("%s %s %s %s %s %d %d %v",
		strings.Split(realRemoteAddr, ":")[0],
		username,
		req.Request.Method,
		req.Request.URL.RequestURI(),
		req.Request.Proto,
		resp.StatusCode(),
		resp.ContentLength(),
		time.Now().Sub(now),
	)

}
func parseUsername(r *http.Request) string {
	var username string
	u, err := usermgr.AuthHandler.GetAuthenticationUser(r)
	if err != nil || u == nil || u.Name == "" {
		username = "-"
	} else {
		username = u.Name
	}
	return username
}
```

6. 认证过滤器：

```go
func AuthCheck(request *restful.Request, response *restful.Response, chain *restful.FilterChain) {
	if needCheckURI(request.Request.RequestURI) {
		if !usermgr.AuthHandler.IsAuthenticated(response.ResponseWriter, request.Request) {
			helper.ErrorResponse(response, fmt.Errorf("fail to pass the authentication"))
			return
		}
	}
	chain.ProcessFilter(request, response)
}
```