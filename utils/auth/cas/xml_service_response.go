package cas

import (
	"encoding/xml"
	"time"
	"transwarp.io/applied-ai/central-auth-service/dao"
)

type xmlServiceResponse struct {
	XMLName xml.Name `xml:"http://www.yale.edu/tp/cas serviceResponse"`

	Failure *xmlAuthenticationFailure
	Success *xmlAuthenticationSuccess
}

type xmlAuthenticationFailure struct {
	XMLName xml.Name `xml:"authenticationFailure"`
	Code    string   `xml:"code,attr"`
	Message string   `xml:",innerxml"`
}

type xmlAuthenticationSuccess struct {
	XMLName             xml.Name           `xml:"authenticationSuccess"`
	User                string             `xml:"user"`
	ProxyGrantingTicket string             `xml:"proxyGrantingTicket,omitempty"`
	Proxies             *xmlProxies        `xml:"proxies"`
	Attributes          *xmlAttributes     `xml:"attributes"`
	ExtraAttributes     []*xmlAnyAttribute `xml:",any"`
}

type xmlProxies struct {
	XMLName xml.Name `xml:"proxies"`
	Proxies []string `xml:"proxy"`
}

func (p *xmlProxies) AddProxy(proxy string) {
	p.Proxies = append(p.Proxies, proxy)
}

type xmlAttributes struct {
	XMLName                                xml.Name `xml:"attributes"`
	AuthenticationDate                     time.Time
	AuthenticationDateStr                  string   `xml:"authenticationDate"`
	LongTermAuthenticationRequestTokenUsed bool     `xml:"longTermAuthenticationRequestTokenUsed"`
	IsFromNewLogin                         bool     `xml:"isFromNewLogin"`
	MemberOf                               []string `xml:"memberOf"`
	UserAttributes                         *xmlUserAttributes
	ExtraAttributes                        []*xmlAnyAttribute `xml:",any"`
}

type xmlUserAttributes struct {
	XMLName       xml.Name             `xml:"userAttributes"`
	Attributes    []*xmlNamedAttribute `xml:"attribute"`
	AnyAttributes []*xmlAnyAttribute   `xml:",any"`
}

type xmlNamedAttribute struct {
	XMLName xml.Name `xml:"attribute"`
	Name    string   `xml:"name,attr,omitempty"`
	Value   string   `xml:",innerxml"`
}

type xmlAnyAttribute struct {
	XMLName xml.Name
	Value   string `xml:",chardata"`
}

func (xsr *xmlServiceResponse) marshalXML(indent int) ([]byte, error) {
	if indent == 0 {
		return xml.Marshal(xsr)
	}

	indentStr := ""
	for i := 0; i < indent; i++ {
		indentStr += " "
	}

	return xml.MarshalIndent(xsr, "", indentStr)
}

func NewServiceResponse() *xmlServiceResponse {
	return &xmlServiceResponse{}
}

func NewFailureServiceResponse(code, message string) []byte {
	sr := NewServiceResponse()
	sr.Failure = &xmlAuthenticationFailure{
		Code:    code,
		Message: message,
	}
	data, _ := sr.marshalXML(4)
	return data
}

func NewSuccessServiceResponse(user *dao.User, pgt string) []byte {
	sr := NewServiceResponse()
	sr.Success = &xmlAuthenticationSuccess{
		User:                user.Name,
		ProxyGrantingTicket: pgt,
		Attributes: &xmlAttributes{
			AuthenticationDate:                     time.Now(),
			AuthenticationDateStr:                  time.Now().Format("2006-01-02T15:04:05.999-07:00 MST"),
			LongTermAuthenticationRequestTokenUsed: false,
			IsFromNewLogin:                         true,
			UserAttributes:                         &xmlUserAttributes{},
		},
	}
	var userAttributes []*xmlNamedAttribute
	for _, role := range user.Roles {
		userAttributes = append(userAttributes, &xmlNamedAttribute{
			Name:  "roles",
			Value: role.Name,
		})
	}
	sr.Success.Attributes.UserAttributes.Attributes = userAttributes
	data, _ := sr.marshalXML(4)
	return data
}
