package client

import (
	"context"
	"net/http"
	"time"
	utils "transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/central-auth-service/utils/auth/cas"
)

type key int

const ( // emulating enums is actually pretty ugly in go.
	clientKey key = iota
	authenticationResponseKey
)

// SetClient associates a Client with a http.Request.
func SetClient(r *http.Request, c *Client) {
	ctx := context.WithValue(r.Context(), clientKey, c)
	r2 := r.WithContext(ctx)
	*r = *r2
}

// GetClient retrieves the Client associated with the http.Request.
func GetClient(r *http.Request) *Client {
	if c := r.Context().Value(clientKey); c != nil {
		return c.(*Client)
	}

	return nil // explicitly pass along the nil to caller -- conforms to previous impl
}

// setAuthenticationResponse associates an AuthenticationResponse with
// a http.Request.
func setAuthenticationResponse(r *http.Request, a *cas.AuthenticationResponse) {
	ctx := context.WithValue(r.Context(), authenticationResponseKey, a)
	r2 := r.WithContext(ctx)
	*r = *r2
}

// GetAuthenticationResponse retrieves the AuthenticationResponse associated
// with a http.Request.
func GetAuthenticationResponse(r *http.Request) *cas.AuthenticationResponse {
	if a := r.Context().Value(authenticationResponseKey); a != nil {
		return a.(*cas.AuthenticationResponse)
	}

	return nil // explicitly pass along the nil to caller -- conforms to previous impl
}

// IsAuthenticated indicates whether the request has been authenticated with CAS.
func IsAuthenticated(r *http.Request) bool {
	if a := GetAuthenticationResponse(r); a != nil {
		return true
	}

	return false
}

// Username returns the authenticated users username
func Username(request *http.Request) string {
	//if a := GetAuthenticationResponse(r); a != nil {
	//	return a.User
	//}
	token, err := utils.ParseTokenFromRequest(request)
	if err != nil {
		return ""
	}
	return token.GetUsername()
}

// Attributes returns the authenticated users attributes.
func Attributes(r *http.Request) cas.UserAttributes {
	if a := GetAuthenticationResponse(r); a != nil {
		return a.Attributes
	}

	return nil
}

// AuthenticationDate returns the date and time that authentication was performed.
//
// This may return time.IsZero if Authentication Date information is not included
// in the CAS service validation response. This will be the case for CAS 2.0
// protocol servers.
func AuthenticationDate(r *http.Request) time.Time {
	var t time.Time
	if a := GetAuthenticationResponse(r); a != nil {
		t = a.AuthenticationDate
	}

	return t
}

// IsNewLogin indicates whether the CAS service ticket was granted following a
// new authentication.
//
// This may incorrectly return false if Is New Login information is not included
// in the CAS service validation response. This will be the case for CAS 2.0
// protocol servers.
func IsNewLogin(r *http.Request) bool {
	if a := GetAuthenticationResponse(r); a != nil {
		return a.IsNewLogin
	}

	return false
}

// IsRememberedLogin indicates whether the CAS service ticket was granted by the
// presence of a long term authentication token.
//
// This may incorrectly return false if Remembered Login information is not included
// in the CAS service validation response. This will be the case for CAS 2.0
// protocol servers.
func IsRememberedLogin(r *http.Request) bool {
	if a := GetAuthenticationResponse(r); a != nil {
		return a.IsRememberedLogin
	}

	return false
}

// MemberOf returns the list of groups which the user belongs to.
func MemberOf(r *http.Request) []string {
	if a := GetAuthenticationResponse(r); a != nil {
		return a.MemberOf
	}

	return nil
}
