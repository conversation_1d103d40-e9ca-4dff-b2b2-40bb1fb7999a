package client

import (
	"net/http"
	"net/url"

	"github.com/golang/glog"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/utils/auth/cas"
)

// Options : Client configuration options
type Options struct {
	URL             *url.URL     // URL to the CAS service
	ProxyURL        *url.URL     // URL to the CAS service's proxy
	Store           TicketStore  // Custom TicketStore, if nil a MemoryStore will be used
	Client          *http.Client // Custom http client to allow options for http connections
	SendService     bool         // Custom sendService to determine whether you need to send service param
	RedirectToIndex bool         // Custom redirectToIndex to determine whether you need to redirect to index page
	URLScheme       URLScheme    // Custom url scheme, can be used to modify the request urls for the client
	ProtocolVersion string       // cas server version
}

// Client implements the main protocol
type Client struct {
	tickets   TicketStore
	client    *http.Client
	urlScheme URLScheme

	sendService     bool
	redirectToIndex bool

	stValidator *ServiceTicketValidator

	protocolVersion cas.ProtocolVersion // cas server version
}

// NewClient creates a Client with the provided Options.
func NewClient(options *Options) *Client {
	if glog.V(2) {
		glog.Infof("cas: new client with options %v", options)
	}

	var tickets TicketStore
	if options.Store != nil {
		tickets = options.Store
	} else {
		tickets = &MemoryStore{}
	}

	var urlScheme URLScheme
	if options.URLScheme != nil {
		urlScheme = options.URLScheme
	} else {
		urlScheme = NewDefaultURLScheme(options.URL)
	}

	var client *http.Client
	if options.Client != nil {
		client = options.Client
	} else {
		client = &http.Client{}
	}

	var protocolVersion cas.ProtocolVersion
	switch options.ProtocolVersion {
	case cas.V1, cas.V2, cas.V3:
		protocolVersion = options.ProtocolVersion
	default:
		protocolVersion = cas.V3
	}
	return &Client{
		tickets:         tickets,
		client:          client,
		urlScheme:       urlScheme,
		sendService:     options.SendService,
		redirectToIndex: options.RedirectToIndex,
		stValidator:     NewServiceTicketValidator(client, options.URL),
		protocolVersion: protocolVersion,
	}
}

// RequestURL determines an absolute URL from the http.Request.
func RequestURL(r *http.Request, redirectToIndex bool) (*url.URL, error) {
	var u *url.URL
	var err error
	if redirectToIndex {
		u, err = url.Parse("/")
	} else {
		u, err = url.Parse(r.URL.String())
	}
	if err != nil {
		return nil, err
	}

	// 追溯到浏览器地址栏中的地址，默认前端都走 HTTPS
	u.Scheme = "https"
	// if scheme := r.Header.Get("X-Forwarded-Proto"); scheme != "" {
	//	u.Scheme = scheme
	// }
	u.Host = r.Host
	if host := r.Header.Get("X-Forwarded-Host"); host != "" {
		u.Host = host
	}

	return u, nil
}

// LoginUrlForRequest determines the CAS login URL for the http.Request.
func (c *Client) LoginUrlForRequest(r *http.Request) (string, error) {
	u, err := c.urlScheme.Login()
	if err != nil {
		return "", err
	}

	service, err := RequestURL(r, false)
	if err != nil {
		return "", err
	}

	q := u.Query()
	q.Add("service", cas.SanitisedURLString(service))
	u.RawQuery = q.Encode()

	return u.String(), nil
}

// LogoutUrlForRequest determines the CAS logout URL for the http.Request.
func (c *Client) LogoutUrlForRequest(r *http.Request) (string, error) {
	u, err := c.urlScheme.Logout()
	if err != nil {
		return "", err
	}

	if c.sendService {
		service, err := RequestURL(r, c.redirectToIndex)
		if err != nil {
			return "", err
		}

		q := u.Query()
		q.Add("service", cas.SanitisedURLString(service))
		u.RawQuery = q.Encode()
	}

	return u.String(), nil
}

// ServiceValidateUrlForRequest determines the CAS serviceValidate URL for the ticket and http.Request.
func (c *Client) ServiceValidateUrlForRequest(ticket string, r *http.Request) (string, error) {
	service, err := RequestURL(r, false)
	if err != nil {
		return "", err
	}
	return c.stValidator.ServiceValidateUrl(service, ticket, c.protocolVersion)
}

// ValidateUrlForRequest determines the CAS validate URL for the ticket and http.Request.
func (c *Client) ValidateUrlForRequest(ticket string, r *http.Request) (string, error) {
	service, err := RequestURL(r, false)
	if err != nil {
		return "", err
	}
	return c.stValidator.ValidateUrl(service, ticket)
}

// validateTicket performs CAS ticket validation with the given ticket and service.
func (c *Client) validateTicket(ticket string, service *http.Request) error {
	serviceURL, err := RequestURL(service, false)
	if err != nil {
		return stderr.Wrap(err, "get the service url for validating ticket")
	}

	success, err := c.stValidator.ValidateTicket(serviceURL, ticket, c.protocolVersion)
	if err != nil {
		return stderr.Wrap(err, "validate ticket with url=%s, ticket=%s, ver=%s", serviceURL, ticket, c.protocolVersion)
	}
	stdlog.Infof("success valide ticket %s: %+v", ticket, success)
	if err := c.tickets.Write(ticket, success); err != nil {
		return err
	}

	return nil
}

// GetSession finds or creates a session for the request.
//
// A cookie is set on the response if one is not provided with the request.
// Validates the ticket if the URL parameter is provided.
func (c *Client) GetSession(w http.ResponseWriter, r *http.Request) {
	ticket := r.URL.Query().Get("ticket")
	if ticket == "" {
		stdlog.Infof("no ticket found, skip cas login")
		return
	}

	// 检查ticket本身合法性
	if err := c.validateTicket(ticket, r); err != nil {
		stdlog.WithError(err).Errorf("invalid ticket: %s", ticket)
		return // allow ServeHTTP()
	}

	// 检查ticket是否在ticketStore中
	t, err := c.tickets.Read(ticket)
	if err != nil {
		stdlog.Infof("ticket %v not in %T: %v", ticket, c.tickets, err)
		return
	}

	stdlog.Infof("validated ticket %s for %s", ticket, t.User)
	// 将ticket的详细信息保存到request context中
	setAuthenticationResponse(r, t)
	return
}

// DeleteTicket removes the ticket from the client
func (c *Client) DeleteTicket(id string) error {
	return c.tickets.Delete(id)
}
