package server

import (
	"net/http"
	"time"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao"
)

const (
	TGTCookieName = "CASTGC"
)

func NewTGTCookie(tgt *dao.TicketGrantingTicket) *http.Cookie {
	duration := conf.C.Auth.CAS.TicketValidity.TicketGrantingTicket
	return &http.Cookie{
		Name:     TGTCookieName,
		Value:    tgt.Ticket,
		Path:     "/",
		Expires:  time.Now().Add(duration),
		MaxAge:   int(duration.Seconds()),
		Secure:   true,
		HttpOnly: true,
		SameSite: http.SameSiteNoneMode,
	}
}

func NewExpiredTGTCookie() *http.Cookie {
	return &http.Cookie{
		Name:   TGTCookieName,
		Path:   "/",
		MaxAge: -1,
	}
}
