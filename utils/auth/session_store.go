package auth

import (
	"context"
	"fmt"
	"os"
	"slices"
	"time"

	"github.com/patrickmn/go-cache"
	"github.com/redis/go-redis/v9"
	stdcli "transwarp.io/applied-ai/aiot/vision-std/clients"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/clients"
	"transwarp.io/applied-ai/central-auth-service/conf"
)

const (
	Prefix string = "auth"
)

type SessionStore interface {
	// Get the token with the session id
	Get(sessionID string) (string, bool)

	// Get the tokens map with session ids: map[sessionid]token
	GetAll(sessionIDs ...string) (map[string]string, error)

	// Set the session with a token
	Set(sessionID string, token string) error

	// Delete the session
	Delete(sessionID string) error

	// Clear all sessions
	Clear() error

	// Keys fetch all sessions
	Keys() ([]string, error)

	RecordUserLoginSessionID(sessionID string, username string) error
	CleanUserLoginSessionID(username string) error
}

func NewSessionStore(cfg *conf.SessionStoreConfig) (SessionStore, error) {
	switch SessionStoreType(cfg.Type) {
	case Memory:
		if cfg.Memory == nil {
			return nil, fmt.Errorf("memory's config for the session store is nil")
		}
		return &memorySessionStore{
			store: cache.New(cfg.Memory.ExpireTime, cfg.Memory.ExpireTime),
		}, nil
	case Redis:
		if conf.C.Redis == nil {
			return nil, fmt.Errorf("redis's config for the session store is nil")
		}
		if conf.C.Redis.Addrs == "" {
			conf.C.Redis.Addrs = os.Getenv("AUTOCV_REDIS_SERVICE_SERVICE_ADDRS")
		}

		return &redisSessionStore{
			rc:         clients.RedisCli,
			expiration: conf.C.Redis.ExpireTime,
		}, nil
	default:
		return nil, fmt.Errorf("unsupported type for the session store: %s", cfg.Type)
	}
}

type memorySessionStore struct {
	store *cache.Cache
}

func (m *memorySessionStore) Get(sessionID string) (string, bool) {
	value, ok := m.store.Get(sessionID)
	if !ok {
		return "", false
	}
	session, ok := value.(string)
	if !ok {
		return "", false
	}
	return session, true
}

func (m *memorySessionStore) GetAll(sessionIDs ...string) (map[string]string, error) {
	var (
		ok    bool
		items = m.store.Items()
		r     = make(map[string]string, len(items))
	)
	if len(sessionIDs) > 0 {
		for _, s := range sessionIDs {
			r[s], ok = items[s].Object.(string)
			if !ok {
				stdlog.Warnf("unexpected session value: %#v", items[s].Object)
				continue
			}
		}
	} else {
		for s, t := range items {
			r[s], ok = t.Object.(string)
			if !ok {
				stdlog.Warnf("unexpected session value: %#v", items[s].Object)
				continue
			}
		}
	}
	return r, nil
}

func (m *memorySessionStore) Set(sessionID string, token string) error {
	m.store.SetDefault(sessionID, token)
	return nil
}

func (m *memorySessionStore) Delete(sessionID string) error {
	m.store.Delete(sessionID)
	return nil
}

func (m *memorySessionStore) Clear() error {
	m.store.Flush()
	return nil
}

func (m *memorySessionStore) Keys() ([]string, error) {
	items := m.store.Items()
	r := make([]string, len(items))
	i := 0
	for k := range items {
		r[i] = k
		i++
	}
	return r, nil
}

func (m *memorySessionStore) RecordUserLoginSessionID(sessionID string, username string) error {
	// TODO
	return nil
}

func (m *memorySessionStore) CleanUserLoginSessionID(username string) error {
	// TODO
	return nil
}

type redisSessionStore struct {
	rc         stdcli.RedisClient
	expiration time.Duration
}

func (r *redisSessionStore) Get(sessionID string) (string, bool) {
	token, err := r.rc.Get(sessionID)
	if err != nil {
		return "", false
	}
	return token, true
}

func (r *redisSessionStore) GetAll(sessionIDs ...string) (map[string]string, error) {
	if len(sessionIDs) == 0 {
		seids, err := r.Keys()
		if err != nil {
			return nil, err
		}
		sessionIDs = slices.DeleteFunc(seids, func(id string) bool {
			return len(id) != 32
		})
	}
	vs, err := r.rc.GetRdb().MGet(context.TODO(), sessionIDs...).Result()
	if err != nil {
		return nil, err
	}
	var ok bool
	res := make(map[string]string, len(sessionIDs))
	for i, s := range vs {
		res[sessionIDs[i]], ok = s.(string)
		if !ok {
			stdlog.Warnf("unexpected session value: %#v", s)
			continue
		}
	}
	return res, nil
}

func (r *redisSessionStore) Set(sessionID string, token string) error {
	return r.rc.Set(sessionID, token, r.expiration)
}

func (r *redisSessionStore) Delete(sessionID string) error {
	return r.rc.Del(sessionID)
}

func (r *redisSessionStore) Clear() error {
	panic("implement me")
}

func (r *redisSessionStore) Keys() ([]string, error) {
	return r.rc.GetRdb().Keys(context.Background(), "*").Result()
}

func (r *redisSessionStore) RecordUserLoginSessionID(sessionID string, username string) error {
	return r.rc.SAdd(redisKey(username), sessionID)
}

func (r *redisSessionStore) CleanUserLoginSessionID(username string) error {
	members, err := r.rc.SMembers(redisKey(username))
	if err != nil {
		return err
	}
	if len(members) == 0 {
		return nil
	}

	var keys []string
	for _, member := range members {
		keys = append(keys, member)
	}

	pipe := r.rc.GetRdb().Pipeline()
	delCmds := make([]*redis.IntCmd, 0, len(keys))
	for _, key := range keys {
		delCmd := pipe.Del(context.Background(), key)
		delCmds = append(delCmds, delCmd)
	}

	_, err = pipe.Exec(context.Background())
	if err != nil {
		return err
	}

	for i, cmd := range delCmds {
		_, err := cmd.Result()
		if err != nil {
			stdlog.Errorf("Failed to delete key %s: %v", keys[i], err)
		}
	}
	return r.rc.Del(redisKey(username))
}

func redisKey(key string) string {
	return fmt.Sprintf("%s-%s", Prefix, key)
}
