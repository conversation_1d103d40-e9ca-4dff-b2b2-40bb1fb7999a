package auth

import (
	"crypto/tls"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"github.com/golang/glog"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/utils/auth/cas"
	"transwarp.io/applied-ai/central-auth-service/utils/auth/cas/client"
)

func NewCASHandler(c *conf.CASConfig) (*CASHandler, error) {
	protocolVersion := cas.V3
	serverUrl := conf.C.Auth.CAS.ServerUrl
	if !c.External() {
		serverUrl = fmt.Sprintf("https://%s%s", c.EmbeddedServerHost, helper.PathAuthCAS)
	}
	casURL, err := url.Parse(serverUrl)
	if err != nil {
		return nil, stderr.Wrap(err, "invalid cas server url : %s", serverUrl)
	}

	return &CASHandler{
		handler: _h,
		c: client.NewClient(&client.Options{
			URL: casURL,
			Client: &http.Client{
				Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}},
				Timeout:   30 * time.Second,
			},
			SendService:     true,
			RedirectToIndex: true,
			ProtocolVersion: protocolVersion,
		}),
	}, nil
}

// CASHandler handles CAS Protocol HTTP requests
type CASHandler struct {
	*handler
	c *client.Client
}

// ServeHTTP handles HTTP requests, processes CAS requests
func (ch *CASHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	if glog.V(2) {
		glog.Infof("cas: handling %v request for %v", r.Method, r.URL)
	}

	client.SetClient(r, ch.c)

	if isSingleLogoutRequest(r) {
		ch.performSingleLogout(w, r)
		return
	}

	ch.c.GetSession(w, r)
	return
}

func (ch *CASHandler) IsAuthenticated(w http.ResponseWriter, r *http.Request) bool {
	if ch.handler.IsAuthenticated(w, r) {
		// 请求头包含 JWT, 登录验证通过
		return true
	}

	// 初步验证未通过, 判断是否进行过单点登录
	ch.ServeHTTP(w, r)
	return client.IsAuthenticated(r)
}

func (ch *CASHandler) GetAuthenticationUser(r *http.Request) (*dao.User, error) {
	user, err := ch.handler.GetAuthenticationUser(r)
	if err == nil {
		return user, nil
	}

	ar := client.GetAuthenticationResponse(r)
	if ar == nil {
		return nil, fmt.Errorf("fail to get the user passing the authentication")
	}

	roles := make([]*dao.Role, 0)
	for attribute, values := range ar.Attributes {
		if attribute == "roles" {
			for _, value := range values {
				roles = append(roles, &dao.Role{Name: value})
			}
			break
		}
	}

	return &dao.User{
		Name:  ar.User,
		Roles: roles,
	}, nil
}

func (ch *CASHandler) LoginUrl(r *http.Request) (string, error) {
	loginUrl, err := ch.c.LoginUrlForRequest(r)
	if err != nil {
		return "", err
	}

	login, err := url.Parse(loginUrl)
	if err != nil {
		return "", err
	}
	q := login.Query()
	q.Set("service", r.FormValue("service"))
	login.RawQuery = q.Encode()
	return login.String(), nil
}
func (ch *CASHandler) LogoutUrl(r *http.Request) (string, error) {
	logoutUrl, err := ch.c.LogoutUrlForRequest(r)
	if err != nil {
		return "", err
	}
	logout, err := url.Parse(logoutUrl)
	if err != nil {
		return "", err
	}
	q := logout.Query()
	q.Set("service", r.FormValue("service"))
	logout.RawQuery = q.Encode()
	return logout.String(), nil
}
func (ch *CASHandler) ServiceValidateUrl(r *http.Request) (string, error) {
	serviceValidateUrl, err := ch.c.ServiceValidateUrlForRequest("", r)
	if err != nil {
		return "", err
	}
	serviceValidate, err := url.Parse(serviceValidateUrl)
	if err != nil {
		return "", err
	}
	q := serviceValidate.Query()
	q.Set("service", r.FormValue("service"))
	q.Set("ticket", r.FormValue("ticket"))
	serviceValidate.RawQuery = q.Encode()
	return serviceValidate.String(), nil
}

func (ch *CASHandler) RedirectBeforeLoginForRequest(r *http.Request, service string) (string, error) {
	loginUrl, err := ch.c.LoginUrlForRequest(r) // 默认会跳转到 RedirectBeforeLoginForRequest 对应的 URL
	if err != nil {
		return "", err
	}
	login, err := url.Parse(loginUrl)
	if err != nil {
		return "", err
	}
	q := login.Query()
	q.Set("service", service)
	login.RawQuery = q.Encode()
	return login.String(), nil
}

func (ch *CASHandler) RedirectAfterLogoutForRequest(r *http.Request, service string) (string, error) {
	logoutUrl, err := ch.c.LogoutUrlForRequest(r)
	if err != nil {
		return "", err
	}
	logout, err := url.Parse(logoutUrl)
	if err != nil {
		return "", err
	}
	q := logout.Query()
	q.Set("service", service)
	logout.RawQuery = q.Encode()
	return logout.String(), nil
}

// isSingleLogoutRequest determines if the http.Request is a CAS Single Logout Request.
//
// The rules for a SLO request are, HTTP POST urlencoded form with a logoutRequest parameter.
func isSingleLogoutRequest(r *http.Request) bool {
	if r.Method != "POST" {
		return false
	}

	contentType := r.Header.Get("Content-Type")
	if contentType != "application/x-www-form-urlencoded" {
		return false
	}

	if v := r.FormValue("logoutRequest"); v == "" {
		return false
	}

	return true
}

// performSingleLogout processes a single logout request
func (ch *CASHandler) performSingleLogout(w http.ResponseWriter, r *http.Request) {
	rawXML := r.FormValue("logoutRequest")
	logoutRequest, err := client.ParseLogoutRequest([]byte(rawXML))
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	if err = ch.c.DeleteTicket(logoutRequest.SessionIndex); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	fmt.Fprintln(w, "OK")
}
