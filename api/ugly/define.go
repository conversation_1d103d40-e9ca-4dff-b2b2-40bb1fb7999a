package ugly

import (
	"crypto/tls"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"

	"github.com/emicklei/go-restful/v3"

	utils "transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/api/auth"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
)

func (r *Resource) GetRole(request *restful.Request, response *restful.Response) {
	if !auth.GetAuthHandler().IsAuthenticated(response.ResponseWriter, request.Request) {
		helper.ErrorResponse(response, stderr.Unauthenticated.Error("用户未登录"))
		return
	}
	user, err := auth.GetAuthHandler().GetAuthenticationUser(request.Request)
	if err != nil {
		helper.ErrorResponse(response, stderr.Unauthenticated.Error("用户信息获取失败！"))
		return
	}
	sophonRole := "SOPHON_BASIC"
	for _, role := range user.Roles {
		if role.Name == "SOPHON_ADMIN" {
			sophonRole = role.Name
			break
		}
	}
	response.Write([]byte(sophonRole))
}

type Profile struct {
	Id                        string        `json:"id"`
	Uid                       string        `json:"uid"`
	Email                     interface{}   `json:"email"`
	RegisterTime              string        `json:"registerTime"`
	SecurityLevel             int           `json:"securityLevel"`
	Rank                      int           `json:"rank"`
	Gender                    interface{}   `json:"gender"`
	FullName                  interface{}   `json:"fullName"`
	NickName                  interface{}   `json:"nickName"`
	Domain                    interface{}   `json:"domain"`
	Icon                      interface{}   `json:"icon"`
	AreaOfInterests           []interface{} `json:"areaOfInterests"`
	PersonalIntroduction      interface{}   `json:"personalIntroduction"`
	CompanyName               interface{}   `json:"companyName"`
	CompanyUrl                interface{}   `json:"companyUrl"`
	Industry                  interface{}   `json:"industry"`
	Province                  interface{}   `json:"province"`
	City                      interface{}   `json:"city"`
	ContactPhoneNumber        interface{}   `json:"contactPhoneNumber"`
	NotebookConfig            interface{}   `json:"notebookConfig"`
	NotebookSessionId         interface{}   `json:"notebookSessionId"`
	ArticlesNotificationLevel int           `json:"articlesNotificationLevel"`
	ReviewsNotificationLevel  int           `json:"reviewsNotificationLevel"`
}

func (r *Resource) GetProfile(request *restful.Request, response *restful.Response) {
	if !auth.GetAuthHandler().IsAuthenticated(response.ResponseWriter, request.Request) {
		helper.ErrorResponse(response, stderr.Unauthenticated.Error("用户未登录"))
		return
	}
	user, err := auth.GetAuthHandler().GetAuthenticationUser(request.Request)
	if err != nil {
		helper.ErrorResponse(response, stderr.Unauthenticated.Error("用户信息获取失败！"))
		return
	}
	profile := new(Profile)
	profile.Id = strconv.Itoa(int(user.ID))
	profile.Uid = user.Name
	helper.SuccessResponse(response, profile)
}

func handleOauth2Redirect(oauth2Login string, request *restful.Request, response *restful.Response) error {
	// 检查是否需要提前由 Server 端获取该地址并处理 302 重定向
	if conf.C.Auth == nil || conf.C.Auth.OAuth2 == nil || !conf.C.Auth.OAuth2.PreCheckRedirect {
		return nil
	}
	stdlog.Infof("raw OAuth2 login URL: %s", oauth2Login)
	client := &http.Client{
		// CheckRedirect: func(req *http.Request, via []*http.Request) error {
		// 	// 允许最多 10 次重定向
		// 	if len(via) >= 10 {
		// 		return fmt.Errorf("stopped after 10 redirects")
		// 	}
		// 	return nil
		// },
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	req, err := http.NewRequest("GET", oauth2Login, nil)
	if err != nil {
		return fmt.Errorf("failed to create request for OAuth2 login URL: %v", err)
	}

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to pre-check OAuth2 login URL: %v", err)
	}
	defer resp.Body.Close()

	// 如果响应是 302 重定向，则返回最终的目标地址
	if resp.StatusCode == http.StatusFound || resp.StatusCode == http.StatusSeeOther {
		redirectURL, err := resp.Location()
		stdlog.Infof("redirect URL: %s", redirectURL)
		if err != nil {
			return fmt.Errorf("failed to parse redirect location from OAuth2 login URL check: %v", err)
		}
		for k, v := range resp.Header {
			if len(v) == 0 {
				continue
			}
			response.AddHeader(k, v[0])
		}
		http.Redirect(response.ResponseWriter, request.Request, redirectURL.String(), http.StatusFound)
		return nil
	}

	// 否则返回原始的登录 URL
	stdlog.Infof("Need not rediect OAuth2 login URL")
	http.Redirect(response.ResponseWriter, request.Request, oauth2Login, http.StatusFound)
	return nil
}

func (r *Resource) Redirect2LoginPage(request *restful.Request, response *restful.Response) {
	svc := request.QueryParameter(helper.QueryService)
	if svc == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("缺少请求参数 service"))
		return
	}

	loginUrl, err := auth.GetAuthHandler().RedirectBeforeLoginForRequest(request.Request, svc)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "登录页面地址获取失败，无法重定向"))
		return
	}

	if conf.C.Auth != nil && conf.C.Auth.OAuth2 != nil && conf.C.Auth.OAuth2.PreCheckRedirect {
		if err := handleOauth2Redirect(loginUrl, request, response); err != nil {
			helper.ErrorResponse(response, stderr.Internal.Cause(err, "登录页面地址获取失败，无法重定向"))
		}
		return
	}

	loginUrl, err = conf.C.Auth.CAS.HandleEmbeddedCasRedirectUrl(loginUrl)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "登录页面地址获取失败，无法重定向"))
		return
	}
	helper.Redirect(request, response, loginUrl)
}
func (r *Resource) LoginCheck(request *restful.Request, response *restful.Response) {
	if !auth.GetAuthHandler().IsAuthenticated(response.ResponseWriter, request.Request) {
		helper.ErrorResponse(response, stderr.Unauthenticated.Error("用户认证失败！"))
		return
	}
	user, err := auth.GetAuthHandler().GetAuthenticationUser(request.Request)
	if err != nil {
		helper.ErrorResponse(response, stderr.Unauthenticated.Error("用户获取失败！"))
		return
	}
	if err := auth.GetAuthHandler().SaveSession(response.ResponseWriter, request.Request, user); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("用户认证信息缓存失败"))
		return
	}

	// redirect
	redirect := helper.ParseRedirectFromRequest(request)
	helper.Redirect(request, response, redirect)
}
func (r *Resource) Logout(request *restful.Request, response *restful.Response) {
	if err := auth.GetAuthHandler().ClearSession(response.ResponseWriter, request.Request); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "用户认证信息清除失败"))
		return
	}

	service := helper.ParseServiceFromRequest(request)
	logoutUrl, err := auth.GetAuthHandler().RedirectAfterLogoutForRequest(request.Request, service)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "登出界面地址获取失败，无法重定向"))
		return
	}
	logoutUrl, err = conf.C.Auth.CAS.HandleEmbeddedCasRedirectUrl(logoutUrl)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "登出页面地址获取失败，无法重定向"))
		return
	}
	helper.Redirect(request, response, logoutUrl)
}

func (r *Resource) CreateAccessToken(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	token := new(dao.AccessToken)
	if err := request.ReadEntity(token); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "请求体解析失败"))
		return
	}
	// 为指定用户创建 TOKEN
	if token.Username == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户"))
		return
	}
	user, err := r.us.GetUserByName(token.Username)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "获取用户失败"))
		return
	}
	token.Token = utils.JWTokenBuilder{}.Username(user.GetUsername()).Roles(user.GetRoles()...).
		TimeToLive(36500 * 24 * time.Hour).Scope(utils.JWTTokenScopeExternal).Build().Token()

	if err := r.ts.CreateAccessToken(creator.GetUsername(), token); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "用户 TOKEN 创建失败"))
		return
	}
	helper.SuccessResponse(response, token)
}
func (r *Resource) DeleteAccessTokenByUsername(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	username := request.QueryParameter(helper.QueryUsername)
	if username == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户名"))
		return
	}
	if err := r.ts.DeleteAccessTokenByUsername(username); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "删除用户 TOKEN 失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}
func (r *Resource) DeleteAccessTokenByID(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	tokenID := request.PathParameter(helper.PathTokenID)
	if tokenID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的 TOKEN ID"))
		return
	}
	if err := r.ts.DeleteAccessTokenByID(tokenID); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "删除用户 TOKEN 失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}
func (r *Resource) UpdateAccessToken(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	token := new(dao.AccessToken)
	if err := request.ReadEntity(token); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "请求体解析失败"))
		return
	}
	if token.ID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的 TOKEN ID"))
		return
	}

	if err := r.ts.UpdateAccessToken(token); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "更新用户 TOKEN 失败"))
		return
	}
	helper.SuccessResponse(response, token)
}
func (r *Resource) DisableAccessTokenByUsername(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	username := request.QueryParameter(helper.QueryUsername)
	if username == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户名"))
		return
	}
	if err := r.ts.DisableAccessTokenByUsername(username); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "禁用用户 TOKEN 失败"))
		return
	}

	tokens, err := r.ts.GetAccessTokenByUsername(username)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取指定用户的 TOKEN 失败"))
		return
	}
	helper.SuccessResponse(response, tokens)
}
func (r *Resource) DisableAccessTokenByID(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	tokenID := request.PathParameter(helper.PathTokenID)
	if tokenID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的 TOKEN ID"))
		return
	}
	if err := r.ts.DisableAccessTokenByID(tokenID); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "禁用用户 TOKEN 失败"))
		return
	}

	token, err := r.ts.GetAccessTokenByID(tokenID)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取用户 TOKEN 失败"))
		return
	}
	helper.SuccessResponse(response, token)
}
func (r *Resource) GetAccessTokenByUsername(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	username := request.QueryParameter(helper.QueryUsername)
	if username == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户名"))
		return
	}
	tokens, err := r.ts.GetAccessTokenByUsername(username)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取指定用户的 TOKEN 失败"))
		return
	}
	helper.SuccessResponse(response, tokens)
}
func (r *Resource) GetAccessTokenByID(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	tokenID := request.PathParameter(helper.PathTokenID)
	if tokenID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的 TOKEN ID"))
		return
	}
	token, err := r.ts.GetAccessTokenByID(tokenID)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取用户 TOKEN 失败"))
		return
	}
	helper.SuccessResponse(response, token)
}
