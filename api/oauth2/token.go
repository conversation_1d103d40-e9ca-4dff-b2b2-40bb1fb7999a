package oauth2

import (
	"bytes"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"text/template"
	"time"

	"golang.org/x/oauth2"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type BaseToken interface {
	GetUserName() string     // 用户帐号
	GetUserFullName() string // 中文名
	GetUserEmail() string
	// GetUserExpire() time.Time
}

func AsUserReq(t BaseToken) *models.UserReq {
	dc := conf.C.Auth.OAuth2.DefaultUserConf
	return &models.UserReq{
		UserName:             t.GetUserName(),
		FullName:             t.Get<PERSON>ser<PERSON>(),
		Email:                t.GetUserEmail(),
		Password:             dc.Password,
		UserGroupNames:       dc.UserGroups,
		PlatformRoleId:       uint64(helper.GeneraUserID),
		DefaultProject:       dc.Project,
		Status:               models.Enable,
		ExpirationTimeSelect: models.Nolimit,
		WhiteIps:             nil,
	}
}

type TranswarpToken struct {
	Exp            int      `json:"exp"`
	Iat            int      `json:"iat"`
	AuthTime       int      `json:"auth_time"`
	Jti            string   `json:"jti"`
	Iss            string   `json:"iss"`
	Aud            string   `json:"aud"`
	Sub            string   `json:"sub"`
	Typ            string   `json:"typ"`
	Azp            string   `json:"azp"`
	SessionState   string   `json:"session_state"`
	Acr            string   `json:"acr"`
	AllowedOrigins []string `json:"allowed-origins"`
	RealmAccess    struct {
		Roles []string `json:"roles"`
	} `json:"realm_access"`
	ResourceAccess struct {
		Account struct {
			Roles []string `json:"roles"`
		} `json:"account"`
	} `json:"resource_access"`
	Scope             string `json:"scope"`
	EmailVerified     bool   `json:"email_verified"`
	Name              string `json:"name"`
	PreferredUsername string `json:"preferred_username"`
	GivenName         string `json:"given_name"`
	FamilyName        string `json:"family_name"`
	Email             string `json:"email"`
}

func (t *TranswarpToken) GetUserName() string {
	return t.PreferredUsername
}
func (t *TranswarpToken) GetUserFullName() string {
	return t.FamilyName + t.GivenName
}
func (t *TranswarpToken) GetUserEmail() string {
	return t.Email
}

type CustomToken struct {
	RawClaims map[string]interface{}
	FieldMap  map[string]string // eg: {"username": ".preferred_username"}

	// 目标字段
	userName     string // 用户帐号
	userFullName string // 中文名
	userEmail    string
}

func (t *CustomToken) GetUserName() string {
	return t.userName
}

func (t *CustomToken) GetUserFullName() string {
	return t.userFullName
}

func (t *CustomToken) GetUserEmail() string {
	return t.userEmail
}

type ExternalToken struct {
	RawClaims map[string]interface{}      // JWT payload 数据
	Config    *conf.UserInfoFetcherConfig // 外部接口配置
}

func (t *ExternalToken) GetUserName() string {
	return getField(t.RawClaims, t.Config.ResponseMap.Username)
}

func (t *ExternalToken) GetUserFullName() string {
	return getField(t.RawClaims, t.Config.ResponseMap.Fullname)
}

func (t *ExternalToken) GetUserEmail() string {
	return getField(t.RawClaims, t.Config.ResponseMap.Email)
}

func (t *ExternalToken) GetUserMobile() string {
	return getField(t.RawClaims, t.Config.ResponseMap.Mobile)
}

// 使用 Go template 提取字段值
func getField(data map[string]interface{}, expr string) string {
	if expr == "" {
		return ""
	}

	tmpl, err := template.New("field").Parse(expr)
	if err != nil {
		stdlog.Warnf("failed to parse go template %q: %v", expr, err)
		return ""
	}

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, data)
	if err != nil || buf.String() == "<no value>" {
		stdlog.Warnf("failed to execute go template %q: %v", expr, err)
		return ""
	}

	return buf.String()
}

func getJwtFromToken(token *oauth2.Token) (jwt []byte, err error) {
	parts := strings.Split(token.AccessToken, ".")
	if len(parts) != 3 {
		return nil, stderr.InvalidParam.Errorf("invalid jwt, expect 3 parts, but got %d", len(parts))
	}
	sc := parts[1]

	jwt, err = base64.StdEncoding.WithPadding(base64.NoPadding).DecodeString(sc)
	if err != nil {
		return nil, stderr.InvalidParam.Errorf("can not url decode b64: %s", sc)
	}
	return jwt, nil
}

func parseUserInfoFromToken(token *oauth2.Token, c *conf.OAuth2Config) (BaseToken, error) {
	var t BaseToken
	switch c.TokenMode {
	case conf.TokenModeTranswarp:
		str, err := getJwtFromToken(token)
		if err != nil {
			return nil, err
		}
		t = new(TranswarpToken)
		if err = json.Unmarshal(str, t); err != nil {
			return nil, stderr.InvalidParam.Errorf("can not json unmarshal: %s", token.AccessToken)
		}
	case conf.TokenModeCustom:
		str, err := getJwtFromToken(token)
		if err != nil {
			return nil, err
		}
		if c.FieldsMap == nil {
			return nil, stderr.InvalidParam.Errorf("fields_map must be set for custom token mode")
		}

		ct := &CustomToken{
			RawClaims: make(map[string]interface{}),
			FieldMap:  c.FieldsMap,
		}

		if err = json.Unmarshal(str, &ct.RawClaims); err != nil {
			return nil, stderr.InvalidParam.Errorf("can not json unmarshal custom token: %s", token.AccessToken)
		}

		ct.userEmail = getField(ct.RawClaims, ct.FieldMap["email"])
		ct.userFullName = getField(ct.RawClaims, ct.FieldMap["fullname"])
		ct.userName = getField(ct.RawClaims, ct.FieldMap["username"])
		if ct.userName == "" {
			stdlog.Warnf("raw claims: \n%s", toolkit.SprintPrettyJson(ct.RawClaims))
			stdlog.Warnf("custom config: \n%s", toolkit.SprintPrettyJson(c.FieldsMap))
			return nil, stderr.InvalidParam.Errorf("username field is empty, please check your oauth2 config.")
		}

		return ct, nil
	case conf.TokenModeExternal:
		if c.UserInfoFetcher == nil {
			return nil, stderr.InvalidParam.Errorf("user_info_fetcher must be configured for external mode")
		}

		// 调用外部接口获取用户信息
		ut, err := fetchUserInfo(token, c.UserInfoFetcher)
		if err != nil {
			stdlog.WithError(err).Infof("fetch user info with token and config: token: %+v, config: %+v", token, c.UserInfoFetcher)
			return nil, stderr.Internal.Cause(err, "fetch user info")
		}
		stdlog.Infof("got userinfo from external token: %s", toolkit.SprintPrettyJson(ut))
		return ut, nil
	default:
		return nil, stderr.InvalidParam.Errorf("invalid token mode %s", c.TokenMode)
	}
	return t, nil
}

func fetchUserInfo(tk *oauth2.Token, c *conf.UserInfoFetcherConfig) (BaseToken, error) {
	// FetchUserInfo 调用外部用户信息接口，并返回提取后的用户信息字段

	// 1. 替换查询参数模板
	queryParams := make(map[string]string)
	for k, v := range c.QueryParams {
		val, err := ExecuteTemplate(v, tk)
		if err != nil {
			stdlog.Warnf("failed to execute query param template %q: %v", v, err)
			return nil, stderr.Internal.Errorf("failed to render query param %s", k)
		}
		queryParams[k] = val
	}

	// 2. 替换请求头模板
	headers := make(map[string]string)
	for k, v := range c.Headers {
		val, err := ExecuteTemplate(v, tk)
		if err != nil {
			stdlog.Warnf("failed to execute header template %q for %q: %v", v, k, err)
			return nil, stderr.Internal.Errorf("failed to render header %s", k)
		}
		headers[k] = val
	}

	// 3. 替换请求体模板
	var body string
	if c.Body != "" {
		val, err := ExecuteTemplate(c.Body, tk)
		if err != nil {
			stdlog.Warnf("failed to execute body template %q: %v", c.Body, err)
			return nil, stderr.Internal.Errorf("failed to render request body")
		}
		body = val
	}

	// 4. 构造请求 URL（带查询参数）
	endpoint := c.Endpoint
	if len(queryParams) > 0 {
		queryStr := ""
		for k, v := range queryParams {
			queryStr += "&" + k + "=" + v
		}
		endpoint += "?" + strings.TrimPrefix(queryStr, "&")
	}

	// 5. 创建 HTTP 客户端并发送请求
	req, err := http.NewRequest(c.Method, endpoint, strings.NewReader(body))
	if err != nil {
		return nil, stderr.Internal.Errorf("failed to create http request: %v", err)
	}

	// 设置请求头
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	// 执行请求
	client := &http.Client{
		Timeout: 10 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	resp, err := client.Do(req)
	if err != nil {
		return nil, stderr.Internal.Errorf("failed to do http request: %v", err)
	}
	defer resp.Body.Close()

	// 6. 解析响应体
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, stderr.Internal.Errorf("failed to read response body: %v", err)
	}

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		stdlog.Warnf("external user info fetch failed with status code %d, response: %s", resp.StatusCode, string(bodyBytes))
		return nil, stderr.Internal.Errorf("http request failed with status code %d", resp.StatusCode)
	}

	var userInfo map[string]interface{}
	if err = json.Unmarshal(bodyBytes, &userInfo); err != nil {
		stdlog.Warnf("failed to unmarshal external user info response: %v\n%s", err, string(bodyBytes))
		return nil, stderr.Internal.Errorf("failed to parse user info response")
	}

	return &ExternalToken{
		RawClaims: userInfo,
		Config:    c,
	}, nil
}

// ExecuteTemplate 渲染模板字符串
func ExecuteTemplate(tmplStr string, data interface{}) (string, error) {
	tmpl, err := template.New("dynamic").Parse(tmplStr)
	if err != nil {
		return "", err
	}
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", err
	}
	return buf.String(), nil
}
