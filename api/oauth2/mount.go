package oauth2

import (
	"net/http"

	"github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"

	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/service/rbac"
)

func NewOAuth2API(prefix string, rbacUs *rbac.UserService, ) *restful.WebService {
	return (&Resource{
		rbacUs: rbacUs,
	}).WebService(prefix)
}

type Resource struct {
	rbacUs *rbac.UserService
}

func (r *Resource) WebService(prefix string) *restful.WebService {
	tags := []string{"OAuth2认证管理"}
	metaK := restfulspec.KeyOpenAPITags

	ws := new(restful.WebService)
	ws.Path(prefix)
	ws.
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.GET("/auth").
		To(r.<PERSON>).
		Doc("用来接收OAuth2登录成功后的redirect请求,通常会携带 session_state 与 code 两个请求参数").Metadata(metaK, tags).
		Param(codeQP.Param()).
		Param(sessionStateQP.Param()).
		Param(serviceQP.Param()).
		Produces(restful.MIME_JSON).Writes(helper.EmptyRsp{}).
		Returns(http.StatusFound, http.StatusText(http.StatusFound), helper.EmptyRsp{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	return ws
}
