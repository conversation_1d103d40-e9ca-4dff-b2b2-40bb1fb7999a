package oauth2

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type Cipher interface {
	Encrypt(plaintext []byte) (string, error)
	Decrypt(ciphertext string) (string, error)
}

func NewRandomCipher() Cipher {
	bs := make([]byte, 32)
	n, err := io.ReadFull(rand.Reader, bs)
	if err != nil || n != 32 {
		stdlog.WithError(err).Warnf("read 32 byte random bytes")
		bs = []byte("32-byte-long-encryption-key-1234")
	}

	return &innerCipher{encryptionKey: bs} // 32字节的加密密钥}
}

type innerCipher struct {
	encryptionKey []byte
}

func (c *innerCipher) Encrypt(plaintext []byte) (string, error) {
	block, err := aes.NewCipher(c.encryptionKey)
	if err != nil {
		return "", err
	}

	ciphertext := make([]byte, aes.BlockSize+len(plaintext))
	iv := ciphertext[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", err
	}

	stream := cipher.NewCFBEncrypter(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], plaintext)

	return base64.URLEncoding.EncodeToString(ciphertext), nil
}

func (c *innerCipher) Decrypt(ciphertext string) (string, error) {
	block, err := aes.NewCipher(c.encryptionKey)
	if err != nil {
		return "", err
	}

	decodedCiphertext, err := base64.URLEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	if len(decodedCiphertext) < aes.BlockSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	iv := decodedCiphertext[:aes.BlockSize]
	decodedCiphertext = decodedCiphertext[aes.BlockSize:]

	stream := cipher.NewCFBDecrypter(block, iv)
	stream.XORKeyStream(decodedCiphertext, decodedCiphertext)

	return string(decodedCiphertext), nil
}
