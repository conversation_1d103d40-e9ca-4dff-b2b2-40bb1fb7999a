package oauth2

import (
	"context"
	"crypto/tls"
	"encoding/base64"
	"net/http"
	"time"

	"github.com/emicklei/go-restful/v3"
	"golang.org/x/oauth2"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/central-auth-service/api/auth"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
	auth2 "transwarp.io/applied-ai/central-auth-service/utils/auth"
)

var (
	sessionStateQP = stdsrv.NewQueryParam("session_state", " OAuth2 认证过程中用于维持会话状态的一个参数，通常用来防止CSRF攻击。")
	codeQP         = stdsrv.NewQueryParam("code", "授权码，用于后续换取访问令牌。")
	stateQP        = stdsrv.NewQueryParam("state", "客户端生成的防 CSRF Token（原样返回）")
	serviceQP      = stdsrv.NewQueryParam(auth2.FinalRedirectServiceQueryParamKey, "登录成功后返回时, 携带的登录前原始浏览器地址")
)

func NewOauth2ClientContext(parentCtx context.Context) context.Context {
	return context.WithValue(parentCtx, oauth2.HTTPClient, http.Client{
		Timeout: time.Minute,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	})
}

// LoginWithToken
// http://**************:27281/api/v1/oauth2/auth
func (r *Resource) LoginWithToken(request *restful.Request, response *restful.Response) {
	code := codeQP.GetValue(request)
	ssState := sessionStateQP.GetValue(request)
	stdlog.Infof("ss state: %s", ssState)
	state := stateQP.GetValue(request)
	var service = ""
	if state != "" {
		stateBs, err := base64.StdEncoding.DecodeString(state)
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "invalid state"))
			return
		}
		service = string(stateBs)
	}

	// post oauth2 token api
	// service := serviceQP.GetValue(request)

	// oc := conf.C.Auth.OAuth2.WithRedirectParam("service", service)
	// tk, err := oc.Exchange(request.Request.Context(), code, oauth2.SetAuthURLParam("testaok", "testaov"))
	oc := conf.C.Auth.OAuth2.ToOAuth2Config()
	tk, err := oc.Exchange(NewOauth2ClientContext(request.Request.Context()), code)
	if err != nil {
		helper.ErrorResponse(response, stderr.Trace(err))
		return
	}
	if !tk.Valid() {
		stdlog.Infof("tk is not valid %+v", tk)
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("oauth2 token is not valid"))
		return
	}
	stdlog.Infof("tk is valid %+v", tk)
	mtk, err := parseUserInfoFromToken(tk, conf.C.Auth.OAuth2)
	if err != nil {
		stdlog.WithError(err).Errorf("parse oauth2 token")
		helper.ErrorResponse(response, stderr.Wrap(err, "parse oauth2 token"))
		return
	}

	u := r.rbacUs.GetUserByName(mtk.GetUserName())
	if u == nil || u.Name == "" {
		err = r.rbacUs.CreateUser("thinger", AsUserReq(mtk), nil)
		if err != nil {
			stdlog.WithError(err).Errorf("create user")
			helper.ErrorResponse(response, stderr.Wrap(err, "create user"))
			return
		}
	}
	if err = auth.GetAuthHandler().SaveSession(response, request.Request, &dao.User{Name: mtk.GetUserName()}); err != nil {
		helper.ErrorResponse(response, stderr.Wrap(err, "saving login session"))
		return
	}

	helper.Redirect(request, response, service)
	return
}
