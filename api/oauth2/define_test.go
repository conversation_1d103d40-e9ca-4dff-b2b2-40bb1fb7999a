package oauth2

import (
	"reflect"
	"testing"

	"golang.org/x/oauth2"
)

func Test_parseToken(t *testing.T) {
	type args struct {
		token *oauth2.Token
	}
	tests := []struct {
		name    string
		args    args
		want    *TranswarpToken
		wantErr bool
	}{
		{
			name: "",
			args: args{

				token: &oauth2.Token{
					AccessToken: "AccessToken:************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
				},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseUserInfoFromToken(tt.args.token, nil)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseUserInfoFromToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("parseUserInfoFromToken() got = %v, want %v", got, tt.want)
			}
		})
	}
}
