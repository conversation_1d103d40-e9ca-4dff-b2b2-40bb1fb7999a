package oauth2

import (
	"testing"
	"time"

	"golang.org/x/oauth2"

	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/central-auth-service/conf"
)

func Test_fetchUserInfo(t *testing.T) {
	type args struct {
		tk *oauth2.Token
		c  *conf.UserInfoFetcherConfig
	}
	tests := []struct {
		name    string
		args    args
		want    BaseToken
		wantErr bool
	}{
		{
			name: "",
			args: args{
				tk: &oauth2.Token{
					AccessToken:  "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
					TokenType:    "",
					RefreshToken: "",
					Expiry:       time.Time{},
					ExpiresIn:    0,
				},
				c: conf.C.Auth.OAuth2.UserInfoFetcher,
				// c: &conf.UserInfoFetcherConfig{
				// 	Endpoint: "https://oidc.cregc.com.cn/nepoch-oidc/userinfo",
				// 	Method:   "GET",
				// 	Headers:  nil,
				// 	QueryParams: map[string]string{
				// 		"access_token": "{{.AccessToken}}",
				// 	},
				// 	Body: "",
				// 	ResponseMap: &conf.Oauth2UserInfoRespMap{
				// 		Username: "{{ .account }}",
				// 		Fullname: "{{ .name }}",
				// 		Email:    "{{ .email }}",
				// 		Mobile:   "{{ .mobilephone }}",
				// 	},
				// },
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := fetchUserInfo(tt.args.tk, tt.args.c)
			if (err != nil) != tt.wantErr {
				t.Errorf("fetchUserInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf(toolkit.SprintPrettyJson(got))
			t.Logf("%s", got.GetUserName())
			t.Logf("%s", got.GetUserEmail())
			t.Logf("%s", got.GetUserFullName())

		})
	}
}
