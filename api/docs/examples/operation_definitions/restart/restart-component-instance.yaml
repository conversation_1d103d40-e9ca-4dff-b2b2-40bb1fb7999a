apiVersion: oam.tdc.transwarp.io/v1alpha1
kind: OperationDefinition
metadata:
  labels:
    operator: restart
    operand-kind: ComponentInstance
    default: "true"
  namespace: tdcsys
  name: restart-component-instance
spec:
  operand:
    kind: ComponentInstance
  operator: restart
  policies:
    - name: sequential
      shortName: seq
      operationTrait:
        cue:
          template: |
            package trait
            import "transwarp.io/tdc/oam/op"
            #PreProcessor: "op-trait-template"

            context: {
              operationDefinition: {
                name: string
              }

              operandRef: op.meta.#MetaData
              
              policy: {
                name: string
                ...
              }
            }

            result: {
              operationDefinition: context.operationDefinition.name,
              policy: context.policy.name,
              operand: context.operandRef,
              dependsOn: [],
              includes: [
                {
                  operationDefinition: "stop-component-instance",
                  policy: "sequential",
                  operand: context.operandRef
                },
                {
                  operationDefinition: "start-component-instance",
                  policy: "sequential",
                  operand: context.operandRef
                }
              ]
            }
      operationPlan:
        cue:
          template: |
            import (
              "list"
              "transwarp.io/tdc/oam/op"
              )
            
            #Debug:        *"false" | "true"
            #PreProcessor: "op-plan-template"
            
            context:  op.operation.#PlanTemplateContext
            _context: context
            
            result: {
              executeMode: op.workflow.#ModeStep
                opdRef:      _context.operand
                opdMeta:     op.meta.#Meta & opdRef
                
                // 1. Pre check step is not required
                // 2. Generate cascading workflow-typed steps
                genInclWfSteps: op.operation.#GenerateWorkflowSteps & {
                  parentContext: _context
                    opTraits:      _context.opTrait.includes
                    mode:          executeMode
                }
                inclWorkflowSteps: genInclWfSteps.result.steps
                // 3. Post check step is not required
                // 4. Generate workflow
                workflow: op.workflow.#Workflow & {
                  template: op.workflow.#Template & {
                    context: {
                      operandRef: {
                        kind:      _context.operand.kind
                          namespace: _context.operand.namespace
                          name:      _context.operand.name
                      }
                    }
                      mode: {
                        steps:    executeMode
                          subSteps: op.workflow.#ModeStep
                      }
                      steps: list.Concat([
                        inclWorkflowSteps,
                      ])
                  }
                }
            }

