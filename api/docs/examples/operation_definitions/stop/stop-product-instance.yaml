apiVersion: oam.tdc.transwarp.io/v1alpha1
kind: OperationDefinition
metadata:
  labels:
    operator: stop
    operand-kind: ProductInstance
    default: "true"
  namespace: tdcsys
  name: stop-product-instance
spec:
  operand:
    kind: ProductInstance
  operator: stop
  policies:
    - name: sequential
      shortName: seq
      operationTrait:
        cue:
          template: |
            package trait
            import "transwarp.io/tdc/oam/op"
            #PreProcessor: "op-trait-template"

            context: {
              operationDefinition: {
                name: string
              }

              operandRef: op.meta.#MetaData
              
              policy: {
                name: string
                ...
              }
            }

            componentInstances: op.meta.#ComponentInstances & {kind: "ProductInstance", name: context.operandRef.name, namespace: context.operandRef.namespace}
            result: {
              operationDefinition: context.operationDefinition.name,
              policy: context.policy.name,
              operand: context.operandRef,
              dependsOn: [],
              includes: [
                for ci in componentInstances.result {
                  operationDefinition: "stop-component-instance",
                  policy: "sequential",
                  operand: ci
                }
              ]
            }
      operationPlan:
        cue:
          template: |
            import (
              "list"
              "transwarp.io/tdc/oam/op"
            )

            #Debug:        *"false" | "true"
            #PreProcessor: "op-plan-template"

            // ----------Type Definition Start-----------
            #callServiceAPI: {
              //若host和port传，默认使用用户传入的host和port，否则使用label和namespace进行service过滤
              service: op.service.#Service
              path:    string
              method:  op.service.#HTTPMethod
              body?: {...}
              header?: [string]: string
              expectedBody?: {...}
            }
            // ----------Type Definition End-----------

            context:  op.operation.#PlanTemplateContext
            _context: context

            result: {
              executeMode: *op.workflow.#ModeDAG | op.workflow.#Mode
              opdRef:      _context.operand
              opdMeta:     op.meta.#Meta & opdRef

              // 1. Pre check step is not required
              // 2. Generate cascading workflow-typed steps
              genInclWfSteps: op.operation.#GenerateWorkflowSteps & {
                parentContext: _context
                opTraits:      _context.opTrait.includes
                mode:          executeMode
              }
              genInclWfStepsRes:     genInclWfSteps.result
              inclWorkflowSteps:     genInclWfStepsRes.steps
              dependedOnInclWfSteps: genInclWfStepsRes.dependedOn

              // 3. Generate post check step
              postCheckStep: op.workflow.#Step & {
                op.workflow.#StepBase & {
                  name:      "postcheck-for-product-instance-terminate"
                  dependsOn: dependedOnInclWfSteps
                  properties: {
                    callServiceAPI: #callServiceAPI & {
                      service: op.service.ServiceWALM
                      path:    "/api/v1/product-instance/namespace/" + opdMeta.namespace + "/name/" + opdMeta.name
                      method:  op.service.#MethodGET
                      expectedBody: runningStatus: {
                        isTerminate: true
                        isCompleted: true
                        isActive:    false
                      }
                    }
                  }
                }
                singleStep: op.workflow.#SingleStep & {
                  definition: op.workflow.#StepDefinition & {type: "call-service-api"}
                }
              }

              // 4. Generate workflow
              workflow: op.workflow.#Workflow & {
                template: op.workflow.#Template & {
                  context: {
                    operandRef: {
                      kind:      _context.operand.kind
                      namespace: _context.operand.namespace
                      name:      _context.operand.name
                    }
                  }
                  mode: {
                    steps:    executeMode
                    subSteps: op.workflow.#ModeStep
                  }
                  steps: list.Concat([
                    inclWorkflowSteps,
                    [postCheckStep],
                  ])
                }
              }
            }
