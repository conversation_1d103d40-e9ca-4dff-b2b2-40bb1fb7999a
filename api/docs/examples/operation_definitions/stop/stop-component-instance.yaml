apiVersion: oam.tdc.transwarp.io/v1alpha1
kind: OperationDefinition
metadata:
  labels:
    operator: stop
    operand-kind: ComponentInstance
    default: "true"
  annotations: {}
  namespace: tdcsys
  name: stop-component-instance
spec:
  operand:
    kind: ComponentInstance
  operator: stop
  policies:
    - name: sequential
      shortName: seq
      content: "{}"

      operationTrait:
        cue:
          template: |
            package trait
            import "transwarp.io/tdc/oam/op"
            #PreProcessor: "op-trait-template"

            context: {
              operationDefinition: {
                name: string
              },
              operandRef: op.meta.#MetaData,
              
              policy: {
                name: string
              }
            }

            deps: op.meta.#Dependencies & {kind: "ComponentInstance", name: context.operandRef.name, namespace: context.operandRef.namespace, direction: "BACKWARD"}
            roles: op.meta.#Roles & {kind: "ComponentInstance", name: context.operandRef.name, namespace: context.operandRef.namespace, sortByDependency: "DESCENDING"}
            result: {
              operationDefinition: context.operationDefinition.name,
              policy: context.policy.name,
              operand: context.operandRef,
              dependsOn: [
                for dep in deps.result {
                  operationDefinition: context.operationDefinition.name,
                  policy: context.policy.name,
                  operand: dep
                }
              ], 
              includes: [
                for role in roles.result {
                  operationDefinition: "stop-role",
                  policy: "default",
                  operand: role
                }
              ]
            }

      operationPlan:
        cue:
          template: |
            import (
              "list"
              "transwarp.io/tdc/oam/op"
            )

            #Debug:        *"false" | "true"
            #PreProcessor: "op-plan-template"

            // ----------Type Definition Start-----------
            #compinstDep: {
              kind:      string
              namespace: string
              name:      string
            }

            #callServiceAPI: {
              //若host和port传，默认使用用户传入的host和port，否则使用label和namespace进行service过滤
              service: op.service.#Service
              path:    string
              method:  op.service.#HTTPMethod
              body?: {...}
              header?: [string]: string
              expectedBody?: {...}
            }
            // ----------Type Definition End-----------

            context:  op.operation.#PlanTemplateContext
            _context: context

            result: {
              executeMode: *op.workflow.#ModeDAG | op.workflow.#Mode
              opdRef:      _context.operand
              opdMeta:     op.meta.#Meta & opdRef

              // Get component instance dependencies
              getdeps: op.meta.#Dependencies & opdRef & {direction: op.meta.#DirBackward}
              deps:    getdeps.result
              compinstDeps: [ for dep in deps {
                #compinstDep & {
                  kind:      dep.kind
                  namespace: dep.namespace
                  name:      dep.name
                }
              }]

              // 1. Generate pre check step
              preCheckStep: op.workflow.#Step & {
                op.workflow.#StepBase & {
                  name: "precheck-for-component-instance-terminate"
                }
                stepGroup: op.workflow.#StepGroup & {
                  subSteps: [
                    op.workflow.#SubStep & {
                      op.workflow.#StepBase & {
                        name: "check-component-instance-dependencies-terminated-status"
                        inputs: [{
                          from:         "context.operandRef.dependencies"
                          parameterKey: "operandRefs"
                        }]
                        properties: expectStatus: "TERMINATED"
                      }
                      op.workflow.#SingleStep & {
                        definition: op.workflow.#StepDefinition & {type: "check-component-instance-status"}
                      }
                    },
                  ]
                }
              }

              // 2. Generate cascading workflow-typed steps
              genInclWfSteps: op.operation.#GenerateWorkflowSteps & {
                parentContext: _context
                opTraits:      _context.opTrait.includes
                mode:          executeMode
                dependsOn: [preCheckStep.name]
              }
              genInclWfStepsRes:     genInclWfSteps.result
              inclWorkflowSteps:     genInclWfStepsRes.steps
              dependedOnInclWfSteps: genInclWfStepsRes.dependedOn

              // 3. Generate post check step
              postCheckStep: op.workflow.#Step & {
                op.workflow.#StepBase & {
                  name:      "postcheck-for-component-instance-terminate"
                  dependsOn: dependedOnInclWfSteps
                }
                stepGroup: op.workflow.#StepGroup & {
                  subSteps: [
                    op.workflow.#SubStep & {
                      op.workflow.#StepBase & {
                        name: "check-component-instance-terminated-status"
                        properties: {
                          callServiceAPI: #callServiceAPI & {
                            service: op.service.ServiceWALM
                            path:    "/api/v1/component-instance/namespace/" + opdMeta.namespace + "/name/" + opdMeta.name
                            method:  op.service.#MethodGET
                            expectedBody: runningStatus: {
                              isTerminate: true
                              isCompleted: true
                              isActive:    false
                            }
                          }
                        }
                      }
                      op.workflow.#SingleStep & {
                        definition: op.workflow.#StepDefinition & {type: "call-service-api"}
                      }
                    },
                  ]
                }
              }

              // 4. Generate workflow
              workflow: op.workflow.#Workflow & {
                template: op.workflow.#Template & {
                  context: {
                    operandRef: {
                      kind:         _context.operand.kind
                      namespace:    _context.operand.namespace
                      name:         _context.operand.name
                      dependencies: [...#compinstDep] & compinstDeps
                    }
                  }
                  mode: {
                    steps:    executeMode
                    subSteps: op.workflow.#ModeStep
                  }
                  // 把precheck step, inline workflow-typed steps 和 postcheck step 按序加入workflow中
                  steps: list.Concat([
                    [preCheckStep],
                    inclWorkflowSteps,
                    [postCheckStep],
                  ])
                }
              }
            }
