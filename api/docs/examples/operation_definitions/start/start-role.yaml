apiVersion: oam.tdc.transwarp.io/v1alpha1
kind: OperationDefinition
metadata:
  name: start-role
  namespace: tdcsys
  labels:
    operator: start
    operand-kind: Role
    default: "true"
spec:
  operand:
    kind: Role
  operator: start
  policies:
    - name: default
      operationTrait:
        cue:
          template: |
            package trait
            import "transwarp.io/tdc/oam/op"
            #Debug:        *"false" | "true"
            #PreProcessor: "op-trait-template"

            context: {
            	operationDefinition: {
            		name: string
            	}
            	operandRef: op.meta.#MetaData

            	policy: {
            		name: string
            		content: {
            			roles: [...string]
            		}
            	}
            }

            roleDeps: op.meta.#Dependencies & {
              kind: "Role"
              namespace: context.operandRef.namespace
              name: context.operandRef.name
              parent: context.operandRef.parent
            }

            result: {
            	operationDefinition: context.operationDefinition.name
            	policy:              context.policy.name
            	operand:             context.operandRef
            	dependsOn: [for dep in roleDeps.result {
                operationDefinition: context.operationDefinition.name
                policy: "default"
                operand: dep
              }]
            	includes: []
            }
      operationPlan:
        cue:
          template: |
            import "transwarp.io/tdc/oam/op"

            #Debug:        *"false" | "true"
            #PreProcessor: "op-plan-template"

            // ----------Type Definition Start-----------
            #workloadRef: {
              apiVersion: string
              kind:       string
              name:       string
              namespace:  string
            }

            #roleRef: {
              kind:         string
              namespace:    string
              name:         string
              workloadRef?: #workloadRef
            }

            #roleDep: #roleRef

            // ----------Type Definition End-----------

            context:  op.operation.#PlanTemplateContext
            _context: context

            result: {
              // 使用一个predefined Workflow template创建一个workflow
              workflowRef: "start-role"
              opdRef:      _context.operand
              opdMeta:     op.meta.#Meta & opdRef
              role:        op.meta.#Role & opdMeta
              roleRef:     #roleRef & {
                kind:      role.kind
                namespace: role.namespace
                name:      role.name
              }

              // Get workload reference
              getWlRef: op.meta.#GetWorkloadRef & {
                kind:   opdRef.kind
                name:   opdRef.name
                parent: opdRef.parent
              }
              wlRefRes: getWlRef.result
              wlRef:    #workloadRef & {
                apiVersion: wlRefRes.apiVersion
                kind:       wlRefRes.kind
                name:       wlRefRes.name
                namespace:  wlRefRes.namespace
              }

              // Get role dependencies
              getdeps: op.meta.#Dependencies & opdRef
              deps:    getdeps.result
              roleDepMap: {
                for idx, dep in deps {
                  "\(idx)": {
                    // for each dep, query workload ref
                    getWlRef: op.meta.#GetWorkloadRef & {
                      kind:   dep.kind
                      name:   dep.name
                      parent: dep.parent
                    }
                    wlRefRes: getWlRef.result
                    role:     #roleRef & {
                      kind:        dep.kind
                      namespace:   dep.namespace
                      name:        dep.name
                      workloadRef: #workloadRef & {
                        apiVersion: wlRefRes.apiVersion
                        kind:       wlRefRes.kind
                        name:       wlRefRes.name
                        namespace:  wlRefRes.namespace
                      }
                    }
                  }
                }
              }
              roleDeps: [ for idx, dep in deps {roleDepMap["\(idx)"].role}]

              // Generate workflow
              workflow: op.workflow.#Workflow & {
                reference: {
                  reference: workflowRef
                  context: {
                    operandRef: {
                      kind:         roleRef.kind
                      namespace:    roleRef.namespace
                      name:         roleRef.name
                      workloadRef:  #workloadRef & wlRef
                      dependencies: [...#roleDep] & roleDeps
                    }
                  }
                }
              }
            }




