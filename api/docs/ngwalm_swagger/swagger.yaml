definitions:
  github_com_go-errors_errors.Error:
    properties:
      err: {}
    type: object
  github_com_kubevela_workflow_api_condition.Condition:
    properties:
      lastTransitionTime:
        description: |-
          LastTransitionTime is the last time this condition transitioned from one
          status to another.
        type: string
      message:
        description: |-
          A Message containing details about this condition's last transition from
          one status to another, if any.
          +optional
        type: string
      reason:
        allOf:
          - $ref: '#/definitions/github_com_kubevela_workflow_api_condition.ConditionReason'
        description: A Reason for this condition's last transition from one status
          to another.
      status:
        allOf:
          - $ref: '#/definitions/k8s_io_api_core_v1.ConditionStatus'
        description: Status of this condition; is it currently True, False, or Unknown?
      type:
        allOf:
          - $ref: '#/definitions/github_com_kubevela_workflow_api_condition.ConditionType'
        description: |-
          Type of this condition. At most one of each condition type may apply to
          a resource at any point in time.
    type: object
  github_com_kubevela_workflow_api_condition.ConditionReason:
    enum:
      - Available
      - Unavailable
      - Creating
      - Deleting
      - ReconcileSuccess
      - ReconcileError
    type: string
    x-enum-varnames:
      - ReasonAvailable
      - ReasonUnavailable
      - ReasonCreating
      - ReasonDeleting
      - ReasonReconcileSuccess
      - ReasonReconcileError
  github_com_kubevela_workflow_api_condition.ConditionType:
    enum:
      - Ready
      - Synced
    type: string
    x-enum-varnames:
      - TypeReady
      - TypeSynced
  k8s_io_api_core_v1.ConditionStatus:
    enum:
      - "True"
      - "False"
      - Unknown
    type: string
    x-enum-varnames:
      - ConditionTrue
      - ConditionFalse
      - ConditionUnknown
  k8s_io_api_core_v1.ObjectReference:
    properties:
      apiVersion:
        description: |-
          API version of the referent.
          +optional
        type: string
      fieldPath:
        description: |-
          If referring to a piece of an object instead of an entire object, this string
          should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
          For example, if the object reference is to a container within a pod, this would take on a value like:
          "spec.containers{name}" (where "name" refers to the name of the container that triggered
          the event) or if no container name is specified "spec.containers[2]" (container with
          index 2 in this pod). This syntax is chosen only to have some well-defined way of
          referencing a part of an object.
          TODO: this design is not final and this field is subject to change in the future.
          +optional
        type: string
      kind:
        description: |-
          Kind of the referent.
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
          +optional
        type: string
      name:
        description: |-
          Name of the referent.
          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
          +optional
        type: string
      namespace:
        description: |-
          Namespace of the referent.
          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
          +optional
        type: string
      resourceVersion:
        description: |-
          Specific resourceVersion to which this reference is made, if any.
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
          +optional
        type: string
      uid:
        description: |-
          UID of the referent.
          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
          +optional
        type: string
    type: object
  pkg_server_rest_v1_meta.FeatureQueryRequest:
    properties:
      reference:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawReference'
    type: object
  pkg_server_rest_v1_meta.FeatureQueryResponse:
    properties:
      feature:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawFeature'
      reference:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawReference'
    type: object
  pkg_server_rest_v1_operation.DefinitionRef:
    properties:
      operatorWithPolicy:
        allOf:
          - $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.OperatorWithPolicy'
        description: Otherwise, provide operator and policy, and thus operation definition
          policy is left to be selected by resolution order
      policyDefinitionRef:
        allOf:
          - $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.PolicyDefinitionReference'
        description: Select a particular operation definition policy
    type: object
  pkg_server_rest_v1_operation.Handler:
    properties:
      workflow:
        $ref: '#/definitions/v1alpha1.WorkflowRun'
    type: object
  pkg_server_rest_v1_operation.Request:
    properties:
      definitionRef:
        $ref: '#/definitions/pkg_server_rest_v1_operation.DefinitionRef'
      operandRef:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawProperty'
      parameter: {}
    type: object
  pkg_server_rest_v1_snapshot.RecoverCompInstRequest:
    properties:
      componentInstance:
        description: ComponentInstance in snapshot to recover
        type: string
      dryrun:
        description: Dryrun prevents from transaction to be committed, instead, it
          gives result back to client
        type: boolean
      snapshotId:
        type: integer
      targetNamespace:
        description: TargetNamespace to recover component instance
        type: string
      targetProductInstance:
        description: TargetProductInstance to recover component instance
        type: string
    type: object
  pkg_server_rest_v1_snapshot.Request:
    properties:
      dryrun:
        description: Dryrun prevents from transaction to be committed, instead, it
          gives result back to client
        type: boolean
      snapshotId:
        description: SnapshotId used for recovery
        type: integer
      targetNamespace:
        description: TargetNamespace to recover metadata
        type: string
    type: object
  pkg_server_rest_v1_tenant.TDHManagerInfo:
    properties:
      managerPassword:
        type: string
      managerUrl:
        type: string
      managerUsername:
        type: string
    type: object
  pkg_server_rest_v1_tenant.TDHTenantRequest:
    properties:
      createTime:
        type: string
      creator:
        type: string
      managerInfo:
        $ref: '#/definitions/pkg_server_rest_v1_tenant.TDHManagerInfo'
      tenantDescription:
        type: string
      tenantName:
        type: string
      tenantType:
        $ref: '#/definitions/pkg_server_rest_v1_tenant.TenantType'
    type: object
  pkg_server_rest_v1_tenant.TDHTenantUpdate:
    properties:
      creator:
        type: string
      managerInfo:
        $ref: '#/definitions/pkg_server_rest_v1_tenant.TDHManagerInfo'
      tenantDescription:
        type: string
      tenantName:
        type: string
    type: object
  pkg_server_rest_v1_tenant.TenantMetadata:
    properties:
      authInfo:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_tenant.GuardianAuthInfo'
      createTime:
        type: string
      creator:
        type: string
      fedTenantName:
        type: string
      id:
        type: integer
      managerInfo:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_tenant.ManagerInfo'
      permissions:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_tenant.TenantPermission'
        type: array
      tccUrl:
        type: string
      tenantDescription:
        type: string
      tenantName:
        type: string
      tenantType:
        $ref: '#/definitions/pkg_server_rest_v1_tenant.TenantType'
      tenantUid:
        type: string
    type: object
  pkg_server_rest_v1_tenant.TenantType:
    enum:
      - TDH
      - TDC
    type: string
    x-enum-varnames:
      - TDH
      - TDC
  runtime.RawExtension:
    type: object
  transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaData:
    properties:
      id:
        type: integer
      kind:
        type: string
      name:
        type: string
      namespace:
        type: string
      parent:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaDataBase'
      resourceVersion:
        type: string
      uuid:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaDataBase:
    properties:
      id:
        type: integer
      kind:
        type: string
      name:
        type: string
      namespace:
        type: string
      uuid:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.OperationDefinitionReference:
    properties:
      name:
        type: string
      namespace:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.OperatorWithPolicy:
    properties:
      operator:
        type: string
      policy:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.PolicyDefinitionReference:
    properties:
      operationDefinitionRef:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.OperationDefinitionReference'
      policy:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawFeature:
    properties:
      content: {}
      name:
        type: string
      supported:
        type: boolean
    type: object
  transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawProperty:
    properties:
      kind:
        type: string
      properties: {}
    type: object
  transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawReference:
    type: object
  transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_v1alpha1.OperationTrait:
    properties:
      apiVersion:
        description: |-
          APIVersion defines the versioned schema of this representation of an object.
          Servers should convert recognized schemas to the latest internal value, and
          may reject unrecognized values.
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
          +optional
        type: string
      kind:
        description: |-
          Kind is a string value representing the REST resource this object represents.
          Servers may infer this from the endpoint the client submits requests to.
          Cannot be updated.
          In CamelCase.
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
          +optional
        type: string
      metadata:
        $ref: '#/definitions/v1.ObjectMeta'
      spec:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_v1alpha1.OperationTraitSpec'
    type: object
  transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_v1alpha1.OperationTraitRef:
    properties:
      operand:
        allOf:
          - $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawReference'
        description: OperandReference refers to OAM object(s)
      operationDefinition:
        description: OperationDefinition refers to an operation definition instance
        type: string
      policy:
        description: Policy refers to a specific policy in operation definition
        type: string
    type: object
  transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_v1alpha1.OperationTraitSpec:
    properties:
      dependsOn:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_v1alpha1.OperationTraitRef'
        type: array
      includes:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_v1alpha1.OperationTraitRef'
        type: array
      operand:
        allOf:
          - $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawReference'
        description: OperandReference refers to OAM object(s)
      operationDefinition:
        description: OperationDefinition refers to an operation definition instance
        type: string
      policy:
        description: Policy refers to a specific policy in operation definition
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_access_guardianV2_client.NodeVo:
    properties:
      type:
        description: Node type
        type: string
      value:
        description: Node value
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_access_guardianV2_client.PermVo:
    properties:
      action:
        type: string
      resourceVo:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_access_guardianV2_client.ResourceVo'
    type: object
  transwarp_io_TDC_ngwalm_pkg_access_guardianV2_client.ResourceVo:
    properties:
      dataSource:
        description: The NodeVo list representing the resource
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_access_guardianV2_client.NodeVo'
        type: array
      serviceName:
        description: Service name
        type: string
      serviceType:
        description: Service type
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_access_meta.ConnectionArea:
    enum:
      - internal
      - external
    type: string
    x-enum-varnames:
      - CAInternal
      - CAExternal
  transwarp_io_TDC_ngwalm_pkg_access_meta.EntryType:
    enum:
      - development
      - maintenance
      - visit
    type: string
    x-enum-varnames:
      - ETDevelopment
      - ETMaintenance
      - ETVisit
  transwarp_io_TDC_ngwalm_pkg_common.ServiceProvider:
    enum:
      - TDC
      - TDH
    type: string
    x-enum-varnames:
      - ServiceProviderTDC
      - ServiceProviderTDH
  transwarp_io_TDC_ngwalm_pkg_common_errs.ErrorCode:
    enum:
      - 10000
      - 10001
      - 10002
      - 10003
      - 10004
      - 10005
      - 11000
      - 11001
      - 11002
      - 20000
      - 20010
      - 20011
      - 20012
      - 20013
      - 21000
      - 21200
      - 21201
      - 21400
    type: integer
    x-enum-comments:
      CodeDB: Database connection errors
      CodeNotFound: Resource not found
      CodeServerError: Internal server errors
      CodeWalmService: Error invoking walm
      ErrCueCodeTemplating: Error occurred when rendering cue templates
    x-enum-varnames:
      - CodeUnknown
      - CodeRepositoryNotFound
      - CodeNotFound
      - CodeDB
      - CodeIllegalArg
      - ErrTypeConversion
      - CodeWalmService
      - CodeK8s
      - CodeClient
      - CodeServerError
      - ErrCueCodeTemplating
      - ErrCuePackageImports
      - ErrCueValueUnMarshals
      - ErrCueProcessorExecutes
      - ErrOpDefNotFound
      - ErrOpTraitNotFound
      - ErrOpTraitGenerates
      - ErrOpPlanGenerates
  transwarp_io_TDC_ngwalm_pkg_common_errs.ErrorReason:
    enum:
      - InternalError
      - UnKnown
      - NotFound
      - AlreadyExists
      - Illegal
      - NotImplemented
      - NotSupported
      - ServerTimeout
      - BadRequest
    type: string
    x-enum-varnames:
      - ErrReasonInternal
      - ErrReasonUnKnown
      - ErrReasonNotFound
      - ErrReasonAlreadyExists
      - ErrReasonIllegal
      - ErrReasonNotImplemented
      - ErrReasonNotSupported
      - ErrReasonServerTimeout
      - ErrReasonBadRequest
  transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError:
    properties:
      err:
        allOf:
          - $ref: '#/definitions/github_com_go-errors_errors.Error'
        description: Err is a wrapped error
      errorCode:
        allOf:
          - $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.ErrorCode'
        description: '--------Required fields--------'
      errorReason:
        allOf:
          - $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.ErrorReason'
        description: '--------Optional fields--------'
      message:
        type: string
      statusCode:
        type: integer
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_common_runningstate.PlainStatus:
    properties:
      info:
        type: string
      isActive:
        type: boolean
      isCompleted:
        type: boolean
      isTerminate:
        type: boolean
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.ComponentInstance:
    properties:
      componentInstanceClass:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_relationship_relationcommon.ComponentInstanceClass'
      dependencies:
        description: Dependencies contains upstream members' identity and dependency
          relationship details
        items:
          properties:
            content:
              allOf:
                - $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaData'
              description: Content of depended entity
            required:
              type: boolean
          type: object
        type: array
      displayName:
        type: string
      id:
        type: integer
      kind:
        type: string
      kubeResources:
        allOf:
          - $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.KubeResources'
        description: |-
          KubeResources are optional fields including ObjectMeta of variant kinds of k8s resources associated with this component instance
          empty by default.
      name:
        type: string
      namespace:
        type: string
      parent:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaDataBase'
      resourceVersion:
        type: string
      roles:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.Role'
        type: array
      runningStatus: {}
      serviceProvider:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common.ServiceProvider'
      staticRef:
        allOf:
          - $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_component.Component'
        description: StaticRef is reference to the static object
      status:
        type: string
      type:
        type: string
      uuid:
        type: string
      version:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.KubeResources:
    properties:
      configMaps:
        items:
          $ref: '#/definitions/v1.ObjectMeta'
        type: array
      ingresses:
        items:
          $ref: '#/definitions/v1.ObjectMeta'
        type: array
      jobs:
        items:
          $ref: '#/definitions/v1.ObjectMeta'
        type: array
      monitor:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.MonitoringResources'
      releaseConfig:
        $ref: '#/definitions/v1beta1.ReleaseConfig'
      secrets:
        items:
          $ref: '#/definitions/v1.ObjectMeta'
        type: array
      services:
        items:
          $ref: '#/definitions/v1.ObjectMeta'
        type: array
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.MonitoringResources:
    properties:
      prometheusRules:
        items:
          $ref: '#/definitions/v1.ObjectMeta'
        type: array
      serviceMonitors:
        items:
          $ref: '#/definitions/v1.ObjectMeta'
        type: array
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.Role:
    properties:
      dependencies:
        description: Dependencies contains upstream members' identity and dependency
          relationship details
        items:
          properties:
            content:
              allOf:
                - $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaData'
              description: Content of depended entity
            required:
              type: boolean
          type: object
        type: array
      id:
        type: integer
      kind:
        type: string
      name:
        type: string
      namespace:
        type: string
      parent:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaDataBase'
      resourceVersion:
        type: string
      runningStatus: {}
      staticRef:
        allOf:
          - $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_component.StaticRole'
        description: StaticRef is reference to the static object
      type:
        type: string
      uuid:
        type: string
      version:
        type: string
      workloadRef:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.RoleWorkloadRef'
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.RoleWorkloadRef:
    properties:
      apiVersion:
        type: string
      kind:
        type: string
      name:
        type: string
      namespace:
        type: string
      workloadType:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.WorkloadType'
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.WorkloadType:
    enum:
      - StatefulSet
      - WarpStatefulSet
      - Deployment
      - DaemonSet
      - Job
      - ReplicaSet
    type: string
    x-enum-varnames:
      - StatefulSet
      - WarpStatefulSet
      - Deployment
      - DaemonSet
      - Job
      - ReplicaSet
  transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_productinstance.ProductInstance:
    properties:
      dependencies:
        description: Dependencies contains upstream members' identity and dependency
          relationship details
        items:
          properties:
            content:
              allOf:
                - $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaData'
              description: Content of depended entity
            required:
              type: boolean
          type: object
        type: array
      id:
        type: integer
      items:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.ComponentInstance'
        type: array
      kind:
        type: string
      name:
        type: string
      namespace:
        type: string
      parent:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaDataBase'
      references:
        description: Referenced component instances are separately managed
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.ComponentInstance'
        type: array
      resourceVersion:
        type: string
      runningStatus:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_common_runningstate.PlainStatus'
      staticRef:
        allOf:
          - $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_product.Product'
        description: StaticRef is reference to the static object
      type:
        type: string
      uuid:
        type: string
      version:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_relationship_relationcommon.ComponentInstanceClass:
    enum:
      - sysctx
      - cluster
    type: string
    x-enum-varnames:
      - CompInstClzSysCtx
      - CompInstClzCluster
  transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleAppAppCollRel:
    properties:
      application_collection_id:
        type: string
      application_collection_type:
        type: string
      application_id:
        type: string
      id:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleAppDep:
    properties:
      application_dependency_id:
        type: string
      application_dependency_name:
        type: string
      application_dependency_type:
        type: string
      application_id:
        type: string
      application_name:
        type: string
      application_type:
        type: string
      id:
        type: string
      username:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleApplication:
    properties:
      application_type:
        type: string
      application_version:
        type: string
      auth_isolation_level:
        type: string
      cloud_provider:
        type: string
      config_flag:
        type: string
      created_time:
        type: string
      creator:
        type: string
      id:
        type: string
      macvlan_enabled:
        type: string
      modified_time:
        type: string
      msg:
        type: string
      name:
        type: string
      pv_recycle_enabled:
        type: string
      release_name:
        type: string
      share_description:
        type: string
      share_status:
        type: string
      status:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleCluster:
    properties:
      cause:
        type: string
      created_time:
        type: string
      creator:
        type: string
      data_catalog_accessing:
        type: string
      description:
        type: string
      err_log:
        type: string
      id:
        type: string
      iobound_anti_affinity_enabled:
        type: string
      message:
        type: string
      modified_time:
        type: string
      name:
        type: string
      name_on_k8s:
        type: string
      product_uuid:
        type: string
      project_name:
        type: string
      pv_recycle_enabled:
        type: string
      redeployable:
        type: string
      security_enabled:
        type: string
      status:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleSecAppRelation:
    properties:
      application_id:
        type: string
      created_time:
        type: string
      creator:
        type: string
      description:
        type: string
      id:
        type: string
      relating_method:
        type: string
      secret_id:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleSecret:
    properties:
      created_time:
        type: string
      creator:
        type: string
      data_type:
        type: string
      id:
        type: string
      modified_time:
        type: string
      name:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.ProductInstanceSnapshotTDCMeta:
    properties:
      ockleAppAppCollRel:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleAppAppCollRel'
        type: array
      ockleAppDep:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleAppDep'
        type: array
      ockleApplications:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleApplication'
        type: array
      ockleCluster:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleCluster'
      ockleSecAppRelation:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleSecAppRelation'
        type: array
      ockleSecret:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleSecret'
        type: array
      tccAppKV:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.TccAppKV'
        type: array
      tccApplication:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.TccApplication'
        type: array
      tccInstance:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.TccInstance'
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.TccAppKV:
    properties:
      application_id:
        type: string
      config_file:
        type: string
      config_type:
        type: string
      created_at:
        type: string
      data_type:
        type: string
      description:
        type: string
      id:
        type: string
      key:
        type: string
      latest_value:
        type: string
      role:
        type: string
      status:
        type: string
      value:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.TccApplication:
    properties:
      applicationId:
        type: string
      creator:
        type: string
      id:
        type: string
      instanceId:
        type: string
      jvm_deploy_status:
        type: string
      last_modified_ts:
        type: string
      need_restart_application:
        type: string
      oauth2_client_id:
        type: string
      replicas:
        type: string
      reservation_status:
        type: string
      roles_page_cache_status:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.TccInstance:
    properties:
      category_id:
        type: string
      cluster_id:
        type: string
      create_time:
        type: string
      creator:
        type: string
      description:
        type: string
      id:
        type: string
      macvlan_enabled:
        type: string
      macvlan_external_network_name:
        type: string
      macvlan_naz_name:
        type: string
      name:
        type: string
      nodes:
        type: string
      product_uuid:
        type: string
      status:
        description: ProjectId                  *string `json:"project_id,omitempty"`
        type: string
      support_podgroup:
        type: string
      tcu_num:
        type: string
      uuid:
        type: string
      visibility:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_component.Component:
    properties:
      id:
        type: integer
      kind:
        type: string
      name:
        type: string
      namespace:
        type: string
      parent:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaDataBase'
      resourceVersion:
        type: string
      type:
        type: string
      uuid:
        type: string
      version:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_component.RoleType:
    enum:
      - container
      - deployment
      - transwarpStatefulset
      - job
      - daemonset
    type: string
    x-enum-varnames:
      - RoleTypeContainer
      - RoleTypeDeploy
      - RoleTypeWsts
      - RoleTypeJob
      - RoleTypeDaemonSet
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_component.StaticRole:
    properties:
      dependsOn:
        items:
          type: string
        type: array
      id:
        type: integer
      kind:
        type: string
      name:
        type: string
      namespace:
        type: string
      parent:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaDataBase'
      resourceVersion:
        type: string
      roleType:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_component.RoleType'
      uuid:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_product.Product:
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_tenant.GuardianAuthInfo:
    properties:
      guardianPassword:
        type: string
      guardianUrl:
        type: string
      guardianUsername:
        type: string
      hosts:
        items:
          type: string
        type: array
      kdc:
        items:
          type: string
        type: array
      realm:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_tenant.ManagerInfo:
    properties:
      managerPassword:
        type: string
      managerUrl:
        type: string
      managerUsername:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_tenant.TenantPermission:
    properties:
      tdhClusterName:
        type: string
      tdhServiceName:
        type: string
      tenantUid:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.ActionResource:
    properties:
      action:
        type: string
      resourceVo:
        allOf:
          - $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.ResourceAllData'
        description: 带有AllData后缀代表返回值中，guardian已经返回了正确的值，但是为了提供给eco/tcc需要将最里面的value字段进行切割
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.AddPermsReq:
    properties:
      guardianPassword:
        type: string
      guardianUsername:
        type: string
      resourceVo:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.ResourceVo'
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.AddTrustRelationReq:
    properties:
      creator:
        type: string
      guardian_name:
        type: string
      guardian_password:
        type: string
      target_tenant_name:
        type: string
      type:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.DeletePermsReq:
    properties:
      guardianPassword:
        type: string
      guardianUsername:
        type: string
      resourceVo:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.ResourceVo'
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.DeleteTrustRelationReq:
    properties:
      guardian_name:
        type: string
      guardian_password:
        type: string
      relation_type:
        type: string
      target_tenant_name:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.GuardianPackage:
    properties:
      application:
        type: string
      ds_access_token:
        type: string
      ds_admin_password:
        type: string
      ds_admin_username:
        type: string
      guardian_server_address:
        type: string
      k8s_guardian_server_address:
        type: string
      kerberos:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.Kerberos'
      name:
        type: string
      namespace:
        type: string
      network_domain_suffix:
        type: string
      security_users_meta:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.UserSecurityMeta'
        type: array
      trusted:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.GuardianPackage'
        type: array
      trusting:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.GuardianPackage'
        type: array
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.KerbConfig:
    properties:
      hosts:
        items:
          type: string
        type: array
      kdc_addresses:
        items:
          type: string
        type: array
      network_domain_suffix:
        type: string
      principal_suffix:
        type: string
      realm:
        type: string
      tenant_name:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.Kerberos:
    properties:
      hosts:
        items:
          type: string
        type: array
      kdc_addresses:
        items:
          type: string
        type: array
      network_domain_suffix:
        type: string
      principal_suffix:
        type: string
      realm:
        type: string
      tenant_name:
        type: string
      trusts_krb_configs:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.KerbConfig'
        type: array
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException:
    properties:
      detail:
        type: string
      error_code:
        type: string
      msg:
        type: string
      request:
        type: string
      status_code:
        type: integer
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NodeAllData:
    properties:
      clusterName:
        type: string
      serviceName:
        description: 返回值需要将guardian的返回值进行拆分，value按照@符号切割成以下三个字段
        type: string
      serviceSid:
        type: string
      tenantUid:
        type: string
      type:
        description: |-
          Node value
          Value string `json:"value"`
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NodeVo:
    properties:
      clusterName:
        type: string
      serviceSid:
        description: |-
          Node value
          Value string `json:"value"`
        type: string
      tenantUid:
        description: tdh tenantuid
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.PermVoResponse:
    properties:
      actionResources:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.ActionResource'
        type: array
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.ResourceAllData:
    properties:
      dataSource:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NodeAllData'
        type: array
      serviceName:
        description: Service name
        type: string
      serviceType:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.ResourceVo:
    properties:
      dataSource:
        description: The NodeVo list representing the resource
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NodeVo'
        type: array
      serviceName:
        description: Service name tdc tenantUid
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.TrustParams:
    properties:
      relationType:
        type: string
      sourceGuardianPackage:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.GuardianPackage'
      targetGuardianPackage:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.GuardianPackage'
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.UserSecurityMeta:
    properties:
      secrets:
        items:
          type: string
        type: array
      user_name:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_operations_action_workflow.Dependency:
    properties:
      name:
        type: string
      phase:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_operations_action_workflow.WorkflowDetail:
    properties:
      dependencies:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_operations_action_workflow.Dependency'
        type: array
      endTime:
        type: string
      includes:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_operations_action_workflow.WorkflowDetail'
        type: array
      isWorkFlow:
        type: boolean
      name:
        type: string
      namespace:
        type: string
      operandKind:
        type: string
      operandName:
        type: string
      operator:
        type: string
      phase:
        type: string
      startTime:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_domains_operations_action_workflow.WorkflowSummary:
    properties:
      endTime:
        type: string
      name:
        type: string
      namespace:
        type: string
      operandKind:
        type: string
      operandName:
        type: string
      operator:
        type: string
      phase:
        type: string
      startTime:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Authentication:
    properties:
      authInfo:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ItemInfo'
        type: array
      mode:
        description: Mode support kerberos, LDAP, access_token , password
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Component:
    properties:
      component_name:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ComponentInstance:
    properties:
      componentInstanceName:
        type: string
      url:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Connection:
    properties:
      connectionArea:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_access_meta.ConnectionArea'
      connectionType:
        type: string
      dbName:
        type: string
      dbType:
        type: string
      hostname:
        type: string
      port:
        type: integer
      properties:
        type: string
      url:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_legacy_controllers.DevelopToolInfo:
    properties:
      toolType:
        type: string
      toolTypeName:
        type: string
      toolTypeNameEn:
        type: string
      tools:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Tool'
        type: array
    type: object
  transwarp_io_TDC_ngwalm_pkg_legacy_controllers.DevelopmentInfo:
    properties:
      authentications:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Authentication'
        type: array
      component_instance_version:
        type: string
      componentInstanceName:
        type: string
      componentInstanceType:
        type: string
      connections:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Connection'
        type: array
      driverInfo:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.DriverInfo'
        type: array
      others:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ItemInfo'
        type: array
      resourceUrl:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_legacy_controllers.DriverInfo:
    properties:
      componentName:
        type: string
      componentVersion:
        type: string
      driverType:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ItemInfo:
    properties:
      attributes:
        additionalProperties:
          type: string
        description: Decode support base64
        type: object
      decode:
        description: Attributes support kundb classification like backup file
        type: string
      name:
        type: string
      type:
        description: Type support string, file
        type: string
      value:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ProductDevelopmentInformation:
    properties:
      developToolInfo:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.DevelopToolInfo'
        type: array
      developmentInfo:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.DevelopmentInfo'
        type: array
      productInstanceId:
        type: integer
    type: object
  transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ProductInstance:
    properties:
      componentInstances:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ComponentInstance'
        type: array
      id:
        type: integer
      name:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Tool:
    properties:
      components:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Component'
        type: array
      description:
        type: string
      descriptionEn:
        type: string
      displayName:
        type: string
      displayNameEn:
        type: string
      entryType:
        $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_access_meta.EntryType'
      resourceUrl:
        type: string
      toolName:
        type: string
    type: object
  transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ToolComponent:
    properties:
      namespace:
        type: string
      productInstances:
        items:
          $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ProductInstance'
        type: array
    type: object
  transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException:
    properties:
      detail:
        type: string
      error_code:
        type: string
      msg:
        type: string
      request:
        type: string
      status_code:
        type: integer
    type: object
  v1.FieldsV1:
    type: object
  v1.ManagedFieldsEntry:
    properties:
      apiVersion:
        description: |-
          APIVersion defines the version of this resource that this field set
          applies to. The format is "group/version" just like the top-level
          APIVersion field. It is necessary to track the version of a field
          set because it cannot be automatically converted.
        type: string
      fieldsType:
        description: |-
          FieldsType is the discriminator for the different fields format and version.
          There is currently only one possible value: "FieldsV1"
        type: string
      fieldsV1:
        allOf:
          - $ref: '#/definitions/v1.FieldsV1'
        description: |-
          FieldsV1 holds the first JSON version format as described in the "FieldsV1" type.
          +optional
      manager:
        description: Manager is an identifier of the workflow managing these fields.
        type: string
      operation:
        allOf:
          - $ref: '#/definitions/v1.ManagedFieldsOperationType'
        description: |-
          Operation is the type of operation which lead to this ManagedFieldsEntry being created.
          The only valid values for this field are 'Apply' and 'Update'.
      subresource:
        description: |-
          Subresource is the name of the subresource used to update that object, or
          empty string if the object was updated through the main resource. The
          value of this field is used to distinguish between managers, even if they
          share the same name. For example, a status update will be distinct from a
          regular update using the same manager name.
          Note that the APIVersion field is not related to the Subresource field and
          it always corresponds to the version of the main resource.
        type: string
      time:
        description: |-
          Time is the timestamp of when the ManagedFields entry was added. The
          timestamp will also be updated if a field is added, the manager
          changes any of the owned fields value or removes a field. The
          timestamp does not update when a field is removed from the entry
          because another manager took it over.
          +optional
        type: string
    type: object
  v1.ManagedFieldsOperationType:
    enum:
      - Apply
      - Update
    type: string
    x-enum-varnames:
      - ManagedFieldsOperationApply
      - ManagedFieldsOperationUpdate
  v1.ObjectMeta:
    properties:
      annotations:
        additionalProperties:
          type: string
        description: |-
          Annotations is an unstructured key value map stored with a resource that may be
          set by external tools to store and retrieve arbitrary metadata. They are not
          queryable and should be preserved when modifying objects.
          More info: http://kubernetes.io/docs/user-guide/annotations
          +optional
        type: object
      creationTimestamp:
        description: |-
          CreationTimestamp is a timestamp representing the server time when this object was
          created. It is not guaranteed to be set in happens-before order across separate operations.
          Clients may not set this value. It is represented in RFC3339 form and is in UTC.

          Populated by the system.
          Read-only.
          Null for lists.
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
          +optional
        type: string
      deletionGracePeriodSeconds:
        description: |-
          Number of seconds allowed for this object to gracefully terminate before
          it will be removed from the system. Only set when deletionTimestamp is also set.
          May only be shortened.
          Read-only.
          +optional
        type: integer
      deletionTimestamp:
        description: |-
          DeletionTimestamp is RFC 3339 date and time at which this resource will be deleted. This
          field is set by the server when a graceful deletion is requested by the user, and is not
          directly settable by a client. The resource is expected to be deleted (no longer visible
          from resource lists, and not reachable by name) after the time in this field, once the
          finalizers list is empty. As long as the finalizers list contains items, deletion is blocked.
          Once the deletionTimestamp is set, this value may not be unset or be set further into the
          future, although it may be shortened or the resource may be deleted prior to this time.
          For example, a user may request that a pod is deleted in 30 seconds. The Kubelet will react
          by sending a graceful termination signal to the containers in the pod. After that 30 seconds,
          the Kubelet will send a hard termination signal (SIGKILL) to the container and after cleanup,
          remove the pod from the API. In the presence of network partitions, this object may still
          exist after this timestamp, until an administrator or automated process can determine the
          resource is fully terminated.
          If not set, graceful deletion of the object has not been requested.

          Populated by the system when a graceful deletion is requested.
          Read-only.
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
          +optional
        type: string
      finalizers:
        description: |-
          Must be empty before the object is deleted from the registry. Each entry
          is an identifier for the responsible component that will remove the entry
          from the list. If the deletionTimestamp of the object is non-nil, entries
          in this list can only be removed.
          Finalizers may be processed and removed in any order.  Order is NOT enforced
          because it introduces significant risk of stuck finalizers.
          finalizers is a shared field, any actor with permission can reorder it.
          If the finalizer list is processed in order, then this can lead to a situation
          in which the component responsible for the first finalizer in the list is
          waiting for a signal (field value, external system, or other) produced by a
          component responsible for a finalizer later in the list, resulting in a deadlock.
          Without enforced ordering finalizers are free to order amongst themselves and
          are not vulnerable to ordering changes in the list.
          +optional
          +patchStrategy=merge
        items:
          type: string
        type: array
      generateName:
        description: |-
          GenerateName is an optional prefix, used by the server, to generate a unique
          name ONLY IF the Name field has not been provided.
          If this field is used, the name returned to the client will be different
          than the name passed. This value will also be combined with a unique suffix.
          The provided value has the same validation rules as the Name field,
          and may be truncated by the length of the suffix required to make the value
          unique on the server.

          If this field is specified and the generated name exists, the server will return a 409.

          Applied only if Name is not specified.
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#idempotency
          +optional
        type: string
      generation:
        description: |-
          A sequence number representing a specific generation of the desired state.
          Populated by the system. Read-only.
          +optional
        type: integer
      labels:
        additionalProperties:
          type: string
        description: |-
          Map of string keys and values that can be used to organize and categorize
          (scope and select) objects. May match selectors of replication controllers
          and services.
          More info: http://kubernetes.io/docs/user-guide/labels
          +optional
        type: object
      managedFields:
        description: |-
          ManagedFields maps workflow-id and version to the set of fields
          that are managed by that workflow. This is mostly for internal
          housekeeping, and users typically shouldn't need to set or
          understand this field. A workflow can be the user's name, a
          controller's name, or the name of a specific apply path like
          "ci-cd". The set of fields is always in the version that the
          workflow used when modifying the object.

          +optional
        items:
          $ref: '#/definitions/v1.ManagedFieldsEntry'
        type: array
      name:
        description: |-
          Name must be unique within a namespace. Is required when creating resources, although
          some resources may allow a client to request the generation of an appropriate name
          automatically. Name is primarily intended for creation idempotence and configuration
          definition.
          Cannot be updated.
          More info: http://kubernetes.io/docs/user-guide/identifiers#names
          +optional
        type: string
      namespace:
        description: |-
          Namespace defines the space within which each name must be unique. An empty namespace is
          equivalent to the "default" namespace, but "default" is the canonical representation.
          Not all objects are required to be scoped to a namespace - the value of this field for
          those objects will be empty.

          Must be a DNS_LABEL.
          Cannot be updated.
          More info: http://kubernetes.io/docs/user-guide/namespaces
          +optional
        type: string
      ownerReferences:
        description: |-
          List of objects depended by this object. If ALL objects in the list have
          been deleted, this object will be garbage collected. If this object is managed by a controller,
          then an entry in this list will point to this controller, with the controller field set to true.
          There cannot be more than one managing controller.
          +optional
          +patchMergeKey=uid
          +patchStrategy=merge
        items:
          $ref: '#/definitions/v1.OwnerReference'
        type: array
      resourceVersion:
        description: |-
          An opaque value that represents the internal version of this object that can
          be used by clients to determine when objects have changed. May be used for optimistic
          concurrency, change detection, and the watch operation on a resource or set of resources.
          Clients must treat these values as opaque and passed unmodified back to the server.
          They may only be valid for a particular resource or set of resources.

          Populated by the system.
          Read-only.
          Value must be treated as opaque by clients and .
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
          +optional
        type: string
      selfLink:
        description: |-
          Deprecated: selfLink is a legacy read-only field that is no longer populated by the system.
          +optional
        type: string
      uid:
        description: |-
          UID is the unique in time and space value for this object. It is typically generated by
          the server on successful creation of a resource and is not allowed to change on PUT
          operations.

          Populated by the system.
          Read-only.
          More info: http://kubernetes.io/docs/user-guide/identifiers#uids
          +optional
        type: string
    type: object
  v1.OwnerReference:
    properties:
      apiVersion:
        description: API version of the referent.
        type: string
      blockOwnerDeletion:
        description: |-
          If true, AND if the owner has the "foregroundDeletion" finalizer, then
          the owner cannot be deleted from the key-value store until this
          reference is removed.
          See https://kubernetes.io/docs/concepts/architecture/garbage-collection/#foreground-deletion
          for how the garbage collector interacts with this field and enforces the foreground deletion.
          Defaults to false.
          To set this field, a user needs "delete" permission of the owner,
          otherwise 422 (Unprocessable Entity) will be returned.
          +optional
        type: boolean
      controller:
        description: |-
          If true, this reference points to the managing controller.
          +optional
        type: boolean
      kind:
        description: |-
          Kind of the referent.
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
        type: string
      name:
        description: |-
          Name of the referent.
          More info: http://kubernetes.io/docs/user-guide/identifiers#names
        type: string
      uid:
        description: |-
          UID of the referent.
          More info: http://kubernetes.io/docs/user-guide/identifiers#uids
        type: string
    type: object
  v1.Secret:
    properties:
      apiVersion:
        description: |-
          APIVersion defines the versioned schema of this representation of an object.
          Servers should convert recognized schemas to the latest internal value, and
          may reject unrecognized values.
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
          +optional
        type: string
      data:
        additionalProperties:
          items:
            type: integer
          type: array
        description: |-
          Data contains the secret data. Each key must consist of alphanumeric
          characters, '-', '_' or '.'. The serialized form of the secret data is a
          base64 encoded string, representing the arbitrary (possibly non-string)
          data value here. Described in https://tools.ietf.org/html/rfc4648#section-4
          +optional
        type: object
      immutable:
        description: |-
          Immutable, if set to true, ensures that data stored in the Secret cannot
          be updated (only object metadata can be modified).
          If not set to true, the field can be modified at any time.
          Defaulted to nil.
          +optional
        type: boolean
      kind:
        description: |-
          Kind is a string value representing the REST resource this object represents.
          Servers may infer this from the endpoint the client submits requests to.
          Cannot be updated.
          In CamelCase.
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
          +optional
        type: string
      metadata:
        allOf:
          - $ref: '#/definitions/v1.ObjectMeta'
        description: |-
          Standard object's metadata.
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
          +optional
      stringData:
        additionalProperties:
          type: string
        description: |-
          stringData allows specifying non-binary secret data in string form.
          It is provided as a write-only input field for convenience.
          All keys and values are merged into the data field on write, overwriting any existing values.
          The stringData field is never output when reading from the API.
          +k8s:conversion-gen=false
          +optional
        type: object
      type:
        allOf:
          - $ref: '#/definitions/v1.SecretType'
        description: |-
          Used to facilitate programmatic handling of secret data.
          More info: https://kubernetes.io/docs/concepts/configuration/secret/#secret-types
          +optional
    type: object
  v1.SecretType:
    enum:
      - Opaque
      - kubernetes.io/service-account-token
      - kubernetes.io/dockercfg
      - kubernetes.io/dockerconfigjson
      - kubernetes.io/basic-auth
      - kubernetes.io/ssh-auth
      - kubernetes.io/tls
      - bootstrap.kubernetes.io/token
    type: string
    x-enum-varnames:
      - SecretTypeOpaque
      - SecretTypeServiceAccountToken
      - SecretTypeDockercfg
      - SecretTypeDockerConfigJson
      - SecretTypeBasicAuth
      - SecretTypeSSHAuth
      - SecretTypeTLS
      - SecretTypeBootstrapToken
  v1alpha1.StepStatus:
    properties:
      firstExecuteTime:
        description: FirstExecuteTime is the first time this step execution.
        type: string
      id:
        type: string
      lastExecuteTime:
        description: LastExecuteTime is the last time this step execution.
        type: string
      message:
        description: A human readable message indicating details about why the workflowStep
          is in this state.
        type: string
      name:
        type: string
      description:
        type: string
      phase:
        $ref: '#/definitions/v1alpha1.WorkflowStepPhase'
      reason:
        description: A brief CamelCase message indicating details about why the workflowStep
          is in this state.
        type: string
      type:
        type: string
    type: object
  v1alpha1.WorkflowExecuteMode:
    properties:
      steps:
        allOf:
          - $ref: '#/definitions/v1alpha1.WorkflowMode'
        description: Steps is the mode of workflow steps execution
      subSteps:
        allOf:
          - $ref: '#/definitions/v1alpha1.WorkflowMode'
        description: SubSteps is the mode of workflow sub steps execution
    type: object
  v1alpha1.WorkflowMode:
    enum:
      - DAG
      - StepByStep
    type: string
    x-enum-varnames:
      - WorkflowModeDAG
      - WorkflowModeStep
  v1alpha1.WorkflowRun:
    properties:
      apiVersion:
        description: |-
          APIVersion defines the versioned schema of this representation of an object.
          Servers should convert recognized schemas to the latest internal value, and
          may reject unrecognized values.
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
          +optional
        type: string
      kind:
        description: |-
          Kind is a string value representing the REST resource this object represents.
          Servers may infer this from the endpoint the client submits requests to.
          Cannot be updated.
          In CamelCase.
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
          +optional
        type: string
      metadata:
        $ref: '#/definitions/v1.ObjectMeta'
      spec:
        $ref: '#/definitions/v1alpha1.WorkflowRunSpec'
      status:
        $ref: '#/definitions/v1alpha1.WorkflowRunStatus'
    type: object
  v1alpha1.WorkflowRunPhase:
    enum:
      - initializing
      - executing
      - suspending
      - terminated
      - failed
      - succeeded
      - skipped
      - manuallySkipped
    type: string
    x-enum-varnames:
      - WorkflowStateInitializing
      - WorkflowStateExecuting
      - WorkflowStateSuspending
      - WorkflowStateTerminated
      - WorkflowStateFailed
      - WorkflowStateSucceeded
      - WorkflowStateSkipped
      - WorkflowStateManuallySkipped
  v1alpha1.WorkflowRunSpec:
    properties:
      context:
        allOf:
          - $ref: '#/definitions/runtime.RawExtension'
        description: +kubebuilder:pruning:PreserveUnknownFields
      mode:
        $ref: '#/definitions/v1alpha1.WorkflowExecuteMode'
      workflowRef:
        type: string
      workflowSpec:
        $ref: '#/definitions/v1alpha1.WorkflowSpec'
    type: object
  v1alpha1.WorkflowRunStatus:
    properties:
      conditions:
        description: |-
          Conditions of the resource.
          +optional
        items:
          $ref: '#/definitions/github_com_kubevela_workflow_api_condition.Condition'
        type: array
      contextBackend:
        $ref: '#/definitions/k8s_io_api_core_v1.ObjectReference'
      endTime:
        type: string
      finished:
        type: boolean
      message:
        type: string
      mode:
        $ref: '#/definitions/v1alpha1.WorkflowExecuteMode'
      startTime:
        type: string
      status:
        $ref: '#/definitions/v1alpha1.WorkflowRunPhase'
      steps:
        items:
          $ref: '#/definitions/v1alpha1.WorkflowStepStatus'
        type: array
      suspend:
        type: boolean
      suspendState:
        type: string
      terminated:
        type: boolean
    type: object
  v1alpha1.WorkflowSpec:
    properties:
      steps:
        items:
          $ref: '#/definitions/v1alpha1.WorkflowStep'
        type: array
    type: object
  v1alpha1.WorkflowStep:
    properties:
      contextInherited:
        description: |-
          ContextInherited is set to true if the step
          should inherit parent context as its base properties.
        type: boolean
      dependsOn:
        description: DependsOn is the dependency of the step
        items:
          type: string
        type: array
      if:
        description: If is the if condition of the step
        type: string
      inputs:
        description: Inputs is the inputs of the step
        items:
          $ref: '#/definitions/v1alpha1.inputItem'
        type: array
      meta:
        allOf:
          - $ref: '#/definitions/v1alpha1.WorkflowStepMeta'
        description: Meta is the meta data of the workflow step.
      name:
        description: Name is the unique name of the workflow step.
        type: string
      description:
        description: Description this workflow step.
        type: string
      outputs:
        description: Outputs is the outputs of the step
        items:
          $ref: '#/definitions/v1alpha1.outputItem'
        type: array
      ownerReferred:
        description: |-
          OwnerReferred is set to true if the step
          should have OwnerReference to its parent.
        type: boolean
      properties:
        allOf:
          - $ref: '#/definitions/runtime.RawExtension'
        description: |-
          Properties is the properties of the step
          +kubebuilder:pruning:PreserveUnknownFields
      subSteps:
        items:
          $ref: '#/definitions/v1alpha1.WorkflowStepBase'
        type: array
      timeout:
        description: Timeout is the timeout of the step
        type: string
      type:
        description: Type is the type of the workflow step.
        type: string
    type: object
  v1alpha1.WorkflowStepBase:
    properties:
      contextInherited:
        description: |-
          ContextInherited is set to true if the step
          should inherit parent context as its base properties.
        type: boolean
      dependsOn:
        description: DependsOn is the dependency of the step
        items:
          type: string
        type: array
      if:
        description: If is the if condition of the step
        type: string
      inputs:
        description: Inputs is the inputs of the step
        items:
          $ref: '#/definitions/v1alpha1.inputItem'
        type: array
      meta:
        allOf:
          - $ref: '#/definitions/v1alpha1.WorkflowStepMeta'
        description: Meta is the meta data of the workflow step.
      name:
        description: Name is the unique name of the workflow step.
        type: string
      description:
        description: Description this workflow step.
        type: string
      outputs:
        description: Outputs is the outputs of the step
        items:
          $ref: '#/definitions/v1alpha1.outputItem'
        type: array
      ownerReferred:
        description: |-
          OwnerReferred is set to true if the step
          should have OwnerReference to its parent.
        type: boolean
      properties:
        allOf:
          - $ref: '#/definitions/runtime.RawExtension'
        description: |-
          Properties is the properties of the step
          +kubebuilder:pruning:PreserveUnknownFields
      timeout:
        description: Timeout is the timeout of the step
        type: string
      type:
        description: Type is the type of the workflow step.
        type: string
    type: object
  v1alpha1.WorkflowStepMeta:
    properties:
      alias:
        type: string
    type: object
  v1alpha1.WorkflowStepPhase:
    enum:
      - succeeded
      - failed
      - skipped
      - running
      - pending
      - manuallySkipped
    type: string
    x-enum-varnames:
      - WorkflowStepPhaseSucceeded
      - WorkflowStepPhaseFailed
      - WorkflowStepPhaseSkipped
      - WorkflowStepPhaseRunning
      - WorkflowStepPhasePending
      - WorkflowStepManuallySkipped
  v1alpha1.WorkflowStepStatus:
    properties:
      firstExecuteTime:
        description: FirstExecuteTime is the first time this step execution.
        type: string
      id:
        type: string
      lastExecuteTime:
        description: LastExecuteTime is the last time this step execution.
        type: string
      message:
        description: A human readable message indicating details about why the workflowStep
          is in this state.
        type: string
      name:
        type: string
      description:
        type: string
      phase:
        $ref: '#/definitions/v1alpha1.WorkflowStepPhase'
      reason:
        description: A brief CamelCase message indicating details about why the workflowStep
          is in this state.
        type: string
      subSteps:
        items:
          $ref: '#/definitions/v1alpha1.StepStatus'
        type: array
      type:
        type: string
    type: object
  v1alpha1.inputItem:
    properties:
      from:
        type: string
      parameterKey:
        type: string
    type: object
  v1alpha1.outputItem:
    properties:
      name:
        type: string
      valueFrom:
        type: string
    type: object
  v1beta1.Isomate:
    properties:
      configValues:
        additionalProperties: true
        type: object
      name:
        type: string
      plugins:
        items:
          $ref: '#/definitions/v1beta1.ReleasePlugin'
        type: array
    type: object
  v1beta1.IsomateConfig:
    properties:
      defaultIsomateName:
        type: string
      isomates:
        items:
          $ref: '#/definitions/v1beta1.Isomate'
        type: array
    type: object
  v1beta1.ReleaseConfig:
    properties:
      apiVersion:
        description: |-
          APIVersion defines the versioned schema of this representation of an object.
          Servers should convert recognized schemas to the latest internal value, and
          may reject unrecognized values.
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
          +optional
        type: string
      kind:
        description: |-
          Kind is a string value representing the REST resource this object represents.
          Servers may infer this from the endpoint the client submits requests to.
          Cannot be updated.
          In CamelCase.
          More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
          +optional
        type: string
      metadata:
        $ref: '#/definitions/v1.ObjectMeta'
      spec:
        $ref: '#/definitions/v1beta1.ReleaseConfigSpec'
      status:
        $ref: '#/definitions/v1beta1.ReleaseConfigStatus'
    type: object
  v1beta1.ReleaseConfigSpec:
    properties:
      chartAppVersion:
        type: string
      chartImage:
        type: string
      chartName:
        type: string
      chartVersion:
        type: string
      chartWalmVersion:
        type: string
      configValues:
        additionalProperties: true
        type: object
      dependencies:
        additionalProperties:
          type: string
        type: object
      dependenciesConfigValues:
        additionalProperties: true
        type: object
      isomateConfig:
        $ref: '#/definitions/v1beta1.IsomateConfig'
      outputConfig:
        additionalProperties: true
        type: object
      repo:
        type: string
    type: object
  v1beta1.ReleaseConfigStatus:
    type: object
  v1beta1.ReleasePlugin:
    properties:
      args:
        type: string
      disable:
        type: boolean
      name:
        type: string
      version:
        type: string
    type: object
info:
  contact: {}
paths:
  /api/v1/component-instance/namespace/{namespace}/name/{name}:
    get:
      consumes:
        - application/json
      operationId: v1GetComponentInstance
      parameters:
        - description: tenant-1
          in: path
          name: namespace
          required: true
          type: string
        - description: clus-hdfs-9-0-0-123456
          in: path
          name: name
          required: true
          type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.ComponentInstance'
      summary: get component instance using namespace and name
      tags:
        - ComponentInstance
  /api/v1/component-instance/namespace/{namespace}/name/{name}/enable-security:
    post:
      consumes:
        - application/json
      operationId: v1EnableComponentInstanceSecurity
      parameters:
        - description: tenant-1
          in: path
          name: namespace
          required: true
          type: string
        - description: clus-hdfs-9-0-0-123456
          in: path
          name: name
          required: true
          type: string
        - description: "true"
          in: query
          name: dryrun
          required: true
          type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.Secret'
      summary: enable security for component
      tags:
        - ComponentInstance
  /api/v1/componentInstance/{componentInstanceId}/developEntry:
    get:
      consumes:
        - application/json
      description: component instance develop entry info api
      parameters:
        - description: component instance id
          in: path
          name: componentInstanceId
          required: true
          type: integer
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.DevelopmentInfo'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException'
      summary: get component instance develop entry info
      tags:
        - '[Legacy]componentInstanceDevelopEntry'
  /api/v1/kinds/{kind}/object-features/{feature}/queries:
    post:
      consumes:
        - application/json
      parameters:
        - description: query for a list of objects
          in: body
          name: q
          required: true
          schema:
            items:
              $ref: '#/definitions/pkg_server_rest_v1_meta.FeatureQueryRequest'
            type: array
        - description: kind name e.g. Pod, Role, ComponentInstance, ProductInstance
          in: path
          name: kind
          required: true
          type: string
        - description: feature name e.g. decommission
          in: path
          name: feature
          required: true
          type: string
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/pkg_server_rest_v1_meta.FeatureQueryResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
      summary: execute feature queries for a list objects of one kind
      tags:
        - meta
  /api/v1/operation:
    post:
      consumes:
        - application/json
      parameters:
        - description: query
          in: body
          name: q
          required: true
          schema:
            $ref: '#/definitions/pkg_server_rest_v1_operation.Request'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pkg_server_rest_v1_operation.Handler'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
      summary: execute an operation by generating a plan, execute it and returns a
        handler to trace execution status
      tags:
        - operation
  /api/v1/operation-trait/dryrun:
    post:
      consumes:
        - application/json
      parameters:
        - description: query
          in: body
          name: q
          required: true
          schema:
            $ref: '#/definitions/pkg_server_rest_v1_operation.Request'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_v1alpha1.OperationTrait'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
      summary: dry run a operation trait
      tags:
        - operation
  /api/v1/operation/dryrun:
    post:
      consumes:
        - application/json
      parameters:
        - description: query
          in: body
          name: q
          required: true
          schema:
            $ref: '#/definitions/pkg_server_rest_v1_operation.Request'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pkg_server_rest_v1_operation.Handler'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
      summary: dry run an operation by generating a plan
      tags:
        - operation
  /api/v1/product-instance/namespace/{namespace}/name/{name}:
    delete:
      consumes:
        - application/json
      operationId: v1DeleteProductInstance
      parameters:
        - description: tenant-1
          in: path
          name: namespace
          required: true
          type: string
        - description: clus-1
          in: path
          name: name
          required: true
          type: string
      responses:
        "200":
          description: OK
          schema:
            type: object
      summary: delete product instance using namespace and name
      tags:
        - ProductInstance
    get:
      consumes:
        - application/json
      operationId: v1GetProductInstance
      parameters:
        - description: tenant-1
          in: path
          name: namespace
          required: true
          type: string
        - description: clus-1
          in: path
          name: name
          required: true
          type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_productinstance.ProductInstance'
      summary: get product instance using namespace and name
      tags:
        - ProductInstance
  /api/v1/product-instance/namespace/{namespace}/name/{name}/rebuild-dependencies:
    post:
      operationId: v1RebuildProductInstanceDependencies
      parameters:
        - description: tenant-1
          in: path
          name: namespace
          required: true
          type: string
        - description: clus-1
          in: path
          name: name
          required: true
          type: string
      responses:
        "200":
          description: OK
          schema:
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
      summary: rebuild product instance's dependencies
      tags:
        - ProductInstance
  /api/v1/product-instance/namespace/{namespace}/name/{name}/status:
    post:
      operationId: v1UpdateProductInstanceStatus
      parameters:
        - description: tenant-1
          in: path
          name: namespace
          required: true
          type: string
        - description: clus-1
          in: path
          name: name
          required: true
          type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_productinstance.ProductInstance'
      summary: update product instance's status
      tags:
        - ProductInstance
  /api/v1/productInstance/{productInstanceID}/development:
    get:
      consumes:
        - application/json
      description: product development info api
      parameters:
        - description: product instance id
          in: path
          name: productInstanceID
          required: true
          type: integer
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ProductDevelopmentInformation'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException'
      summary: get product development info
      tags:
        - '[Legacy]productInstance'
  /api/v1/security/perms:
    delete:
      consumes:
        - application/json
      description: delete svc info api
      parameters:
        - description: query
          in: body
          name: q
          required: true
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.DeletePermsReq'
      responses:
        "200":
          description: OK
          schema:
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
      summary: delete service perms info
      tags:
        - security
    post:
      consumes:
        - application/json
      description: add perms info api
      parameters:
        - description: query
          in: body
          name: q
          required: true
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.AddPermsReq'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_access_guardianV2_client.PermVo'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
      summary: add perm for tenant
      tags:
        - security
  /api/v1/security/service/authorized-datasources:
    post:
      consumes:
        - application/json
      description: GetAuthorized info api
      parameters:
        - description: tdhUid
          in: query
          name: tdhUid
          required: true
          type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.PermVoResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
      summary: get auth data source for tenant
      tags:
        - security
  /api/v1/security/trust-relation:
    delete:
      consumes:
        - application/json
      description: delete trust info api
      parameters:
        - description: query
          in: body
          name: q
          required: true
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.DeleteTrustRelationReq'
        - description: tenant id
          in: query
          name: tenantUid
          required: true
          type: string
      responses:
        "200":
          description: OK
      summary: delete trust relation info
      tags:
        - security
    post:
      consumes:
        - application/json
      parameters:
        - description: query
          in: body
          name: q
          required: true
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.AddTrustRelationReq'
        - description: tenant id
          in: query
          name: tenantUid
          required: true
          type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.TrustParams'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
      summary: post trust relation info
      tags:
        - security
  /api/v1/snapshot/recover-component-instance:
    post:
      consumes:
        - application/json
      parameters:
        - description: query
          in: body
          name: q
          required: true
          schema:
            $ref: '#/definitions/pkg_server_rest_v1_snapshot.RecoverCompInstRequest'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleApplication'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
      summary: recover metadata for component instance
      tags:
        - snapshot
  /api/v1/snapshot/recover-meta:
    post:
      consumes:
        - application/json
      parameters:
        - description: query
          in: body
          name: q
          required: true
          schema:
            $ref: '#/definitions/pkg_server_rest_v1_snapshot.Request'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.ProductInstanceSnapshotTDCMeta'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError'
      summary: recover metadata for snapshot
      tags:
        - snapshot
  /api/v1/tenants/{tenantUid}/tdh:
    delete:
      consumes:
        - application/json
      description: remove tenant
      parameters:
        - description: tdh tenantUid
          in: path
          name: tenantUid
          required: true
          type: string
      responses:
        "200":
          description: OK
      summary: remove TDH tenant
      tags:
        - TDH tenant
    get:
      consumes:
        - application/json
      description: get tenant detail
      parameters:
        - description: tdh tenantUid
          in: path
          name: tenantUid
          required: true
          type: string
      responses: {}
      summary: get TDH tenant detail
      tags:
        - TDH tenant
    put:
      consumes:
        - application/json
      description: update TDH tenant
      parameters:
        - description: tdh update request
          in: body
          name: request
          required: true
          schema:
            $ref: '#/definitions/pkg_server_rest_v1_tenant.TDHTenantUpdate'
        - description: tdh tenantUid
          in: path
          name: tenantUid
          required: true
          type: string
      responses:
        "200":
          description: OK
      summary: update TDH tenant
      tags:
        - TDH tenant
  /api/v1/tenants/{tenantUid}/tdh/clusters:
    get:
      consumes:
        - application/json
      description: get TDH clusters
      parameters:
        - description: tdh tenantUid
          in: path
          name: tenantUid
          required: true
          type: string
      responses:
        "200":
          description: OK
      summary: get TDH clusters
      tags:
        - TDH tenant
  /api/v1/tenants/{tenantUid}/tdh/clusters/{clusterId}/services:
    get:
      consumes:
        - application/json
      description: get TDH services
      parameters:
        - description: tdh tenantUid
          in: path
          name: tenantUid
          required: true
          type: string
        - description: tdh clusterId
          in: path
          name: clusterId
          required: true
          type: string
      responses:
        "200":
          description: OK
      summary: get TDH services
      tags:
        - TDH tenant
  /api/v1/tenants/tdh:
    post:
      consumes:
        - application/json
      description: register TDH tenant
      parameters:
        - description: tdh request
          in: body
          name: request
          required: true
          schema:
            $ref: '#/definitions/pkg_server_rest_v1_tenant.TDHTenantRequest'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/pkg_server_rest_v1_tenant.TenantMetadata'
      summary: register TDH tenant
      tags:
        - TDH tenant
  /api/v1/tool/{toolType}/{toolName}/developTool:
    get:
      consumes:
        - application/json
      description: tool component info api
      parameters:
        - description: namespace
          in: query
          name: namespace
          required: true
          type: string
        - description: tool type
          in: path
          name: toolType
          required: true
          type: string
        - description: tool name
          in: path
          name: toolName
          required: true
          type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ToolComponent'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException'
      summary: get tool component info
      tags:
        - '[Legacy]toolComponentInstance'
  /api/v1/virtual-release/register:
    post:
      consumes:
        - application/json
      description: register virtual release api
      parameters:
        - description: query
          in: body
          name: q
          required: true
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.AddPermsReq'
      responses:
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
      summary: register virtual release for tdh tenant and add cluster && application
        to ockledb
      tags:
        - virtual_release
  /api/v1/virtual-release/remove:
    delete:
      consumes:
        - application/json
      description: Remove virtual release api
      parameters:
        - description: tdhUid
          in: query
          name: tdhUid
          required: true
          type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.PermVoResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException'
      summary: remove virtual release
      tags:
        - virtual_release
  /api/v1/workflow-run:
    get:
      consumes:
        - application/json
      parameters:
        - description: tenant-1
          in: query
          name: namespace
          type: string
        - description: clus-22
          in: query
          name: operandName
          type: string
        - description: stop,start,snapshot
          in: query
          name: operators
          type: string
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_operations_action_workflow.WorkflowSummary'
            type: array
      summary: list workflowRun summaries
      tags:
        - workflow
  /api/v1/workflow-run/namespace/{namespace}/name/{name}:
    get:
      consumes:
        - application/json
      parameters:
        - description: tenant-1
          in: path
          name: namespace
          required: true
          type: string
        - description: start-clus-26-0808220702
          in: path
          name: name
          required: true
          type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_operations_action_workflow.WorkflowDetail'
      summary: get workflowRun details
      tags:
        - workflow
  /api/v1/workflow-run/namespace/{namespace}/name/{name}/continue:
    post:
      consumes:
        - application/json
      parameters:
        - description: tenant-1
          in: path
          name: namespace
          required: true
          type: string
        - description: start-clus-22-**********
          in: path
          name: name
          required: true
          type: string
      responses: {}
      summary: continue a workflowRun
      tags:
        - workflow
  /api/v1/workflow-run/namespace/{namespace}/name/{name}/log:
    get:
      consumes:
        - application/json
      parameters:
        - description: tenant-1
          in: path
          name: namespace
          required: true
          type: string
        - description: start-clus-26-0808220702
          in: path
          name: name
          required: true
          type: string
      responses:
        "200":
          description: OK
          schema:
            type: string
      summary: get workflowRun logs
      tags:
        - workflow
  /api/v1/workflow-run/namespace/{namespace}/name/{name}/restart:
    post:
      consumes:
        - application/json
      parameters:
        - description: tenant-1
          in: path
          name: namespace
          required: true
          type: string
        - description: start-clus-22-**********
          in: path
          name: name
          required: true
          type: string
      responses: {}
      summary: restart a workflowRun
      tags:
        - workflow
  /api/v1/workflow-run/namespace/{namespace}/name/{name}/resume:
    post:
      consumes:
        - application/json
      parameters:
        - description: tenant-1
          in: path
          name: namespace
          required: true
          type: string
        - description: start-clus-22-**********
          in: path
          name: name
          required: true
          type: string
      responses: {}
      summary: resume a workflowRun
      tags:
        - workflow
  /api/v1/workflow-run/namespace/{namespace}/name/{name}/skip:
    post:
      consumes:
        - application/json
      parameters:
        - description: tenant-1
          in: path
          name: namespace
          required: true
          type: string
        - description: start-clus-22-**********
          in: path
          name: name
          required: true
          type: string
      responses: {}
      summary: skip a workflowRun
      tags:
        - workflow
  /api/v1/workflow-run/namespace/{namespace}/name/{name}/suspend:
    post:
      consumes:
        - application/json
      parameters:
        - description: tenant-1
          in: path
          name: namespace
          required: true
          type: string
        - description: start-clus-22-**********
          in: path
          name: name
          required: true
          type: string
      responses: {}
      summary: suspend a workflowRun
      tags:
        - workflow
  /api/v1/workflow-run/namespace/{namespace}/name/{name}/terminate:
    post:
      consumes:
        - application/json
      parameters:
        - description: tenant-1
          in: path
          name: namespace
          required: true
          type: string
        - description: start-clus-22-**********
          in: path
          name: name
          required: true
          type: string
      responses: {}
      summary: terminate a workflowRun
      tags:
        - workflow
  /health/liveness:
    get:
      consumes:
        - application/json
      description: liveness api
      responses:
        "200":
          description: OK
      summary: get liveness message
      tags:
        - health
  /health/readiness:
    get:
      consumes:
        - application/json
      description: readiness api
      responses:
        "200":
          description: OK
      summary: get readiness message
      tags:
        - health
swagger: "2.0"
