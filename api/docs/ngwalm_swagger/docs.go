// Code generated by swaggo/swag. DO NOT EDIT.

package ngwalm_swagger

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/component-instance/namespace/{namespace}/name/{name}": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "ComponentInstance"
                ],
                "summary": "get component instance using namespace and name",
                "operationId": "v1GetComponentInstance",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tenant-1",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "clus-hdfs-9-0-0-123456",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.ComponentInstance"
                        }
                    }
                }
            }
        },
        "/api/v1/component-instance/namespace/{namespace}/name/{name}/enable-security": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "ComponentInstance"
                ],
                "summary": "enable security for component",
                "operationId": "v1EnableComponentInstanceSecurity",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tenant-1",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "clus-hdfs-9-0-0-123456",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "true",
                        "name": "dryrun",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.Secret"
                        }
                    }
                }
            }
        },
        "/api/v1/componentInstance/{componentInstanceId}/developEntry": {
            "get": {
                "description": "component instance develop entry info api",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "[Legacy]componentInstanceDevelopEntry"
                ],
                "summary": "get component instance develop entry info",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "component instance id",
                        "name": "componentInstanceId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.DevelopmentInfo"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException"
                        }
                    }
                }
            }
        },
        "/api/v1/kinds/{kind}/object-features/{feature}/queries": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "meta"
                ],
                "summary": "execute feature queries for a list objects of one kind",
                "parameters": [
                    {
                        "description": "query for a list of objects",
                        "name": "q",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/pkg_server_rest_v1_meta.FeatureQueryRequest"
                            }
                        }
                    },
                    {
                        "type": "string",
                        "description": "kind name e.g. Pod, Role, ComponentInstance, ProductInstance",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "feature name e.g. decommission",
                        "name": "feature",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/pkg_server_rest_v1_meta.FeatureQueryResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    }
                }
            }
        },
        "/api/v1/operation": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "operation"
                ],
                "summary": "execute an operation by generating a plan, execute it and returns a handler to trace execution status",
                "parameters": [
                    {
                        "description": "query",
                        "name": "q",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pkg_server_rest_v1_operation.Request"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pkg_server_rest_v1_operation.Handler"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    }
                }
            }
        },
        "/api/v1/operation-trait/dryrun": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "operation"
                ],
                "summary": "dry run a operation trait",
                "parameters": [
                    {
                        "description": "query",
                        "name": "q",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pkg_server_rest_v1_operation.Request"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_v1alpha1.OperationTrait"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    }
                }
            }
        },
        "/api/v1/operation/dryrun": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "operation"
                ],
                "summary": "dry run an operation by generating a plan",
                "parameters": [
                    {
                        "description": "query",
                        "name": "q",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pkg_server_rest_v1_operation.Request"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pkg_server_rest_v1_operation.Handler"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    }
                }
            }
        },
        "/api/v1/product-instance/namespace/{namespace}/name/{name}": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "ProductInstance"
                ],
                "summary": "get product instance using namespace and name",
                "operationId": "v1GetProductInstance",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tenant-1",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "clus-1",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_productinstance.ProductInstance"
                        }
                    }
                }
            },
            "delete": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "ProductInstance"
                ],
                "summary": "delete product instance using namespace and name",
                "operationId": "v1DeleteProductInstance",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tenant-1",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "clus-1",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object"
                        }
                    }
                }
            }
        },
        "/api/v1/product-instance/namespace/{namespace}/name/{name}/rebuild-dependencies": {
            "post": {
                "tags": [
                    "ProductInstance"
                ],
                "summary": "rebuild product instance's dependencies",
                "operationId": "v1RebuildProductInstanceDependencies",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tenant-1",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "clus-1",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    }
                }
            }
        },
        "/api/v1/product-instance/namespace/{namespace}/name/{name}/status": {
            "post": {
                "tags": [
                    "ProductInstance"
                ],
                "summary": "update product instance's status",
                "operationId": "v1UpdateProductInstanceStatus",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tenant-1",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "clus-1",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_productinstance.ProductInstance"
                        }
                    }
                }
            }
        },
        "/api/v1/productInstance/{productInstanceID}/development": {
            "get": {
                "description": "product development info api",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "[Legacy]productInstance"
                ],
                "summary": "get product development info",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "product instance id",
                        "name": "productInstanceID",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ProductDevelopmentInformation"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException"
                        }
                    }
                }
            }
        },
        "/api/v1/security/perms": {
            "post": {
                "description": "add perms info api",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "security"
                ],
                "summary": "add perm for tenant",
                "parameters": [
                    {
                        "description": "query",
                        "name": "q",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.AddPermsReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_access_guardianV2_client.PermVo"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    }
                }
            },
            "delete": {
                "description": "delete svc info api",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "security"
                ],
                "summary": "delete service perms info",
                "parameters": [
                    {
                        "description": "query",
                        "name": "q",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.DeletePermsReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    }
                }
            }
        },
        "/api/v1/security/service/authorized-datasources": {
            "post": {
                "description": "GetAuthorized info api",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "security"
                ],
                "summary": "get auth data source for tenant",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tdhUid",
                        "name": "tdhUid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.PermVoResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    }
                }
            }
        },
        "/api/v1/security/trust-relation": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "security"
                ],
                "summary": "post trust relation info",
                "parameters": [
                    {
                        "description": "query",
                        "name": "q",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.AddTrustRelationReq"
                        }
                    },
                    {
                        "type": "string",
                        "description": "tenant id",
                        "name": "tenantUid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.TrustParams"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    }
                }
            },
            "delete": {
                "description": "delete trust info api",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "security"
                ],
                "summary": "delete trust relation info",
                "parameters": [
                    {
                        "description": "query",
                        "name": "q",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.DeleteTrustRelationReq"
                        }
                    },
                    {
                        "type": "string",
                        "description": "tenant id",
                        "name": "tenantUid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/api/v1/snapshot/recover-component-instance": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "snapshot"
                ],
                "summary": "recover metadata for component instance",
                "parameters": [
                    {
                        "description": "query",
                        "name": "q",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pkg_server_rest_v1_snapshot.RecoverCompInstRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleApplication"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    }
                }
            }
        },
        "/api/v1/snapshot/recover-meta": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "snapshot"
                ],
                "summary": "recover metadata for snapshot",
                "parameters": [
                    {
                        "description": "query",
                        "name": "q",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pkg_server_rest_v1_snapshot.Request"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.ProductInstanceSnapshotTDCMeta"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError"
                        }
                    }
                }
            }
        },
        "/api/v1/tenants/tdh": {
            "post": {
                "description": "register TDH tenant",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "TDH tenant"
                ],
                "summary": "register TDH tenant",
                "parameters": [
                    {
                        "description": "tdh request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pkg_server_rest_v1_tenant.TDHTenantRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pkg_server_rest_v1_tenant.TenantMetadata"
                        }
                    }
                }
            }
        },
        "/api/v1/tenants/{tenantUid}/tdh": {
            "get": {
                "description": "get tenant detail",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "TDH tenant"
                ],
                "summary": "get TDH tenant detail",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tdh tenantUid",
                        "name": "tenantUid",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {}
            },
            "put": {
                "description": "update TDH tenant",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "TDH tenant"
                ],
                "summary": "update TDH tenant",
                "parameters": [
                    {
                        "description": "tdh update request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pkg_server_rest_v1_tenant.TDHTenantUpdate"
                        }
                    },
                    {
                        "type": "string",
                        "description": "tdh tenantUid",
                        "name": "tenantUid",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            },
            "delete": {
                "description": "remove tenant",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "TDH tenant"
                ],
                "summary": "remove TDH tenant",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tdh tenantUid",
                        "name": "tenantUid",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/api/v1/tenants/{tenantUid}/tdh/clusters": {
            "get": {
                "description": "get TDH clusters",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "TDH tenant"
                ],
                "summary": "get TDH clusters",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tdh tenantUid",
                        "name": "tenantUid",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/api/v1/tenants/{tenantUid}/tdh/clusters/{clusterId}/services": {
            "get": {
                "description": "get TDH services",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "TDH tenant"
                ],
                "summary": "get TDH services",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tdh tenantUid",
                        "name": "tenantUid",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "tdh clusterId",
                        "name": "clusterId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/api/v1/tool/{toolType}/{toolName}/developTool": {
            "get": {
                "description": "tool component info api",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "[Legacy]toolComponentInstance"
                ],
                "summary": "get tool component info",
                "parameters": [
                    {
                        "type": "string",
                        "description": "namespace",
                        "name": "namespace",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "tool type",
                        "name": "toolType",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "tool name",
                        "name": "toolName",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ToolComponent"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException"
                        }
                    }
                }
            }
        },
        "/api/v1/virtual-release/register": {
            "post": {
                "description": "register virtual release api",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "virtual_release"
                ],
                "summary": "register virtual release for tdh tenant and add cluster \u0026\u0026 application to ockledb",
                "parameters": [
                    {
                        "description": "query",
                        "name": "q",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.AddPermsReq"
                        }
                    }
                ],
                "responses": {
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    }
                }
            }
        },
        "/api/v1/virtual-release/remove": {
            "delete": {
                "description": "Remove virtual release api",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "virtual_release"
                ],
                "summary": "remove virtual release",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tdhUid",
                        "name": "tdhUid",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.PermVoResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException"
                        }
                    }
                }
            }
        },
        "/api/v1/workflow-run": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "workflow"
                ],
                "summary": "list workflowRun summaries",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tenant-1",
                        "name": "namespace",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "clus-22",
                        "name": "operandName",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "stop,start,snapshot",
                        "name": "operators",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_operations_action_workflow.WorkflowSummary"
                            }
                        }
                    }
                }
            }
        },
        "/api/v1/workflow-run/namespace/{namespace}/name/{name}": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "workflow"
                ],
                "summary": "get workflowRun details",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tenant-1",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "start-clus-26-0808220702",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_operations_action_workflow.WorkflowDetail"
                        }
                    }
                }
            }
        },
        "/api/v1/workflow-run/namespace/{namespace}/name/{name}/continue": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "workflow"
                ],
                "summary": "continue a workflowRun",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tenant-1",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "start-clus-22-**********",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {}
            }
        },
        "/api/v1/workflow-run/namespace/{namespace}/name/{name}/log": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "workflow"
                ],
                "summary": "get workflowRun logs",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tenant-1",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "start-clus-26-0808220702",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/v1/workflow-run/namespace/{namespace}/name/{name}/restart": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "workflow"
                ],
                "summary": "restart a workflowRun",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tenant-1",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "start-clus-22-**********",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {}
            }
        },
        "/api/v1/workflow-run/namespace/{namespace}/name/{name}/resume": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "workflow"
                ],
                "summary": "resume a workflowRun",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tenant-1",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "start-clus-22-**********",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {}
            }
        },
        "/api/v1/workflow-run/namespace/{namespace}/name/{name}/skip": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "workflow"
                ],
                "summary": "skip a workflowRun",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tenant-1",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "start-clus-22-**********",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {}
            }
        },
        "/api/v1/workflow-run/namespace/{namespace}/name/{name}/suspend": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "workflow"
                ],
                "summary": "suspend a workflowRun",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tenant-1",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "start-clus-22-**********",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {}
            }
        },
        "/api/v1/workflow-run/namespace/{namespace}/name/{name}/terminate": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "workflow"
                ],
                "summary": "terminate a workflowRun",
                "parameters": [
                    {
                        "type": "string",
                        "description": "tenant-1",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "start-clus-22-**********",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {}
            }
        },
        "/health/liveness": {
            "get": {
                "description": "liveness api",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "health"
                ],
                "summary": "get liveness message",
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/health/readiness": {
            "get": {
                "description": "readiness api",
                "consumes": [
                    "application/json"
                ],
                "tags": [
                    "health"
                ],
                "summary": "get readiness message",
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        }
    },
    "definitions": {
        "github_com_go-errors_errors.Error": {
            "type": "object",
            "properties": {
                "err": {}
            }
        },
        "github_com_kubevela_workflow_api_condition.Condition": {
            "type": "object",
            "properties": {
                "lastTransitionTime": {
                    "description": "LastTransitionTime is the last time this condition transitioned from one\nstatus to another.",
                    "type": "string"
                },
                "message": {
                    "description": "A Message containing details about this condition's last transition from\none status to another, if any.\n+optional",
                    "type": "string"
                },
                "reason": {
                    "description": "A Reason for this condition's last transition from one status to another.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/github_com_kubevela_workflow_api_condition.ConditionReason"
                        }
                    ]
                },
                "status": {
                    "description": "Status of this condition; is it currently True, False, or Unknown?",
                    "allOf": [
                        {
                            "$ref": "#/definitions/k8s_io_api_core_v1.ConditionStatus"
                        }
                    ]
                },
                "type": {
                    "description": "Type of this condition. At most one of each condition type may apply to\na resource at any point in time.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/github_com_kubevela_workflow_api_condition.ConditionType"
                        }
                    ]
                }
            }
        },
        "github_com_kubevela_workflow_api_condition.ConditionReason": {
            "type": "string",
            "enum": [
                "Available",
                "Unavailable",
                "Creating",
                "Deleting",
                "ReconcileSuccess",
                "ReconcileError"
            ],
            "x-enum-varnames": [
                "ReasonAvailable",
                "ReasonUnavailable",
                "ReasonCreating",
                "ReasonDeleting",
                "ReasonReconcileSuccess",
                "ReasonReconcileError"
            ]
        },
        "github_com_kubevela_workflow_api_condition.ConditionType": {
            "type": "string",
            "enum": [
                "Ready",
                "Synced"
            ],
            "x-enum-varnames": [
                "TypeReady",
                "TypeSynced"
            ]
        },
        "k8s_io_api_core_v1.ConditionStatus": {
            "type": "string",
            "enum": [
                "True",
                "False",
                "Unknown"
            ],
            "x-enum-varnames": [
                "ConditionTrue",
                "ConditionFalse",
                "ConditionUnknown"
            ]
        },
        "k8s_io_api_core_v1.ObjectReference": {
            "type": "object",
            "properties": {
                "apiVersion": {
                    "description": "API version of the referent.\n+optional",
                    "type": "string"
                },
                "fieldPath": {
                    "description": "If referring to a piece of an object instead of an entire object, this string\nshould contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].\nFor example, if the object reference is to a container within a pod, this would take on a value like:\n\"spec.containers{name}\" (where \"name\" refers to the name of the container that triggered\nthe event) or if no container name is specified \"spec.containers[2]\" (container with\nindex 2 in this pod). This syntax is chosen only to have some well-defined way of\nreferencing a part of an object.\nTODO: this design is not final and this field is subject to change in the future.\n+optional",
                    "type": "string"
                },
                "kind": {
                    "description": "Kind of the referent.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\n+optional",
                    "type": "string"
                },
                "name": {
                    "description": "Name of the referent.\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\n+optional",
                    "type": "string"
                },
                "namespace": {
                    "description": "Namespace of the referent.\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/\n+optional",
                    "type": "string"
                },
                "resourceVersion": {
                    "description": "Specific resourceVersion to which this reference is made, if any.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency\n+optional",
                    "type": "string"
                },
                "uid": {
                    "description": "UID of the referent.\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids\n+optional",
                    "type": "string"
                }
            }
        },
        "pkg_server_rest_v1_meta.FeatureQueryRequest": {
            "type": "object",
            "properties": {
                "reference": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawReference"
                }
            }
        },
        "pkg_server_rest_v1_meta.FeatureQueryResponse": {
            "type": "object",
            "properties": {
                "feature": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawFeature"
                },
                "reference": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawReference"
                }
            }
        },
        "pkg_server_rest_v1_operation.DefinitionRef": {
            "type": "object",
            "properties": {
                "operatorWithPolicy": {
                    "description": "Otherwise, provide operator and policy, and thus operation definition policy is left to be selected by resolution order",
                    "allOf": [
                        {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.OperatorWithPolicy"
                        }
                    ]
                },
                "policyDefinitionRef": {
                    "description": "Select a particular operation definition policy",
                    "allOf": [
                        {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.PolicyDefinitionReference"
                        }
                    ]
                }
            }
        },
        "pkg_server_rest_v1_operation.Handler": {
            "type": "object",
            "properties": {
                "workflow": {
                    "$ref": "#/definitions/v1alpha1.WorkflowRun"
                }
            }
        },
        "pkg_server_rest_v1_operation.Request": {
            "type": "object",
            "properties": {
                "definitionRef": {
                    "$ref": "#/definitions/pkg_server_rest_v1_operation.DefinitionRef"
                },
                "operandRef": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawProperty"
                },
                "parameter": {}
            }
        },
        "pkg_server_rest_v1_snapshot.RecoverCompInstRequest": {
            "type": "object",
            "properties": {
                "componentInstance": {
                    "description": "ComponentInstance in snapshot to recover",
                    "type": "string"
                },
                "dryrun": {
                    "description": "Dryrun prevents from transaction to be committed, instead, it gives result back to client",
                    "type": "boolean"
                },
                "snapshotId": {
                    "type": "integer"
                },
                "targetNamespace": {
                    "description": "TargetNamespace to recover component instance",
                    "type": "string"
                },
                "targetProductInstance": {
                    "description": "TargetProductInstance to recover component instance",
                    "type": "string"
                }
            }
        },
        "pkg_server_rest_v1_snapshot.Request": {
            "type": "object",
            "properties": {
                "dryrun": {
                    "description": "Dryrun prevents from transaction to be committed, instead, it gives result back to client",
                    "type": "boolean"
                },
                "snapshotId": {
                    "description": "SnapshotId used for recovery",
                    "type": "integer"
                },
                "targetNamespace": {
                    "description": "TargetNamespace to recover metadata",
                    "type": "string"
                }
            }
        },
        "pkg_server_rest_v1_tenant.TDHManagerInfo": {
            "type": "object",
            "properties": {
                "managerPassword": {
                    "type": "string"
                },
                "managerUrl": {
                    "type": "string"
                },
                "managerUsername": {
                    "type": "string"
                }
            }
        },
        "pkg_server_rest_v1_tenant.TDHTenantRequest": {
            "type": "object",
            "properties": {
                "createTime": {
                    "type": "string"
                },
                "creator": {
                    "type": "string"
                },
                "managerInfo": {
                    "$ref": "#/definitions/pkg_server_rest_v1_tenant.TDHManagerInfo"
                },
                "tenantDescription": {
                    "type": "string"
                },
                "tenantName": {
                    "type": "string"
                },
                "tenantType": {
                    "$ref": "#/definitions/pkg_server_rest_v1_tenant.TenantType"
                }
            }
        },
        "pkg_server_rest_v1_tenant.TDHTenantUpdate": {
            "type": "object",
            "properties": {
                "creator": {
                    "type": "string"
                },
                "managerInfo": {
                    "$ref": "#/definitions/pkg_server_rest_v1_tenant.TDHManagerInfo"
                },
                "tenantDescription": {
                    "type": "string"
                },
                "tenantName": {
                    "type": "string"
                }
            }
        },
        "pkg_server_rest_v1_tenant.TenantMetadata": {
            "type": "object",
            "properties": {
                "authInfo": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_tenant.GuardianAuthInfo"
                },
                "createTime": {
                    "type": "string"
                },
                "creator": {
                    "type": "string"
                },
                "fedTenantName": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "managerInfo": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_tenant.ManagerInfo"
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_tenant.TenantPermission"
                    }
                },
                "tccUrl": {
                    "type": "string"
                },
                "tenantDescription": {
                    "type": "string"
                },
                "tenantName": {
                    "type": "string"
                },
                "tenantType": {
                    "$ref": "#/definitions/pkg_server_rest_v1_tenant.TenantType"
                },
                "tenantUid": {
                    "type": "string"
                }
            }
        },
        "pkg_server_rest_v1_tenant.TenantType": {
            "type": "string",
            "enum": [
                "TDH",
                "TDC"
            ],
            "x-enum-varnames": [
                "TDH",
                "TDC"
            ]
        },
        "runtime.RawExtension": {
            "type": "object"
        },
        "transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaData": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "kind": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "parent": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaDataBase"
                },
                "resourceVersion": {
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaDataBase": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "kind": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.OperationDefinitionReference": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.OperatorWithPolicy": {
            "type": "object",
            "properties": {
                "operator": {
                    "type": "string"
                },
                "policy": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.PolicyDefinitionReference": {
            "type": "object",
            "properties": {
                "operationDefinitionRef": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.OperationDefinitionReference"
                },
                "policy": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawFeature": {
            "type": "object",
            "properties": {
                "content": {},
                "name": {
                    "type": "string"
                },
                "supported": {
                    "type": "boolean"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawProperty": {
            "type": "object",
            "properties": {
                "kind": {
                    "type": "string"
                },
                "properties": {}
            }
        },
        "transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawReference": {
            "type": "object"
        },
        "transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_v1alpha1.OperationTrait": {
            "type": "object",
            "properties": {
                "apiVersion": {
                    "description": "APIVersion defines the versioned schema of this representation of an object.\nServers should convert recognized schemas to the latest internal value, and\nmay reject unrecognized values.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\n+optional",
                    "type": "string"
                },
                "kind": {
                    "description": "Kind is a string value representing the REST resource this object represents.\nServers may infer this from the endpoint the client submits requests to.\nCannot be updated.\nIn CamelCase.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\n+optional",
                    "type": "string"
                },
                "metadata": {
                    "$ref": "#/definitions/v1.ObjectMeta"
                },
                "spec": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_v1alpha1.OperationTraitSpec"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_v1alpha1.OperationTraitRef": {
            "type": "object",
            "properties": {
                "operand": {
                    "description": "OperandReference refers to OAM object(s)",
                    "allOf": [
                        {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawReference"
                        }
                    ]
                },
                "operationDefinition": {
                    "description": "OperationDefinition refers to an operation definition instance",
                    "type": "string"
                },
                "policy": {
                    "description": "Policy refers to a specific policy in operation definition",
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_v1alpha1.OperationTraitSpec": {
            "type": "object",
            "properties": {
                "dependsOn": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_v1alpha1.OperationTraitRef"
                    }
                },
                "includes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_v1alpha1.OperationTraitRef"
                    }
                },
                "operand": {
                    "description": "OperandReference refers to OAM object(s)",
                    "allOf": [
                        {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.RawReference"
                        }
                    ]
                },
                "operationDefinition": {
                    "description": "OperationDefinition refers to an operation definition instance",
                    "type": "string"
                },
                "policy": {
                    "description": "Policy refers to a specific policy in operation definition",
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_access_guardianV2_client.NodeVo": {
            "type": "object",
            "properties": {
                "type": {
                    "description": "Node type",
                    "type": "string"
                },
                "value": {
                    "description": "Node value",
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_access_guardianV2_client.PermVo": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string"
                },
                "resourceVo": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_access_guardianV2_client.ResourceVo"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_access_guardianV2_client.ResourceVo": {
            "type": "object",
            "properties": {
                "dataSource": {
                    "description": "The NodeVo list representing the resource",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_access_guardianV2_client.NodeVo"
                    }
                },
                "serviceName": {
                    "description": "Service name",
                    "type": "string"
                },
                "serviceType": {
                    "description": "Service type",
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_access_meta.ConnectionArea": {
            "type": "string",
            "enum": [
                "internal",
                "external"
            ],
            "x-enum-varnames": [
                "CAInternal",
                "CAExternal"
            ]
        },
        "transwarp_io_TDC_ngwalm_pkg_access_meta.EntryType": {
            "type": "string",
            "enum": [
                "development",
                "maintenance",
                "visit"
            ],
            "x-enum-varnames": [
                "ETDevelopment",
                "ETMaintenance",
                "ETVisit"
            ]
        },
        "transwarp_io_TDC_ngwalm_pkg_common.ServiceProvider": {
            "type": "string",
            "enum": [
                "TDC",
                "TDH"
            ],
            "x-enum-varnames": [
                "ServiceProviderTDC",
                "ServiceProviderTDH"
            ]
        },
        "transwarp_io_TDC_ngwalm_pkg_common_errs.ErrorCode": {
            "type": "integer",
            "enum": [
                10000,
                10001,
                10002,
                10003,
                10004,
                10005,
                11000,
                11001,
                11002,
                20000,
                20010,
                20011,
                20012,
                20013,
                21000,
                21200,
                21201,
                21400
            ],
            "x-enum-comments": {
                "CodeDB": "Database connection errors",
                "CodeNotFound": "Resource not found",
                "CodeServerError": "Internal server errors",
                "CodeWalmService": "Error invoking walm",
                "ErrCueCodeTemplating": "Error occurred when rendering cue templates"
            },
            "x-enum-varnames": [
                "CodeUnknown",
                "CodeRepositoryNotFound",
                "CodeNotFound",
                "CodeDB",
                "CodeIllegalArg",
                "ErrTypeConversion",
                "CodeWalmService",
                "CodeK8s",
                "CodeClient",
                "CodeServerError",
                "ErrCueCodeTemplating",
                "ErrCuePackageImports",
                "ErrCueValueUnMarshals",
                "ErrCueProcessorExecutes",
                "ErrOpDefNotFound",
                "ErrOpTraitNotFound",
                "ErrOpTraitGenerates",
                "ErrOpPlanGenerates"
            ]
        },
        "transwarp_io_TDC_ngwalm_pkg_common_errs.ErrorReason": {
            "type": "string",
            "enum": [
                "InternalError",
                "UnKnown",
                "NotFound",
                "AlreadyExists",
                "Illegal",
                "NotImplemented",
                "NotSupported",
                "ServerTimeout",
                "BadRequest"
            ],
            "x-enum-varnames": [
                "ErrReasonInternal",
                "ErrReasonUnKnown",
                "ErrReasonNotFound",
                "ErrReasonAlreadyExists",
                "ErrReasonIllegal",
                "ErrReasonNotImplemented",
                "ErrReasonNotSupported",
                "ErrReasonServerTimeout",
                "ErrReasonBadRequest"
            ]
        },
        "transwarp_io_TDC_ngwalm_pkg_common_errs.NgWalmError": {
            "type": "object",
            "properties": {
                "err": {
                    "description": "Err is a wrapped error",
                    "allOf": [
                        {
                            "$ref": "#/definitions/github_com_go-errors_errors.Error"
                        }
                    ]
                },
                "errorCode": {
                    "description": "--------Required fields--------",
                    "allOf": [
                        {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.ErrorCode"
                        }
                    ]
                },
                "errorReason": {
                    "description": "--------Optional fields--------",
                    "allOf": [
                        {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common_errs.ErrorReason"
                        }
                    ]
                },
                "message": {
                    "type": "string"
                },
                "statusCode": {
                    "type": "integer"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_common_runningstate.PlainStatus": {
            "type": "object",
            "properties": {
                "info": {
                    "type": "string"
                },
                "isActive": {
                    "type": "boolean"
                },
                "isCompleted": {
                    "type": "boolean"
                },
                "isTerminate": {
                    "type": "boolean"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.ComponentInstance": {
            "type": "object",
            "properties": {
                "componentInstanceClass": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_relationship_relationcommon.ComponentInstanceClass"
                },
                "dependencies": {
                    "description": "Dependencies contains upstream members' identity and dependency relationship details",
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "content": {
                                "description": "Content of depended entity",
                                "allOf": [
                                    {
                                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaData"
                                    }
                                ]
                            },
                            "required": {
                                "type": "boolean"
                            }
                        }
                    }
                },
                "displayName": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "kind": {
                    "type": "string"
                },
                "kubeResources": {
                    "description": "KubeResources are optional fields including ObjectMeta of variant kinds of k8s resources associated with this component instance\nempty by default.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.KubeResources"
                        }
                    ]
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "parent": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaDataBase"
                },
                "resourceVersion": {
                    "type": "string"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.Role"
                    }
                },
                "runningStatus": {},
                "serviceProvider": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_common.ServiceProvider"
                },
                "staticRef": {
                    "description": "StaticRef is reference to the static object",
                    "allOf": [
                        {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_component.Component"
                        }
                    ]
                },
                "status": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                },
                "version": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.KubeResources": {
            "type": "object",
            "properties": {
                "configMaps": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.ObjectMeta"
                    }
                },
                "ingresses": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.ObjectMeta"
                    }
                },
                "jobs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.ObjectMeta"
                    }
                },
                "monitor": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.MonitoringResources"
                },
                "releaseConfig": {
                    "$ref": "#/definitions/v1beta1.ReleaseConfig"
                },
                "secrets": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.ObjectMeta"
                    }
                },
                "services": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.ObjectMeta"
                    }
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.MonitoringResources": {
            "type": "object",
            "properties": {
                "prometheusRules": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.ObjectMeta"
                    }
                },
                "serviceMonitors": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.ObjectMeta"
                    }
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.Role": {
            "type": "object",
            "properties": {
                "dependencies": {
                    "description": "Dependencies contains upstream members' identity and dependency relationship details",
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "content": {
                                "description": "Content of depended entity",
                                "allOf": [
                                    {
                                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaData"
                                    }
                                ]
                            },
                            "required": {
                                "type": "boolean"
                            }
                        }
                    }
                },
                "id": {
                    "type": "integer"
                },
                "kind": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "parent": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaDataBase"
                },
                "resourceVersion": {
                    "type": "string"
                },
                "runningStatus": {},
                "staticRef": {
                    "description": "StaticRef is reference to the static object",
                    "allOf": [
                        {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_component.StaticRole"
                        }
                    ]
                },
                "type": {
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                },
                "version": {
                    "type": "string"
                },
                "workloadRef": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.RoleWorkloadRef"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.RoleWorkloadRef": {
            "type": "object",
            "properties": {
                "apiVersion": {
                    "type": "string"
                },
                "kind": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "workloadType": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.WorkloadType"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.WorkloadType": {
            "type": "string",
            "enum": [
                "StatefulSet",
                "WarpStatefulSet",
                "Deployment",
                "DaemonSet",
                "Job",
                "ReplicaSet"
            ],
            "x-enum-varnames": [
                "StatefulSet",
                "WarpStatefulSet",
                "Deployment",
                "DaemonSet",
                "Job",
                "ReplicaSet"
            ]
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_productinstance.ProductInstance": {
            "type": "object",
            "properties": {
                "dependencies": {
                    "description": "Dependencies contains upstream members' identity and dependency relationship details",
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "content": {
                                "description": "Content of depended entity",
                                "allOf": [
                                    {
                                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaData"
                                    }
                                ]
                            },
                            "required": {
                                "type": "boolean"
                            }
                        }
                    }
                },
                "id": {
                    "type": "integer"
                },
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.ComponentInstance"
                    }
                },
                "kind": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "parent": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaDataBase"
                },
                "references": {
                    "description": "Referenced component instances are separately managed",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_componentinstance.ComponentInstance"
                    }
                },
                "resourceVersion": {
                    "type": "string"
                },
                "runningStatus": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_common_runningstate.PlainStatus"
                },
                "staticRef": {
                    "description": "StaticRef is reference to the static object",
                    "allOf": [
                        {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_product.Product"
                        }
                    ]
                },
                "type": {
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                },
                "version": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_runtime_relationship_relationcommon.ComponentInstanceClass": {
            "type": "string",
            "enum": [
                "sysctx",
                "cluster"
            ],
            "x-enum-varnames": [
                "CompInstClzSysCtx",
                "CompInstClzCluster"
            ]
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleAppAppCollRel": {
            "type": "object",
            "properties": {
                "application_collection_id": {
                    "type": "string"
                },
                "application_collection_type": {
                    "type": "string"
                },
                "application_id": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleAppDep": {
            "type": "object",
            "properties": {
                "application_dependency_id": {
                    "type": "string"
                },
                "application_dependency_name": {
                    "type": "string"
                },
                "application_dependency_type": {
                    "type": "string"
                },
                "application_id": {
                    "type": "string"
                },
                "application_name": {
                    "type": "string"
                },
                "application_type": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleApplication": {
            "type": "object",
            "properties": {
                "application_type": {
                    "type": "string"
                },
                "application_version": {
                    "type": "string"
                },
                "auth_isolation_level": {
                    "type": "string"
                },
                "cloud_provider": {
                    "type": "string"
                },
                "config_flag": {
                    "type": "string"
                },
                "created_time": {
                    "type": "string"
                },
                "creator": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "macvlan_enabled": {
                    "type": "string"
                },
                "modified_time": {
                    "type": "string"
                },
                "msg": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "pv_recycle_enabled": {
                    "type": "string"
                },
                "release_name": {
                    "type": "string"
                },
                "share_description": {
                    "type": "string"
                },
                "share_status": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleCluster": {
            "type": "object",
            "properties": {
                "cause": {
                    "type": "string"
                },
                "created_time": {
                    "type": "string"
                },
                "creator": {
                    "type": "string"
                },
                "data_catalog_accessing": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "err_log": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "iobound_anti_affinity_enabled": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "modified_time": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "name_on_k8s": {
                    "type": "string"
                },
                "product_uuid": {
                    "type": "string"
                },
                "project_name": {
                    "type": "string"
                },
                "pv_recycle_enabled": {
                    "type": "string"
                },
                "redeployable": {
                    "type": "string"
                },
                "security_enabled": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleSecAppRelation": {
            "type": "object",
            "properties": {
                "application_id": {
                    "type": "string"
                },
                "created_time": {
                    "type": "string"
                },
                "creator": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "relating_method": {
                    "type": "string"
                },
                "secret_id": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleSecret": {
            "type": "object",
            "properties": {
                "created_time": {
                    "type": "string"
                },
                "creator": {
                    "type": "string"
                },
                "data_type": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "modified_time": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.ProductInstanceSnapshotTDCMeta": {
            "type": "object",
            "properties": {
                "ockleAppAppCollRel": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleAppAppCollRel"
                    }
                },
                "ockleAppDep": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleAppDep"
                    }
                },
                "ockleApplications": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleApplication"
                    }
                },
                "ockleCluster": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleCluster"
                },
                "ockleSecAppRelation": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleSecAppRelation"
                    }
                },
                "ockleSecret": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.OckleSecret"
                    }
                },
                "tccAppKV": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.TccAppKV"
                    }
                },
                "tccApplication": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.TccApplication"
                    }
                },
                "tccInstance": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.TccInstance"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.TccAppKV": {
            "type": "object",
            "properties": {
                "application_id": {
                    "type": "string"
                },
                "config_file": {
                    "type": "string"
                },
                "config_type": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "data_type": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "key": {
                    "type": "string"
                },
                "latest_value": {
                    "type": "string"
                },
                "role": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "value": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.TccApplication": {
            "type": "object",
            "properties": {
                "applicationId": {
                    "type": "string"
                },
                "creator": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "instanceId": {
                    "type": "string"
                },
                "jvm_deploy_status": {
                    "type": "string"
                },
                "last_modified_ts": {
                    "type": "string"
                },
                "need_restart_application": {
                    "type": "string"
                },
                "oauth2_client_id": {
                    "type": "string"
                },
                "replicas": {
                    "type": "string"
                },
                "reservation_status": {
                    "type": "string"
                },
                "roles_page_cache_status": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_snapshot.TccInstance": {
            "type": "object",
            "properties": {
                "category_id": {
                    "type": "string"
                },
                "cluster_id": {
                    "type": "string"
                },
                "create_time": {
                    "type": "string"
                },
                "creator": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "macvlan_enabled": {
                    "type": "string"
                },
                "macvlan_external_network_name": {
                    "type": "string"
                },
                "macvlan_naz_name": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "nodes": {
                    "type": "string"
                },
                "product_uuid": {
                    "type": "string"
                },
                "status": {
                    "description": "ProjectId                  *string ` + "`" + `json:\"project_id,omitempty\"` + "`" + `",
                    "type": "string"
                },
                "support_podgroup": {
                    "type": "string"
                },
                "tcu_num": {
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                },
                "visibility": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_component.Component": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "kind": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "parent": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaDataBase"
                },
                "resourceVersion": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                },
                "version": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_component.RoleType": {
            "type": "string",
            "enum": [
                "container",
                "deployment",
                "transwarpStatefulset",
                "job",
                "daemonset"
            ],
            "x-enum-varnames": [
                "RoleTypeContainer",
                "RoleTypeDeploy",
                "RoleTypeWsts",
                "RoleTypeJob",
                "RoleTypeDaemonSet"
            ]
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_component.StaticRole": {
            "type": "object",
            "properties": {
                "dependsOn": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "id": {
                    "type": "integer"
                },
                "kind": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "parent": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_apis_oam_tdc_transwarp_io_common.MetaDataBase"
                },
                "resourceVersion": {
                    "type": "string"
                },
                "roleType": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_component.RoleType"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_product.Product": {
            "type": "object"
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_tenant.GuardianAuthInfo": {
            "type": "object",
            "properties": {
                "guardianPassword": {
                    "type": "string"
                },
                "guardianUrl": {
                    "type": "string"
                },
                "guardianUsername": {
                    "type": "string"
                },
                "hosts": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "kdc": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "realm": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_tenant.ManagerInfo": {
            "type": "object",
            "properties": {
                "managerPassword": {
                    "type": "string"
                },
                "managerUrl": {
                    "type": "string"
                },
                "managerUsername": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_tenant.TenantPermission": {
            "type": "object",
            "properties": {
                "tdhClusterName": {
                    "type": "string"
                },
                "tdhServiceName": {
                    "type": "string"
                },
                "tenantUid": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.ActionResource": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string"
                },
                "resourceVo": {
                    "description": "带有AllData后缀代表返回值中，guardian已经返回了正确的值，但是为了提供给eco/tcc需要将最里面的value字段进行切割",
                    "allOf": [
                        {
                            "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.ResourceAllData"
                        }
                    ]
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.AddPermsReq": {
            "type": "object",
            "properties": {
                "guardianPassword": {
                    "type": "string"
                },
                "guardianUsername": {
                    "type": "string"
                },
                "resourceVo": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.ResourceVo"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.AddTrustRelationReq": {
            "type": "object",
            "properties": {
                "creator": {
                    "type": "string"
                },
                "guardian_name": {
                    "type": "string"
                },
                "guardian_password": {
                    "type": "string"
                },
                "target_tenant_name": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.DeletePermsReq": {
            "type": "object",
            "properties": {
                "guardianPassword": {
                    "type": "string"
                },
                "guardianUsername": {
                    "type": "string"
                },
                "resourceVo": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.ResourceVo"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.DeleteTrustRelationReq": {
            "type": "object",
            "properties": {
                "guardian_name": {
                    "type": "string"
                },
                "guardian_password": {
                    "type": "string"
                },
                "relation_type": {
                    "type": "string"
                },
                "target_tenant_name": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.GuardianPackage": {
            "type": "object",
            "properties": {
                "application": {
                    "type": "string"
                },
                "ds_access_token": {
                    "type": "string"
                },
                "ds_admin_password": {
                    "type": "string"
                },
                "ds_admin_username": {
                    "type": "string"
                },
                "guardian_server_address": {
                    "type": "string"
                },
                "k8s_guardian_server_address": {
                    "type": "string"
                },
                "kerberos": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.Kerberos"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "network_domain_suffix": {
                    "type": "string"
                },
                "security_users_meta": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.UserSecurityMeta"
                    }
                },
                "trusted": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.GuardianPackage"
                    }
                },
                "trusting": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.GuardianPackage"
                    }
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.KerbConfig": {
            "type": "object",
            "properties": {
                "hosts": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "kdc_addresses": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "network_domain_suffix": {
                    "type": "string"
                },
                "principal_suffix": {
                    "type": "string"
                },
                "realm": {
                    "type": "string"
                },
                "tenant_name": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.Kerberos": {
            "type": "object",
            "properties": {
                "hosts": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "kdc_addresses": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "network_domain_suffix": {
                    "type": "string"
                },
                "principal_suffix": {
                    "type": "string"
                },
                "realm": {
                    "type": "string"
                },
                "tenant_name": {
                    "type": "string"
                },
                "trusts_krb_configs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.KerbConfig"
                    }
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NGWalmException": {
            "type": "object",
            "properties": {
                "detail": {
                    "type": "string"
                },
                "error_code": {
                    "type": "string"
                },
                "msg": {
                    "type": "string"
                },
                "request": {
                    "type": "string"
                },
                "status_code": {
                    "type": "integer"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NodeAllData": {
            "type": "object",
            "properties": {
                "clusterName": {
                    "type": "string"
                },
                "serviceName": {
                    "description": "返回值需要将guardian的返回值进行拆分，value按照@符号切割成以下三个字段",
                    "type": "string"
                },
                "serviceSid": {
                    "type": "string"
                },
                "tenantUid": {
                    "type": "string"
                },
                "type": {
                    "description": "Node value\nValue string ` + "`" + `json:\"value\"` + "`" + `",
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NodeVo": {
            "type": "object",
            "properties": {
                "clusterName": {
                    "type": "string"
                },
                "serviceSid": {
                    "description": "Node value\nValue string ` + "`" + `json:\"value\"` + "`" + `",
                    "type": "string"
                },
                "tenantUid": {
                    "description": "tdh tenantuid",
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.PermVoResponse": {
            "type": "object",
            "properties": {
                "actionResources": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.ActionResource"
                    }
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.ResourceAllData": {
            "type": "object",
            "properties": {
                "dataSource": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NodeAllData"
                    }
                },
                "serviceName": {
                    "description": "Service name",
                    "type": "string"
                },
                "serviceType": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.ResourceVo": {
            "type": "object",
            "properties": {
                "dataSource": {
                    "description": "The NodeVo list representing the resource",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.NodeVo"
                    }
                },
                "serviceName": {
                    "description": "Service name tdc tenantUid",
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.TrustParams": {
            "type": "object",
            "properties": {
                "relationType": {
                    "type": "string"
                },
                "sourceGuardianPackage": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.GuardianPackage"
                },
                "targetGuardianPackage": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.GuardianPackage"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_meta_static_trust.UserSecurityMeta": {
            "type": "object",
            "properties": {
                "secrets": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "user_name": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_operations_action_workflow.Dependency": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "phase": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_operations_action_workflow.WorkflowDetail": {
            "type": "object",
            "properties": {
                "dependencies": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_operations_action_workflow.Dependency"
                    }
                },
                "endTime": {
                    "type": "string"
                },
                "includes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_domains_operations_action_workflow.WorkflowDetail"
                    }
                },
                "isWorkFlow": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "operandKind": {
                    "type": "string"
                },
                "operandName": {
                    "type": "string"
                },
                "operator": {
                    "type": "string"
                },
                "phase": {
                    "type": "string"
                },
                "startTime": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_domains_operations_action_workflow.WorkflowSummary": {
            "type": "object",
            "properties": {
                "endTime": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "operandKind": {
                    "type": "string"
                },
                "operandName": {
                    "type": "string"
                },
                "operator": {
                    "type": "string"
                },
                "phase": {
                    "type": "string"
                },
                "startTime": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Authentication": {
            "type": "object",
            "properties": {
                "authInfo": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ItemInfo"
                    }
                },
                "mode": {
                    "description": "Mode support kerberos, LDAP, access_token , password",
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Component": {
            "type": "object",
            "properties": {
                "component_name": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ComponentInstance": {
            "type": "object",
            "properties": {
                "componentInstanceName": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Connection": {
            "type": "object",
            "properties": {
                "connectionArea": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_access_meta.ConnectionArea"
                },
                "connectionType": {
                    "type": "string"
                },
                "dbName": {
                    "type": "string"
                },
                "dbType": {
                    "type": "string"
                },
                "hostname": {
                    "type": "string"
                },
                "port": {
                    "type": "integer"
                },
                "properties": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_legacy_controllers.DevelopToolInfo": {
            "type": "object",
            "properties": {
                "toolType": {
                    "type": "string"
                },
                "toolTypeName": {
                    "type": "string"
                },
                "toolTypeNameEn": {
                    "type": "string"
                },
                "tools": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Tool"
                    }
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_legacy_controllers.DevelopmentInfo": {
            "type": "object",
            "properties": {
                "authentications": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Authentication"
                    }
                },
                "componentInstanceName": {
                    "type": "string"
                },
                "componentInstanceType": {
                    "type": "string"
                },
                "component_instance_version": {
                    "type": "string"
                },
                "connections": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Connection"
                    }
                },
                "driverInfo": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.DriverInfo"
                    }
                },
                "others": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ItemInfo"
                    }
                },
                "resourceUrl": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_legacy_controllers.DriverInfo": {
            "type": "object",
            "properties": {
                "componentName": {
                    "type": "string"
                },
                "componentVersion": {
                    "type": "string"
                },
                "driverType": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ItemInfo": {
            "type": "object",
            "properties": {
                "attributes": {
                    "description": "Decode support base64",
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "decode": {
                    "description": "Attributes support kundb classification like backup file",
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "type": {
                    "description": "Type support string, file",
                    "type": "string"
                },
                "value": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ProductDevelopmentInformation": {
            "type": "object",
            "properties": {
                "developToolInfo": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.DevelopToolInfo"
                    }
                },
                "developmentInfo": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.DevelopmentInfo"
                    }
                },
                "productInstanceId": {
                    "type": "integer"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ProductInstance": {
            "type": "object",
            "properties": {
                "componentInstances": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ComponentInstance"
                    }
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Tool": {
            "type": "object",
            "properties": {
                "components": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.Component"
                    }
                },
                "description": {
                    "type": "string"
                },
                "descriptionEn": {
                    "type": "string"
                },
                "displayName": {
                    "type": "string"
                },
                "displayNameEn": {
                    "type": "string"
                },
                "entryType": {
                    "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_access_meta.EntryType"
                },
                "resourceUrl": {
                    "type": "string"
                },
                "toolName": {
                    "type": "string"
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ToolComponent": {
            "type": "object",
            "properties": {
                "namespace": {
                    "type": "string"
                },
                "productInstances": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/transwarp_io_TDC_ngwalm_pkg_legacy_controllers.ProductInstance"
                    }
                }
            }
        },
        "transwarp_io_TDC_ngwalm_pkg_legacy_errs.NGWalmException": {
            "type": "object",
            "properties": {
                "detail": {
                    "type": "string"
                },
                "error_code": {
                    "type": "string"
                },
                "msg": {
                    "type": "string"
                },
                "request": {
                    "type": "string"
                },
                "status_code": {
                    "type": "integer"
                }
            }
        },
        "v1.FieldsV1": {
            "type": "object"
        },
        "v1.ManagedFieldsEntry": {
            "type": "object",
            "properties": {
                "apiVersion": {
                    "description": "APIVersion defines the version of this resource that this field set\napplies to. The format is \"group/version\" just like the top-level\nAPIVersion field. It is necessary to track the version of a field\nset because it cannot be automatically converted.",
                    "type": "string"
                },
                "fieldsType": {
                    "description": "FieldsType is the discriminator for the different fields format and version.\nThere is currently only one possible value: \"FieldsV1\"",
                    "type": "string"
                },
                "fieldsV1": {
                    "description": "FieldsV1 holds the first JSON version format as described in the \"FieldsV1\" type.\n+optional",
                    "allOf": [
                        {
                            "$ref": "#/definitions/v1.FieldsV1"
                        }
                    ]
                },
                "manager": {
                    "description": "Manager is an identifier of the workflow managing these fields.",
                    "type": "string"
                },
                "operation": {
                    "description": "Operation is the type of operation which lead to this ManagedFieldsEntry being created.\nThe only valid values for this field are 'Apply' and 'Update'.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/v1.ManagedFieldsOperationType"
                        }
                    ]
                },
                "subresource": {
                    "description": "Subresource is the name of the subresource used to update that object, or\nempty string if the object was updated through the main resource. The\nvalue of this field is used to distinguish between managers, even if they\nshare the same name. For example, a status update will be distinct from a\nregular update using the same manager name.\nNote that the APIVersion field is not related to the Subresource field and\nit always corresponds to the version of the main resource.",
                    "type": "string"
                },
                "time": {
                    "description": "Time is the timestamp of when the ManagedFields entry was added. The\ntimestamp will also be updated if a field is added, the manager\nchanges any of the owned fields value or removes a field. The\ntimestamp does not update when a field is removed from the entry\nbecause another manager took it over.\n+optional",
                    "type": "string"
                }
            }
        },
        "v1.ManagedFieldsOperationType": {
            "type": "string",
            "enum": [
                "Apply",
                "Update"
            ],
            "x-enum-varnames": [
                "ManagedFieldsOperationApply",
                "ManagedFieldsOperationUpdate"
            ]
        },
        "v1.ObjectMeta": {
            "type": "object",
            "properties": {
                "annotations": {
                    "description": "Annotations is an unstructured key value map stored with a resource that may be\nset by external tools to store and retrieve arbitrary metadata. They are not\nqueryable and should be preserved when modifying objects.\nMore info: http://kubernetes.io/docs/user-guide/annotations\n+optional",
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "creationTimestamp": {
                    "description": "CreationTimestamp is a timestamp representing the server time when this object was\ncreated. It is not guaranteed to be set in happens-before order across separate operations.\nClients may not set this value. It is represented in RFC3339 form and is in UTC.\n\nPopulated by the system.\nRead-only.\nNull for lists.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata\n+optional",
                    "type": "string"
                },
                "deletionGracePeriodSeconds": {
                    "description": "Number of seconds allowed for this object to gracefully terminate before\nit will be removed from the system. Only set when deletionTimestamp is also set.\nMay only be shortened.\nRead-only.\n+optional",
                    "type": "integer"
                },
                "deletionTimestamp": {
                    "description": "DeletionTimestamp is RFC 3339 date and time at which this resource will be deleted. This\nfield is set by the server when a graceful deletion is requested by the user, and is not\ndirectly settable by a client. The resource is expected to be deleted (no longer visible\nfrom resource lists, and not reachable by name) after the time in this field, once the\nfinalizers list is empty. As long as the finalizers list contains items, deletion is blocked.\nOnce the deletionTimestamp is set, this value may not be unset or be set further into the\nfuture, although it may be shortened or the resource may be deleted prior to this time.\nFor example, a user may request that a pod is deleted in 30 seconds. The Kubelet will react\nby sending a graceful termination signal to the containers in the pod. After that 30 seconds,\nthe Kubelet will send a hard termination signal (SIGKILL) to the container and after cleanup,\nremove the pod from the API. In the presence of network partitions, this object may still\nexist after this timestamp, until an administrator or automated process can determine the\nresource is fully terminated.\nIf not set, graceful deletion of the object has not been requested.\n\nPopulated by the system when a graceful deletion is requested.\nRead-only.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata\n+optional",
                    "type": "string"
                },
                "finalizers": {
                    "description": "Must be empty before the object is deleted from the registry. Each entry\nis an identifier for the responsible component that will remove the entry\nfrom the list. If the deletionTimestamp of the object is non-nil, entries\nin this list can only be removed.\nFinalizers may be processed and removed in any order.  Order is NOT enforced\nbecause it introduces significant risk of stuck finalizers.\nfinalizers is a shared field, any actor with permission can reorder it.\nIf the finalizer list is processed in order, then this can lead to a situation\nin which the component responsible for the first finalizer in the list is\nwaiting for a signal (field value, external system, or other) produced by a\ncomponent responsible for a finalizer later in the list, resulting in a deadlock.\nWithout enforced ordering finalizers are free to order amongst themselves and\nare not vulnerable to ordering changes in the list.\n+optional\n+patchStrategy=merge",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "generateName": {
                    "description": "GenerateName is an optional prefix, used by the server, to generate a unique\nname ONLY IF the Name field has not been provided.\nIf this field is used, the name returned to the client will be different\nthan the name passed. This value will also be combined with a unique suffix.\nThe provided value has the same validation rules as the Name field,\nand may be truncated by the length of the suffix required to make the value\nunique on the server.\n\nIf this field is specified and the generated name exists, the server will return a 409.\n\nApplied only if Name is not specified.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#idempotency\n+optional",
                    "type": "string"
                },
                "generation": {
                    "description": "A sequence number representing a specific generation of the desired state.\nPopulated by the system. Read-only.\n+optional",
                    "type": "integer"
                },
                "labels": {
                    "description": "Map of string keys and values that can be used to organize and categorize\n(scope and select) objects. May match selectors of replication controllers\nand services.\nMore info: http://kubernetes.io/docs/user-guide/labels\n+optional",
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "managedFields": {
                    "description": "ManagedFields maps workflow-id and version to the set of fields\nthat are managed by that workflow. This is mostly for internal\nhousekeeping, and users typically shouldn't need to set or\nunderstand this field. A workflow can be the user's name, a\ncontroller's name, or the name of a specific apply path like\n\"ci-cd\". The set of fields is always in the version that the\nworkflow used when modifying the object.\n\n+optional",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.ManagedFieldsEntry"
                    }
                },
                "name": {
                    "description": "Name must be unique within a namespace. Is required when creating resources, although\nsome resources may allow a client to request the generation of an appropriate name\nautomatically. Name is primarily intended for creation idempotence and configuration\ndefinition.\nCannot be updated.\nMore info: http://kubernetes.io/docs/user-guide/identifiers#names\n+optional",
                    "type": "string"
                },
                "namespace": {
                    "description": "Namespace defines the space within which each name must be unique. An empty namespace is\nequivalent to the \"default\" namespace, but \"default\" is the canonical representation.\nNot all objects are required to be scoped to a namespace - the value of this field for\nthose objects will be empty.\n\nMust be a DNS_LABEL.\nCannot be updated.\nMore info: http://kubernetes.io/docs/user-guide/namespaces\n+optional",
                    "type": "string"
                },
                "ownerReferences": {
                    "description": "List of objects depended by this object. If ALL objects in the list have\nbeen deleted, this object will be garbage collected. If this object is managed by a controller,\nthen an entry in this list will point to this controller, with the controller field set to true.\nThere cannot be more than one managing controller.\n+optional\n+patchMergeKey=uid\n+patchStrategy=merge",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1.OwnerReference"
                    }
                },
                "resourceVersion": {
                    "description": "An opaque value that represents the internal version of this object that can\nbe used by clients to determine when objects have changed. May be used for optimistic\nconcurrency, change detection, and the watch operation on a resource or set of resources.\nClients must treat these values as opaque and passed unmodified back to the server.\nThey may only be valid for a particular resource or set of resources.\n\nPopulated by the system.\nRead-only.\nValue must be treated as opaque by clients and .\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency\n+optional",
                    "type": "string"
                },
                "selfLink": {
                    "description": "Deprecated: selfLink is a legacy read-only field that is no longer populated by the system.\n+optional",
                    "type": "string"
                },
                "uid": {
                    "description": "UID is the unique in time and space value for this object. It is typically generated by\nthe server on successful creation of a resource and is not allowed to change on PUT\noperations.\n\nPopulated by the system.\nRead-only.\nMore info: http://kubernetes.io/docs/user-guide/identifiers#uids\n+optional",
                    "type": "string"
                }
            }
        },
        "v1.OwnerReference": {
            "type": "object",
            "properties": {
                "apiVersion": {
                    "description": "API version of the referent.",
                    "type": "string"
                },
                "blockOwnerDeletion": {
                    "description": "If true, AND if the owner has the \"foregroundDeletion\" finalizer, then\nthe owner cannot be deleted from the key-value store until this\nreference is removed.\nSee https://kubernetes.io/docs/concepts/architecture/garbage-collection/#foreground-deletion\nfor how the garbage collector interacts with this field and enforces the foreground deletion.\nDefaults to false.\nTo set this field, a user needs \"delete\" permission of the owner,\notherwise 422 (Unprocessable Entity) will be returned.\n+optional",
                    "type": "boolean"
                },
                "controller": {
                    "description": "If true, this reference points to the managing controller.\n+optional",
                    "type": "boolean"
                },
                "kind": {
                    "description": "Kind of the referent.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds",
                    "type": "string"
                },
                "name": {
                    "description": "Name of the referent.\nMore info: http://kubernetes.io/docs/user-guide/identifiers#names",
                    "type": "string"
                },
                "uid": {
                    "description": "UID of the referent.\nMore info: http://kubernetes.io/docs/user-guide/identifiers#uids",
                    "type": "string"
                }
            }
        },
        "v1.Secret": {
            "type": "object",
            "properties": {
                "apiVersion": {
                    "description": "APIVersion defines the versioned schema of this representation of an object.\nServers should convert recognized schemas to the latest internal value, and\nmay reject unrecognized values.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\n+optional",
                    "type": "string"
                },
                "data": {
                    "description": "Data contains the secret data. Each key must consist of alphanumeric\ncharacters, '-', '_' or '.'. The serialized form of the secret data is a\nbase64 encoded string, representing the arbitrary (possibly non-string)\ndata value here. Described in https://tools.ietf.org/html/rfc4648#section-4\n+optional",
                    "type": "object",
                    "additionalProperties": {
                        "type": "array",
                        "items": {
                            "type": "integer"
                        }
                    }
                },
                "immutable": {
                    "description": "Immutable, if set to true, ensures that data stored in the Secret cannot\nbe updated (only object metadata can be modified).\nIf not set to true, the field can be modified at any time.\nDefaulted to nil.\n+optional",
                    "type": "boolean"
                },
                "kind": {
                    "description": "Kind is a string value representing the REST resource this object represents.\nServers may infer this from the endpoint the client submits requests to.\nCannot be updated.\nIn CamelCase.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\n+optional",
                    "type": "string"
                },
                "metadata": {
                    "description": "Standard object's metadata.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata\n+optional",
                    "allOf": [
                        {
                            "$ref": "#/definitions/v1.ObjectMeta"
                        }
                    ]
                },
                "stringData": {
                    "description": "stringData allows specifying non-binary secret data in string form.\nIt is provided as a write-only input field for convenience.\nAll keys and values are merged into the data field on write, overwriting any existing values.\nThe stringData field is never output when reading from the API.\n+k8s:conversion-gen=false\n+optional",
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "type": {
                    "description": "Used to facilitate programmatic handling of secret data.\nMore info: https://kubernetes.io/docs/concepts/configuration/secret/#secret-types\n+optional",
                    "allOf": [
                        {
                            "$ref": "#/definitions/v1.SecretType"
                        }
                    ]
                }
            }
        },
        "v1.SecretType": {
            "type": "string",
            "enum": [
                "Opaque",
                "kubernetes.io/service-account-token",
                "kubernetes.io/dockercfg",
                "kubernetes.io/dockerconfigjson",
                "kubernetes.io/basic-auth",
                "kubernetes.io/ssh-auth",
                "kubernetes.io/tls",
                "bootstrap.kubernetes.io/token"
            ],
            "x-enum-varnames": [
                "SecretTypeOpaque",
                "SecretTypeServiceAccountToken",
                "SecretTypeDockercfg",
                "SecretTypeDockerConfigJson",
                "SecretTypeBasicAuth",
                "SecretTypeSSHAuth",
                "SecretTypeTLS",
                "SecretTypeBootstrapToken"
            ]
        },
        "v1alpha1.StepStatus": {
            "type": "object",
            "properties": {
                "firstExecuteTime": {
                    "description": "FirstExecuteTime is the first time this step execution.",
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "lastExecuteTime": {
                    "description": "LastExecuteTime is the last time this step execution.",
                    "type": "string"
                },
                "message": {
                    "description": "A human readable message indicating details about why the workflowStep is in this state.",
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "phase": {
                    "$ref": "#/definitions/v1alpha1.WorkflowStepPhase"
                },
                "reason": {
                    "description": "A brief CamelCase message indicating details about why the workflowStep is in this state.",
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "v1alpha1.WorkflowExecuteMode": {
            "type": "object",
            "properties": {
                "steps": {
                    "description": "Steps is the mode of workflow steps execution",
                    "allOf": [
                        {
                            "$ref": "#/definitions/v1alpha1.WorkflowMode"
                        }
                    ]
                },
                "subSteps": {
                    "description": "SubSteps is the mode of workflow sub steps execution",
                    "allOf": [
                        {
                            "$ref": "#/definitions/v1alpha1.WorkflowMode"
                        }
                    ]
                }
            }
        },
        "v1alpha1.WorkflowMode": {
            "type": "string",
            "enum": [
                "DAG",
                "StepByStep"
            ],
            "x-enum-varnames": [
                "WorkflowModeDAG",
                "WorkflowModeStep"
            ]
        },
        "v1alpha1.WorkflowRun": {
            "type": "object",
            "properties": {
                "apiVersion": {
                    "description": "APIVersion defines the versioned schema of this representation of an object.\nServers should convert recognized schemas to the latest internal value, and\nmay reject unrecognized values.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\n+optional",
                    "type": "string"
                },
                "kind": {
                    "description": "Kind is a string value representing the REST resource this object represents.\nServers may infer this from the endpoint the client submits requests to.\nCannot be updated.\nIn CamelCase.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\n+optional",
                    "type": "string"
                },
                "metadata": {
                    "$ref": "#/definitions/v1.ObjectMeta"
                },
                "spec": {
                    "$ref": "#/definitions/v1alpha1.WorkflowRunSpec"
                },
                "status": {
                    "$ref": "#/definitions/v1alpha1.WorkflowRunStatus"
                }
            }
        },
        "v1alpha1.WorkflowRunPhase": {
            "type": "string",
            "enum": [
                "initializing",
                "executing",
                "suspending",
                "terminated",
                "failed",
                "succeeded",
                "skipped",
                "manuallySkipped"
            ],
            "x-enum-varnames": [
                "WorkflowStateInitializing",
                "WorkflowStateExecuting",
                "WorkflowStateSuspending",
                "WorkflowStateTerminated",
                "WorkflowStateFailed",
                "WorkflowStateSucceeded",
                "WorkflowStateSkipped",
                "WorkflowStateManuallySkipped"
            ]
        },
        "v1alpha1.WorkflowRunSpec": {
            "type": "object",
            "properties": {
                "context": {
                    "description": "+kubebuilder:pruning:PreserveUnknownFields",
                    "allOf": [
                        {
                            "$ref": "#/definitions/runtime.RawExtension"
                        }
                    ]
                },
                "mode": {
                    "$ref": "#/definitions/v1alpha1.WorkflowExecuteMode"
                },
                "workflowRef": {
                    "type": "string"
                },
                "workflowSpec": {
                    "$ref": "#/definitions/v1alpha1.WorkflowSpec"
                }
            }
        },
        "v1alpha1.WorkflowRunStatus": {
            "type": "object",
            "properties": {
                "conditions": {
                    "description": "Conditions of the resource.\n+optional",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/github_com_kubevela_workflow_api_condition.Condition"
                    }
                },
                "contextBackend": {
                    "$ref": "#/definitions/k8s_io_api_core_v1.ObjectReference"
                },
                "endTime": {
                    "type": "string"
                },
                "finished": {
                    "type": "boolean"
                },
                "message": {
                    "type": "string"
                },
                "mode": {
                    "$ref": "#/definitions/v1alpha1.WorkflowExecuteMode"
                },
                "startTime": {
                    "type": "string"
                },
                "status": {
                    "$ref": "#/definitions/v1alpha1.WorkflowRunPhase"
                },
                "steps": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1alpha1.WorkflowStepStatus"
                    }
                },
                "suspend": {
                    "type": "boolean"
                },
                "suspendState": {
                    "type": "string"
                },
                "terminated": {
                    "type": "boolean"
                }
            }
        },
        "v1alpha1.WorkflowSpec": {
            "type": "object",
            "properties": {
                "steps": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1alpha1.WorkflowStep"
                    }
                }
            }
        },
        "v1alpha1.WorkflowStep": {
            "type": "object",
            "properties": {
                "contextInherited": {
                    "description": "ContextInherited is set to true if the step\nshould inherit parent context as its base properties.",
                    "type": "boolean"
                },
                "dependsOn": {
                    "description": "DependsOn is the dependency of the step",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "if": {
                    "description": "If is the if condition of the step",
                    "type": "string"
                },
                "inputs": {
                    "description": "Inputs is the inputs of the step",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1alpha1.inputItem"
                    }
                },
                "meta": {
                    "description": "Meta is the meta data of the workflow step.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/v1alpha1.WorkflowStepMeta"
                        }
                    ]
                },
                "name": {
                    "description": "Name is the unique name of the workflow step.",
                    "type": "string"
                },
               "description": {
                    "description": "Description this workflow step.",
                    "type": "string"
                },
                "outputs": {
                    "description": "Outputs is the outputs of the step",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1alpha1.outputItem"
                    }
                },
                "ownerReferred": {
                    "description": "OwnerReferred is set to true if the step\nshould have OwnerReference to its parent.",
                    "type": "boolean"
                },
                "properties": {
                    "description": "Properties is the properties of the step\n+kubebuilder:pruning:PreserveUnknownFields",
                    "allOf": [
                        {
                            "$ref": "#/definitions/runtime.RawExtension"
                        }
                    ]
                },
                "subSteps": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1alpha1.WorkflowStepBase"
                    }
                },
                "timeout": {
                    "description": "Timeout is the timeout of the step",
                    "type": "string"
                },
                "type": {
                    "description": "Type is the type of the workflow step.",
                    "type": "string"
                }
            }
        },
        "v1alpha1.WorkflowStepBase": {
            "type": "object",
            "properties": {
                "contextInherited": {
                    "description": "ContextInherited is set to true if the step\nshould inherit parent context as its base properties.",
                    "type": "boolean"
                },
                "dependsOn": {
                    "description": "DependsOn is the dependency of the step",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "if": {
                    "description": "If is the if condition of the step",
                    "type": "string"
                },
                "inputs": {
                    "description": "Inputs is the inputs of the step",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1alpha1.inputItem"
                    }
                },
                "meta": {
                    "description": "Meta is the meta data of the workflow step.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/v1alpha1.WorkflowStepMeta"
                        }
                    ]
                },
                "name": {
                    "description": "Name is the unique name of the workflow step.",
                    "type": "string"
                },
                "description": {
                    "description": "Description this workflow step.",
                    "type": "string"
                },
                "outputs": {
                    "description": "Outputs is the outputs of the step",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1alpha1.outputItem"
                    }
                },
                "ownerReferred": {
                    "description": "OwnerReferred is set to true if the step\nshould have OwnerReference to its parent.",
                    "type": "boolean"
                },
                "properties": {
                    "description": "Properties is the properties of the step\n+kubebuilder:pruning:PreserveUnknownFields",
                    "allOf": [
                        {
                            "$ref": "#/definitions/runtime.RawExtension"
                        }
                    ]
                },
                "timeout": {
                    "description": "Timeout is the timeout of the step",
                    "type": "string"
                },
                "type": {
                    "description": "Type is the type of the workflow step.",
                    "type": "string"
                }
            }
        },
        "v1alpha1.WorkflowStepMeta": {
            "type": "object",
            "properties": {
                "alias": {
                    "type": "string"
                }
            }
        },
        "v1alpha1.WorkflowStepPhase": {
            "type": "string",
            "enum": [
                "succeeded",
                "failed",
                "skipped",
                "running",
                "pending",
                "manuallySkipped"
            ],
            "x-enum-varnames": [
                "WorkflowStepPhaseSucceeded",
                "WorkflowStepPhaseFailed",
                "WorkflowStepPhaseSkipped",
                "WorkflowStepPhaseRunning",
                "WorkflowStepPhasePending",
                "WorkflowStepManuallySkipped"
            ]
        },
        "v1alpha1.WorkflowStepStatus": {
            "type": "object",
            "properties": {
                "firstExecuteTime": {
                    "description": "FirstExecuteTime is the first time this step execution.",
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "lastExecuteTime": {
                    "description": "LastExecuteTime is the last time this step execution.",
                    "type": "string"
                },
                "message": {
                    "description": "A human readable message indicating details about why the workflowStep is in this state.",
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "phase": {
                    "$ref": "#/definitions/v1alpha1.WorkflowStepPhase"
                },
                "reason": {
                    "description": "A brief CamelCase message indicating details about why the workflowStep is in this state.",
                    "type": "string"
                },
                "subSteps": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1alpha1.StepStatus"
                    }
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "v1alpha1.inputItem": {
            "type": "object",
            "properties": {
                "from": {
                    "type": "string"
                },
                "parameterKey": {
                    "type": "string"
                }
            }
        },
        "v1alpha1.outputItem": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "valueFrom": {
                    "type": "string"
                }
            }
        },
        "v1beta1.Isomate": {
            "type": "object",
            "properties": {
                "configValues": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string"
                },
                "plugins": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1beta1.ReleasePlugin"
                    }
                }
            }
        },
        "v1beta1.IsomateConfig": {
            "type": "object",
            "properties": {
                "defaultIsomateName": {
                    "type": "string"
                },
                "isomates": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/v1beta1.Isomate"
                    }
                }
            }
        },
        "v1beta1.ReleaseConfig": {
            "type": "object",
            "properties": {
                "apiVersion": {
                    "description": "APIVersion defines the versioned schema of this representation of an object.\nServers should convert recognized schemas to the latest internal value, and\nmay reject unrecognized values.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\n+optional",
                    "type": "string"
                },
                "kind": {
                    "description": "Kind is a string value representing the REST resource this object represents.\nServers may infer this from the endpoint the client submits requests to.\nCannot be updated.\nIn CamelCase.\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\n+optional",
                    "type": "string"
                },
                "metadata": {
                    "$ref": "#/definitions/v1.ObjectMeta"
                },
                "spec": {
                    "$ref": "#/definitions/v1beta1.ReleaseConfigSpec"
                },
                "status": {
                    "$ref": "#/definitions/v1beta1.ReleaseConfigStatus"
                }
            }
        },
        "v1beta1.ReleaseConfigSpec": {
            "type": "object",
            "properties": {
                "chartAppVersion": {
                    "type": "string"
                },
                "chartImage": {
                    "type": "string"
                },
                "chartName": {
                    "type": "string"
                },
                "chartVersion": {
                    "type": "string"
                },
                "chartWalmVersion": {
                    "type": "string"
                },
                "configValues": {
                    "type": "object",
                    "additionalProperties": true
                },
                "dependencies": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "dependenciesConfigValues": {
                    "type": "object",
                    "additionalProperties": true
                },
                "isomateConfig": {
                    "$ref": "#/definitions/v1beta1.IsomateConfig"
                },
                "outputConfig": {
                    "type": "object",
                    "additionalProperties": true
                },
                "repo": {
                    "type": "string"
                }
            }
        },
        "v1beta1.ReleaseConfigStatus": {
            "type": "object"
        },
        "v1beta1.ReleasePlugin": {
            "type": "object",
            "properties": {
                "args": {
                    "type": "string"
                },
                "disable": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "version": {
                    "type": "string"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "",
	Description:      "",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
