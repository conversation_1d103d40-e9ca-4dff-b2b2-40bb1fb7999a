package article

import (
	"fmt"
	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"
	"net/http"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/service"
)

const (
	QueryParamProjectId     = "project_id"
	QueryParamArticleStatus = "status"
	QueryParamArticleID     = "article_id"
)

type ID struct {
	ID string `json:"id"`
}

func NewArticleAPI(root string, as *service.ArticleService) *restful.WebService {
	return (&Resource{as}).WebService(root)
}

type Resource struct {
	as *service.ArticleService
}

func (r *Resource) WebService(root string) *restful.WebService {
	tags := []string{"文章管理"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(root + "artimgr")
	ws.Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	projectIDQueryParam := ws.QueryParameter(QueryParamProjectId, "空间id").Required(true)

	ws.Route(ws.GET("/articles").To(r.ListArticles).
		Doc("获取所有文章列表").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Param(ws.QueryParameter(QueryParamArticleStatus,
			fmt.Sprintf("%s:发布中-待审核，%s:发布完成-已审核", dao.ArticleStatusPublishing, dao.ArticleStatusPublished))).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []*dao.Article{}))

	ws.Route(ws.GET(fmt.Sprintf("/articles/{%s}/content", QueryParamArticleID)).To(r.GetArticleContent).
		Doc("获取单篇文章内容").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Param(ws.PathParameter(QueryParamArticleID, "文章id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.Article{}))

	ws.Route(ws.POST("/articles:publish").To(r.PublishArticle).
		Doc("发布一篇文章").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Reads(PublishArticleReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), ID{}))

	ws.Route(ws.POST("/articles:examine").To(r.ExamineArticle).
		Doc("将文章审核通过").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Reads(ID{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	ws.Route(ws.PATCH("/articles").To(r.UpdateArticle).
		Doc("对文章进行修改").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Reads(dao.Article{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	return ws
}
