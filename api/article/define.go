package article

import (
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/utils/auth/cas/client"
)

func (r *Resource) ListArticles(request *restful.Request, response *restful.Response) {
	projectId := request.QueryParameter(QueryParamProjectId)
	if projectId == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的空间ID"))
		return
	}
	status := request.QueryParameter(QueryParamArticleStatus)
	articles, err := r.as.ListArticles(projectId, status)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, articles)
}
func (r *Resource) GetArticleContent(request *restful.Request, response *restful.Response) {
	id := request.PathParameter(QueryParamArticleID)
	article, err := r.as.GetArticle(id)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, article)
}
func (r *Resource) PublishArticle(request *restful.Request, response *restful.Response) {
	projectId := request.QueryParameter(QueryParamProjectId)
	if projectId == "" {
		helper.ErrorResponse(response, stderr.Error("the projectId is empty str"))
		return
	}
	articleReq := new(PublishArticleReq)
	if err := request.ReadEntity(articleReq); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	articleDO := &dao.Article{
		Name:       articleReq.Name,
		Abstract:   articleReq.Abstract,
		Content:    articleReq.Content,
		Source:     articleReq.Source,
		ProjectId:  projectId,
		CreateUser: client.Username(request.Request),
	}
	id, err := r.as.CreateArticle(articleDO)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, ID{id})

}
func (r *Resource) ExamineArticle(request *restful.Request, response *restful.Response) {
	ID := new(ID)
	if err := request.ReadEntity(ID); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	article := &dao.Article{Status: dao.ArticleStatusPublished}
	if err := r.as.UpdateArticle(ID.ID, article); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, ID)

}
func (r *Resource) UpdateArticle(request *restful.Request, response *restful.Response) {
	articleReq := new(UpdateArticleReq)
	if err := request.ReadEntity(articleReq); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	if articleReq.Id == "" {
		err := stderr.Error("the id of article is empty str")
		helper.ErrorResponse(response, err)
		return
	}
	articleDO := &dao.Article{
		Name:     articleReq.Name,
		Abstract: articleReq.Abstract,
		Content:  articleReq.Content,
		Source:   articleReq.Source,
	}
	if err := r.as.UpdateArticle(articleReq.Id, articleDO); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, ID{articleReq.Id})
}

type PublishArticleReq struct {
	Name     string `json:"name" description:"文章名称"`
	Abstract string `json:"abstract" description:"摘要"`
	Content  string `json:"content"  description:"内容"`
	Source   string `json:"source" description:"来源"`
}

type UpdateArticleReq struct {
	Id string `json:"id" description:"文章id,必填"`
	PublishArticleReq
}
