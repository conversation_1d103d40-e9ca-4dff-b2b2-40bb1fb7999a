package usermgr

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/emicklei/go-restful/v3"
	com_client "transwarp.io/aip/llmops-common/pkg/client"
	stdauth "transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/api/auth"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/tenant"
	httpclient "transwarp.io/applied-ai/central-auth-service/service/tenant/client"
	customtypes "transwarp.io/applied-ai/central-auth-service/service/tenant/types"
	"transwarp.io/applied-ai/central-auth-service/utils/auth/cas/client"
)

func (r *Resource) CreateTenant(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	tenantReq := new(models.Tenant)
	if err := request.ReadEntity(tenantReq); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}

	// check tenant name is exists
	err := r.ts.ValidateTenantName(ctx, tenantReq.TenantName)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	// check tenant uid is exists
	err = r.ts.ValidateTenantUid(ctx, tenantReq.TenantUid)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	user, err := auth.GetAuthHandler().GetAuthenticationUser(request.Request)
	if err != nil {
		helper.ErrorResponse(response, stderr.Unauthenticated.Error("用户信息获取失败！"))
		return
	}

	tenantReq.Creator = user.Name
	tenantReq.CreateTime = uint64(time.Now().Unix())
	labels := map[string]string{
		customtypes.NamespaceLableNsType: string(customtypes.TenantNs),
	}

	tenantReq.TenantQuotas.Hard.QuotaType = tenantReq.TenantQuotas.QuotaType

	tenant, err := r.ts.CreateTenant(ctx, tenantReq, labels, false)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, tenant)
}

func (r *Resource) UpdateTenant(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	tenantReq := new(models.Tenant)
	if err := request.ReadEntity(tenantReq); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	tenantId := request.PathParameter(helper.TenantId)
	if tenantId == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("no tenant id"))
	}
	tenantReq.TenantUid = tenantId

	labels := map[string]string{
		customtypes.NamespaceLableNsType: string(customtypes.TenantNs),
	}

	tenantReq.TenantQuotas.Hard.QuotaType = tenantReq.TenantQuotas.QuotaType

	tenant, err := r.ts.UpdateTenant(ctx, tenantReq, labels)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, tenant)
}

func (r *Resource) DeleteTenant(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	tenantId := request.PathParameter(helper.TenantId)
	err := r.ts.DeleteTenant(ctx, tenantId)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) BatchDeleteTenant(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	IDs := new(models.TenantIDs)
	if err := request.ReadEntity(IDs); err != nil {
		helper.ErrorResponse(response, err)
	}
	err := r.ts.BatchDeleteTenant(ctx, IDs.IDs)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) ListTenants(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	tenants, err := r.ts.ListTenants(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	// 注入关联的project
	projects, err := r.ps.ListProjects(client.Username(request.Request), "")
	if err != nil {
		helper.ErrorResponse(response, err)
	}
	groups := models.ProjectGroupByTenant(projects)
	for _, t := range tenants {
		if v, ok := groups[t.TenantUid]; ok {
			projs := make([]models.ProjectIDInfo, 0)
			for _, p := range v {
				projs = append(projs, models.ProjectIDInfo{
					ProjectID:   p.ProjectId,
					ProjectName: p.Name,
					Creator:     p.CreateUser,
				})
			}
			if len(projs) > 0 {
				t.CanDelete = false
			}
			t.ProjectIDInfos = projs
		}
	}
	helper.SuccessResponse(response, tenants)
}

func (r *Resource) GetTenant(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	tenantId := request.PathParameter(helper.TenantId)
	tenant, err := r.ts.GetTenant(ctx, tenantId, false)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projects, err := r.ps.ListProjects(client.Username(request.Request), "")
	if err != nil {
		helper.ErrorResponse(response, err)
	}
	groups := models.ProjectGroupByTenant(projects)

	if v, ok := groups[tenantId]; ok {
		projs := make([]models.ProjectIDInfo, 0)
		for _, p := range v {
			projs = append(projs, models.ProjectIDInfo{
				ProjectID:   p.ProjectId,
				ProjectName: p.Name,
				Creator:     p.CreateUser,
			})
		}
		tenant.ProjectIDInfos = projs
	}
	helper.SuccessResponse(response, tenant)
}

func (r *Resource) EnsureTenantLlmopsBasic(request *restful.Request, response *restful.Response) {
	tenantId := request.PathParameter(helper.TenantId)
	hippo, err := r.ts.EnsureLlmopsBasic(tenantId)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, hippo)
}

func (r *Resource) EnsureTenantHippo(request *restful.Request, response *restful.Response) {
	tenantId := request.PathParameter(helper.TenantId)
	hippo, err := r.ts.EnsureHippo(tenantId)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, hippo)
}

func (r *Resource) GetTenantHippo(request *restful.Request, response *restful.Response) {
	tenantId := request.PathParameter(helper.TenantId)
	hippo, err := r.ts.GetHippo(tenantId)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, hippo)
}

func (r *Resource) GetTenantResourceQuota(request *restful.Request, response *restful.Response) {
	tenantId := request.PathParameter(helper.TenantId)
	quota, err := r.ts.GetResourceQuota(tenantId)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, quota)
}

func (r *Resource) UpdateTenantResourceQuota(request *restful.Request, response *restful.Response) {
	tenantUid := request.PathParameter(helper.TenantId)
	quotaSpec := models.TenantResourceQuota{}
	if err := request.ReadEntity(&quotaSpec); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	token := request.Request.Header.Get(stdauth.TokenHeader)
	user, _ := auth.GetAuthHandler().GetAuthenticationUser(request.Request)
	ctx := context.Background()
	ctx = context.WithValue(ctx, "token", token)
	ctx = context.WithValue(ctx, "username", user.Name)

	quotaSpec.Hard.QuotaType = quotaSpec.QuotaType

	quota, err := r.ts.UpdateResourceQuota(ctx, tenantUid, &quotaSpec.Hard)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, quota)
}

func (r *Resource) GetDefaultResourceQuota(request *restful.Request, response *restful.Response) {
	quota := r.ts.GetDefaultResourceQuota()
	helper.SuccessResponse(response, quota)
}

func (r *Resource) ValidateTenantName(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	name := request.QueryParameter("tenant_name")
	err := r.ts.ValidateTenantName(ctx, name)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, struct{}{})
}

func (r *Resource) ValidateTenantUid(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	uid := request.QueryParameter("tenant_uid")
	err := r.ts.ValidateTenantUid(ctx, uid)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, struct{}{})
}

func (r *Resource) GetTenantEnv(request *restful.Request, response *restful.Response) {
	baseURL := conf.C.Tenant.TDC5.TenantService.BaseURL
	parsedURL, err := url.Parse(baseURL)
	cleanBaseURL := ""
	if err != nil {
		stdlog.Errorf("parse tdc5 url failed: %+v", err)
	} else {
		if parsedURL.Host != "" {
			cleanBaseURL = fmt.Sprintf("%s://%s", parsedURL.Scheme, parsedURL.Host)
		}
	}

	env := models.TenantEnv{
		GuardianAccessToken: httpclient.GetGuardianAccessToken(),
		Strategy:            conf.C.Tenant.Strategy,
		TDC5Address:         cleanBaseURL,
	}
	helper.SuccessResponse(response, env)
}

func (r *Resource) GetTenantStatus(request *restful.Request, response *restful.Response) {
	tenantUid := request.PathParameter(helper.TenantId)
	baseUrl := request.QueryParameter("base_url")

	http.DefaultTransport.(*http.Transport).TLSClientConfig = &tls.Config{InsecureSkipVerify: true}
	resp, err := http.Get(fmt.Sprintf("%s/tdc/ignitor?guardian_access_token=%s", baseUrl, httpclient.GetGuardianAccessToken()))
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	defer resp.Body.Close()

	cookies := resp.Cookies()

	parsedURL, err := url.Parse(baseUrl)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	for _, cookie := range cookies {
		cookie.Domain = parsedURL.Hostname()
		http.SetCookie(response, cookie)
	}
	locationUrl := fmt.Sprintf("%s/tdc/ignitor/#/register/status/%s", baseUrl, tenantUid)
	helper.SuccessResponse(response, models.TenantStatusLocationUrl{Location: locationUrl})
}

func (r *Resource) CreateInress(request *restful.Request, response *restful.Response) {
	tenantUid := request.PathParameter(helper.TenantId)
	err := r.ts.CreateTenantIngress(tenantUid)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) InitTenant(request *restful.Request, response *restful.Response) {
	projectId := request.PathParameter(helper.ParamProjectId)
	if projectId == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("项目ID不能为空"))
		return
	}
	ctx := com_client.SetToken(request.Request.Context(), request.Request.Header.Get(stdauth.TokenHeader))
	tenantid, err := tenant.InitTenant(ctx, projectId, r.ts)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, tenantid)
}

func (r *Resource) NxgToResourceQuota(request *restful.Request, response *restful.Response) {
	groupIDsStr := request.QueryParameter(helper.GroupIDs)
	groupIDs := strings.Split(groupIDsStr, ",")

	res, err := r.ts.NxgToResourceQuota(groupIDs)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}
