package usermgr

import (
	"net/http"

	"transwarp.io/applied-ai/central-auth-service/service/project"

	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/tenant"
)

func NewTenantAPI(root string, ts tenant.TenantService, ps *project.ProjectService) *restful.WebService {
	return (&Resource{ts: ts, ps: ps}).TenantService(root)
}

type Resource struct {
	ts tenant.TenantService
	ps *project.ProjectService
}

func (r *Resource) TenantService(root string) *restful.WebService {
	ws := new(restful.WebService)
	ws.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)
	metaK := restfulspec.KeyOpenAPITags
	tags := []string{"租户管理"}

	// tenant
	ws.Route(ws.POST("/").Doc("新建租户").
		To(r.CreateTenant).Metadata(metaK, tags).
		Consumes(restful.MIME_JSON).Reads(models.Tenant{}).
		Returns(http.StatusOK, "OK", models.Tenant{}))
	ws.Route(ws.PUT("/{tenant_uid}").Doc("更新租户").
		To(r.UpdateTenant).Metadata(metaK, tags).
		Consumes(restful.MIME_JSON).Reads(models.Tenant{}).
		Returns(http.StatusOK, "OK", models.Tenant{}))
	ws.Route(ws.DELETE("/{tenant_uid}").Doc("删除租户").
		Param(ws.PathParameter("tenant_uid", "租户id").DataType("string").Required(true)).
		To(r.DeleteTenant).Metadata(metaK, tags).
		Returns(http.StatusOK, "OK", helper.EmptyRsp{}))
	ws.Route(ws.POST(":batch_delete").Doc("批量删除租户").
		Consumes(restful.MIME_JSON).Reads(models.TenantIDs{}).
		To(r.BatchDeleteTenant).Metadata(metaK, tags).
		Returns(http.StatusOK, "OK", helper.EmptyRsp{}))
	ws.Route(ws.GET("/").Doc("获取租户列表").
		To(r.ListTenants).Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes([]models.Tenant{}).
		Returns(http.StatusOK, "OK", []models.Tenant{}))
	ws.Route(ws.GET("/{tenant_uid}").Doc("获取租户").
		Param(ws.PathParameter("tenant_uid", "租户id").DataType("string").Required(true)).
		To(r.GetTenant).Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes(models.Tenant{}).
		Returns(http.StatusOK, "OK", models.Tenant{}))
	ws.Route(ws.POST("/{tenant_uid}/llmops-basic").Doc("检查llmops-basic状态，未安装就执行安装").
		Param(ws.PathParameter("tenant_uid", "租户id").DataType("string").Required(true)).
		To(r.EnsureTenantLlmopsBasic).Metadata(metaK, tags).
		Consumes(restful.MIME_JSON).Reads(struct{}{}).
		Returns(http.StatusOK, "OK", models.Instance{}))
	ws.Route(ws.POST("/{tenant_uid}/hippo").Doc("检查hippo实例状态，未安装就执行安装").
		Param(ws.PathParameter("tenant_uid", "租户id").DataType("string").Required(true)).
		To(r.EnsureTenantHippo).Metadata(metaK, tags).
		Consumes(restful.MIME_JSON).Reads(struct{}{}).
		Returns(http.StatusOK, "OK", models.Instance{}))
	ws.Route(ws.GET("/{tenant_uid}/hippo").Doc("获取hippo实例状态").
		Param(ws.PathParameter("tenant_uid", "租户id").DataType("string").Required(true)).
		To(r.GetTenantHippo).Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes(models.Instance{}).
		Returns(http.StatusOK, "OK", models.Instance{}))
	ws.Route(ws.GET("/{tenant_uid}/resource_quota").Doc("获取租户资源额度").
		Param(ws.PathParameter("tenant_uid", "租户id").DataType("string").Required(true)).
		To(r.GetTenantResourceQuota).Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes(models.TenantResourceQuota{}).
		Returns(http.StatusOK, "OK", models.TenantResourceQuota{}))
	ws.Route(ws.PUT("/{tenant_uid}/resource_quota").Doc("更新租户资源额度").
		Param(ws.PathParameter("tenant_uid", "租户id").DataType("string").Required(true)).
		To(r.UpdateTenantResourceQuota).Metadata(metaK, tags).
		Metadata("examine", "resource_quota").
		Consumes(restful.MIME_JSON).Reads(models.TenantResourceQuota{}).
		Returns(http.StatusOK, "OK", models.TenantResourceQuota{}))
	ws.Route(ws.GET("/default_quota").Doc("获取租户默认资源额度").
		To(r.GetDefaultResourceQuota).Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes(models.TenantResourceQuota{}).
		Returns(http.StatusOK, "OK", models.TenantResourceQuota{}))
	ws.Route(ws.GET("/validate/name").Doc("验证租户名称是否合法").
		Param(ws.QueryParameter("tenant_name", "租户name").DataType("string").Required(true)).
		To(r.ValidateTenantName).Metadata(metaK, tags).
		Returns(http.StatusOK, "OK", struct{}{}))
	ws.Route(ws.GET("/validate/uid").Doc("验证租户uid是否合法").
		Param(ws.QueryParameter("tenant_uid", "租户uid").DataType("string").Required(true)).
		To(r.ValidateTenantUid).Metadata(metaK, tags).
		Returns(http.StatusOK, "OK", struct{}{}))
	ws.Route(ws.GET("/envs").Doc("获取租户配置信息").
		To(r.GetTenantEnv).Metadata(metaK, tags).
		Returns(http.StatusOK, "OK", models.TenantEnv{}))
	ws.Route(ws.GET("/{tenant_uid}/tdc/status").Doc("获取tdc租户状态").
		Param(ws.PathParameter("tenant_uid", "租户id").DataType("string").Required(true)).
		Param(ws.QueryParameter("base_url", "base_url").DataType("string").Required(true)).
		To(r.GetTenantStatus).Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes(models.TenantStatusLocationUrl{}).
		Returns(http.StatusOK, "OK", models.TenantStatusLocationUrl{}))
	ws.Route(ws.POST("/{tenant_uid}/ingress").Doc("New Ingress").
		Param(ws.PathParameter("tenant_uid", "租户id").DataType("string").Required(true)).
		To(r.CreateInress).Metadata(metaK, tags).
		Consumes(restful.MIME_JSON).Reads(struct{}{}).
		Returns(http.StatusOK, "OK", struct{}{}))
	ws.Route(ws.GET("/nxg-resource-quota").Doc("gpu group to resource quota").
		Param(ws.QueryParameter("group_ids", "租户id").DataType("string").Required(true)).
		To(r.NxgToResourceQuota).Metadata(metaK, tags).
		Returns(http.StatusOK, "OK", models.TenantResourceQuota{}))
	// .
	// 	To(r.NotImplementYet).
	// 	Returns(http.StatusOK, http.StatusText(http.StatusOK), nil))
	//
	return ws
}
