package monitor

import (
	"net/http"

	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/central-auth-service/service/monitor"
)

func (r *Resource) WebService(root string) *restful.WebService {
	tags := []string{"指标监控"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(root + "/monitor")
	ws.Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	projectIDQueryParam := ws.QueryParameter(QueryParamProjectId, "空间id").Required(true)

	ws.Route(ws.GET("/services/{sid}/metrics").To(r.ListSvcMetrics).
		Doc("获取某服务的指标列表").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Param(ws.PathParameter(PathParamServiceId, "服务的id")).
		Param(ws.QueryParameter(QueryParamServiceSourceType, "服务的source_type,来源类型：0未知、1模型仓库、2应用仓库、3vlab、4、远程模型、11自定义镜像(需要传)")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), monitor.ListMetricsRsp{}))

	ws.Route(ws.GET("/services/{sid}/dashboard").To(r.QuerySvcDashboardData).
		Doc("查询某服务的指标数据(包含多个图表)").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Param(ws.PathParameter(PathParamServiceId, "服务的id")).
		Param(ws.QueryParameter(QueryParamServiceSourceType, "服务的source_type,来源类型：0未知、1模型仓库、2应用仓库、3vlab、4、远程模型、11自定义镜像(需要传)")).
		Param(ws.QueryParameter(QueryParamFrom, "开始时间,毫秒时间戳")).
		Param(ws.QueryParameter(QueryParamTo, "结束时间,毫秒时间戳")).
		Param(ws.QueryParameter(QueryParamStep, "聚合时间粒度(时间段长度), 毫秒")).
		Param(ws.QueryParameter(QueryParamTab, "dashboard tab页选择, 取值参考pb/serving/mlops_service.pb.go DashboardTabType")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), monitor.SvcDashboardData{}))

	ws.Route(ws.GET("/global/options").To(r.ListGlobalGPUDashboardOptions).
		Doc("全局算力大屏-获取选项列表").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), monitor.GlobalGPUOptions{}))

	ws.Route(ws.POST("/global/gpu/overview").To(r.QueryGlobalGPUOverview).
		Doc("全局算力大屏-查询GPU资源概览").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Reads(monitor.GlobalGPUFilter{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), monitor.GlobalGPUOverview{}))

	ws.Route(ws.POST("/global/gpu/usage-trends").To(r.QueryGlobalGPUUsageTrends).
		Doc("全局算力大屏-查询GPU资源使用趋势").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Reads(monitor.GlobalGPUFilter{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), monitor.PanelWithData{}))

	ws.Route(ws.POST("/global/base/overview").To(r.QueryGlobalBaseOverview).
		Doc("全局算力大屏-查询基础资源概览").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Reads(monitor.GlobalGPUFilter{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), monitor.GlobalBaseOverview{}))

	ws.Route(ws.POST("/global/base/usage-trends").To(r.QueryGlobalBaseUsageTrends).
		Doc("全局算力大屏-查询基础资源使用趋势").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Reads(monitor.GlobalGPUFilter{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), monitor.PanelWithData{}))

	ws.Route(ws.POST("/global/usage-rankings").To(r.QueryGlobalUsageRankings).
		Doc("全局算力/服务大屏-查询资源使用排行").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Reads(monitor.GlobalGPUFilter{}).
		Param(ws.QueryParameter(QueryParamType, "筛选运行资源类型, service:服务, 为空时查询全部")).
		Param(ws.QueryParameter(QueryParamTopK, "top K, 为空或小于等于0时保留全部结果")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), monitor.GlobalUsageRankings{}))

	ws.Route(ws.POST("/global/gpu/card-base").To(r.QueryGlobalGPUCardBase).
		Reads(monitor.GlobalGPUFilter{}).
		Doc("全局算力大屏-查询GPU算力显卡详情-卡片信息").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), monitor.GlobalNodeGPUCardBase{}))

	ws.Route(ws.GET("/global/gpu/usage/{device_uuid}").To(r.QueryGlobalGPUInfo).
		Doc("全局算力大屏-查询GPU资源显卡详情-详情信息").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(restful.PathParameter("device_uuid", "device_uuid")).
		Param(restful.PathParameter("gpu_node", "gpu_node")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), monitor.GPUInfo{}))

	ws.Route(ws.GET("/global/svc/overview").To(r.QueryGlobalSvcOverview).
		Doc("全局服务大屏-查询服务概览").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), monitor.GlobalSvcOverview{}))

	ws.Route(ws.GET("/global/svc/list/{device_uuid}").To(r.QueryGlobalSvcList).
		Param(ws.PathParameter(PathParamCardDeviceUUID, "设备名字")).
		Param(ws.QueryParameter(QueryParamName, "名字")).
		Param(ws.QueryParameter(QueryParamNode, "节点")).
		Param(ws.QueryParameter("page", "[第几页]page").DataType("integer")).
		Param(ws.QueryParameter("pageSize", "[页大小]page size").DataType("integer")).
		Doc("全局服务大屏-查询服务列表").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), monitor.ServiceList{}))

	return ws
}
