package trust

import (
	"reflect"
)

var (
	empty = GuardianPackage{}
)

func DiscoverTDCGuardianPackage(namespace string) (GuardianPackage, error) {
	var tdc GuardianPackage
	//var pwd, user []byte
	// TODO: use guardian password in config
	//if namespace == common.TDCNamespace {
	//	// currently discover this from release config
	//	release, exist, err := kube.FindNamespaced[v1beta1.ReleaseConfig](kube.GetReleaseConfigInformer(), namespace, common.TDCGuardianReleaseName)
	//	if err != nil || !exist {
	//		return empty, errs.NotFound("guardian not found for tenant %s", namespace)
	//	}
	//	user = []byte("admin")
	//	adv := release.Spec.ConfigValues["Advance_Config"].(map[string]interface{})
	//	pwd = []byte(
	//		adv["guardian_ds_properties"].(map[string]interface{})["guardian.ds.admin.password"].(string),
	//	)
	//} else {
	//	data, err := walm.Walm.GetSecret(namespace, "guardian-secret")
	//	if err != nil {
	//		return empty, err
	//	}
	//	pwd64 := data["guardian_ds_admin_password"]
	//	user64 := data["guardian_ds_admin_username"]
	//	pwd, err = base64.StdEncoding.DecodeString(pwd64)
	//	if err != nil {
	//		fmt.Println("decode password is wrong:", err)
	//	}
	//
	//	// 对用户名进行解码
	//	user, err = base64.StdEncoding.DecodeString(user64)
	//	if err != nil {
	//		return empty, err
	//	}
	//}

	tdc.Namespace = namespace
	tdc.Name = namespace + "-guardian"
	//tdc.DSAdminUsername = string(user)
	//tdc.DSAdminPassword = string(pwd)
	tdc.DSAdminUsername = "admin"
	tdc.DSAdminPassword = "admin"
	//release, err := walm.Walm.GetRelease(namespace, tdc.Name)
	//if err != nil {
	//	return empty, err
	//}
	//// TODO: use exported config to decouple with guardian-server's chart implementation
	//svcs := release.ReleaseStatus.Services
	//var addr = ""
	//var p int32
	//for _, svc := range svcs {
	//	if strings.Contains(svc.Name, "guardian-server") && !strings.Contains(svc.Name, "guardian-server-hl") {
	//		addr = svc.Name + "." + svc.Namespace + ".svc"
	//		for _, port := range svc.Ports {
	//			p = port.Port
	//		}
	//	}
	//}
	//sp := strconv.Itoa(int(p))
	//addr = addr + ":" + sp
	tdc.GuardianServerAddress = "https://************:8380"
	tdc.K8sGuardianServerAddress = "https://************:8380"
	return tdc, nil
}

type Package struct {
	FuncFactory map[string]interface{}
}

func NewPackage() *Package {
	return &Package{
		FuncFactory: make(map[string]interface{}),
	}
}

func (p *Package) GetInvalidAttrs(attrNames []string, aliasAttrNames []string) []string {
	invalids := make([]string, 0)
	aliasAttrMap := map[string]string{
		// Update with proper mapping of alias to attribute name
	}

	for _, an := range aliasAttrNames {
		n, ok := aliasAttrMap[an]
		if ok {
			attrNames = append(attrNames, n)
		} else {
			invalids = append(invalids, an)
		}
	}

	for _, n := range attrNames {
		val := reflect.ValueOf(p).Elem().FieldByName(n).Interface()
		switch v := val.(type) {
		case string:
			if len(v) == 0 {
				invalids = append(invalids, n)
			}
		case []string:
			if len(v) == 0 {
				invalids = append(invalids, n)
			}
			// Add additional cases for other types as needed
		}
	}

	return invalids
}

func (p *Package) SetAttrWithAlias(alias string, attrValue interface{}) {
	aliasAttrMap := map[string]string{
		// Update with proper mapping of alias to attribute name
	}

	attr, ok := aliasAttrMap[alias]
	if ok {
		field := reflect.ValueOf(p).Elem().FieldByName(attr)
		switch field.Kind() {
		case reflect.Slice:
			field.Set(reflect.Append(field, reflect.ValueOf(attrValue)))
		default:
			field.Set(reflect.ValueOf(attrValue))
		}
	}
}

func (p *Package) GetFunc(funcType string) interface{} {
	return p.FuncFactory[funcType]
}

func (p *Package) SetFunc(funcType string, function interface{}) {
	if _, ok := p.FuncFactory[funcType]; ok || reflect.TypeOf(function).Kind() != reflect.Func {
		return
	}
	p.FuncFactory[funcType] = function
}
