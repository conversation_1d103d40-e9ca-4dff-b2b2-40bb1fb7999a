package trust

import (
	"context"
	"crypto/tls"
	"encoding/base64"
	"fmt"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	guardianv2 "transwarp.io/applied-ai/central-auth-service/api/guardian/client"
)

var (
	GuardianV2 GuardianV2Client
)

type HttpApiErrorResp struct {
	ErrCode    int32  `json:"status"`
	ErrMessage string `json:"message"`
}

func (he HttpApiErrorResp) Error() string {
	return fmt.Sprintf("http clent resp status code: %d, error message: %s", he.ErrCode, he.ErrMessage)
}

type GuardianV2Client interface {
	VerifyAccessToken(body guardianv2.AccessTokenVo) (guardianv2.AccessTokenVo, error)
	Login(body guardianv2.LoginRequestVo) (guardianv2.SessionVo, error)
	Logout() error
	//添加Perm
	AddPerm(body guardianv2.PermVo) (guardianv2.PermVo, error)
	//grant权限到admin用户
	GrantPerm(body guardianv2.PrincPermVo) error
	//验证获取datasource信息接口
	GetAuthorizedDataSources(body guardianv2.AuthorizedResourceRequestVo) ([][]guardianv2.NodeVo, error)
	//删除权限
	DeleteServicePerms(serviceName string) error
	//删除TDH集群的共享关系以及权限
	DeleteSharedRelation(body guardianv2.PermVo) error
	RequestKeytab(principals []string, base64Encoded bool) ([]byte, error)
}

type GuardianClient struct {
	g *guardianv2.APIClient
	//logger  logger.NgWalmLogger
	timeOut time.Duration
}

func (g *GuardianClient) VerifyAccessToken(body guardianv2.AccessTokenVo) (guardianv2.AccessTokenVo, error) {
	//g.logger.Infof("VerifyAccessToken : %s", utils.JsonOutPut(body))
	ctx, cancel := context.WithTimeout(context.Background(), g.timeOut)
	token, _, err := g.g.LoginApi.VerifyAccessTokenUsingPOST1(ctx, body)
	//if err != nil {
	//	g.logger.Errorf(err, "failed to VerifyAccessToken")
	//}
	cancel()
	return token, err
}

func (g *GuardianClient) Login(body guardianv2.LoginRequestVo) (guardianv2.SessionVo, error) {
	//g.logger.Infof("Login : %s", utils.JsonOutPut(body))
	ctx, cancel := context.WithTimeout(context.Background(), g.timeOut)
	login, _, err := g.g.LoginApi.LoginUsingPOST(ctx, body)
	//if err != nil {
	//	g.logger.Errorf(err, "failed to login")
	//}
	cancel()
	fmt.Println("login success!")
	return login, err
}
func (g *GuardianClient) Logout() error {
	//g.logger.Infof("logout")
	ctx, cancel := context.WithTimeout(context.Background(), g.timeOut)
	_, err := g.g.LoginApi.LogoutUsingPOST1(ctx)
	//if err != nil {
	//	g.logger.Errorf(err, "failed to logout")
	//}
	cancel()
	return err
}

func (g *GuardianClient) DeleteSharedRelation(body guardianv2.PermVo) error {
	//g.logger.Infof("DeleteClusterAction: %s", utils.JsonOutPut(body))
	ctx, cancel := context.WithTimeout(context.Background(), g.timeOut)
	_, err := g.g.PermissionsApi.DeletePermUsingPOST(ctx, body)
	//if err != nil {
	//	g.logger.Errorf(err, "failed to AddPerm")
	//}
	cancel()
	return err
}

func (g *GuardianClient) AddPerm(body guardianv2.PermVo) (guardianv2.PermVo, error) {
	//g.logger.Infof("AddPerm: %s", utils.JsonOutPut(body))
	ctx, cancel := context.WithTimeout(context.Background(), g.timeOut)
	res, _, err := g.g.PermissionsApi.AddPermUsingPOST(ctx, body)
	//if err != nil {
	//	g.logger.Errorf(err, "failed to AddPerm")
	//}
	cancel()
	return res, err
}

func (g *GuardianClient) GrantPerm(body guardianv2.PrincPermVo) error {
	//g.logger.Infof("GrantPermission: %s", utils.JsonOutPut(body))
	ctx, cancel := context.WithTimeout(context.Background(), g.timeOut)
	_, err := g.g.PermissionsApi.GrantPermUsingPUT1(ctx, body)
	//if err != nil {
	//	g.logger.Errorf(err, "failed to GrantPermission")
	//}
	cancel()
	return err
}

func (g *GuardianClient) GetAuthorizedDataSources(body guardianv2.AuthorizedResourceRequestVo) ([][]guardianv2.NodeVo, error) {
	//g.logger.Infof("GetAuthorizedDataSources: %s", utils.JsonOutPut(body))
	ctx, cancel := context.WithTimeout(context.Background(), g.timeOut)
	ds, _, err := g.g.PermissionsApi.GetAuthorizedDataSourcesUsingPOST(ctx, body)
	//if err != nil {
	//	g.logger.Errorf(err, "failed to GetAuthorizedDataSources")
	//}
	cancel()
	return ds, err
}

func (g *GuardianClient) DeleteServicePerms(serviceName string) error {
	//g.logger.Infof("DeleteServicePerms: %s", utils.JsonOutPut(serviceName))
	ctx, cancel := context.WithTimeout(context.Background(), g.timeOut)
	_, err := g.g.PermissionsApi.DeleteServicePermsUsingDELETE(ctx, serviceName)
	//if err != nil {
	//	g.logger.Errorf(err, "failed to DeleteServicePerms")
	//}
	cancel()
	return err
}

func (g *GuardianClient) RequestKeytab(principals []string, base64Encoded bool) ([]byte, error) {
	ctx, cancel := context.WithTimeout(context.Background(), g.timeOut)
	defer cancel()

	resource, _, err := g.g.UsersApi.GenerateMultipleKeytabUsingPOST(ctx, guardianv2.KeytabPrincipalVo{
		Principals: principals,
	})
	//g.logger.Infof("%v", resource)
	if err != nil {
		return nil, err
	}

	if base64Encoded {
		resource = []byte(base64.StdEncoding.EncodeToString(resource))
	}
	return resource, err
}

func (g *GuardianClient) GetGuardianAPIClient() *guardianv2.APIClient {
	return g.g
}

//func GetGuardianClusterIPInCluster() (string, error) {
//	client, err := kube.GetKubeHelper().NewKubeService()
//	if err != nil {
//		return "", err
//	}
//	svcs, err := client.DiscoverServices("tdcsys", "k8s-app=guardian-server")
//	if err != nil {
//		return "", err
//	}
//
//	for _, svc := range svcs {
//		if svc.Spec.Type == coreV1.ServiceTypeNodePort {
//			guardianAddress := fmt.Sprintf("http://%s:8380", svc.Spec.ClusterIP)
//			return guardianAddress, nil
//		}
//	}
//	return "", fmt.Errorf("No guardian service found by label k8s-app=guardian-server in tdcsys ")
//}

// InitGuardianV2Client generate guardian client
func InitGuardianV2Client(host string) (*GuardianClient, error) {
	C := GuardianClient{
		//logger:  logger.Logger("guardianV2-client"),
		timeOut: time.Duration(5) * time.Second,
	}
	defaultHeader := map[string]string{
		"Content-Type": "application/json",
		"accept":       "application/json",
	}
	rc := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})

	url := host
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		url = "https://" + url
	}
	fmt.Println("url:" + url)
	config := &guardianv2.Configuration{
		// todo get guardian address in cluster by kubernetes client
		BasePath:      url,
		DefaultHeader: defaultHeader,
		HTTPClient:    rc.GetClient(),
		UserAgent:     "tdcsys/compoennt/ngwalm",
	}
	C.g = guardianv2.NewAPIClient(config)
	return &C, nil
}
