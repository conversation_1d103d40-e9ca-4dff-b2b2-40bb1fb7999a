/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type Url struct {
	Authority   string       `json:"authority,omitempty"`
	Content     *interface{} `json:"content,omitempty"`
	DefaultPort int32        `json:"defaultPort,omitempty"`
	File        string       `json:"file,omitempty"`
	Host        string       `json:"host,omitempty"`
	Path        string       `json:"path,omitempty"`
	Port        int32        `json:"port,omitempty"`
	Protocol    string       `json:"protocol,omitempty"`
	Query       string       `json:"query,omitempty"`
	Ref         string       `json:"ref,omitempty"`
	UserInfo    string       `json:"userInfo,omitempty"`
}
