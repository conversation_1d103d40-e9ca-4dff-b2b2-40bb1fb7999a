/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type TrustRelationVo struct {
	SourceDomain *DomainVo `json:"sourceDomain,omitempty"`
	TargetDomain *DomainVo `json:"targetDomain"`
	// Trust relation type: outgoing, incoming or two-way
	Type_ string `json:"type"`
}
