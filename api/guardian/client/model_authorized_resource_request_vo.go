/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type AuthorizedResourceRequestVo struct {
	Action      string       `json:"action,omitempty"`
	Inheritance bool         `json:"inheritance,omitempty"`
	PrincipalVo *PrincipalVo `json:"principalVo,omitempty"`
	ResourceVo  *ResourceVo  `json:"resourceVo,omitempty"`
	SearchValue string       `json:"searchValue,omitempty"`
}
