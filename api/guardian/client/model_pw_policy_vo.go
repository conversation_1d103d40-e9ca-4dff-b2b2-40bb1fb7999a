/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

// Guardian global password policy view object
type PwPolicyVo struct {
	// Whether to allow user change the password.
	PwdAllowUserChange bool `json:"pwdAllowUserChange,omitempty"`
	// The maximum number of seconds before a password is due to expire that expiration warning messages will be returned to an authenticating user
	PwdExpireWarning int32 `json:"pwdExpireWarning,omitempty"`
	// The number of seconds after which the password failures are purged from the failure counter
	PwdFailureCountInterval int32 `json:"pwdFailureCountInterval,omitempty"`
	// The number of times an expired password can be used to authenticate.
	PwdGraceAuthNLimit int32 `json:"pwdGraceAuthNLimit,omitempty"`
	// The number of passwords we keep in the password history
	PwdInHistory int32 `json:"pwdInHistory,omitempty"`
	// The duration of passwords we keep in the password history
	PwdInHistoryDuration int32 `json:"pwdInHistoryDuration,omitempty"`
	// The delay in seconds we wait before allowing a new attempt when the password hs been locked
	PwdLockoutDuration int32 `json:"pwdLockoutDuration,omitempty"`
	// The number of seconds after which a modified password will expire
	PwdMaxAge int32 `json:"pwdMaxAge,omitempty"`
	// The maximum number of failure we accept before locking the password
	PwdMaxFailure int32 `json:"pwdMaxFailure,omitempty"`
	// The number of seconds that must elapse between modifications to the password.
	PwdMinAge int32 `json:"pwdMinAge,omitempty"`
	// The minimum number of character classes required in a password. The five character classes are lower case, upper case, numbers, punctuation, and whitespace/invisible characters
	PwdMinClasses int32 `json:"pwdMinClasses,omitempty"`
	// The minimum length of the password
	PwdMinLength int32 `json:"pwdMinLength,omitempty"`
	// Whether password must be reset in case of first login or password expiration
	PwdMustChange bool `json:"pwdMustChange,omitempty"`
	// Whether password string should not contain username
	PwdNoUsername bool `json:"pwdNoUsername,omitempty"`
	// Whether original password must be given when changing password
	PwdSafeModify bool `json:"pwdSafeModify,omitempty"`
}
