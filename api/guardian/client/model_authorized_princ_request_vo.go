/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type AuthorizedPrincRequestVo struct {
	Action        string      `json:"action,omitempty"`
	Inheritance   bool        `json:"inheritance,omitempty"`
	PrincipalType string      `json:"principalType,omitempty"`
	ResourceVo    *ResourceVo `json:"resourceVo,omitempty"`
}
