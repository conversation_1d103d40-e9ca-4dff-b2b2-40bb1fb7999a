/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

// Hadoop service registered with Guardian
type ServiceVo struct {
	// cluster service belong to
	ClusterName string `json:"clusterName,omitempty"`
	// service configs used to connect to the service
	Configs map[string]string `json:"configs,omitempty"`
	// service descriptions
	Description string `json:"description,omitempty"`
	// last timestamp service sends a heartbeat
	LastHeartbeatTimestamp int64 `json:"lastHeartbeatTimestamp,omitempty"`
	// service offline timestamp
	OfflineTimestamp int64 `json:"offlineTimestamp,omitempty"`
	// service hosts registered to guardian
	ServiceHosts []string `json:"serviceHosts,omitempty"`
	// service name (id)
	ServiceName string `json:"serviceName"`
	// service status
	ServiceStatus string `json:"serviceStatus,omitempty"`
	// service type
	ServiceType string `json:"serviceType,omitempty"`
	// service register time (of latest registered service host)
	Timestamp int64 `json:"timestamp,omitempty"`
}
