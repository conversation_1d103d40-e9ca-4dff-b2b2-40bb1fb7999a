/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

// Guardian resource view object representing a resource
type ResourceVo struct {
	// The NodeVo list representing the resource
	DataSource []NodeVo `json:"dataSource"`
	// Service name
	ServiceName string `json:"serviceName"`
	// Service type
	ServiceType string `json:"serviceType,omitempty"`
}
