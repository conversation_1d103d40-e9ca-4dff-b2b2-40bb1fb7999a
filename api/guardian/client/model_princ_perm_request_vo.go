/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type PrincPermRequestVo struct {
	Inheritance bool         `json:"inheritance,omitempty"`
	PageNumber  int32        `json:"pageNumber,omitempty"`
	PageSize    int32        `json:"pageSize,omitempty"`
	PrincipalVo *PrincipalVo `json:"principalVo,omitempty"`
	ResourceVo  *ResourceVo  `json:"resourceVo,omitempty"`
	SearchValue string       `json:"searchValue,omitempty"`
	Subtree     bool         `json:"subtree,omitempty"`
}
