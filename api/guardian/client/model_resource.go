/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

import (
	"os"
)

type Resource struct {
	Description string       `json:"description,omitempty"`
	File        **os.File    `json:"file,omitempty"`
	Filename    string       `json:"filename,omitempty"`
	InputStream *InputStream `json:"inputStream,omitempty"`
	Open        bool         `json:"open,omitempty"`
	Readable    bool         `json:"readable,omitempty"`
	Uri         *Uri         `json:"uri,omitempty"`
	Url         *Url         `json:"url,omitempty"`
}
