/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type ResourceOwnerEntry struct {
	DataNode     *NodeVo  `json:"dataNode,omitempty"`
	Datasource   []NodeVo `json:"datasource,omitempty"`
	OwnerName    string   `json:"ownerName,omitempty"`
	OwnerType    string   `json:"ownerType,omitempty"`
	ResourceType string   `json:"resourceType,omitempty"`
}
