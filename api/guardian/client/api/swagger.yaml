openapi: 3.0.1
info:
  title: Guardian
  description: Guardian Swagger API /API/V2
  version: /api/v2
servers:
- url: //*************/
tags:
- name: abac
  description: ABAC API
- name: access tokens
  description: Access Token API
- name: admin
  description: Admin API
- name: audit
  description: Audit API
- name: changes
  description: Change API
- name: configs
  description: Configuration API
- name: groups
  description: Group API
- name: health
  description: Health Status API
- name: labels
  description: Label API
- name: login
  description: Authentication API
- name: permissions
  description: Permission API
- name: quotas
  description: Quota API
- name: resources
  description: Resource API
- name: roles
  description: Role API
- name: security context
  description: Security Context API
- name: services
  description: Service API
- name: trust relation
  description: Trust Relation API
- name: users
  description: User API
paths:
  /api/v2/abac/check-policy:
    post:
      tags:
      - abac
      summary: Check a policy
      operationId: getPolicyResultUsingPOST_1
      requestBody:
        description: policyCheckVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PolicyCheckVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: string
                enum:
                - ALLOW
                - DENY
                - NOT_APPLY
                - DENY_DEPENDS
                x-content-type: '*/*'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: policyCheckVo
  /api/v2/abac/condition-key-op:
    get:
      tags:
      - abac
      summary: List condition key and operators
      operationId: getConditionKeyOpsUsingGET_1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConditionKeyOpVo'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/abac/condition-qualifier:
    get:
      tags:
      - abac
      summary: List condition qualifiers
      operationId: getConditionQualifierVoUsingGET_1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConditionQualifierVo'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/abac/policy:
    get:
      tags:
      - abac
      summary: List policies
      operationId: getPoliciesUsingGET_1
      parameters:
      - name: serviceId
        in: query
        description: serviceId
        required: false
        style: form
        explode: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PolicyVo'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    post:
      tags:
      - abac
      summary: Create a policy
      operationId: addPolicyUsingPOST_1
      requestBody:
        description: policyVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PolicyVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/PolicyVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: policyVo
  /api/v2/abac/policy-update-time/{serviceName}:
    get:
      tags:
      - abac
      summary: Get the policy update time for a service
      operationId: getPolicyUpdateTimeUsingGET_1
      parameters:
      - name: serviceName
        in: path
        description: serviceName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Timestamp'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    put:
      tags:
      - abac
      summary: Refresh the policy update time for a service
      operationId: refreshPolicyUpdateTimeUsingPUT_1
      parameters:
      - name: serviceName
        in: path
        description: serviceName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Timestamp'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/abac/policy/{id}:
    get:
      tags:
      - abac
      summary: Get policy by id
      operationId: getPolicyByIdUsingGET_1
      parameters:
      - name: id
        in: path
        description: id
        required: true
        style: simple
        explode: false
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/PolicyVo'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    put:
      tags:
      - abac
      summary: Update policy by id
      operationId: updatePolicyByIdUsingPUT_1
      parameters:
      - name: id
        in: path
        description: id
        required: true
        style: simple
        explode: false
        schema:
          type: integer
          format: int64
      requestBody:
        description: policyVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PolicyVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/PolicyVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: policyVo
    delete:
      tags:
      - abac
      summary: Delete policy by id
      operationId: deletePolicyByIdUsingDELETE_1
      parameters:
      - name: id
        in: path
        description: id
        required: true
        style: simple
        explode: false
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/abac/statement:
    get:
      tags:
      - abac
      summary: List statements
      operationId: getStatementsUsingGET_1
      parameters:
      - name: serviceId
        in: query
        description: serviceId
        required: false
        style: form
        explode: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/StatementVo'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/access-tokens:
    get:
      tags:
      - access tokens
      summary: Get access tokens by owner
      operationId: getAccessTokenByOwnerUsingGET_1
      parameters:
      - name: getAll
        in: query
        description: getAll
        required: false
        style: form
        explode: true
        schema:
          type: boolean
          default: false
      - name: owner
        in: query
        description: owner
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: pageNumber
        in: query
        description: pageNumber
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int32
          default: -1
      - name: pageSize
        in: query
        description: pageSize
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int32
          default: -1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    put:
      tags:
      - access tokens
      summary: Update access token by id
      operationId: updateAccessTokenByIdUsingPUT_1
      requestBody:
        description: accessTokenVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccessTokenVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AccessTokenVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: accessTokenVo
    post:
      tags:
      - access tokens
      summary: Create an access token
      operationId: createAccessTokenUsingPOST_1
      requestBody:
        description: accessTokenVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccessTokenVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AccessTokenVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: accessTokenVo
  /api/v2/access-tokens/{id}:
    get:
      tags:
      - access tokens
      summary: Get access token by id
      operationId: getAccessTokenByIdUsingGET_1
      parameters:
      - name: id
        in: path
        description: id
        required: true
        style: simple
        explode: false
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AccessTokenVo'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    delete:
      tags:
      - access tokens
      summary: Delete access token by id
      operationId: deleteAccessTokenByIdUsingDELETE_1
      parameters:
      - name: id
        in: path
        description: id
        required: true
        style: simple
        explode: false
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/admin/admin-roles:
    get:
      tags:
      - admin
      summary: Get admin roles
      operationId: getAdminRolesUsingGET
      parameters:
      - name: pageNumber
        in: query
        description: pageNumber
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int32
          default: -1
      - name: pageSize
        in: query
        description: pageSize
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int32
          default: -1
      - name: searchVal
        in: query
        description: searchVal
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: sorting
        in: query
        description: sorting
        required: false
        style: form
        explode: true
        schema:
          type: boolean
          default: false
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    put:
      tags:
      - admin
      summary: Update admin role
      operationId: updateAdminRoleUsingPUT_1
      requestBody:
        description: adminRoleVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminRoleVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AdminRoleVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: adminRoleVo
    post:
      tags:
      - admin
      summary: Add admin role
      operationId: addAdminRoleUsingPOST_1
      requestBody:
        description: adminRoleVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminRoleVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AdminRoleVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: adminRoleVo
  /api/v2/admin/admin-roles/assign:
    put:
      tags:
      - admin
      summary: Assign admin role to user
      operationId: assignUsingPUT_2
      requestBody:
        description: userAdminRoleVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserAdminRoleVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: userAdminRoleVo
  /api/v2/admin/admin-roles/deassign:
    put:
      tags:
      - admin
      summary: Deassign admin role from user
      operationId: deassignUsingPUT_2
      requestBody:
        description: userAdminRoleVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserAdminRoleVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: userAdminRoleVo
  /api/v2/admin/admin-roles/grant:
    put:
      tags:
      - admin
      summary: Grant admin role permission
      operationId: grantPermUsingPUT
      requestBody:
        description: adminRolePermVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminRolePermVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: adminRolePermVo
  /api/v2/admin/admin-roles/has-role:
    get:
      tags:
      - admin
      summary: Check whether the user has all/any of the admin roles
      operationId: hasRoleUsingGET_1
      parameters:
      - name: adminRole
        in: query
        description: adminRole
        required: true
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: checkAny
        in: query
        description: checkAny
        required: false
        style: form
        explode: true
        schema:
          type: boolean
          default: false
      - name: username
        in: query
        description: username
        required: true
        style: form
        explode: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/CheckResultVo'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/admin/admin-roles/revoke:
    put:
      tags:
      - admin
      summary: Revoke admin role permission
      operationId: revokePermUsingPUT
      requestBody:
        description: adminRolePermVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminRolePermVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: adminRolePermVo
  /api/v2/admin/admin-roles/{adminRoleName}:
    get:
      tags:
      - admin
      summary: Get admin role
      operationId: getAdminRoleUsingGET_1
      parameters:
      - name: adminRoleName
        in: path
        description: adminRoleName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AdminRoleVo'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    delete:
      tags:
      - admin
      summary: Delete admin role
      operationId: deleteAdminRoleUsingDELETE_1
      parameters:
      - name: adminRoleName
        in: path
        description: adminRoleName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/admin/admin-roles/{adminRoleName}/assigned-users:
    get:
      tags:
      - admin
      summary: Get admin role assigned users
      operationId: getAssignedUsersUsingGET_1
      parameters:
      - name: adminRoleName
        in: path
        description: adminRoleName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserVo'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/admin/admin-roles/{adminRoleName}/perms:
    get:
      tags:
      - admin
      summary: Get admin role permissions
      operationId: getAdminRolePermsUsingGET
      parameters:
      - name: adminRoleName
        in: path
        description: adminRoleName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminPermVo'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/admin/pwd-policy:
    get:
      tags:
      - admin
      summary: Return password policy
      operationId: getPasswordPolicyUsingGET_1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/PwPolicyVo'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    put:
      tags:
      - admin
      summary: Update the password policy
      operationId: updatePasswordPolicyUsingPUT_1
      requestBody:
        description: pwdPolicyVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PwPolicyVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/PwPolicyVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: pwdPolicyVo
  /api/v2/admin/users/{username}/admin-roles:
    get:
      tags:
      - admin
      summary: Get user assigned admin roles
      operationId: getAssignedAdminRolesUsingGET
      parameters:
      - name: username
        in: path
        description: username
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminRoleVo'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/audit:
    get:
      tags:
      - audit
      summary: Get audit records
      operationId: getAuditRecordsUsingGET_1
      parameters:
      - name: field
        in: query
        description: field
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: from
        in: query
        description: from
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int64
          default: 0
      - name: level
        in: query
        description: level
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: operation
        in: query
        description: operation
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: order
        in: query
        description: order
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: pageNumber
        in: query
        description: pageNumber
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int32
          default: 0
      - name: pageSize
        in: query
        description: pageSize
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int32
          default: 0
      - name: requestClass
        in: query
        description: requestClass
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: to
        in: query
        description: to
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int64
          default: 0
      - name: user
        in: query
        description: user
        required: false
        style: form
        explode: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/SearchResult«AuditRecord»'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/audit/audit-level:
    get:
      tags:
      - audit
      summary: Get audit level
      operationId: getActiveAuditLevelUsingGET_1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  type: string
                  enum:
                  - READ
                  - ADD
                  - UPDATE
                  - DELETE
                  - CHECK
                  - LOGIN
                  - DOWNLOAD
                  - ALL
                  - NONE
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    put:
      tags:
      - audit
      summary: Change audit level
      operationId: changeAuditLevelUsingPUT_1
      requestBody:
        description: entity
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangeAuditLevelRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: entity
  /api/v2/auth-info:
    get:
      tags:
      - login
      summary: Return the authentication information
      operationId: getAuthInfoUsingGET_1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                additionalProperties:
                  type: array
                  items:
                    type: string
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/authenticate:
    post:
      tags:
      - login
      summary: Authenticate with Guardian account
      operationId: authenticateUsingPOST
      requestBody:
        description: loginRequestVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/SessionVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: loginRequestVo
  /api/v2/changes:
    get:
      tags:
      - changes
      summary: Get change list
      description: Internal Use
      operationId: getChangListUsingGET_2
      parameters:
      - name: reqVersion
        in: query
        description: reqVersion
        required: true
        style: form
        explode: true
        schema:
          type: integer
          format: int64
      - name: startTime
        in: query
        description: startTime
        required: true
        style: form
        explode: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ChangeList'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/changes/invalidate:
    post:
      tags:
      - changes
      summary: Invalidate all client caches
      description: Login is needed
      operationId: invalidateChangeUsingPOST_1
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/changes/invalidate/{component}:
    post:
      tags:
      - changes
      summary: Invalidate all client caches of component
      description: Login is needed
      operationId: invalidateComponentUsingPOST_1
      parameters:
      - name: component
        in: path
        description: component
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/changes/start-time:
    get:
      tags:
      - changes
      summary: Get the start time of Change keeper
      description: Internal Use
      operationId: getChangeStartTimeUsingGET_1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: integer
                format: int64
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/changes/{component}:
    get:
      tags:
      - changes
      summary: Get component change list
      description: Internal Use
      operationId: getChangListUsingGET_1
      parameters:
      - name: component
        in: path
        description: component
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: reqVersion
        in: query
        description: reqVersion
        required: true
        style: form
        explode: true
        schema:
          type: integer
          format: int64
      - name: startTime
        in: query
        description: startTime
        required: true
        style: form
        explode: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ChangeList'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/configs:
    get:
      tags:
      - configs
      summary: Get Guardian configuration properties
      operationId: listConfigsUsingGET
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/groups:
    get:
      tags:
      - groups
      summary: Get groups
      operationId: getGroupsUsingGET
      parameters:
      - name: pageNumber
        in: query
        description: pageNumber
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int32
          default: -1
      - name: pageSize
        in: query
        description: pageSize
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int32
          default: -1
      - name: searchVal
        in: query
        description: searchVal
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: sorting
        in: query
        description: sorting
        required: false
        style: form
        explode: true
        schema:
          type: boolean
          default: false
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    post:
      tags:
      - groups
      summary: Add group
      operationId: addGroupUsingPOST_1
      requestBody:
        description: groupVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/GroupVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: groupVo
  /api/v2/groups/assign:
    put:
      tags:
      - groups
      summary: Assign group to user or group
      operationId: assignGroupUsingPUT
      requestBody:
        description: princGroupVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrincGroupVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: princGroupVo
  /api/v2/groups/deassign:
    put:
      tags:
      - groups
      summary: Deassign group from user or group
      operationId: deassignGroupUsingPUT
      requestBody:
        description: princGroupVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrincGroupVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: princGroupVo
  /api/v2/groups/{groupName}:
    get:
      tags:
      - groups
      summary: Get group
      operationId: getGroupUsingGET_1
      parameters:
      - name: groupName
        in: path
        description: groupName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/GroupVo'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    put:
      tags:
      - groups
      summary: Update group
      operationId: updateGroupUsingPUT_1
      parameters:
      - name: groupName
        in: path
        description: groupName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      requestBody:
        description: groupVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/GroupVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: groupVo
    delete:
      tags:
      - groups
      summary: Delete group
      operationId: deleteGroupUsingDELETE_1
      parameters:
      - name: groupName
        in: path
        description: groupName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/groups/{groupName}/labels:
    get:
      tags:
      - groups
      summary: Get group labels
      operationId: getGroupLabelsUsingGET_1
      parameters:
      - name: groupName
        in: path
        description: groupName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LabelVo'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    delete:
      tags:
      - groups
      summary: Clear group labels
      operationId: untagGroupUsingDELETE_3
      parameters:
      - name: groupName
        in: path
        description: groupName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/groups/{groupName}/labels/{label}:
    put:
      tags:
      - groups
      summary: Tag group
      operationId: tagGroupUsingPUT
      parameters:
      - name: groupName
        in: path
        description: groupName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: label
        in: path
        description: label
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    delete:
      tags:
      - groups
      summary: Untag group
      operationId: untagGroupUsingDELETE_2
      parameters:
      - name: groupName
        in: path
        description: groupName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: label
        in: path
        description: label
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/groups/{groupName}/owner/{username}:
    put:
      tags:
      - groups
      summary: Add owner to group
      operationId: addGroupOwnerUsingPUT
      parameters:
      - name: groupName
        in: path
        description: groupName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: username
        in: path
        description: username
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    delete:
      tags:
      - groups
      summary: Delete owner from group
      operationId: deleteGroupOwnerUsingDELETE
      parameters:
      - name: groupName
        in: path
        description: groupName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: username
        in: path
        description: username
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/groups/{groupName}/owners:
    get:
      tags:
      - groups
      summary: Get group owners
      operationId: getGroupOwnersUsingGET
      parameters:
      - name: groupName
        in: path
        description: groupName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: inheritance
        in: query
        description: inheritance
        required: false
        style: form
        explode: true
        schema:
          type: boolean
          default: false
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserVo'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/health:
    get:
      tags:
      - health
      summary: Check health status
      operationId: getStatusUsingGET_1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/HealthStatus'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/labels:
    get:
      tags:
      - labels
      summary: Return labels
      description: login is needed
      operationId: getUserLabelsUsingGET_2
      parameters:
      - name: principal
        in: query
        description: principal
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: principalType
        in: query
        description: principalType
        required: false
        style: form
        explode: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LabelVo'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    put:
      tags:
      - labels
      summary: Update label by name
      description: login is needed
      operationId: updateLabelUsingPUT_1
      requestBody:
        description: labelVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LabelVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/LabelVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: labelVo
    post:
      tags:
      - labels
      summary: Create a label
      description: login is needed
      operationId: createLabelUsingPOST_1
      requestBody:
        description: labelVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LabelVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/LabelVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: labelVo
  /api/v2/labels/{label}:
    get:
      tags:
      - labels
      summary: Get label by name
      description: login is needed
      operationId: getLabelUsingGET_1
      parameters:
      - name: label
        in: path
        description: label
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/LabelVo'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    delete:
      tags:
      - labels
      summary: Delete label by name
      description: login is needed
      operationId: deleteLabelUsingDELETE_1
      parameters:
      - name: label
        in: path
        description: label
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/labels/{label}/principals:
    get:
      tags:
      - labels
      summary: Return principals by label
      description: login is needed
      operationId: getLabelPrincipalsUsingGET_1
      parameters:
      - name: label
        in: path
        description: label
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: principalType
        in: query
        description: principalType
        required: false
        style: form
        explode: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PrincipalVo'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    delete:
      tags:
      - labels
      summary: Remove all principals from label
      description: login is needed
      operationId: clearLabelPrincipalsUsingDELETE_1
      parameters:
      - name: label
        in: path
        description: label
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: principalType
        in: query
        description: principalType
        required: false
        style: form
        explode: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/labels/{label}/tag:
    post:
      tags:
      - labels
      summary: Tag a principal with a label
      description: login is needed
      operationId: tagPrincipalUsingPOST_1
      parameters:
      - name: label
        in: path
        description: label
        required: true
        style: simple
        explode: false
        schema:
          type: string
      requestBody:
        description: principalVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrincipalVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: principalVo
  /api/v2/labels/{label}/untag:
    post:
      tags:
      - labels
      summary: Untag a principal from a label
      description: login is needed
      operationId: untagPrincipalUsingPOST_1
      parameters:
      - name: label
        in: path
        description: label
        required: true
        style: simple
        explode: false
        schema:
          type: string
      requestBody:
        description: principalVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrincipalVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: principalVo
  /api/v2/login:
    post:
      tags:
      - login
      summary: Login Guardian service
      operationId: loginUsingPOST
      requestBody:
        description: loginRequestVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/SessionVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: loginRequestVo
  /api/v2/logout:
    post:
      tags:
      - login
      summary: Logout from Guardian Service
      operationId: logoutUsingPOST_1
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/perms:
    post:
      tags:
      - permissions
      summary: Add permission
      operationId: addPermUsingPOST
      requestBody:
        description: permVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PermVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/PermVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: permVo
  /api/v2/perms/authorized-data-nodes:
    post:
      tags:
      - permissions
      summary: Get authorized data nodes
      operationId: getAuthorizedDataNodesUsingPOST
      requestBody:
        description: authorizedResourceRequestVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedResourceRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/NodeVo'
                x-content-type: '*/*'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: authorizedResourceRequestVo
  /api/v2/perms/authorized-data-sources:
    post:
      tags:
      - permissions
      summary: Get authorized data sources
      operationId: getAuthorizedDataSourcesUsingPOST
      requestBody:
        description: authorizedResourceRequestVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedResourceRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/NodeVo'
                x-content-type: '*/*'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: authorizedResourceRequestVo
  /api/v2/perms/authorized-perms:
    post:
      tags:
      - permissions
      summary: Get authorized permissions based on principal
      operationId: getAuthorizedPermsUsingPOST
      requestBody:
        description: princPermRequestVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrincPermRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                x-content-type: '*/*'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: princPermRequestVo
  /api/v2/perms/authorized-principals:
    post:
      tags:
      - permissions
      summary: Get all principals to which a permission is granted
      operationId: getAuthorizedPrincipalsUsingPOST
      requestBody:
        description: authorizedPrincRequestVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedPrincRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PrincipalVo'
                x-content-type: '*/*'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: authorizedPrincRequestVo
  /api/v2/perms/check:
    put:
      tags:
      - permissions
      summary: Check permission
      operationId: checkUsingPUT_1
      requestBody:
        description: checkPrincPermVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CheckPrincPermVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/CheckResultVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: checkPrincPermVo
  /api/v2/perms/delete:
    post:
      tags:
      - permissions
      summary: Delete permission
      operationId: deletePermUsingPOST
      requestBody:
        description: permVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PermVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: permVo
  /api/v2/perms/grant:
    put:
      tags:
      - permissions
      summary: Grant permission
      operationId: grantPermUsingPUT_1
      requestBody:
        description: princPermVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrincPermVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: princPermVo
  /api/v2/perms/grant-on-resource:
    put:
      tags:
      - permissions
      summary: Grant permission on one resource to specified user
      operationId: grantPermsOnResourceUsingPUT
      requestBody:
        description: princPermOnResourceVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrincPermOnResourceVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: princPermOnResourceVo
  /api/v2/perms/principal-perms:
    post:
      tags:
      - permissions
      summary: Get principal permission relations based on resource
      operationId: getPrincipalPermsUsingPOST
      requestBody:
        description: resourcePermRequestVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourcePermRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PrincPermVo'
                x-content-type: '*/*'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: resourcePermRequestVo
  /api/v2/perms/resource-perm-actions:
    post:
      tags:
      - permissions
      summary: Get perm actions of a resource
      operationId: getResourcePermActionsUsingPOST
      requestBody:
        description: resourceVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  type: string
                x-content-type: '*/*'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: resourceVo
  /api/v2/perms/resource-perms:
    post:
      tags:
      - permissions
      summary: List permissions
      description: login is needed
      operationId: listPermissionsUsingPOST
      requestBody:
        description: resourceRequestVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PermVo'
                x-content-type: '*/*'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: resourceRequestVo
  /api/v2/perms/resource-perms/delete:
    post:
      tags:
      - permissions
      summary: Delete resource permission
      operationId: deleteResourcePermsUsingPOST
      requestBody:
        description: resourceVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: resourceVo
  /api/v2/perms/resources:
    post:
      tags:
      - permissions
      summary: Get perm-related resources
      operationId: getPermRelatedResourcesUsingPOST
      requestBody:
        description: resourceSearchRequestVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceSearchRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                x-content-type: '*/*'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: resourceSearchRequestVo
  /api/v2/perms/revoke:
    put:
      tags:
      - permissions
      summary: Revoke permission
      operationId: revokePermUsingPUT_1
      requestBody:
        description: princPermVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrincPermVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: princPermVo
  /api/v2/perms/revoke-on-resource:
    put:
      tags:
      - permissions
      summary: Revoke permission on one resource from specified user
      operationId: revokePermsOnResourceUsingPUT
      requestBody:
        description: princPermOnResourceVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrincPermOnResourceVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: princPermOnResourceVo
  /api/v2/perms/service-perms:
    delete:
      tags:
      - permissions
      summary: Delete service permission
      operationId: deleteServicePermsUsingDELETE
      parameters:
      - name: serviceName
        in: query
        description: serviceName
        required: true
        style: form
        explode: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/quotas:
    put:
      tags:
      - quotas
      summary: Update quota
      operationId: updateQuotaUsingPUT_1
      requestBody:
        description: quotaVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuotaVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: quotaVo
    post:
      tags:
      - quotas
      summary: Add quota
      operationId: addQuotaUsingPOST_1
      requestBody:
        description: quotaVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuotaVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: quotaVo
  /api/v2/quotas/delete:
    post:
      tags:
      - quotas
      summary: Delete quota
      operationId: deleteQuotaUsingPOST
      requestBody:
        description: resourceRequestVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: resourceRequestVo
  /api/v2/quotas/read:
    post:
      tags:
      - quotas
      summary: Read quota
      operationId: readQuotaUsingPOST
      requestBody:
        description: resourceVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/QuotaVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: resourceVo
  /api/v2/quotas/search:
    post:
      tags:
      - quotas
      summary: Search quotas
      operationId: searchQuotasUsingPOST
      requestBody:
        description: resourceSearchRequestVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceSearchRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                x-content-type: '*/*'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: resourceSearchRequestVo
  /api/v2/resources/child-nodes:
    post:
      tags:
      - resources
      summary: Get child nodes of a resource
      operationId: getChildNodesUsingPOST
      requestBody:
        description: resourceVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/NodeVo'
                x-content-type: '*/*'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: resourceVo
  /api/v2/resources/delete:
    post:
      tags:
      - resources
      summary: Delete resource
      operationId: deleteResourceUsingPOST
      requestBody:
        description: resourceRequestVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: resourceRequestVo
  /api/v2/resources/descendants:
    post:
      tags:
      - resources
      summary: Get descendant resources of a resource
      operationId: getDescendantResourcesUsingPOST
      requestBody:
        description: resourceVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ResourceVo'
                x-content-type: '*/*'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: resourceVo
  /api/v2/resources/rename:
    put:
      tags:
      - resources
      summary: Rename one node of data source
      operationId: renameNodeUsingPUT
      requestBody:
        description: renameNodeVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RenameNodeRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: renameNodeVo
  /api/v2/resources/service-resources:
    delete:
      tags:
      - resources
      summary: Delete service resources
      operationId: deleteServiceResourcesUsingDELETE
      parameters:
      - name: serviceName
        in: query
        description: serviceName
        required: true
        style: form
        explode: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/roles:
    get:
      tags:
      - roles
      summary: Get roles
      operationId: getRolesUsingGET_1
      parameters:
      - name: pageNumber
        in: query
        description: pageNumber
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int32
          default: -1
      - name: pageSize
        in: query
        description: pageSize
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int32
          default: -1
      - name: searchVal
        in: query
        description: searchVal
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: sorting
        in: query
        description: sorting
        required: false
        style: form
        explode: true
        schema:
          type: boolean
          default: false
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    post:
      tags:
      - roles
      summary: Add role
      operationId: addRoleUsingPOST_1
      requestBody:
        description: roleVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RoleVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: roleVo
  /api/v2/roles/assign-group:
    put:
      tags:
      - roles
      summary: Assign role to group
      operationId: assignRoleUsingPUT
      requestBody:
        description: groupRoleVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupRoleVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: groupRoleVo
  /api/v2/roles/assign-user:
    put:
      tags:
      - roles
      summary: Assign role to user
      operationId: assignRoleUsingPUT_1
      requestBody:
        description: userRoleVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRoleVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: userRoleVo
  /api/v2/roles/deassign-group:
    put:
      tags:
      - roles
      summary: Assign role from group
      operationId: deassignRoleUsingPUT
      requestBody:
        description: groupRoleVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupRoleVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: groupRoleVo
  /api/v2/roles/deassign-user:
    put:
      tags:
      - roles
      summary: Assign role from user
      operationId: deassignRoleUsingPUT_1
      requestBody:
        description: userRoleVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRoleVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: userRoleVo
  /api/v2/roles/{roleName}:
    get:
      tags:
      - roles
      summary: Get role
      operationId: getRoleUsingGET_1
      parameters:
      - name: roleName
        in: path
        description: roleName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RoleVo'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    put:
      tags:
      - roles
      summary: Update role
      operationId: updateRoleUsingPUT_1
      parameters:
      - name: roleName
        in: path
        description: roleName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      requestBody:
        description: roleVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RoleVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: roleVo
    delete:
      tags:
      - roles
      summary: Delete role
      operationId: deleteRoleUsingDELETE_1
      parameters:
      - name: roleName
        in: path
        description: roleName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/roles/{roleName}/labels:
    get:
      tags:
      - roles
      summary: Get labels of role
      operationId: getRoleLabelsUsingGET_1
      parameters:
      - name: roleName
        in: path
        description: roleName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LabelVo'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    delete:
      tags:
      - roles
      summary: Clear all role labels
      operationId: untagRoleUsingDELETE_3
      parameters:
      - name: roleName
        in: path
        description: roleName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/roles/{roleName}/labels/{label}:
    put:
      tags:
      - roles
      summary: Tag role
      operationId: tagRoleUsingPUT
      parameters:
      - name: roleName
        in: path
        description: roleName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      requestBody:
        description: labelVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LabelVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: labelVo
    delete:
      tags:
      - roles
      summary: Untag role
      operationId: untagRoleUsingDELETE_2
      parameters:
      - name: label
        in: path
        description: label
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: roleName
        in: path
        description: roleName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/security-context/subject:
    get:
      tags:
      - security context
      summary: Get subject for user
      operationId: getSubjectUsingGET_1
      parameters:
      - name: user
        in: query
        description: user
        required: false
        style: form
        explode: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/SubjectVo'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/services:
    get:
      tags:
      - services
      summary: List services
      operationId: listServicesUsingGET_1
      parameters:
      - name: detail
        in: query
        description: detail
        required: false
        style: form
        explode: true
        schema:
          type: boolean
          default: false
      - name: pageNumber
        in: query
        description: pageNumber
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int32
          default: -1
      - name: pageSize
        in: query
        description: pageSize
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int32
          default: -1
      - name: searchVal
        in: query
        description: searchVal
        required: false
        style: form
        explode: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    delete:
      tags:
      - services
      summary: Delete listed services
      operationId: deleteServicesUsingDELETE
      parameters:
      - name: includeResources
        in: query
        description: includeResources
        required: false
        style: form
        explode: true
        schema:
          type: boolean
          default: false
      - name: serviceName
        in: query
        description: serviceName
        required: true
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/services/register:
    post:
      tags:
      - services
      summary: Register a service
      operationId: registerUsingPOST_1
      requestBody:
        description: serviceVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServiceVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: boolean
                x-content-type: '*/*'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: serviceVo
  /api/v2/services/resources/owners/search:
    post:
      tags:
      - services
      summary: Search resource owners
      operationId: searchResourceOwnersUsingPOST
      requestBody:
        description: resourceOwnerRequestVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceOwnerRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/SearchResult«ResourceOwnerEntry»'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: resourceOwnerRequestVo
  /api/v2/services/resources/search:
    post:
      tags:
      - services
      summary: Search service resources
      operationId: searchResourcesUsingPOST
      requestBody:
        description: resourceLookupVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceLookupVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ResourceEntry'
                x-content-type: '*/*'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: resourceLookupVo
  /api/v2/services/service-mapping:
    get:
      tags:
      - services
      summary: Get service mapping
      operationId: getServiceMappingUsingGET_1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                additionalProperties:
                  type: string
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/services/{serviceName}:
    delete:
      tags:
      - services
      summary: Delete service
      operationId: deleteServiceUsingDELETE
      parameters:
      - name: includeResources
        in: query
        description: includeResources
        required: false
        style: form
        explode: true
        schema:
          type: boolean
          default: false
      - name: serviceName
        in: path
        description: serviceName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/services/{serviceName}/effective-service:
    get:
      tags:
      - services
      summary: Get effective service
      operationId: getEffectiveServiceUsingGET
      parameters:
      - name: serviceName
        in: path
        description: serviceName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ServiceVo'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/services/{serviceName}/inactive-scheduler-nodes:
    get:
      tags:
      - services
      summary: Get inactive scheduler nodes
      operationId: getInactiveSchedulerNodesUsingGET_1
      parameters:
      - name: serviceName
        in: path
        description: serviceName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ResourceEntry'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/services/{serviceName}/scheduler-type:
    get:
      tags:
      - services
      summary: Get scheduler type
      operationId: getSchedulerTypeUsingGET_1
      parameters:
      - name: serviceName
        in: path
        description: serviceName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ResourceEntry'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/services/{yarnName}/queues/refresh:
    post:
      tags:
      - services
      summary: Refresh yarn queues
      operationId: refreshYarnQueuesUsingPOST_1
      parameters:
      - name: yarnName
        in: path
        description: yarnName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/services/{yarnName}/scheduler-xml-confs/{fileName}:
    get:
      tags:
      - services
      summary: Generate yarn scheduler conf in xml format
      operationId: genYarnSchedulerXmlConfUsingGET_1
      parameters:
      - name: fileName
        in: path
        description: fileName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: yarnName
        in: path
        description: yarnName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: string
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/single-logout:
    get:
      tags:
      - login
      summary: Single logout
      operationId: singleLogoutUsingGET_1
      responses:
        "200":
          description: OK
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/trust-relations:
    get:
      tags:
      - trust relation
      summary: List trust relations
      operationId: listTrustRelationsUsingGET_1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TrustRelationVo'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    post:
      tags:
      - trust relation
      summary: Add a trust relation
      operationId: addTrustRelationUsingPOST_1
      requestBody:
        description: trustRelationVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TrustRelationVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: trustRelationVo
  /api/v2/trust-relations/delete:
    post:
      tags:
      - trust relation
      summary: Delete a trust relation
      operationId: deleteTrustRelationV2UsingPOST
      requestBody:
        description: trustRelationVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TrustRelationVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: trustRelationVo
  /api/v2/trust-relations/domains:
    put:
      tags:
      - trust relation
      summary: update a domain
      operationId: updateDomainUsingPUT_1
      requestBody:
        description: domainVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DomainVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: domainVo
    post:
      tags:
      - trust relation
      summary: Add a domain
      operationId: addDomainUsingPOST_1
      requestBody:
        description: domainVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DomainVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: domainVo
  /api/v2/trust-relations/domains/{domainName}:
    delete:
      tags:
      - trust relation
      summary: Delete a domain
      operationId: deleteDomainUsingDELETE_1
      parameters:
      - name: domainName
        in: path
        description: domainName
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/trust-relations/local-domain:
    get:
      tags:
      - trust relation
      summary: Get local domain info
      operationId: getLocalDomainInfoUsingGET_1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/DomainVo'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/users:
    get:
      tags:
      - users
      summary: Get users
      operationId: getUsersUsingGET
      parameters:
      - name: pageNumber
        in: query
        description: pageNumber
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int32
          default: -1
      - name: pageSize
        in: query
        description: pageSize
        required: false
        style: form
        explode: true
        schema:
          type: integer
          format: int32
          default: -1
      - name: searchVal
        in: query
        description: searchVal
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: sorting
        in: query
        description: sorting
        required: false
        style: form
        explode: true
        schema:
          type: boolean
          default: false
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    post:
      tags:
      - users
      summary: Add user
      operationId: addUserUsingPOST
      requestBody:
        description: userVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/UserVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: userVo
  /api/v2/users/keytab:
    get:
      tags:
      - users
      summary: Generate user's keytab
      operationId: generateKeytabUsingGET
      parameters:
      - name: username
        in: query
        description: username
        required: true
        style: form
        explode: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Resource'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/users/multiple-keytab:
    post:
      tags:
      - users
      summary: Generate multiple users' keytab
      operationId: generateMultipleKeytabUsingPOST
      requestBody:
        description: keytabPrincipalVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KeytabPrincipalVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Resource'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: keytabPrincipalVo
  /api/v2/users/principals:
    get:
      tags:
      - users
      summary: Get krb5 principals
      operationId: getKrb5PrincipalsUsingGET
      parameters:
      - name: prefix
        in: query
        description: prefix
        required: false
        style: form
        explode: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  type: string
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/users/suggested-password:
    get:
      tags:
      - users
      summary: Generate user password
      operationId: generatePasswordUsingGET_1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  type: string
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/users/{username}:
    get:
      tags:
      - users
      summary: Get user
      operationId: getUserUsingGET_1
      parameters:
      - name: username
        in: path
        description: username
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/UserVo'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    put:
      tags:
      - users
      summary: Update user
      operationId: updateUserUsingPUT_1
      parameters:
      - name: username
        in: path
        description: username
        required: true
        style: simple
        explode: false
        schema:
          type: string
      requestBody:
        description: userVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/UserVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: userVo
    delete:
      tags:
      - users
      summary: Delete user
      operationId: deleteUserUsingDELETE_1
      parameters:
      - name: username
        in: path
        description: username
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/users/{username}/labels:
    get:
      tags:
      - users
      summary: Get user labels
      operationId: getUserLabelsUsingGET_3
      parameters:
      - name: username
        in: path
        description: username
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LabelVo'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    delete:
      tags:
      - users
      summary: Clear user tags
      operationId: untagUserUsingDELETE_3
      parameters:
      - name: username
        in: path
        description: username
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/users/{username}/labels/{label}:
    put:
      tags:
      - users
      summary: Tag user
      operationId: tagUserUsingPUT
      parameters:
      - name: label
        in: path
        description: label
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: username
        in: path
        description: username
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
    delete:
      tags:
      - users
      summary: Untag user
      operationId: untagUserUsingDELETE_2
      parameters:
      - name: label
        in: path
        description: label
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: username
        in: path
        description: username
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "204":
          description: No Content
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
      deprecated: false
  /api/v2/users/{username}/lock:
    put:
      tags:
      - users
      summary: Lock user
      operationId: lockUsingPUT
      parameters:
      - name: username
        in: path
        description: username
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/users/{username}/owned-groups:
    get:
      tags:
      - users
      summary: Get the groups owned by the user
      description: login is needed
      operationId: findOwnedGroupUsingGET_1
      parameters:
      - name: inheritance
        in: query
        description: inheritance
        required: false
        style: form
        explode: true
        schema:
          type: boolean
          default: false
      - name: username
        in: path
        description: username
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GroupVo'
                x-content-type: '*/*'
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/users/{username}/reset-password:
    put:
      tags:
      - users
      summary: Reset user password
      operationId: resetPasswordUsingPUT
      parameters:
      - name: username
        in: path
        description: username
        required: true
        style: simple
        explode: false
        schema:
          type: string
      requestBody:
        description: updatePasswordRequestVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePasswordRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: updatePasswordRequestVo
  /api/v2/users/{username}/unlock:
    put:
      tags:
      - users
      summary: Unlock user
      operationId: unlockUsingPUT
      parameters:
      - name: username
        in: path
        description: username
        required: true
        style: simple
        explode: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
  /api/v2/users/{username}/update-password:
    put:
      tags:
      - users
      summary: Update user password
      operationId: updatePasswordUsingPUT_1
      parameters:
      - name: username
        in: path
        description: username
        required: true
        style: simple
        explode: false
        schema:
          type: string
      requestBody:
        description: updatePasswordRequestVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePasswordRequestVo'
        required: true
      responses:
        "200":
          description: OK
          content: {}
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: updatePasswordRequestVo
  /api/v2/verify-access-token:
    post:
      tags:
      - login
      summary: Verify access token
      operationId: verifyAccessTokenUsingPOST_1
      requestBody:
        description: accessTokenVo
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccessTokenVo'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AccessTokenVo'
        "201":
          description: Created
          content: {}
        "401":
          description: Unauthorized
          content: {}
        "403":
          description: Forbidden
          content: {}
        "404":
          description: Not Found
          content: {}
      deprecated: false
      x-codegen-request-body-name: accessTokenVo
components:
  schemas:
    AccessTokenVo:
      title: AccessTokenVo
      type: object
      properties:
        content:
          type: string
        dstPattern:
          type: string
        expiredTime:
          $ref: '#/components/schemas/Timestamp'
        hidden:
          type: boolean
        id:
          type: integer
          format: int64
        issuedTime:
          $ref: '#/components/schemas/Timestamp'
        name:
          type: string
        owner:
          type: string
        srcPattern:
          type: string
        status:
          type: string
          enum:
          - ACTIVE
          - INACTIVE
      example:
        owner: owner
        srcPattern: srcPattern
        hidden: true
        issuedTime: null
        name: name
        id: 0
        dstPattern: dstPattern
        content: content
        expiredTime:
          date: 0
          hours: 1
          seconds: 7
          month: 5
          nanos: 2
          timezoneOffset: 3
          year: 2
          minutes: 5
          time: 9
          day: 6
        status: ACTIVE
    ActionContainerVo:
      title: ActionContainerVo
      type: object
      properties:
        action:
          type: string
        negative:
          type: boolean
        statementId:
          type: integer
          format: int64
      example:
        negative: true
        statementId: 7
        action: action
    ActionGrantOptionVo:
      title: ActionGrantOptionVo
      type: object
      properties:
        action:
          type: string
        grantOption:
          type: boolean
    AdminPermVo:
      title: AdminPermVo
      type: object
      properties:
        action:
          type: string
      example:
        action: action
    AdminRolePermVo:
      title: AdminRolePermVo
      type: object
      properties:
        adminPermAction:
          type: string
        adminRoleName:
          type: string
    AdminRoleVo:
      title: AdminRoleVo
      type: object
      properties:
        adminRoleDescription:
          type: string
        adminRoleName:
          type: string
        users:
          type: array
          items:
            type: string
      example:
        adminRoleDescription: adminRoleDescription
        adminRoleName: adminRoleName
        users:
        - users
        - users
    AuditRecord:
      title: AuditRecord
      type: object
      properties:
        clientIp:
          type: string
        errorCode:
          type: integer
          format: int32
        field:
          type: string
          enum:
          - ADMINISTRATION
          - USER
          - GROUP
          - ROLE
          - PERMISSION
          - QUOTA
          - SERVICE
          - RESOURCE
          - TRUST_RELATION
          - GUARDIAN
          - AUDIT
          - ACCESS_TOKEN
          - SECURITY_CONTEXT
          - POLICY
          - LABEL
        level:
          type: string
          enum:
          - READ
          - ADD
          - UPDATE
          - DELETE
          - CHECK
          - LOGIN
          - DOWNLOAD
          - ALL
          - NONE
        operation:
          type: string
        requestClass:
          type: string
        serverIp:
          type: string
        statusCode:
          type: integer
          format: int32
        timestamp:
          type: integer
          format: int64
        user:
          type: string
      example:
        field: ADMINISTRATION
        level: READ
        clientIp: clientIp
        errorCode: 0
        requestClass: requestClass
        serverIp: serverIp
        operation: operation
        user: user
        statusCode: 6
        timestamp: 1
    AuthorizedPrincRequestVo:
      title: AuthorizedPrincRequestVo
      type: object
      properties:
        action:
          type: string
        inheritance:
          type: boolean
        principalType:
          type: string
          enum:
          - USER
          - GROUP
          - ROLE
        resourceVo:
          $ref: '#/components/schemas/ResourceVo'
    AuthorizedResourceRequestVo:
      title: AuthorizedResourceRequestVo
      type: object
      properties:
        action:
          type: string
        inheritance:
          type: boolean
        principalVo:
          $ref: '#/components/schemas/PrincipalVo'
        resourceVo:
          $ref: '#/components/schemas/ResourceVo'
        searchValue:
          type: string
    ChangeAuditLevelRequestVo:
      title: ChangeAuditLevelRequestVo
      type: object
      properties:
        auditLevels:
          type: array
          items:
            type: string
            enum:
            - READ
            - ADD
            - UPDATE
            - DELETE
            - CHECK
            - LOGIN
            - DOWNLOAD
            - ALL
            - NONE
    ChangeList:
      title: ChangeList
      type: object
      properties:
        changes:
          type: array
          items:
            type: string
        startTime:
          type: integer
          format: int64
        version:
          type: integer
          format: int64
      example:
        changes:
        - changes
        - changes
        startTime: 0
        version: 6
    CheckPrincPermVo:
      title: CheckPrincPermVo
      type: object
      properties:
        checkAny:
          type: boolean
        permGrantOptionVos:
          type: array
          items:
            $ref: '#/components/schemas/PermGrantOptionVo'
        principalVo:
          $ref: '#/components/schemas/PrincipalVo'
        withDetails:
          type: boolean
    CheckResultVo:
      title: CheckResultVo
      type: object
      properties:
        details:
          type: array
          items:
            type: boolean
        positive:
          type: boolean
      example:
        details:
        - true
        - true
        positive: true
    ConditionKeyOpVo:
      title: ConditionKeyOpVo
      type: object
      properties:
        conditionKeyVo:
          $ref: '#/components/schemas/ConditionKeyVo'
        conditionOpVo:
          $ref: '#/components/schemas/ConditionOpVo'
      example:
        conditionOpVo:
          description: description
          operator: operator
        conditionKeyVo:
          conditionKey: conditionKey
          description: description
    ConditionKeyVo:
      title: ConditionKeyVo
      type: object
      properties:
        conditionKey:
          type: string
        description:
          type: string
      example:
        conditionKey: conditionKey
        description: description
    ConditionOpVo:
      title: ConditionOpVo
      type: object
      properties:
        description:
          type: string
        operator:
          type: string
      example:
        description: description
        operator: operator
    ConditionQualifierVo:
      title: ConditionQualifierVo
      type: object
      properties:
        description:
          type: string
        qualifier:
          type: string
      example:
        qualifier: qualifier
        description: description
    ConditionVo:
      title: ConditionVo
      type: object
      properties:
        conditionKey:
          type: string
        description:
          type: string
        id:
          type: integer
          format: int64
        name:
          type: string
        operator:
          type: string
        qualifier:
          type: string
        statementId:
          type: integer
          format: int64
        values:
          type: array
          items:
            type: string
      example:
        conditionKey: conditionKey
        qualifier: qualifier
        values:
        - values
        - values
        name: name
        statementId: 1
        description: description
        id: 1
        operator: operator
    DomainVo:
      title: DomainVo
      required:
      - realm
      - servers
      type: object
      properties:
        ldapSuffix:
          type: string
          description: "Ldap domain, eg. dc=tdh"
        realm:
          type: string
          description: Realm
        servers:
          type: array
          description: Guardian server info
          items:
            $ref: '#/components/schemas/GuardianServerVo'
      example:
        servers:
        - serverPath: serverPath
          password: password
          tlsEnabled: true
          serverAddr: serverAddr
          accessToken: accessToken
          serverPort: 0
          user: user
        - serverPath: serverPath
          password: password
          tlsEnabled: true
          serverAddr: serverAddr
          accessToken: accessToken
          serverPort: 0
          user: user
        realm: realm
        ldapSuffix: ldapSuffix
    EnvContextVo:
      title: EnvContextVo
      type: object
      properties:
        contextMap:
          type: object
          properties: {}
    File:
      title: File
      type: object
      properties:
        absolute:
          type: boolean
        absoluteFile:
          $ref: '#/components/schemas/File'
        absolutePath:
          type: string
        canonicalFile:
          $ref: '#/components/schemas/File'
        canonicalPath:
          type: string
        directory:
          type: boolean
        file:
          type: boolean
        freeSpace:
          type: integer
          format: int64
        hidden:
          type: boolean
        name:
          type: string
        parent:
          type: string
        parentFile:
          $ref: '#/components/schemas/File'
        path:
          type: string
        totalSpace:
          type: integer
          format: int64
        usableSpace:
          type: integer
          format: int64
      example:
        parent: parent
        parentFile: null
        hidden: true
        freeSpace: 0
        totalSpace: 6
        usableSpace: 1
        canonicalFile: null
        directory: true
        path: path
        absoluteFile: null
        file: true
        absolute: true
        canonicalPath: canonicalPath
        name: name
        absolutePath: absolutePath
    GroupRoleVo:
      title: GroupRoleVo
      type: object
      properties:
        groupName:
          type: string
        roleName:
          type: string
    GroupVo:
      title: GroupVo
      type: object
      properties:
        children:
          type: array
          items:
            type: string
        createTime:
          $ref: '#/components/schemas/Timestamp'
        gidNumber:
          type: integer
          format: int64
        groupDescription:
          type: string
        groupName:
          type: string
        labels:
          type: array
          items:
            type: string
        owners:
          type: array
          items:
            type: string
        parents:
          type: array
          items:
            type: string
        roles:
          type: array
          items:
            type: string
        users:
          type: array
          items:
            type: string
      example:
        groupName: groupName
        groupDescription: groupDescription
        children:
        - children
        - children
        createTime:
          date: 0
          hours: 1
          seconds: 7
          month: 5
          nanos: 2
          timezoneOffset: 3
          year: 2
          minutes: 5
          time: 9
          day: 6
        roles:
        - roles
        - roles
        owners:
        - owners
        - owners
        gidNumber: 0
        users:
        - users
        - users
        labels:
        - labels
        - labels
        parents:
        - parents
        - parents
    GuardianServerVo:
      title: GuardianServerVo
      required:
      - serverAddr
      - serverPort
      type: object
      properties:
        accessToken:
          type: string
          description: user's accessToken
        password:
          type: string
          description: user's password
        serverAddr:
          type: string
          description: guardian server address
        serverPath:
          type: string
          description: guardian server path
        serverPort:
          type: integer
          description: guardian server port
          format: int32
        tlsEnabled:
          type: boolean
          description: whether tls is enabled
        user:
          type: string
          description: user used to connect to guardian
      description: Guardian server info
      example:
        serverPath: serverPath
        password: password
        tlsEnabled: true
        serverAddr: serverAddr
        accessToken: accessToken
        serverPort: 0
        user: user
    HealthStatus:
      title: HealthStatus
      type: object
      properties:
        details:
          type: object
          additionalProperties:
            type: string
        status:
          type: string
          enum:
          - UP
          - DOWN
          - UNKNOWN
      example:
        details:
          key: details
        status: UP
    InputStream:
      title: InputStream
      type: object
    KeytabPrincipalVo:
      title: KeytabPrincipalVo
      type: object
      properties:
        principals:
          type: array
          items:
            type: string
    LabelVo:
      title: LabelVo
      type: object
      properties:
        description:
          type: string
        name:
          type: string
        principals:
          type: array
          items:
            $ref: '#/components/schemas/PrincipalVo'
      description: user labels
      example:
        name: name
        description: description
        principals:
        - principal: principal
          principalType: USER
        - principal: principal
          principalType: USER
    LoginRequestVo:
      title: LoginRequestVo
      type: object
      properties:
        isSystem:
          type: boolean
        password:
          type: string
        username:
          type: string
    NodeVo:
      title: NodeVo
      required:
      - type
      - value
      type: object
      properties:
        type:
          type: string
          description: Node type
        value:
          type: string
          description: Node value
      description: Guardian tree node view object
      example:
        type: type
        value: value
    PermGrantOptionVo:
      title: PermGrantOptionVo
      type: object
      properties:
        grantOption:
          type: boolean
        perm:
          $ref: '#/components/schemas/PermVo'
    PermVo:
      title: PermVo
      type: object
      properties:
        action:
          type: string
        resourceVo:
          $ref: '#/components/schemas/ResourceVo'
      example:
        action: action
        resourceVo:
          serviceType: serviceType
          serviceName: serviceName
          dataSource:
          - type: type
            value: value
          - type: type
            value: value
    PolicyCheckVo:
      title: PolicyCheckVo
      type: object
      properties:
        action:
          type: string
        contextVo:
          $ref: '#/components/schemas/EnvContextVo'
        resourceVo:
          $ref: '#/components/schemas/ResourceVo'
        username:
          type: string
    PolicyVo:
      title: PolicyVo
      type: object
      properties:
        createTime:
          $ref: '#/components/schemas/Timestamp'
        description:
          type: string
        id:
          type: integer
          format: int64
        name:
          type: string
        owner:
          type: string
        statements:
          type: array
          items:
            $ref: '#/components/schemas/StatementVo'
        updateTime:
          $ref: '#/components/schemas/Timestamp'
        version:
          type: string
      example:
        owner: owner
        createTime:
          date: 0
          hours: 1
          seconds: 7
          month: 5
          nanos: 2
          timezoneOffset: 3
          year: 2
          minutes: 5
          time: 9
          day: 6
        name: name
        description: description
        statements:
        - policyId: 7
          effect: effect
          roles:
          - null
          - null
          name: name
          description: description
          groups:
          - negative: true
            name: name
            statementId: 1
          - negative: true
            name: name
            statementId: 1
          resources:
          - negative: true
            statementId: 1
            resourceVo:
              serviceType: serviceType
              serviceName: serviceName
              dataSource:
              - type: type
                value: value
              - type: type
                value: value
            heritable: true
          - negative: true
            statementId: 1
            resourceVo:
              serviceType: serviceType
              serviceName: serviceName
              dataSource:
              - type: type
                value: value
              - type: type
                value: value
            heritable: true
          id: 6
          conditions:
          - conditionKey: conditionKey
            qualifier: qualifier
            values:
            - values
            - values
            name: name
            statementId: 1
            description: description
            id: 1
            operator: operator
          - conditionKey: conditionKey
            qualifier: qualifier
            values:
            - values
            - values
            name: name
            statementId: 1
            description: description
            id: 1
            operator: operator
          actions:
          - negative: true
            statementId: 7
            action: action
          - negative: true
            statementId: 7
            action: action
          users:
          - null
          - null
        - policyId: 7
          effect: effect
          roles:
          - null
          - null
          name: name
          description: description
          groups:
          - negative: true
            name: name
            statementId: 1
          - negative: true
            name: name
            statementId: 1
          resources:
          - negative: true
            statementId: 1
            resourceVo:
              serviceType: serviceType
              serviceName: serviceName
              dataSource:
              - type: type
                value: value
              - type: type
                value: value
            heritable: true
          - negative: true
            statementId: 1
            resourceVo:
              serviceType: serviceType
              serviceName: serviceName
              dataSource:
              - type: type
                value: value
              - type: type
                value: value
            heritable: true
          id: 6
          conditions:
          - conditionKey: conditionKey
            qualifier: qualifier
            values:
            - values
            - values
            name: name
            statementId: 1
            description: description
            id: 1
            operator: operator
          - conditionKey: conditionKey
            qualifier: qualifier
            values:
            - values
            - values
            name: name
            statementId: 1
            description: description
            id: 1
            operator: operator
          actions:
          - negative: true
            statementId: 7
            action: action
          - negative: true
            statementId: 7
            action: action
          users:
          - null
          - null
        updateTime: null
        id: 4
        version: version
    PrincGroupVo:
      title: PrincGroupVo
      type: object
      properties:
        groupVo:
          $ref: '#/components/schemas/GroupVo'
        princ:
          $ref: '#/components/schemas/PrincipalVo'
    PrincPermOnResourceVo:
      title: PrincPermOnResourceVo
      type: object
      properties:
        actionGrantOptions:
          type: array
          items:
            $ref: '#/components/schemas/ActionGrantOptionVo'
        princ:
          $ref: '#/components/schemas/PrincipalVo'
        resourceVo:
          $ref: '#/components/schemas/ResourceVo'
    PrincPermRequestVo:
      title: PrincPermRequestVo
      type: object
      properties:
        inheritance:
          type: boolean
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        principalVo:
          $ref: '#/components/schemas/PrincipalVo'
        resourceVo:
          $ref: '#/components/schemas/ResourceVo'
        searchValue:
          type: string
        subtree:
          type: boolean
    PrincPermVo:
      title: PrincPermVo
      type: object
      properties:
        grantOption:
          type: boolean
        perm:
          $ref: '#/components/schemas/PermVo'
        princ:
          $ref: '#/components/schemas/PrincipalVo'
      example:
        princ:
          principal: principal
          principalType: USER
        perm:
          action: action
          resourceVo:
            serviceType: serviceType
            serviceName: serviceName
            dataSource:
            - type: type
              value: value
            - type: type
              value: value
        grantOption: true
    PrincipalContainerVo:
      title: PrincipalContainerVo
      type: object
      properties:
        name:
          type: string
        negative:
          type: boolean
        statementId:
          type: integer
          format: int64
      example:
        negative: true
        name: name
        statementId: 1
    PrincipalVo:
      title: PrincipalVo
      type: object
      properties:
        principal:
          type: string
        principalType:
          type: string
          enum:
          - USER
          - GROUP
          - ROLE
      example:
        principal: principal
        principalType: USER
    PwPolicyVo:
      title: PwPolicyVo
      type: object
      properties:
        pwdAllowUserChange:
          type: boolean
          description: Whether to allow user change the password.
        pwdExpireWarning:
          type: integer
          description: The maximum number of seconds before a password is due to expire
            that expiration warning messages will be returned to an authenticating
            user
          format: int32
        pwdFailureCountInterval:
          type: integer
          description: The number of seconds after which the password failures are
            purged from the failure counter
          format: int32
        pwdGraceAuthNLimit:
          type: integer
          description: The number of times an expired password can be used to authenticate.
          format: int32
        pwdInHistory:
          type: integer
          description: The number of passwords we keep in the password history
          format: int32
        pwdInHistoryDuration:
          type: integer
          description: The duration of passwords we keep in the password history
          format: int32
        pwdLockoutDuration:
          type: integer
          description: The delay in seconds we wait before allowing a new attempt
            when the password hs been locked
          format: int32
        pwdMaxAge:
          type: integer
          description: The number of seconds after which a modified password will
            expire
          format: int32
        pwdMaxFailure:
          type: integer
          description: The maximum number of failure we accept before locking the
            password
          format: int32
        pwdMinAge:
          type: integer
          description: The number of seconds that must elapse between modifications
            to the password.
          format: int32
        pwdMinClasses:
          type: integer
          description: "The minimum number of character classes required in a password.\
            \ The five character classes are lower case, upper case, numbers, punctuation,\
            \ and whitespace/invisible characters"
          format: int32
        pwdMinLength:
          type: integer
          description: The minimum length of the password
          format: int32
        pwdMustChange:
          type: boolean
          description: Whether password must be reset in case of first login or password
            expiration
        pwdNoUsername:
          type: boolean
          description: Whether password string should not contain username
        pwdSafeModify:
          type: boolean
          description: Whether original password must be given when changing password
      description: Guardian global password policy view object
      example:
        pwdMaxFailure: 9
        pwdMinAge: 3
        pwdLockoutDuration: 2
        pwdExpireWarning: 0
        pwdMustChange: true
        pwdAllowUserChange: true
        pwdInHistory: 5
        pwdMaxAge: 7
        pwdFailureCountInterval: 6
        pwdMinClasses: 2
        pwdMinLength: 4
        pwdSafeModify: true
        pwdNoUsername: true
        pwdGraceAuthNLimit: 1
        pwdInHistoryDuration: 5
    QuotaVo:
      title: QuotaVo
      type: object
      properties:
        properties:
          type: object
          properties: {}
          example: {}
        resourceVo:
          $ref: '#/components/schemas/ResourceVo'
      example:
        resourceVo:
          serviceType: serviceType
          serviceName: serviceName
          dataSource:
          - type: type
            value: value
          - type: type
            value: value
        properties: {}
    RenameNodeRequestVo:
      title: RenameNodeRequestVo
      type: object
      properties:
        newResourceVo:
          $ref: '#/components/schemas/ResourceVo'
        oldResourceVo:
          $ref: '#/components/schemas/ResourceVo'
    Resource:
      title: Resource
      type: object
      properties:
        description:
          type: string
        file:
          $ref: '#/components/schemas/File'
        filename:
          type: string
        inputStream:
          $ref: '#/components/schemas/InputStream'
        open:
          type: boolean
        readable:
          type: boolean
        uri:
          $ref: '#/components/schemas/URI'
        url:
          $ref: '#/components/schemas/URL'
      example:
        readable: true
        file:
          parent: parent
          parentFile: null
          hidden: true
          freeSpace: 0
          totalSpace: 6
          usableSpace: 1
          canonicalFile: null
          directory: true
          path: path
          absoluteFile: null
          file: true
          absolute: true
          canonicalPath: canonicalPath
          name: name
          absolutePath: absolutePath
        filename: filename
        description: description
        inputStream: {}
        uri:
          rawFragment: rawFragment
          userInfo: userInfo
          opaque: true
          scheme: scheme
          query: query
          schemeSpecificPart: schemeSpecificPart
          rawUserInfo: rawUserInfo
          path: path
          fragment: fragment
          rawPath: rawPath
          port: 5
          rawSchemeSpecificPart: rawSchemeSpecificPart
          absolute: true
          rawAuthority: rawAuthority
          authority: authority
          host: host
          rawQuery: rawQuery
        open: true
        url:
          defaultPort: 5
          path: path
          userInfo: userInfo
          protocol: protocol
          ref: ref
          file: file
          port: 2
          authority: authority
          query: query
          host: host
          content: {}
    ResourceContainerVo:
      title: ResourceContainerVo
      type: object
      properties:
        heritable:
          type: boolean
        negative:
          type: boolean
        resourceVo:
          $ref: '#/components/schemas/ResourceVo'
        statementId:
          type: integer
          format: int64
      example:
        negative: true
        statementId: 1
        resourceVo:
          serviceType: serviceType
          serviceName: serviceName
          dataSource:
          - type: type
            value: value
          - type: type
            value: value
        heritable: true
    ResourceEntry:
      title: ResourceEntry
      type: object
      properties:
        dataSource:
          type: array
          items:
            $ref: '#/components/schemas/NodeVo'
        resourceNode:
          $ref: '#/components/schemas/NodeVo'
        status:
          type: string
          enum:
          - normal
          - deleted
      example:
        resourceNode: null
        dataSource:
        - type: type
          value: value
        - type: type
          value: value
        status: normal
    ResourceLookupVo:
      title: ResourceLookupVo
      type: object
      properties:
        parentResourceVo:
          $ref: '#/components/schemas/ResourceVo'
        resourceType:
          type: string
        searchValue:
          type: string
    ResourceOwnerEntry:
      title: ResourceOwnerEntry
      type: object
      properties:
        dataNode:
          $ref: '#/components/schemas/NodeVo'
        datasource:
          type: array
          items:
            $ref: '#/components/schemas/NodeVo'
        ownerName:
          type: string
        ownerType:
          type: string
          enum:
          - USER
          - GROUP
          - ROLE
        resourceType:
          type: string
      example:
        ownerType: USER
        ownerName: ownerName
        datasource:
        - null
        - null
        dataNode:
          type: type
          value: value
        resourceType: resourceType
    ResourceOwnerRequestVo:
      title: ResourceOwnerRequestVo
      type: object
      properties:
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        parentResource:
          $ref: '#/components/schemas/ResourceVo'
        searchValue:
          type: string
    ResourcePermRequestVo:
      title: ResourcePermRequestVo
      type: object
      properties:
        inheritance:
          type: boolean
        resourceVo:
          $ref: '#/components/schemas/ResourceVo'
        searchValue:
          type: string
        subtree:
          type: boolean
    ResourceRequestVo:
      title: ResourceRequestVo
      type: object
      properties:
        resourceVo:
          $ref: '#/components/schemas/ResourceVo'
        subtree:
          type: boolean
    ResourceSearchRequestVo:
      title: ResourceSearchRequestVo
      type: object
      properties:
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        resourceVo:
          $ref: '#/components/schemas/ResourceVo'
        searchValue:
          type: string
        sorting:
          type: boolean
        subtree:
          type: boolean
    ResourceVo:
      title: ResourceVo
      required:
      - dataSource
      - serviceName
      type: object
      properties:
        dataSource:
          type: array
          description: The NodeVo list representing the resource
          items:
            $ref: '#/components/schemas/NodeVo'
        serviceName:
          type: string
          description: Service name
        serviceType:
          type: string
          description: Service type
      description: Guardian resource view object representing a resource
      example:
        serviceType: serviceType
        serviceName: serviceName
        dataSource:
        - type: type
          value: value
        - type: type
          value: value
    RoleVo:
      title: RoleVo
      type: object
      properties:
        createTime:
          $ref: '#/components/schemas/Timestamp'
        groups:
          type: array
          items:
            type: string
        labels:
          type: array
          items:
            type: string
        roleDescription:
          type: string
        roleName:
          type: string
        users:
          type: array
          items:
            type: string
      example:
        createTime:
          date: 0
          hours: 1
          seconds: 7
          month: 5
          nanos: 2
          timezoneOffset: 3
          year: 2
          minutes: 5
          time: 9
          day: 6
        roleName: roleName
        groups:
        - groups
        - groups
        roleDescription: roleDescription
        users:
        - users
        - users
        labels:
        - labels
        - labels
    SearchResult«AuditRecord»:
      title: SearchResult«AuditRecord»
      type: object
      properties:
        body:
          type: array
          items:
            $ref: '#/components/schemas/AuditRecord'
      example:
        body:
        - field: ADMINISTRATION
          level: READ
          clientIp: clientIp
          errorCode: 0
          requestClass: requestClass
          serverIp: serverIp
          operation: operation
          user: user
          statusCode: 6
          timestamp: 1
        - field: ADMINISTRATION
          level: READ
          clientIp: clientIp
          errorCode: 0
          requestClass: requestClass
          serverIp: serverIp
          operation: operation
          user: user
          statusCode: 6
          timestamp: 1
    SearchResult«ResourceOwnerEntry»:
      title: SearchResult«ResourceOwnerEntry»
      type: object
      properties:
        body:
          type: array
          items:
            $ref: '#/components/schemas/ResourceOwnerEntry'
      example:
        body:
        - ownerType: USER
          ownerName: ownerName
          datasource:
          - null
          - null
          dataNode:
            type: type
            value: value
          resourceType: resourceType
        - ownerType: USER
          ownerName: ownerName
          datasource:
          - null
          - null
          dataNode:
            type: type
            value: value
          resourceType: resourceType
    ServiceVo:
      title: ServiceVo
      required:
      - serviceName
      type: object
      properties:
        clusterName:
          type: string
          description: cluster service belong to
        configs:
          type: object
          additionalProperties:
            type: string
          description: service configs used to connect to the service
        description:
          type: string
          description: service descriptions
        lastHeartbeatTimestamp:
          type: integer
          description: last timestamp service sends a heartbeat
          format: int64
        offlineTimestamp:
          type: integer
          description: service offline timestamp
          format: int64
        serviceHosts:
          type: array
          description: service hosts registered to guardian
          items:
            type: string
        serviceName:
          type: string
          description: service name (id)
        serviceStatus:
          type: string
          description: service status
          enum:
          - ONLINE
          - OFFLINE
        serviceType:
          type: string
          description: service type
        timestamp:
          type: integer
          description: service register time (of latest registered service host)
          format: int64
      description: Hadoop service registered with Guardian
      example:
        serviceType: serviceType
        configs:
          key: configs
        offlineTimestamp: 6
        lastHeartbeatTimestamp: 0
        serviceStatus: ONLINE
        clusterName: clusterName
        description: description
        serviceHosts:
        - serviceHosts
        - serviceHosts
        serviceName: serviceName
        timestamp: 1
    SessionVo:
      title: SessionVo
      type: object
      properties:
        adminPerms:
          type: array
          items:
            type: string
        authenticated:
          type: boolean
        beforeExpireSeconds:
          type: integer
          format: int32
        credential:
          type: object
          properties: {}
          example: {}
        sessionId:
          type: string
        userId:
          type: string
        userVo:
          $ref: '#/components/schemas/UserVo'
      example:
        authenticated: true
        beforeExpireSeconds: 0
        credential: {}
        sessionId: sessionId
        adminPerms:
        - adminPerms
        - adminPerms
        userId: userId
        userVo:
          userPassword: userPassword
          adminRoles:
          - adminRoles
          - adminRoles
          krb5Principal: krb5Principal
          userDescription: userDescription
          userDept: userDept
          roles:
          - roles
          - roles
          fullName: fullName
          groups:
          - groups
          - groups
          locale: locale
          userName: userName
          labels:
          - labels
          - labels
          system: true
          userLocked: true
          createTime:
            date: 0
            hours: 1
            seconds: 7
            month: 5
            nanos: 2
            timezoneOffset: 3
            year: 2
            minutes: 5
            time: 9
            day: 6
          phone: phone
          uidNumber: 6
          userEmail: userEmail
          gidNumber: 0
          username: username
    StatementVo:
      title: StatementVo
      type: object
      properties:
        actions:
          type: array
          items:
            $ref: '#/components/schemas/ActionContainerVo'
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/ConditionVo'
        description:
          type: string
        effect:
          type: string
        groups:
          type: array
          items:
            $ref: '#/components/schemas/PrincipalContainerVo'
        id:
          type: integer
          format: int64
        name:
          type: string
        policyId:
          type: integer
          format: int64
        resources:
          type: array
          items:
            $ref: '#/components/schemas/ResourceContainerVo'
        roles:
          type: array
          items:
            $ref: '#/components/schemas/PrincipalContainerVo'
        users:
          type: array
          items:
            $ref: '#/components/schemas/PrincipalContainerVo'
      example:
        policyId: 7
        effect: effect
        roles:
        - null
        - null
        name: name
        description: description
        groups:
        - negative: true
          name: name
          statementId: 1
        - negative: true
          name: name
          statementId: 1
        resources:
        - negative: true
          statementId: 1
          resourceVo:
            serviceType: serviceType
            serviceName: serviceName
            dataSource:
            - type: type
              value: value
            - type: type
              value: value
          heritable: true
        - negative: true
          statementId: 1
          resourceVo:
            serviceType: serviceType
            serviceName: serviceName
            dataSource:
            - type: type
              value: value
            - type: type
              value: value
          heritable: true
        id: 6
        conditions:
        - conditionKey: conditionKey
          qualifier: qualifier
          values:
          - values
          - values
          name: name
          statementId: 1
          description: description
          id: 1
          operator: operator
        - conditionKey: conditionKey
          qualifier: qualifier
          values:
          - values
          - values
          name: name
          statementId: 1
          description: description
          id: 1
          operator: operator
        actions:
        - negative: true
          statementId: 7
          action: action
        - negative: true
          statementId: 7
          action: action
        users:
        - null
        - null
    SubjectVo:
      title: SubjectVo
      type: object
      properties:
        proxy:
          type: boolean
        proxyUser:
          type: string
        tgt:
          type: string
      example:
        tgt: tgt
        proxy: true
        proxyUser: proxyUser
    Timestamp:
      title: Timestamp
      type: object
      properties:
        date:
          type: integer
          format: int32
        day:
          type: integer
          format: int32
        hours:
          type: integer
          format: int32
        minutes:
          type: integer
          format: int32
        month:
          type: integer
          format: int32
        nanos:
          type: integer
          format: int32
        seconds:
          type: integer
          format: int32
        time:
          type: integer
          format: int64
        timezoneOffset:
          type: integer
          format: int32
        year:
          type: integer
          format: int32
      example:
        date: 0
        hours: 1
        seconds: 7
        month: 5
        nanos: 2
        timezoneOffset: 3
        year: 2
        minutes: 5
        time: 9
        day: 6
    TrustRelationVo:
      title: TrustRelationVo
      required:
      - targetDomain
      - type
      type: object
      properties:
        sourceDomain:
          $ref: '#/components/schemas/DomainVo'
        targetDomain:
          $ref: '#/components/schemas/DomainVo'
        type:
          type: string
          description: "Trust relation type: outgoing, incoming or two-way"
          enum:
          - INCOMING
          - OUTGOING
          - TWO_WAY
      example:
        targetDomain: null
        sourceDomain:
          servers:
          - serverPath: serverPath
            password: password
            tlsEnabled: true
            serverAddr: serverAddr
            accessToken: accessToken
            serverPort: 0
            user: user
          - serverPath: serverPath
            password: password
            tlsEnabled: true
            serverAddr: serverAddr
            accessToken: accessToken
            serverPort: 0
            user: user
          realm: realm
          ldapSuffix: ldapSuffix
        type: INCOMING
    URI:
      title: URI
      type: object
      properties:
        absolute:
          type: boolean
        authority:
          type: string
        fragment:
          type: string
        host:
          type: string
        opaque:
          type: boolean
        path:
          type: string
        port:
          type: integer
          format: int32
        query:
          type: string
        rawAuthority:
          type: string
        rawFragment:
          type: string
        rawPath:
          type: string
        rawQuery:
          type: string
        rawSchemeSpecificPart:
          type: string
        rawUserInfo:
          type: string
        scheme:
          type: string
        schemeSpecificPart:
          type: string
        userInfo:
          type: string
      example:
        rawFragment: rawFragment
        userInfo: userInfo
        opaque: true
        scheme: scheme
        query: query
        schemeSpecificPart: schemeSpecificPart
        rawUserInfo: rawUserInfo
        path: path
        fragment: fragment
        rawPath: rawPath
        port: 5
        rawSchemeSpecificPart: rawSchemeSpecificPart
        absolute: true
        rawAuthority: rawAuthority
        authority: authority
        host: host
        rawQuery: rawQuery
    URL:
      title: URL
      type: object
      properties:
        authority:
          type: string
        content:
          type: object
          properties: {}
          example: {}
        defaultPort:
          type: integer
          format: int32
        file:
          type: string
        host:
          type: string
        path:
          type: string
        port:
          type: integer
          format: int32
        protocol:
          type: string
        query:
          type: string
        ref:
          type: string
        userInfo:
          type: string
      example:
        defaultPort: 5
        path: path
        userInfo: userInfo
        protocol: protocol
        ref: ref
        file: file
        port: 2
        authority: authority
        query: query
        host: host
        content: {}
    UpdatePasswordRequestVo:
      title: UpdatePasswordRequestVo
      type: object
      properties:
        currentPassword:
          type: string
        newPassword:
          type: string
    UserAdminRoleVo:
      title: UserAdminRoleVo
      type: object
      properties:
        adminRoleName:
          type: string
        username:
          type: string
    UserRoleVo:
      title: UserRoleVo
      type: object
      properties:
        roleName:
          type: string
        username:
          type: string
    UserVo:
      title: UserVo
      type: object
      properties:
        adminRoles:
          type: array
          items:
            type: string
        createTime:
          $ref: '#/components/schemas/Timestamp'
        fullName:
          type: string
        gidNumber:
          type: integer
          format: int64
        groups:
          type: array
          items:
            type: string
        krb5Principal:
          type: string
        labels:
          type: array
          items:
            type: string
        locale:
          type: string
        phone:
          type: string
        roles:
          type: array
          items:
            type: string
        system:
          type: boolean
        uidNumber:
          type: integer
          format: int64
        userDept:
          type: string
        userDescription:
          type: string
        userEmail:
          type: string
        userLocked:
          type: boolean
        userName:
          type: string
        userPassword:
          type: string
        username:
          type: string
      example:
        userPassword: userPassword
        adminRoles:
        - adminRoles
        - adminRoles
        krb5Principal: krb5Principal
        userDescription: userDescription
        userDept: userDept
        roles:
        - roles
        - roles
        fullName: fullName
        groups:
        - groups
        - groups
        locale: locale
        userName: userName
        labels:
        - labels
        - labels
        system: true
        userLocked: true
        createTime:
          date: 0
          hours: 1
          seconds: 7
          month: 5
          nanos: 2
          timezoneOffset: 3
          year: 2
          minutes: 5
          time: 9
          day: 6
        phone: phone
        uidNumber: 6
        userEmail: userEmail
        gidNumber: 0
        username: username
x-original-swagger-version: "2.0"
