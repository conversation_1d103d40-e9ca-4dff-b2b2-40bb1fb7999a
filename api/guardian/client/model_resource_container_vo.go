/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type ResourceContainerVo struct {
	Heritable   bool        `json:"heritable,omitempty"`
	Negative    bool        `json:"negative,omitempty"`
	ResourceVo  *ResourceVo `json:"resourceVo,omitempty"`
	StatementId int64       `json:"statementId,omitempty"`
}
