/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type CheckPrincPermVo struct {
	CheckAny           bool                `json:"checkAny,omitempty"`
	PermGrantOptionVos []PermGrantOptionVo `json:"permGrantOptionVos,omitempty"`
	PrincipalVo        *PrincipalVo        `json:"principalVo,omitempty"`
	WithDetails        bool                `json:"withDetails,omitempty"`
}
