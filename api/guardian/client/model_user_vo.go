/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type UserVo struct {
	AdminRoles      []string   `json:"adminRoles,omitempty"`
	CreateTime      *Timestamp `json:"createTime,omitempty"`
	FullName        string     `json:"fullName,omitempty"`
	GidNumber       int64      `json:"gidNumber,omitempty"`
	Groups          []string   `json:"groups,omitempty"`
	Krb5Principal   string     `json:"krb5Principal,omitempty"`
	Labels          []string   `json:"labels,omitempty"`
	Locale          string     `json:"locale,omitempty"`
	Phone           string     `json:"phone,omitempty"`
	Roles           []string   `json:"roles,omitempty"`
	System          bool       `json:"system,omitempty"`
	UidNumber       int64      `json:"uidNumber,omitempty"`
	UserDept        string     `json:"userDept,omitempty"`
	UserDescription string     `json:"userDescription,omitempty"`
	UserEmail       string     `json:"userEmail,omitempty"`
	UserLocked      bool       `json:"userLocked,omitempty"`
	UserName        string     `json:"userName,omitempty"`
	UserPassword    string     `json:"userPassword,omitempty"`
	Username        string     `json:"username,omitempty"`
}
