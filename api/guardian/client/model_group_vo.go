/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type GroupVo struct {
	Children         []string   `json:"children,omitempty"`
	CreateTime       *Timestamp `json:"createTime,omitempty"`
	GidNumber        int64      `json:"gidNumber,omitempty"`
	GroupDescription string     `json:"groupDescription,omitempty"`
	GroupName        string     `json:"groupName,omitempty"`
	Labels           []string   `json:"labels,omitempty"`
	Owners           []string   `json:"owners,omitempty"`
	Parents          []string   `json:"parents,omitempty"`
	Roles            []string   `json:"roles,omitempty"`
	Users            []string   `json:"users,omitempty"`
}
