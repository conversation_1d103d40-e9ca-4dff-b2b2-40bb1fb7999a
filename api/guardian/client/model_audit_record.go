/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type AuditRecord struct {
	ClientIp     string `json:"clientIp,omitempty"`
	ErrorCode    int32  `json:"errorCode,omitempty"`
	Field        string `json:"field,omitempty"`
	Level        string `json:"level,omitempty"`
	Operation    string `json:"operation,omitempty"`
	RequestClass string `json:"requestClass,omitempty"`
	ServerIp     string `json:"serverIp,omitempty"`
	StatusCode   int32  `json:"statusCode,omitempty"`
	Timestamp    int64  `json:"timestamp,omitempty"`
	User         string `json:"user,omitempty"`
}
