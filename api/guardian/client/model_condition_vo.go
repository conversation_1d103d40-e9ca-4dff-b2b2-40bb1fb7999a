/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type ConditionVo struct {
	ConditionKey string   `json:"conditionKey,omitempty"`
	Description  string   `json:"description,omitempty"`
	Id           int64    `json:"id,omitempty"`
	Name         string   `json:"name,omitempty"`
	Operator     string   `json:"operator,omitempty"`
	Qualifier    string   `json:"qualifier,omitempty"`
	StatementId  int64    `json:"statementId,omitempty"`
	Values       []string `json:"values,omitempty"`
}
