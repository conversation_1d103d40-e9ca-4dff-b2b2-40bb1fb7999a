/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type StatementVo struct {
	Actions     []ActionContainerVo    `json:"actions,omitempty"`
	Conditions  []ConditionVo          `json:"conditions,omitempty"`
	Description string                 `json:"description,omitempty"`
	Effect      string                 `json:"effect,omitempty"`
	Groups      []PrincipalContainerVo `json:"groups,omitempty"`
	Id          int64                  `json:"id,omitempty"`
	Name        string                 `json:"name,omitempty"`
	PolicyId    int64                  `json:"policyId,omitempty"`
	Resources   []ResourceContainerVo  `json:"resources,omitempty"`
	Roles       []PrincipalContainerVo `json:"roles,omitempty"`
	Users       []PrincipalContainerVo `json:"users,omitempty"`
}
