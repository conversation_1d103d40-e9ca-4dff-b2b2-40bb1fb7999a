/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type AccessTokenVo struct {
	Content     string     `json:"content,omitempty"`
	DstPattern  string     `json:"dstPattern,omitempty"`
	ExpiredTime *Timestamp `json:"expiredTime,omitempty"`
	Hidden      bool       `json:"hidden,omitempty"`
	Id          int64      `json:"id,omitempty"`
	IssuedTime  *Timestamp `json:"issuedTime,omitempty"`
	Name        string     `json:"name,omitempty"`
	Owner       string     `json:"owner,omitempty"`
	SrcPattern  string     `json:"srcPattern,omitempty"`
	Status      string     `json:"status,omitempty"`
}
