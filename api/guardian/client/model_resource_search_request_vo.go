/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type ResourceSearchRequestVo struct {
	PageNumber  int32       `json:"pageNumber,omitempty"`
	PageSize    int32       `json:"pageSize,omitempty"`
	ResourceVo  *ResourceVo `json:"resourceVo,omitempty"`
	SearchValue string      `json:"searchValue,omitempty"`
	Sorting     bool        `json:"sorting,omitempty"`
	Subtree     bool        `json:"subtree,omitempty"`
}
