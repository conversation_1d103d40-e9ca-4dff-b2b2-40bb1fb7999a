# AccessTokenVo

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Content** | **string** |  | [optional] [default to null]
**DstPattern** | **string** |  | [optional] [default to null]
**ExpiredTime** | [***Timestamp**](Timestamp.md) |  | [optional] [default to null]
**Hidden** | **bool** |  | [optional] [default to null]
**Id** | **int64** |  | [optional] [default to null]
**IssuedTime** | [***Timestamp**](Timestamp.md) |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**Owner** | **string** |  | [optional] [default to null]
**SrcPattern** | **string** |  | [optional] [default to null]
**Status** | **string** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

