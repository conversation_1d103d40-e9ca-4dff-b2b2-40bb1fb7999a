# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**GetStatusUsingGET1**](HealthApi.md#GetStatusUsingGET1) | **Get** /api/v2/health | Check health status

# **GetStatusUsingGET1**
> HealthStatus GetStatusUsingGET1(ctx, )
Check health status

### Required Parameters
This endpoint does not need any parameter.

### Return type

[**HealthStatus**](HealthStatus.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

