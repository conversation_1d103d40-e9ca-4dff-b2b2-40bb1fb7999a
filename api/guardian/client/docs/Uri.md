# Uri

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Absolute** | **bool** |  | [optional] [default to null]
**Authority** | **string** |  | [optional] [default to null]
**Fragment** | **string** |  | [optional] [default to null]
**Host** | **string** |  | [optional] [default to null]
**Opaque** | **bool** |  | [optional] [default to null]
**Path** | **string** |  | [optional] [default to null]
**Port** | **int32** |  | [optional] [default to null]
**Query** | **string** |  | [optional] [default to null]
**RawAuthority** | **string** |  | [optional] [default to null]
**RawFragment** | **string** |  | [optional] [default to null]
**RawPath** | **string** |  | [optional] [default to null]
**RawQuery** | **string** |  | [optional] [default to null]
**RawSchemeSpecificPart** | **string** |  | [optional] [default to null]
**RawUserInfo** | **string** |  | [optional] [default to null]
**Scheme** | **string** |  | [optional] [default to null]
**SchemeSpecificPart** | **string** |  | [optional] [default to null]
**UserInfo** | **string** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

