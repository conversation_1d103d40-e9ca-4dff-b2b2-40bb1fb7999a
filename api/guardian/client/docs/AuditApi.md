# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**ChangeAuditLevelUsingPUT1**](AuditApi.md#ChangeAuditLevelUsingPUT1) | **Put** /api/v2/audit/audit-level | Change audit level
[**GetActiveAuditLevelUsingGET1**](AuditApi.md#GetActiveAuditLevelUsingGET1) | **Get** /api/v2/audit/audit-level | Get audit level
[**GetAuditRecordsUsingGET1**](AuditApi.md#GetAuditRecordsUsingGET1) | **Get** /api/v2/audit | Get audit records

# **ChangeAuditLevelUsingPUT1**
> ChangeAuditLevelUsingPUT1(ctx, body)
Change audit level

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ChangeAuditLevelRequestVo**](ChangeAuditLevelRequestVo.md)| entity | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetActiveAuditLevelUsingGET1**
> []string GetActiveAuditLevelUsingGET1(ctx, )
Get audit level

### Required Parameters
This endpoint does not need any parameter.

### Return type

**[]string**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetAuditRecordsUsingGET1**
> SearchResultAuditRecord GetAuditRecordsUsingGET1(ctx, optional)
Get audit records

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
 **optional** | ***AuditApiGetAuditRecordsUsingGET1Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AuditApiGetAuditRecordsUsingGET1Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **field** | **optional.String**| field | 
 **from** | **optional.Int64**| from | [default to 0]
 **level** | **optional.String**| level | 
 **operation** | **optional.String**| operation | 
 **order** | **optional.String**| order | 
 **pageNumber** | **optional.Int32**| pageNumber | [default to 0]
 **pageSize** | **optional.Int32**| pageSize | [default to 0]
 **requestClass** | **optional.String**| requestClass | 
 **to** | **optional.Int64**| to | [default to 0]
 **user** | **optional.String**| user | 

### Return type

[**SearchResultAuditRecord**](SearchResult«AuditRecord».md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

