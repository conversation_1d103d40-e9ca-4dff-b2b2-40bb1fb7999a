# UserVo

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AdminRoles** | **[]string** |  | [optional] [default to null]
**CreateTime** | [***Timestamp**](Timestamp.md) |  | [optional] [default to null]
**FullName** | **string** |  | [optional] [default to null]
**GidNumber** | **int64** |  | [optional] [default to null]
**Groups** | **[]string** |  | [optional] [default to null]
**Krb5Principal** | **string** |  | [optional] [default to null]
**Labels** | **[]string** |  | [optional] [default to null]
**Locale** | **string** |  | [optional] [default to null]
**Phone** | **string** |  | [optional] [default to null]
**Roles** | **[]string** |  | [optional] [default to null]
**System** | **bool** |  | [optional] [default to null]
**UidNumber** | **int64** |  | [optional] [default to null]
**UserDept** | **string** |  | [optional] [default to null]
**UserDescription** | **string** |  | [optional] [default to null]
**UserEmail** | **string** |  | [optional] [default to null]
**UserLocked** | **bool** |  | [optional] [default to null]
**UserName** | **string** |  | [optional] [default to null]
**UserPassword** | **string** |  | [optional] [default to null]
**Username** | **string** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

