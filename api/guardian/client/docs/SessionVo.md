# SessionVo

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AdminPerms** | **[]string** |  | [optional] [default to null]
**Authenticated** | **bool** |  | [optional] [default to null]
**BeforeExpireSeconds** | **int32** |  | [optional] [default to null]
**Credential** | [***interface{}**](interface{}.md) |  | [optional] [default to null]
**SessionId** | **string** |  | [optional] [default to null]
**UserId** | **string** |  | [optional] [default to null]
**UserVo** | [***UserVo**](UserVo.md) |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

