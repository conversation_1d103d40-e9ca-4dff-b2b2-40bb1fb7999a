# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**ListConfigsUsingGET**](ConfigsApi.md#ListConfigsUsingGET) | **Get** /api/v2/configs | Get Guardian configuration properties

# **ListConfigsUsingGET**
> interface{} ListConfigsUsingGET(ctx, )
Get Guardian configuration properties

### Required Parameters
This endpoint does not need any parameter.

### Return type

[**interface{}**](interface{}.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

