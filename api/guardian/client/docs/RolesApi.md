# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**AddRoleUsingPOST1**](RolesApi.md#AddRoleUsingPOST1) | **Post** /api/v2/roles | Add role
[**AssignRoleUsingPUT**](RolesApi.md#AssignRoleUsingPUT) | **Put** /api/v2/roles/assign-group | Assign role to group
[**AssignRoleUsingPUT1**](RolesApi.md#AssignRoleUsingPUT1) | **Put** /api/v2/roles/assign-user | Assign role to user
[**DeassignRoleUsingPUT**](RolesApi.md#DeassignRoleUsingPUT) | **Put** /api/v2/roles/deassign-group | Assign role from group
[**DeassignRoleUsingPUT1**](RolesApi.md#DeassignRoleUsingPUT1) | **Put** /api/v2/roles/deassign-user | Assign role from user
[**DeleteRoleUsingDELETE1**](RolesApi.md#DeleteRoleUsingDELETE1) | **Delete** /api/v2/roles/{roleName} | Delete role
[**GetRoleLabelsUsingGET1**](RolesApi.md#GetRoleLabelsUsingGET1) | **Get** /api/v2/roles/{roleName}/labels | Get labels of role
[**GetRoleUsingGET1**](RolesApi.md#GetRoleUsingGET1) | **Get** /api/v2/roles/{roleName} | Get role
[**GetRolesUsingGET1**](RolesApi.md#GetRolesUsingGET1) | **Get** /api/v2/roles | Get roles
[**TagRoleUsingPUT**](RolesApi.md#TagRoleUsingPUT) | **Put** /api/v2/roles/{roleName}/labels/{label} | Tag role
[**UntagRoleUsingDELETE2**](RolesApi.md#UntagRoleUsingDELETE2) | **Delete** /api/v2/roles/{roleName}/labels/{label} | Untag role
[**UntagRoleUsingDELETE3**](RolesApi.md#UntagRoleUsingDELETE3) | **Delete** /api/v2/roles/{roleName}/labels | Clear all role labels
[**UpdateRoleUsingPUT1**](RolesApi.md#UpdateRoleUsingPUT1) | **Put** /api/v2/roles/{roleName} | Update role

# **AddRoleUsingPOST1**
> RoleVo AddRoleUsingPOST1(ctx, body)
Add role

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RoleVo**](RoleVo.md)| roleVo | 

### Return type

[**RoleVo**](RoleVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **AssignRoleUsingPUT**
> AssignRoleUsingPUT(ctx, body)
Assign role to group

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**GroupRoleVo**](GroupRoleVo.md)| groupRoleVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **AssignRoleUsingPUT1**
> AssignRoleUsingPUT1(ctx, body)
Assign role to user

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**UserRoleVo**](UserRoleVo.md)| userRoleVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeassignRoleUsingPUT**
> DeassignRoleUsingPUT(ctx, body)
Assign role from group

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**GroupRoleVo**](GroupRoleVo.md)| groupRoleVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeassignRoleUsingPUT1**
> DeassignRoleUsingPUT1(ctx, body)
Assign role from user

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**UserRoleVo**](UserRoleVo.md)| userRoleVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteRoleUsingDELETE1**
> DeleteRoleUsingDELETE1(ctx, roleName)
Delete role

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **roleName** | **string**| roleName | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetRoleLabelsUsingGET1**
> []LabelVo GetRoleLabelsUsingGET1(ctx, roleName)
Get labels of role

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **roleName** | **string**| roleName | 

### Return type

[**[]LabelVo**](LabelVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetRoleUsingGET1**
> RoleVo GetRoleUsingGET1(ctx, roleName)
Get role

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **roleName** | **string**| roleName | 

### Return type

[**RoleVo**](RoleVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetRolesUsingGET1**
> interface{} GetRolesUsingGET1(ctx, optional)
Get roles

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
 **optional** | ***RolesApiGetRolesUsingGET1Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a RolesApiGetRolesUsingGET1Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageNumber** | **optional.Int32**| pageNumber | [default to -1]
 **pageSize** | **optional.Int32**| pageSize | [default to -1]
 **searchVal** | **optional.String**| searchVal | 
 **sorting** | **optional.Bool**| sorting | [default to false]

### Return type

[**interface{}**](interface{}.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **TagRoleUsingPUT**
> TagRoleUsingPUT(ctx, body, roleName)
Tag role

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**LabelVo**](LabelVo.md)| labelVo | 
  **roleName** | **string**| roleName | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UntagRoleUsingDELETE2**
> UntagRoleUsingDELETE2(ctx, label, roleName)
Untag role

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **label** | **string**| label | 
  **roleName** | **string**| roleName | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UntagRoleUsingDELETE3**
> UntagRoleUsingDELETE3(ctx, roleName)
Clear all role labels

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **roleName** | **string**| roleName | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateRoleUsingPUT1**
> RoleVo UpdateRoleUsingPUT1(ctx, body, roleName)
Update role

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RoleVo**](RoleVo.md)| roleVo | 
  **roleName** | **string**| roleName | 

### Return type

[**RoleVo**](RoleVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

