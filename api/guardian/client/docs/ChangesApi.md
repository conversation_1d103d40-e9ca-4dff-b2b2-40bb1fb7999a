# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**GetChangListUsingGET1**](ChangesApi.md#GetChangListUsingGET1) | **Get** /api/v2/changes/{component} | Get component change list
[**GetChangListUsingGET2**](ChangesApi.md#GetChangListUsingGET2) | **Get** /api/v2/changes | Get change list
[**GetChangeStartTimeUsingGET1**](ChangesApi.md#GetChangeStartTimeUsingGET1) | **Get** /api/v2/changes/start-time | Get the start time of Change keeper
[**InvalidateChangeUsingPOST1**](ChangesApi.md#InvalidateChangeUsingPOST1) | **Post** /api/v2/changes/invalidate | Invalidate all client caches
[**InvalidateComponentUsingPOST1**](ChangesApi.md#InvalidateComponentUsingPOST1) | **Post** /api/v2/changes/invalidate/{component} | Invalidate all client caches of component

# **GetChangListUsingGET1**
> ChangeList GetChangListUsingGET1(ctx, component, reqVersion, startTime)
Get component change list

Internal Use

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **component** | **string**| component | 
  **reqVersion** | **int64**| reqVersion | 
  **startTime** | **int64**| startTime | 

### Return type

[**ChangeList**](ChangeList.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetChangListUsingGET2**
> ChangeList GetChangListUsingGET2(ctx, reqVersion, startTime)
Get change list

Internal Use

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **reqVersion** | **int64**| reqVersion | 
  **startTime** | **int64**| startTime | 

### Return type

[**ChangeList**](ChangeList.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetChangeStartTimeUsingGET1**
> int64 GetChangeStartTimeUsingGET1(ctx, )
Get the start time of Change keeper

Internal Use

### Required Parameters
This endpoint does not need any parameter.

### Return type

**int64**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **InvalidateChangeUsingPOST1**
> InvalidateChangeUsingPOST1(ctx, )
Invalidate all client caches

Login is needed

### Required Parameters
This endpoint does not need any parameter.

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **InvalidateComponentUsingPOST1**
> InvalidateComponentUsingPOST1(ctx, component)
Invalidate all client caches of component

Login is needed

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **component** | **string**| component | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

