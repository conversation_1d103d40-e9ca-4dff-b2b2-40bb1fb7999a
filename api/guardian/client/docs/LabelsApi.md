# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**ClearLabelPrincipalsUsingDELETE1**](LabelsApi.md#ClearLabelPrincipalsUsingDELETE1) | **Delete** /api/v2/labels/{label}/principals | Remove all principals from label
[**CreateLabelUsingPOST1**](LabelsApi.md#CreateLabelUsingPOST1) | **Post** /api/v2/labels | Create a label
[**DeleteLabelUsingDELETE1**](LabelsApi.md#DeleteLabelUsingDELETE1) | **Delete** /api/v2/labels/{label} | Delete label by name
[**GetLabelPrincipalsUsingGET1**](LabelsApi.md#GetLabelPrincipalsUsingGET1) | **Get** /api/v2/labels/{label}/principals | Return principals by label
[**GetLabelUsingGET1**](LabelsApi.md#GetLabelUsingGET1) | **Get** /api/v2/labels/{label} | Get label by name
[**GetUserLabelsUsingGET2**](LabelsApi.md#GetUserLabelsUsingGET2) | **Get** /api/v2/labels | Return labels
[**TagPrincipalUsingPOST1**](LabelsApi.md#TagPrincipalUsingPOST1) | **Post** /api/v2/labels/{label}/tag | Tag a principal with a label
[**UntagPrincipalUsingPOST1**](LabelsApi.md#UntagPrincipalUsingPOST1) | **Post** /api/v2/labels/{label}/untag | Untag a principal from a label
[**UpdateLabelUsingPUT1**](LabelsApi.md#UpdateLabelUsingPUT1) | **Put** /api/v2/labels | Update label by name

# **ClearLabelPrincipalsUsingDELETE1**
> ClearLabelPrincipalsUsingDELETE1(ctx, label, optional)
Remove all principals from label

login is needed

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **label** | **string**| label | 
 **optional** | ***LabelsApiClearLabelPrincipalsUsingDELETE1Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a LabelsApiClearLabelPrincipalsUsingDELETE1Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------

 **principalType** | **optional.String**| principalType | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **CreateLabelUsingPOST1**
> LabelVo CreateLabelUsingPOST1(ctx, body)
Create a label

login is needed

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**LabelVo**](LabelVo.md)| labelVo | 

### Return type

[**LabelVo**](LabelVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteLabelUsingDELETE1**
> DeleteLabelUsingDELETE1(ctx, label)
Delete label by name

login is needed

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **label** | **string**| label | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetLabelPrincipalsUsingGET1**
> []PrincipalVo GetLabelPrincipalsUsingGET1(ctx, label, optional)
Return principals by label

login is needed

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **label** | **string**| label | 
 **optional** | ***LabelsApiGetLabelPrincipalsUsingGET1Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a LabelsApiGetLabelPrincipalsUsingGET1Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------

 **principalType** | **optional.String**| principalType | 

### Return type

[**[]PrincipalVo**](PrincipalVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetLabelUsingGET1**
> LabelVo GetLabelUsingGET1(ctx, label)
Get label by name

login is needed

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **label** | **string**| label | 

### Return type

[**LabelVo**](LabelVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetUserLabelsUsingGET2**
> []LabelVo GetUserLabelsUsingGET2(ctx, optional)
Return labels

login is needed

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
 **optional** | ***LabelsApiGetUserLabelsUsingGET2Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a LabelsApiGetUserLabelsUsingGET2Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **principal** | **optional.String**| principal | 
 **principalType** | **optional.String**| principalType | 

### Return type

[**[]LabelVo**](LabelVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **TagPrincipalUsingPOST1**
> TagPrincipalUsingPOST1(ctx, body, label)
Tag a principal with a label

login is needed

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**PrincipalVo**](PrincipalVo.md)| principalVo | 
  **label** | **string**| label | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UntagPrincipalUsingPOST1**
> UntagPrincipalUsingPOST1(ctx, body, label)
Untag a principal from a label

login is needed

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**PrincipalVo**](PrincipalVo.md)| principalVo | 
  **label** | **string**| label | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateLabelUsingPUT1**
> LabelVo UpdateLabelUsingPUT1(ctx, body)
Update label by name

login is needed

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**LabelVo**](LabelVo.md)| labelVo | 

### Return type

[**LabelVo**](LabelVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

