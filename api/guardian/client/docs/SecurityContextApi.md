# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**GetSubjectUsingGET1**](SecurityContextApi.md#GetSubjectUsingGET1) | **Get** /api/v2/security-context/subject | Get subject for user

# **GetSubjectUsingGET1**
> SubjectVo GetSubjectUsingGET1(ctx, optional)
Get subject for user

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
 **optional** | ***SecurityContextApiGetSubjectUsingGET1Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a SecurityContextApiGetSubjectUsingGET1Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user** | **optional.String**| user | 

### Return type

[**SubjectVo**](SubjectVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

