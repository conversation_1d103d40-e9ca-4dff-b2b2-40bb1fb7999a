# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**AddPolicyUsingPOST1**](AbacApi.md#AddPolicyUsingPOST1) | **Post** /api/v2/abac/policy | Create a policy
[**DeletePolicyByIdUsingDELETE1**](AbacApi.md#DeletePolicyByIdUsingDELETE1) | **Delete** /api/v2/abac/policy/{id} | Delete policy by id
[**GetConditionKeyOpsUsingGET1**](AbacApi.md#GetConditionKeyOpsUsingGET1) | **Get** /api/v2/abac/condition-key-op | List condition key and operators
[**GetConditionQualifierVoUsingGET1**](AbacApi.md#GetConditionQualifierVoUsingGET1) | **Get** /api/v2/abac/condition-qualifier | List condition qualifiers
[**GetPoliciesUsingGET1**](AbacApi.md#GetPoliciesUsingGET1) | **Get** /api/v2/abac/policy | List policies
[**GetPolicyByIdUsingGET1**](AbacApi.md#GetPolicyByIdUsingGET1) | **Get** /api/v2/abac/policy/{id} | Get policy by id
[**GetPolicyResultUsingPOST1**](AbacApi.md#GetPolicyResultUsingPOST1) | **Post** /api/v2/abac/check-policy | Check a policy
[**GetPolicyUpdateTimeUsingGET1**](AbacApi.md#GetPolicyUpdateTimeUsingGET1) | **Get** /api/v2/abac/policy-update-time/{serviceName} | Get the policy update time for a service
[**GetStatementsUsingGET1**](AbacApi.md#GetStatementsUsingGET1) | **Get** /api/v2/abac/statement | List statements
[**RefreshPolicyUpdateTimeUsingPUT1**](AbacApi.md#RefreshPolicyUpdateTimeUsingPUT1) | **Put** /api/v2/abac/policy-update-time/{serviceName} | Refresh the policy update time for a service
[**UpdatePolicyByIdUsingPUT1**](AbacApi.md#UpdatePolicyByIdUsingPUT1) | **Put** /api/v2/abac/policy/{id} | Update policy by id

# **AddPolicyUsingPOST1**
> PolicyVo AddPolicyUsingPOST1(ctx, body)
Create a policy

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**PolicyVo**](PolicyVo.md)| policyVo | 

### Return type

[**PolicyVo**](PolicyVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeletePolicyByIdUsingDELETE1**
> DeletePolicyByIdUsingDELETE1(ctx, id)
Delete policy by id

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **id** | **int64**| id | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetConditionKeyOpsUsingGET1**
> []ConditionKeyOpVo GetConditionKeyOpsUsingGET1(ctx, )
List condition key and operators

### Required Parameters
This endpoint does not need any parameter.

### Return type

[**[]ConditionKeyOpVo**](ConditionKeyOpVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetConditionQualifierVoUsingGET1**
> []ConditionQualifierVo GetConditionQualifierVoUsingGET1(ctx, )
List condition qualifiers

### Required Parameters
This endpoint does not need any parameter.

### Return type

[**[]ConditionQualifierVo**](ConditionQualifierVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetPoliciesUsingGET1**
> []PolicyVo GetPoliciesUsingGET1(ctx, optional)
List policies

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
 **optional** | ***AbacApiGetPoliciesUsingGET1Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AbacApiGetPoliciesUsingGET1Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **serviceId** | **optional.String**| serviceId | 

### Return type

[**[]PolicyVo**](PolicyVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetPolicyByIdUsingGET1**
> PolicyVo GetPolicyByIdUsingGET1(ctx, id)
Get policy by id

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **id** | **int64**| id | 

### Return type

[**PolicyVo**](PolicyVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetPolicyResultUsingPOST1**
> string GetPolicyResultUsingPOST1(ctx, body)
Check a policy

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**PolicyCheckVo**](PolicyCheckVo.md)| policyCheckVo | 

### Return type

**string**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetPolicyUpdateTimeUsingGET1**
> Timestamp GetPolicyUpdateTimeUsingGET1(ctx, serviceName)
Get the policy update time for a service

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **serviceName** | **string**| serviceName | 

### Return type

[**Timestamp**](Timestamp.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetStatementsUsingGET1**
> []StatementVo GetStatementsUsingGET1(ctx, optional)
List statements

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
 **optional** | ***AbacApiGetStatementsUsingGET1Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AbacApiGetStatementsUsingGET1Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **serviceId** | **optional.String**| serviceId | 

### Return type

[**[]StatementVo**](StatementVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **RefreshPolicyUpdateTimeUsingPUT1**
> Timestamp RefreshPolicyUpdateTimeUsingPUT1(ctx, serviceName)
Refresh the policy update time for a service

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **serviceName** | **string**| serviceName | 

### Return type

[**Timestamp**](Timestamp.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdatePolicyByIdUsingPUT1**
> PolicyVo UpdatePolicyByIdUsingPUT1(ctx, body, id)
Update policy by id

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**PolicyVo**](PolicyVo.md)| policyVo | 
  **id** | **int64**| id | 

### Return type

[**PolicyVo**](PolicyVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

