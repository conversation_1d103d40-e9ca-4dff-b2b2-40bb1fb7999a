# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**DeleteServiceUsingDELETE**](ServicesApi.md#DeleteServiceUsingDELETE) | **Delete** /api/v2/services/{serviceName} | Delete service
[**DeleteServicesUsingDELETE**](ServicesApi.md#DeleteServicesUsingDELETE) | **Delete** /api/v2/services | Delete listed services
[**GenYarnSchedulerXmlConfUsingGET1**](ServicesApi.md#GenYarnSchedulerXmlConfUsingGET1) | **Get** /api/v2/services/{yarnName}/scheduler-xml-confs/{fileName} | Generate yarn scheduler conf in xml format
[**GetEffectiveServiceUsingGET**](ServicesApi.md#GetEffectiveServiceUsingGET) | **Get** /api/v2/services/{serviceName}/effective-service | Get effective service
[**GetInactiveSchedulerNodesUsingGET1**](ServicesApi.md#GetInactiveSchedulerNodesUsingGET1) | **Get** /api/v2/services/{serviceName}/inactive-scheduler-nodes | Get inactive scheduler nodes
[**GetSchedulerTypeUsingGET1**](ServicesApi.md#GetSchedulerTypeUsingGET1) | **Get** /api/v2/services/{serviceName}/scheduler-type | Get scheduler type
[**GetServiceMappingUsingGET1**](ServicesApi.md#GetServiceMappingUsingGET1) | **Get** /api/v2/services/service-mapping | Get service mapping
[**ListServicesUsingGET1**](ServicesApi.md#ListServicesUsingGET1) | **Get** /api/v2/services | List services
[**RefreshYarnQueuesUsingPOST1**](ServicesApi.md#RefreshYarnQueuesUsingPOST1) | **Post** /api/v2/services/{yarnName}/queues/refresh | Refresh yarn queues
[**RegisterUsingPOST1**](ServicesApi.md#RegisterUsingPOST1) | **Post** /api/v2/services/register | Register a service
[**SearchResourceOwnersUsingPOST**](ServicesApi.md#SearchResourceOwnersUsingPOST) | **Post** /api/v2/services/resources/owners/search | Search resource owners
[**SearchResourcesUsingPOST**](ServicesApi.md#SearchResourcesUsingPOST) | **Post** /api/v2/services/resources/search | Search service resources

# **DeleteServiceUsingDELETE**
> DeleteServiceUsingDELETE(ctx, serviceName, optional)
Delete service

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **serviceName** | **string**| serviceName | 
 **optional** | ***ServicesApiDeleteServiceUsingDELETEOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a ServicesApiDeleteServiceUsingDELETEOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------

 **includeResources** | **optional.Bool**| includeResources | [default to false]

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteServicesUsingDELETE**
> DeleteServicesUsingDELETE(ctx, serviceName, optional)
Delete listed services

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **serviceName** | [**[]string**](string.md)| serviceName | 
 **optional** | ***ServicesApiDeleteServicesUsingDELETEOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a ServicesApiDeleteServicesUsingDELETEOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------

 **includeResources** | **optional.Bool**| includeResources | [default to false]

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GenYarnSchedulerXmlConfUsingGET1**
> string GenYarnSchedulerXmlConfUsingGET1(ctx, fileName, yarnName)
Generate yarn scheduler conf in xml format

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **fileName** | **string**| fileName | 
  **yarnName** | **string**| yarnName | 

### Return type

**string**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetEffectiveServiceUsingGET**
> ServiceVo GetEffectiveServiceUsingGET(ctx, serviceName)
Get effective service

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **serviceName** | **string**| serviceName | 

### Return type

[**ServiceVo**](ServiceVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetInactiveSchedulerNodesUsingGET1**
> []ResourceEntry GetInactiveSchedulerNodesUsingGET1(ctx, serviceName)
Get inactive scheduler nodes

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **serviceName** | **string**| serviceName | 

### Return type

[**[]ResourceEntry**](ResourceEntry.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetSchedulerTypeUsingGET1**
> ResourceEntry GetSchedulerTypeUsingGET1(ctx, serviceName)
Get scheduler type

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **serviceName** | **string**| serviceName | 

### Return type

[**ResourceEntry**](ResourceEntry.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetServiceMappingUsingGET1**
> map[string]string GetServiceMappingUsingGET1(ctx, )
Get service mapping

### Required Parameters
This endpoint does not need any parameter.

### Return type

**map[string]string**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListServicesUsingGET1**
> interface{} ListServicesUsingGET1(ctx, optional)
List services

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
 **optional** | ***ServicesApiListServicesUsingGET1Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a ServicesApiListServicesUsingGET1Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **detail** | **optional.Bool**| detail | [default to false]
 **pageNumber** | **optional.Int32**| pageNumber | [default to -1]
 **pageSize** | **optional.Int32**| pageSize | [default to -1]
 **searchVal** | **optional.String**| searchVal | 

### Return type

[**interface{}**](interface{}.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **RefreshYarnQueuesUsingPOST1**
> RefreshYarnQueuesUsingPOST1(ctx, yarnName)
Refresh yarn queues

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **yarnName** | **string**| yarnName | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **RegisterUsingPOST1**
> bool RegisterUsingPOST1(ctx, body)
Register a service

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ServiceVo**](ServiceVo.md)| serviceVo | 

### Return type

**bool**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **SearchResourceOwnersUsingPOST**
> SearchResultResourceOwnerEntry SearchResourceOwnersUsingPOST(ctx, body)
Search resource owners

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ResourceOwnerRequestVo**](ResourceOwnerRequestVo.md)| resourceOwnerRequestVo | 

### Return type

[**SearchResultResourceOwnerEntry**](SearchResult«ResourceOwnerEntry».md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **SearchResourcesUsingPOST**
> []ResourceEntry SearchResourcesUsingPOST(ctx, body)
Search service resources

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ResourceLookupVo**](ResourceLookupVo.md)| resourceLookupVo | 

### Return type

[**[]ResourceEntry**](ResourceEntry.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

