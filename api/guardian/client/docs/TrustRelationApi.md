# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**AddDomainUsingPOST1**](TrustRelationApi.md#AddDomainUsingPOST1) | **Post** /api/v2/trust-relations/domains | Add a domain
[**AddTrustRelationUsingPOST1**](TrustRelationApi.md#AddTrustRelationUsingPOST1) | **Post** /api/v2/trust-relations | Add a trust relation
[**DeleteDomainUsingDELETE1**](TrustRelationApi.md#DeleteDomainUsingDELETE1) | **Delete** /api/v2/trust-relations/domains/{domainName} | Delete a domain
[**DeleteTrustRelationV2UsingPOST**](TrustRelationApi.md#DeleteTrustRelationV2UsingPOST) | **Post** /api/v2/trust-relations/delete | Delete a trust relation
[**GetLocalDomainInfoUsingGET1**](TrustRelationApi.md#GetLocalDomainInfoUsingGET1) | **Get** /api/v2/trust-relations/local-domain | Get local domain info
[**ListTrustRelationsUsingGET1**](TrustRelationApi.md#ListTrustRelationsUsingGET1) | **Get** /api/v2/trust-relations | List trust relations
[**UpdateDomainUsingPUT1**](TrustRelationApi.md#UpdateDomainUsingPUT1) | **Put** /api/v2/trust-relations/domains | update a domain

# **AddDomainUsingPOST1**
> AddDomainUsingPOST1(ctx, body)
Add a domain

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**DomainVo**](DomainVo.md)| domainVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **AddTrustRelationUsingPOST1**
> AddTrustRelationUsingPOST1(ctx, body)
Add a trust relation

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**TrustRelationVo**](TrustRelationVo.md)| trustRelationVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteDomainUsingDELETE1**
> DeleteDomainUsingDELETE1(ctx, domainName)
Delete a domain

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **domainName** | **string**| domainName | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteTrustRelationV2UsingPOST**
> DeleteTrustRelationV2UsingPOST(ctx, body)
Delete a trust relation

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**TrustRelationVo**](TrustRelationVo.md)| trustRelationVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetLocalDomainInfoUsingGET1**
> DomainVo GetLocalDomainInfoUsingGET1(ctx, )
Get local domain info

### Required Parameters
This endpoint does not need any parameter.

### Return type

[**DomainVo**](DomainVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListTrustRelationsUsingGET1**
> []TrustRelationVo ListTrustRelationsUsingGET1(ctx, )
List trust relations

### Required Parameters
This endpoint does not need any parameter.

### Return type

[**[]TrustRelationVo**](TrustRelationVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateDomainUsingPUT1**
> UpdateDomainUsingPUT1(ctx, body)
update a domain

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**DomainVo**](DomainVo.md)| domainVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

