# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**DeleteResourceUsingPOST**](ResourcesApi.md#DeleteResourceUsingPOST) | **Post** /api/v2/resources/delete | Delete resource
[**DeleteServiceResourcesUsingDELETE**](ResourcesApi.md#DeleteServiceResourcesUsingDELETE) | **Delete** /api/v2/resources/service-resources | Delete service resources
[**GetChildNodesUsingPOST**](ResourcesApi.md#GetChildNodesUsingPOST) | **Post** /api/v2/resources/child-nodes | Get child nodes of a resource
[**GetDescendantResourcesUsingPOST**](ResourcesApi.md#GetDescendantResourcesUsingPOST) | **Post** /api/v2/resources/descendants | Get descendant resources of a resource
[**RenameNodeUsingPUT**](ResourcesApi.md#RenameNodeUsingPUT) | **Put** /api/v2/resources/rename | Rename one node of data source

# **DeleteResourceUsingPOST**
> DeleteResourceUsingPOST(ctx, body)
Delete resource

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ResourceRequestVo**](ResourceRequestVo.md)| resourceRequestVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteServiceResourcesUsingDELETE**
> DeleteServiceResourcesUsingDELETE(ctx, serviceName)
Delete service resources

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **serviceName** | **string**| serviceName | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetChildNodesUsingPOST**
> []NodeVo GetChildNodesUsingPOST(ctx, body)
Get child nodes of a resource

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ResourceVo**](ResourceVo.md)| resourceVo | 

### Return type

[**[]NodeVo**](NodeVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetDescendantResourcesUsingPOST**
> []ResourceVo GetDescendantResourcesUsingPOST(ctx, body)
Get descendant resources of a resource

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ResourceVo**](ResourceVo.md)| resourceVo | 

### Return type

[**[]ResourceVo**](ResourceVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **RenameNodeUsingPUT**
> RenameNodeUsingPUT(ctx, body)
Rename one node of data source

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**RenameNodeRequestVo**](RenameNodeRequestVo.md)| renameNodeVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

