# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**AddUserUsingPOST**](UsersApi.md#AddUserUsingPOST) | **Post** /api/v2/users | Add user
[**DeleteUserUsingDELETE1**](UsersApi.md#DeleteUserUsingDELETE1) | **Delete** /api/v2/users/{username} | Delete user
[**FindOwnedGroupUsingGET1**](UsersApi.md#FindOwnedGroupUsingGET1) | **Get** /api/v2/users/{username}/owned-groups | Get the groups owned by the user
[**GenerateKeytabUsingGET**](UsersApi.md#GenerateKeytabUsingGET) | **Get** /api/v2/users/keytab | Generate user&#x27;s keytab
[**GenerateMultipleKeytabUsingPOST**](UsersApi.md#GenerateMultipleKeytabUsingPOST) | **Post** /api/v2/users/multiple-keytab | Generate multiple users&#x27; keytab
[**GeneratePasswordUsingGET1**](UsersApi.md#GeneratePasswordUsingGET1) | **Get** /api/v2/users/suggested-password | Generate user password
[**GetKrb5PrincipalsUsingGET**](UsersApi.md#GetKrb5PrincipalsUsingGET) | **Get** /api/v2/users/principals | Get krb5 principals
[**GetUserLabelsUsingGET3**](UsersApi.md#GetUserLabelsUsingGET3) | **Get** /api/v2/users/{username}/labels | Get user labels
[**GetUserUsingGET1**](UsersApi.md#GetUserUsingGET1) | **Get** /api/v2/users/{username} | Get user
[**GetUsersUsingGET**](UsersApi.md#GetUsersUsingGET) | **Get** /api/v2/users | Get users
[**LockUsingPUT**](UsersApi.md#LockUsingPUT) | **Put** /api/v2/users/{username}/lock | Lock user
[**ResetPasswordUsingPUT**](UsersApi.md#ResetPasswordUsingPUT) | **Put** /api/v2/users/{username}/reset-password | Reset user password
[**TagUserUsingPUT**](UsersApi.md#TagUserUsingPUT) | **Put** /api/v2/users/{username}/labels/{label} | Tag user
[**UnlockUsingPUT**](UsersApi.md#UnlockUsingPUT) | **Put** /api/v2/users/{username}/unlock | Unlock user
[**UntagUserUsingDELETE2**](UsersApi.md#UntagUserUsingDELETE2) | **Delete** /api/v2/users/{username}/labels/{label} | Untag user
[**UntagUserUsingDELETE3**](UsersApi.md#UntagUserUsingDELETE3) | **Delete** /api/v2/users/{username}/labels | Clear user tags
[**UpdatePasswordUsingPUT1**](UsersApi.md#UpdatePasswordUsingPUT1) | **Put** /api/v2/users/{username}/update-password | Update user password
[**UpdateUserUsingPUT1**](UsersApi.md#UpdateUserUsingPUT1) | **Put** /api/v2/users/{username} | Update user

# **AddUserUsingPOST**
> UserVo AddUserUsingPOST(ctx, body)
Add user

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**UserVo**](UserVo.md)| userVo | 

### Return type

[**UserVo**](UserVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteUserUsingDELETE1**
> DeleteUserUsingDELETE1(ctx, username)
Delete user

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **username** | **string**| username | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **FindOwnedGroupUsingGET1**
> []GroupVo FindOwnedGroupUsingGET1(ctx, username, optional)
Get the groups owned by the user

login is needed

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **username** | **string**| username | 
 **optional** | ***UsersApiFindOwnedGroupUsingGET1Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UsersApiFindOwnedGroupUsingGET1Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------

 **inheritance** | **optional.Bool**| inheritance | [default to false]

### Return type

[**[]GroupVo**](GroupVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GenerateKeytabUsingGET**
> Resource GenerateKeytabUsingGET(ctx, username)
Generate user's keytab

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **username** | **string**| username | 

### Return type

[**Resource**](Resource.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GenerateMultipleKeytabUsingPOST**
> Resource GenerateMultipleKeytabUsingPOST(ctx, body)
Generate multiple users' keytab

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**KeytabPrincipalVo**](KeytabPrincipalVo.md)| keytabPrincipalVo | 

### Return type

[**Resource**](Resource.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GeneratePasswordUsingGET1**
> []string GeneratePasswordUsingGET1(ctx, )
Generate user password

### Required Parameters
This endpoint does not need any parameter.

### Return type

**[]string**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetKrb5PrincipalsUsingGET**
> []string GetKrb5PrincipalsUsingGET(ctx, optional)
Get krb5 principals

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
 **optional** | ***UsersApiGetKrb5PrincipalsUsingGETOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UsersApiGetKrb5PrincipalsUsingGETOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **prefix** | **optional.String**| prefix | 

### Return type

**[]string**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetUserLabelsUsingGET3**
> []LabelVo GetUserLabelsUsingGET3(ctx, username)
Get user labels

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **username** | **string**| username | 

### Return type

[**[]LabelVo**](LabelVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetUserUsingGET1**
> UserVo GetUserUsingGET1(ctx, username)
Get user

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **username** | **string**| username | 

### Return type

[**UserVo**](UserVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetUsersUsingGET**
> interface{} GetUsersUsingGET(ctx, optional)
Get users

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
 **optional** | ***UsersApiGetUsersUsingGETOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a UsersApiGetUsersUsingGETOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageNumber** | **optional.Int32**| pageNumber | [default to -1]
 **pageSize** | **optional.Int32**| pageSize | [default to -1]
 **searchVal** | **optional.String**| searchVal | 
 **sorting** | **optional.Bool**| sorting | [default to false]

### Return type

[**interface{}**](interface{}.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **LockUsingPUT**
> LockUsingPUT(ctx, username)
Lock user

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **username** | **string**| username | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ResetPasswordUsingPUT**
> ResetPasswordUsingPUT(ctx, body, username)
Reset user password

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**UpdatePasswordRequestVo**](UpdatePasswordRequestVo.md)| updatePasswordRequestVo | 
  **username** | **string**| username | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **TagUserUsingPUT**
> TagUserUsingPUT(ctx, label, username)
Tag user

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **label** | **string**| label | 
  **username** | **string**| username | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UnlockUsingPUT**
> UnlockUsingPUT(ctx, username)
Unlock user

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **username** | **string**| username | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UntagUserUsingDELETE2**
> UntagUserUsingDELETE2(ctx, label, username)
Untag user

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **label** | **string**| label | 
  **username** | **string**| username | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UntagUserUsingDELETE3**
> UntagUserUsingDELETE3(ctx, username)
Clear user tags

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **username** | **string**| username | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdatePasswordUsingPUT1**
> UpdatePasswordUsingPUT1(ctx, body, username)
Update user password

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**UpdatePasswordRequestVo**](UpdatePasswordRequestVo.md)| updatePasswordRequestVo | 
  **username** | **string**| username | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateUserUsingPUT1**
> UserVo UpdateUserUsingPUT1(ctx, body, username)
Update user

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**UserVo**](UserVo.md)| userVo | 
  **username** | **string**| username | 

### Return type

[**UserVo**](UserVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

