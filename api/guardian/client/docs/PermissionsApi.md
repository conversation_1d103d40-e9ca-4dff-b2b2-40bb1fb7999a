# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**AddPermUsingPOST**](PermissionsApi.md#AddPermUsingPOST) | **Post** /api/v2/perms | Add permission
[**CheckUsingPUT1**](PermissionsApi.md#CheckUsingPUT1) | **Put** /api/v2/perms/check | Check permission
[**DeletePermUsingPOST**](PermissionsApi.md#DeletePermUsingPOST) | **Post** /api/v2/perms/delete | Delete permission
[**DeleteResourcePermsUsingPOST**](PermissionsApi.md#DeleteResourcePermsUsingPOST) | **Post** /api/v2/perms/resource-perms/delete | Delete resource permission
[**DeleteServicePermsUsingDELETE**](PermissionsApi.md#DeleteServicePermsUsingDELETE) | **Delete** /api/v2/perms/service-perms | Delete service permission
[**GetAuthorizedDataNodesUsingPOST**](PermissionsApi.md#GetAuthorizedDataNodesUsingPOST) | **Post** /api/v2/perms/authorized-data-nodes | Get authorized data nodes
[**GetAuthorizedDataSourcesUsingPOST**](PermissionsApi.md#GetAuthorizedDataSourcesUsingPOST) | **Post** /api/v2/perms/authorized-data-sources | Get authorized data sources
[**GetAuthorizedPermsUsingPOST**](PermissionsApi.md#GetAuthorizedPermsUsingPOST) | **Post** /api/v2/perms/authorized-perms | Get authorized permissions based on principal
[**GetAuthorizedPrincipalsUsingPOST**](PermissionsApi.md#GetAuthorizedPrincipalsUsingPOST) | **Post** /api/v2/perms/authorized-principals | Get all principals to which a permission is granted
[**GetPermRelatedResourcesUsingPOST**](PermissionsApi.md#GetPermRelatedResourcesUsingPOST) | **Post** /api/v2/perms/resources | Get perm-related resources
[**GetPrincipalPermsUsingPOST**](PermissionsApi.md#GetPrincipalPermsUsingPOST) | **Post** /api/v2/perms/principal-perms | Get principal permission relations based on resource
[**GetResourcePermActionsUsingPOST**](PermissionsApi.md#GetResourcePermActionsUsingPOST) | **Post** /api/v2/perms/resource-perm-actions | Get perm actions of a resource
[**GrantPermUsingPUT1**](PermissionsApi.md#GrantPermUsingPUT1) | **Put** /api/v2/perms/grant | Grant permission
[**GrantPermsOnResourceUsingPUT**](PermissionsApi.md#GrantPermsOnResourceUsingPUT) | **Put** /api/v2/perms/grant-on-resource | Grant permission on one resource to specified user
[**ListPermissionsUsingPOST**](PermissionsApi.md#ListPermissionsUsingPOST) | **Post** /api/v2/perms/resource-perms | List permissions
[**RevokePermUsingPUT1**](PermissionsApi.md#RevokePermUsingPUT1) | **Put** /api/v2/perms/revoke | Revoke permission
[**RevokePermsOnResourceUsingPUT**](PermissionsApi.md#RevokePermsOnResourceUsingPUT) | **Put** /api/v2/perms/revoke-on-resource | Revoke permission on one resource from specified user

# **AddPermUsingPOST**
> PermVo AddPermUsingPOST(ctx, body)
Add permission

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**PermVo**](PermVo.md)| permVo | 

### Return type

[**PermVo**](PermVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **CheckUsingPUT1**
> CheckResultVo CheckUsingPUT1(ctx, body)
Check permission

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**CheckPrincPermVo**](CheckPrincPermVo.md)| checkPrincPermVo | 

### Return type

[**CheckResultVo**](CheckResultVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeletePermUsingPOST**
> DeletePermUsingPOST(ctx, body)
Delete permission

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**PermVo**](PermVo.md)| permVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteResourcePermsUsingPOST**
> DeleteResourcePermsUsingPOST(ctx, body)
Delete resource permission

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ResourceVo**](ResourceVo.md)| resourceVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteServicePermsUsingDELETE**
> DeleteServicePermsUsingDELETE(ctx, serviceName)
Delete service permission

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **serviceName** | **string**| serviceName | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetAuthorizedDataNodesUsingPOST**
> []NodeVo GetAuthorizedDataNodesUsingPOST(ctx, body)
Get authorized data nodes

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AuthorizedResourceRequestVo**](AuthorizedResourceRequestVo.md)| authorizedResourceRequestVo | 

### Return type

[**[]NodeVo**](NodeVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetAuthorizedDataSourcesUsingPOST**
> [][]NodeVo GetAuthorizedDataSourcesUsingPOST(ctx, body)
Get authorized data sources

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AuthorizedResourceRequestVo**](AuthorizedResourceRequestVo.md)| authorizedResourceRequestVo | 

### Return type

[**[][]NodeVo**](array.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetAuthorizedPermsUsingPOST**
> interface{} GetAuthorizedPermsUsingPOST(ctx, body)
Get authorized permissions based on principal

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**PrincPermRequestVo**](PrincPermRequestVo.md)| princPermRequestVo | 

### Return type

[**interface{}**](interface{}.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetAuthorizedPrincipalsUsingPOST**
> []PrincipalVo GetAuthorizedPrincipalsUsingPOST(ctx, body)
Get all principals to which a permission is granted

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AuthorizedPrincRequestVo**](AuthorizedPrincRequestVo.md)| authorizedPrincRequestVo | 

### Return type

[**[]PrincipalVo**](PrincipalVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetPermRelatedResourcesUsingPOST**
> interface{} GetPermRelatedResourcesUsingPOST(ctx, body)
Get perm-related resources

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ResourceSearchRequestVo**](ResourceSearchRequestVo.md)| resourceSearchRequestVo | 

### Return type

[**interface{}**](interface{}.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetPrincipalPermsUsingPOST**
> []PrincPermVo GetPrincipalPermsUsingPOST(ctx, body)
Get principal permission relations based on resource

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ResourcePermRequestVo**](ResourcePermRequestVo.md)| resourcePermRequestVo | 

### Return type

[**[]PrincPermVo**](PrincPermVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetResourcePermActionsUsingPOST**
> []string GetResourcePermActionsUsingPOST(ctx, body)
Get perm actions of a resource

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ResourceVo**](ResourceVo.md)| resourceVo | 

### Return type

**[]string**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GrantPermUsingPUT1**
> GrantPermUsingPUT1(ctx, body)
Grant permission

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**PrincPermVo**](PrincPermVo.md)| princPermVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GrantPermsOnResourceUsingPUT**
> GrantPermsOnResourceUsingPUT(ctx, body)
Grant permission on one resource to specified user

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**PrincPermOnResourceVo**](PrincPermOnResourceVo.md)| princPermOnResourceVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ListPermissionsUsingPOST**
> []PermVo ListPermissionsUsingPOST(ctx, body)
List permissions

login is needed

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ResourceRequestVo**](ResourceRequestVo.md)| resourceRequestVo | 

### Return type

[**[]PermVo**](PermVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **RevokePermUsingPUT1**
> RevokePermUsingPUT1(ctx, body)
Revoke permission

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**PrincPermVo**](PrincPermVo.md)| princPermVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **RevokePermsOnResourceUsingPUT**
> RevokePermsOnResourceUsingPUT(ctx, body)
Revoke permission on one resource from specified user

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**PrincPermOnResourceVo**](PrincPermOnResourceVo.md)| princPermOnResourceVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

