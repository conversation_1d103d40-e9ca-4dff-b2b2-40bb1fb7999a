# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**AddQuotaUsingPOST1**](QuotasApi.md#AddQuotaUsingPOST1) | **Post** /api/v2/quotas | Add quota
[**DeleteQuotaUsingPOST**](QuotasApi.md#DeleteQuotaUsingPOST) | **Post** /api/v2/quotas/delete | Delete quota
[**ReadQuotaUsingPOST**](QuotasApi.md#ReadQuotaUsingPOST) | **Post** /api/v2/quotas/read | Read quota
[**SearchQuotasUsingPOST**](QuotasApi.md#SearchQuotasUsingPOST) | **Post** /api/v2/quotas/search | Search quotas
[**UpdateQuotaUsingPUT1**](QuotasApi.md#UpdateQuotaUsingPUT1) | **Put** /api/v2/quotas | Update quota

# **AddQuotaUsingPOST1**
> AddQuotaUsingPOST1(ctx, body)
Add quota

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**QuotaVo**](QuotaVo.md)| quotaVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteQuotaUsingPOST**
> DeleteQuotaUsingPOST(ctx, body)
Delete quota

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ResourceRequestVo**](ResourceRequestVo.md)| resourceRequestVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ReadQuotaUsingPOST**
> QuotaVo ReadQuotaUsingPOST(ctx, body)
Read quota

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ResourceVo**](ResourceVo.md)| resourceVo | 

### Return type

[**QuotaVo**](QuotaVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **SearchQuotasUsingPOST**
> interface{} SearchQuotasUsingPOST(ctx, body)
Search quotas

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**ResourceSearchRequestVo**](ResourceSearchRequestVo.md)| resourceSearchRequestVo | 

### Return type

[**interface{}**](interface{}.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateQuotaUsingPUT1**
> UpdateQuotaUsingPUT1(ctx, body)
Update quota

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**QuotaVo**](QuotaVo.md)| quotaVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

