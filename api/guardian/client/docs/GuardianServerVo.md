# GuardianServerVo

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AccessToken** | **string** | user&#x27;s accessToken | [optional] [default to null]
**Password** | **string** | user&#x27;s password | [optional] [default to null]
**ServerAddr** | **string** | guardian server address | [default to null]
**ServerPath** | **string** | guardian server path | [optional] [default to null]
**ServerPort** | **int32** | guardian server port | [default to null]
**TlsEnabled** | **bool** | whether tls is enabled | [optional] [default to null]
**User** | **string** | user used to connect to guardian | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

