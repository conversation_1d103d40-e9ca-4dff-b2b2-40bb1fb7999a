# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**AddGroupOwnerUsingPUT**](GroupsApi.md#AddGroupOwnerUsingPUT) | **Put** /api/v2/groups/{groupName}/owner/{username} | Add owner to group
[**AddGroupUsingPOST1**](GroupsApi.md#AddGroupUsingPOST1) | **Post** /api/v2/groups | Add group
[**AssignGroupUsingPUT**](GroupsApi.md#AssignGroupUsingPUT) | **Put** /api/v2/groups/assign | Assign group to user or group
[**DeassignGroupUsingPUT**](GroupsApi.md#DeassignGroupUsingPUT) | **Put** /api/v2/groups/deassign | Deassign group from user or group
[**DeleteGroupOwnerUsingDELETE**](GroupsApi.md#DeleteGroupOwnerUsingDELETE) | **Delete** /api/v2/groups/{groupName}/owner/{username} | Delete owner from group
[**DeleteGroupUsingDELETE1**](GroupsApi.md#DeleteGroupUsingDELETE1) | **Delete** /api/v2/groups/{groupName} | Delete group
[**GetGroupLabelsUsingGET1**](GroupsApi.md#GetGroupLabelsUsingGET1) | **Get** /api/v2/groups/{groupName}/labels | Get group labels
[**GetGroupOwnersUsingGET**](GroupsApi.md#GetGroupOwnersUsingGET) | **Get** /api/v2/groups/{groupName}/owners | Get group owners
[**GetGroupUsingGET1**](GroupsApi.md#GetGroupUsingGET1) | **Get** /api/v2/groups/{groupName} | Get group
[**GetGroupsUsingGET**](GroupsApi.md#GetGroupsUsingGET) | **Get** /api/v2/groups | Get groups
[**TagGroupUsingPUT**](GroupsApi.md#TagGroupUsingPUT) | **Put** /api/v2/groups/{groupName}/labels/{label} | Tag group
[**UntagGroupUsingDELETE2**](GroupsApi.md#UntagGroupUsingDELETE2) | **Delete** /api/v2/groups/{groupName}/labels/{label} | Untag group
[**UntagGroupUsingDELETE3**](GroupsApi.md#UntagGroupUsingDELETE3) | **Delete** /api/v2/groups/{groupName}/labels | Clear group labels
[**UpdateGroupUsingPUT1**](GroupsApi.md#UpdateGroupUsingPUT1) | **Put** /api/v2/groups/{groupName} | Update group

# **AddGroupOwnerUsingPUT**
> AddGroupOwnerUsingPUT(ctx, groupName, username)
Add owner to group

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **groupName** | **string**| groupName | 
  **username** | **string**| username | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **AddGroupUsingPOST1**
> GroupVo AddGroupUsingPOST1(ctx, body)
Add group

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**GroupVo**](GroupVo.md)| groupVo | 

### Return type

[**GroupVo**](GroupVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **AssignGroupUsingPUT**
> AssignGroupUsingPUT(ctx, body)
Assign group to user or group

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**PrincGroupVo**](PrincGroupVo.md)| princGroupVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeassignGroupUsingPUT**
> DeassignGroupUsingPUT(ctx, body)
Deassign group from user or group

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**PrincGroupVo**](PrincGroupVo.md)| princGroupVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteGroupOwnerUsingDELETE**
> DeleteGroupOwnerUsingDELETE(ctx, groupName, username)
Delete owner from group

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **groupName** | **string**| groupName | 
  **username** | **string**| username | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteGroupUsingDELETE1**
> DeleteGroupUsingDELETE1(ctx, groupName)
Delete group

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **groupName** | **string**| groupName | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetGroupLabelsUsingGET1**
> []LabelVo GetGroupLabelsUsingGET1(ctx, groupName)
Get group labels

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **groupName** | **string**| groupName | 

### Return type

[**[]LabelVo**](LabelVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetGroupOwnersUsingGET**
> []UserVo GetGroupOwnersUsingGET(ctx, groupName, optional)
Get group owners

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **groupName** | **string**| groupName | 
 **optional** | ***GroupsApiGetGroupOwnersUsingGETOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a GroupsApiGetGroupOwnersUsingGETOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------

 **inheritance** | **optional.Bool**| inheritance | [default to false]

### Return type

[**[]UserVo**](UserVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetGroupUsingGET1**
> GroupVo GetGroupUsingGET1(ctx, groupName)
Get group

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **groupName** | **string**| groupName | 

### Return type

[**GroupVo**](GroupVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetGroupsUsingGET**
> interface{} GetGroupsUsingGET(ctx, optional)
Get groups

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
 **optional** | ***GroupsApiGetGroupsUsingGETOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a GroupsApiGetGroupsUsingGETOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageNumber** | **optional.Int32**| pageNumber | [default to -1]
 **pageSize** | **optional.Int32**| pageSize | [default to -1]
 **searchVal** | **optional.String**| searchVal | 
 **sorting** | **optional.Bool**| sorting | [default to false]

### Return type

[**interface{}**](interface{}.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **TagGroupUsingPUT**
> TagGroupUsingPUT(ctx, groupName, label)
Tag group

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **groupName** | **string**| groupName | 
  **label** | **string**| label | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UntagGroupUsingDELETE2**
> UntagGroupUsingDELETE2(ctx, groupName, label)
Untag group

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **groupName** | **string**| groupName | 
  **label** | **string**| label | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UntagGroupUsingDELETE3**
> UntagGroupUsingDELETE3(ctx, groupName)
Clear group labels

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **groupName** | **string**| groupName | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateGroupUsingPUT1**
> GroupVo UpdateGroupUsingPUT1(ctx, body, groupName)
Update group

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**GroupVo**](GroupVo.md)| groupVo | 
  **groupName** | **string**| groupName | 

### Return type

[**GroupVo**](GroupVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

