# StatementVo

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Actions** | [**[]ActionContainerVo**](ActionContainerVo.md) |  | [optional] [default to null]
**Conditions** | [**[]ConditionVo**](ConditionVo.md) |  | [optional] [default to null]
**Description** | **string** |  | [optional] [default to null]
**Effect** | **string** |  | [optional] [default to null]
**Groups** | [**[]PrincipalContainerVo**](PrincipalContainerVo.md) |  | [optional] [default to null]
**Id** | **int64** |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**PolicyId** | **int64** |  | [optional] [default to null]
**Resources** | [**[]ResourceContainerVo**](ResourceContainerVo.md) |  | [optional] [default to null]
**Roles** | [**[]PrincipalContainerVo**](PrincipalContainerVo.md) |  | [optional] [default to null]
**Users** | [**[]PrincipalContainerVo**](PrincipalContainerVo.md) |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

