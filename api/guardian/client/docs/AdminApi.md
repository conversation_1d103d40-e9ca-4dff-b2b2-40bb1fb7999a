# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**AddAdminRoleUsingPOST1**](AdminApi.md#AddAdminRoleUsingPOST1) | **Post** /api/v2/admin/admin-roles | Add admin role
[**AssignUsingPUT2**](AdminApi.md#AssignUsingPUT2) | **Put** /api/v2/admin/admin-roles/assign | Assign admin role to user
[**DeassignUsingPUT2**](AdminApi.md#DeassignUsingPUT2) | **Put** /api/v2/admin/admin-roles/deassign | Deassign admin role from user
[**DeleteAdminRoleUsingDELETE1**](AdminApi.md#DeleteAdminRoleUsingDELETE1) | **Delete** /api/v2/admin/admin-roles/{adminRoleName} | Delete admin role
[**GetAdminRolePermsUsingGET**](AdminApi.md#GetAdminRolePermsUsingGET) | **Get** /api/v2/admin/admin-roles/{adminRoleName}/perms | Get admin role permissions
[**GetAdminRoleUsingGET1**](AdminApi.md#GetAdminRoleUsingGET1) | **Get** /api/v2/admin/admin-roles/{adminRoleName} | Get admin role
[**GetAdminRolesUsingGET**](AdminApi.md#GetAdminRolesUsingGET) | **Get** /api/v2/admin/admin-roles | Get admin roles
[**GetAssignedAdminRolesUsingGET**](AdminApi.md#GetAssignedAdminRolesUsingGET) | **Get** /api/v2/admin/users/{username}/admin-roles | Get user assigned admin roles
[**GetAssignedUsersUsingGET1**](AdminApi.md#GetAssignedUsersUsingGET1) | **Get** /api/v2/admin/admin-roles/{adminRoleName}/assigned-users | Get admin role assigned users
[**GetPasswordPolicyUsingGET1**](AdminApi.md#GetPasswordPolicyUsingGET1) | **Get** /api/v2/admin/pwd-policy | Return password policy
[**GrantPermUsingPUT**](AdminApi.md#GrantPermUsingPUT) | **Put** /api/v2/admin/admin-roles/grant | Grant admin role permission
[**HasRoleUsingGET1**](AdminApi.md#HasRoleUsingGET1) | **Get** /api/v2/admin/admin-roles/has-role | Check whether the user has all/any of the admin roles
[**RevokePermUsingPUT**](AdminApi.md#RevokePermUsingPUT) | **Put** /api/v2/admin/admin-roles/revoke | Revoke admin role permission
[**UpdateAdminRoleUsingPUT1**](AdminApi.md#UpdateAdminRoleUsingPUT1) | **Put** /api/v2/admin/admin-roles | Update admin role
[**UpdatePasswordPolicyUsingPUT1**](AdminApi.md#UpdatePasswordPolicyUsingPUT1) | **Put** /api/v2/admin/pwd-policy | Update the password policy

# **AddAdminRoleUsingPOST1**
> AdminRoleVo AddAdminRoleUsingPOST1(ctx, body)
Add admin role

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AdminRoleVo**](AdminRoleVo.md)| adminRoleVo | 

### Return type

[**AdminRoleVo**](AdminRoleVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **AssignUsingPUT2**
> AssignUsingPUT2(ctx, body)
Assign admin role to user

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**UserAdminRoleVo**](UserAdminRoleVo.md)| userAdminRoleVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeassignUsingPUT2**
> DeassignUsingPUT2(ctx, body)
Deassign admin role from user

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**UserAdminRoleVo**](UserAdminRoleVo.md)| userAdminRoleVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteAdminRoleUsingDELETE1**
> DeleteAdminRoleUsingDELETE1(ctx, adminRoleName)
Delete admin role

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **adminRoleName** | **string**| adminRoleName | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetAdminRolePermsUsingGET**
> []AdminPermVo GetAdminRolePermsUsingGET(ctx, adminRoleName)
Get admin role permissions

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **adminRoleName** | **string**| adminRoleName | 

### Return type

[**[]AdminPermVo**](AdminPermVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetAdminRoleUsingGET1**
> AdminRoleVo GetAdminRoleUsingGET1(ctx, adminRoleName)
Get admin role

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **adminRoleName** | **string**| adminRoleName | 

### Return type

[**AdminRoleVo**](AdminRoleVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetAdminRolesUsingGET**
> interface{} GetAdminRolesUsingGET(ctx, optional)
Get admin roles

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
 **optional** | ***AdminApiGetAdminRolesUsingGETOpts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AdminApiGetAdminRolesUsingGETOpts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageNumber** | **optional.Int32**| pageNumber | [default to -1]
 **pageSize** | **optional.Int32**| pageSize | [default to -1]
 **searchVal** | **optional.String**| searchVal | 
 **sorting** | **optional.Bool**| sorting | [default to false]

### Return type

[**interface{}**](interface{}.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetAssignedAdminRolesUsingGET**
> []AdminRoleVo GetAssignedAdminRolesUsingGET(ctx, username)
Get user assigned admin roles

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **username** | **string**| username | 

### Return type

[**[]AdminRoleVo**](AdminRoleVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetAssignedUsersUsingGET1**
> []UserVo GetAssignedUsersUsingGET1(ctx, adminRoleName)
Get admin role assigned users

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **adminRoleName** | **string**| adminRoleName | 

### Return type

[**[]UserVo**](UserVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetPasswordPolicyUsingGET1**
> PwPolicyVo GetPasswordPolicyUsingGET1(ctx, )
Return password policy

### Required Parameters
This endpoint does not need any parameter.

### Return type

[**PwPolicyVo**](PwPolicyVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GrantPermUsingPUT**
> GrantPermUsingPUT(ctx, body)
Grant admin role permission

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AdminRolePermVo**](AdminRolePermVo.md)| adminRolePermVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **HasRoleUsingGET1**
> CheckResultVo HasRoleUsingGET1(ctx, adminRole, username, optional)
Check whether the user has all/any of the admin roles

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **adminRole** | [**[]string**](string.md)| adminRole | 
  **username** | **string**| username | 
 **optional** | ***AdminApiHasRoleUsingGET1Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AdminApiHasRoleUsingGET1Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


 **checkAny** | **optional.Bool**| checkAny | [default to false]

### Return type

[**CheckResultVo**](CheckResultVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **RevokePermUsingPUT**
> RevokePermUsingPUT(ctx, body)
Revoke admin role permission

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AdminRolePermVo**](AdminRolePermVo.md)| adminRolePermVo | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateAdminRoleUsingPUT1**
> AdminRoleVo UpdateAdminRoleUsingPUT1(ctx, body)
Update admin role

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AdminRoleVo**](AdminRoleVo.md)| adminRoleVo | 

### Return type

[**AdminRoleVo**](AdminRoleVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdatePasswordPolicyUsingPUT1**
> PwPolicyVo UpdatePasswordPolicyUsingPUT1(ctx, body)
Update the password policy

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**PwPolicyVo**](PwPolicyVo.md)| pwdPolicyVo | 

### Return type

[**PwPolicyVo**](PwPolicyVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

