# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**AuthenticateUsingPOST**](LoginApi.md#AuthenticateUsingPOST) | **Post** /api/v2/authenticate | Authenticate with Guardian account
[**GetAuthInfoUsingGET1**](LoginApi.md#GetAuthInfoUsingGET1) | **Get** /api/v2/auth-info | Return the authentication information
[**LoginUsingPOST**](LoginApi.md#LoginUsingPOST) | **Post** /api/v2/login | Login Guardian service
[**LogoutUsingPOST1**](LoginApi.md#LogoutUsingPOST1) | **Post** /api/v2/logout | Logout from Guardian Service
[**SingleLogoutUsingGET1**](LoginApi.md#SingleLogoutUsingGET1) | **Get** /api/v2/single-logout | Single logout
[**VerifyAccessTokenUsingPOST1**](LoginApi.md#VerifyAccessTokenUsingPOST1) | **Post** /api/v2/verify-access-token | Verify access token

# **AuthenticateUsingPOST**
> SessionVo AuthenticateUsingPOST(ctx, body)
Authenticate with Guardian account

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**LoginRequestVo**](LoginRequestVo.md)| loginRequestVo | 

### Return type

[**SessionVo**](SessionVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetAuthInfoUsingGET1**
> map[string][]string GetAuthInfoUsingGET1(ctx, )
Return the authentication information

### Required Parameters
This endpoint does not need any parameter.

### Return type

[**map[string][]string**](array.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **LoginUsingPOST**
> SessionVo LoginUsingPOST(ctx, body)
Login Guardian service

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**LoginRequestVo**](LoginRequestVo.md)| loginRequestVo | 

### Return type

[**SessionVo**](SessionVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **LogoutUsingPOST1**
> LogoutUsingPOST1(ctx, )
Logout from Guardian Service

### Required Parameters
This endpoint does not need any parameter.

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **SingleLogoutUsingGET1**
> SingleLogoutUsingGET1(ctx, )
Single logout

### Required Parameters
This endpoint does not need any parameter.

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **VerifyAccessTokenUsingPOST1**
> AccessTokenVo VerifyAccessTokenUsingPOST1(ctx, body)
Verify access token

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AccessTokenVo**](AccessTokenVo.md)| accessTokenVo | 

### Return type

[**AccessTokenVo**](AccessTokenVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

