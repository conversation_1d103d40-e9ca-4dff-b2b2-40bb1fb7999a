# ServiceVo

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**ClusterName** | **string** | cluster service belong to | [optional] [default to null]
**Configs** | **map[string]string** | service configs used to connect to the service | [optional] [default to null]
**Description** | **string** | service descriptions | [optional] [default to null]
**LastHeartbeatTimestamp** | **int64** | last timestamp service sends a heartbeat | [optional] [default to null]
**OfflineTimestamp** | **int64** | service offline timestamp | [optional] [default to null]
**ServiceHosts** | **[]string** | service hosts registered to guardian | [optional] [default to null]
**ServiceName** | **string** | service name (id) | [default to null]
**ServiceStatus** | **string** | service status | [optional] [default to null]
**ServiceType** | **string** | service type | [optional] [default to null]
**Timestamp** | **int64** | service register time (of latest registered service host) | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

