# ConditionVo

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**ConditionKey** | **string** |  | [optional] [default to null]
**Description** | **string** |  | [optional] [default to null]
**Id** | **int64** |  | [optional] [default to null]
**Name** | **string** |  | [optional] [default to null]
**Operator** | **string** |  | [optional] [default to null]
**Qualifier** | **string** |  | [optional] [default to null]
**StatementId** | **int64** |  | [optional] [default to null]
**Values** | **[]string** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

