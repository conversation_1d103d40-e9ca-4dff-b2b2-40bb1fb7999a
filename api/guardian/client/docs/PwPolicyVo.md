# PwPolicyVo

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**PwdAllowUserChange** | **bool** | Whether to allow user change the password. | [optional] [default to null]
**PwdExpireWarning** | **int32** | The maximum number of seconds before a password is due to expire that expiration warning messages will be returned to an authenticating user | [optional] [default to null]
**PwdFailureCountInterval** | **int32** | The number of seconds after which the password failures are purged from the failure counter | [optional] [default to null]
**PwdGraceAuthNLimit** | **int32** | The number of times an expired password can be used to authenticate. | [optional] [default to null]
**PwdInHistory** | **int32** | The number of passwords we keep in the password history | [optional] [default to null]
**PwdInHistoryDuration** | **int32** | The duration of passwords we keep in the password history | [optional] [default to null]
**PwdLockoutDuration** | **int32** | The delay in seconds we wait before allowing a new attempt when the password hs been locked | [optional] [default to null]
**PwdMaxAge** | **int32** | The number of seconds after which a modified password will expire | [optional] [default to null]
**PwdMaxFailure** | **int32** | The maximum number of failure we accept before locking the password | [optional] [default to null]
**PwdMinAge** | **int32** | The number of seconds that must elapse between modifications to the password. | [optional] [default to null]
**PwdMinClasses** | **int32** | The minimum number of character classes required in a password. The five character classes are lower case, upper case, numbers, punctuation, and whitespace/invisible characters | [optional] [default to null]
**PwdMinLength** | **int32** | The minimum length of the password | [optional] [default to null]
**PwdMustChange** | **bool** | Whether password must be reset in case of first login or password expiration | [optional] [default to null]
**PwdNoUsername** | **bool** | Whether password string should not contain username | [optional] [default to null]
**PwdSafeModify** | **bool** | Whether original password must be given when changing password | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

