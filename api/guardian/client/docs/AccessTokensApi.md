# io.transwarp.walm{{classname}}

All URIs are relative to *//*************/*

Method | HTTP request | Description
------------- | ------------- | -------------
[**CreateAccessTokenUsingPOST1**](AccessTokensApi.md#CreateAccessTokenUsingPOST1) | **Post** /api/v2/access-tokens | Create an access token
[**DeleteAccessTokenByIdUsingDELETE1**](AccessTokensApi.md#DeleteAccessTokenByIdUsingDELETE1) | **Delete** /api/v2/access-tokens/{id} | Delete access token by id
[**GetAccessTokenByIdUsingGET1**](AccessTokensApi.md#GetAccessTokenByIdUsingGET1) | **Get** /api/v2/access-tokens/{id} | Get access token by id
[**GetAccessTokenByOwnerUsingGET1**](AccessTokensApi.md#GetAccessTokenByOwnerUsingGET1) | **Get** /api/v2/access-tokens | Get access tokens by owner
[**UpdateAccessTokenByIdUsingPUT1**](AccessTokensApi.md#UpdateAccessTokenByIdUsingPUT1) | **Put** /api/v2/access-tokens | Update access token by id

# **CreateAccessTokenUsingPOST1**
> AccessTokenVo CreateAccessTokenUsingPOST1(ctx, body)
Create an access token

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AccessTokenVo**](AccessTokenVo.md)| accessTokenVo | 

### Return type

[**AccessTokenVo**](AccessTokenVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **DeleteAccessTokenByIdUsingDELETE1**
> DeleteAccessTokenByIdUsingDELETE1(ctx, id)
Delete access token by id

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **id** | **int64**| id | 

### Return type

 (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetAccessTokenByIdUsingGET1**
> AccessTokenVo GetAccessTokenByIdUsingGET1(ctx, id)
Get access token by id

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **id** | **int64**| id | 

### Return type

[**AccessTokenVo**](AccessTokenVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **GetAccessTokenByOwnerUsingGET1**
> interface{} GetAccessTokenByOwnerUsingGET1(ctx, optional)
Get access tokens by owner

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
 **optional** | ***AccessTokensApiGetAccessTokenByOwnerUsingGET1Opts** | optional parameters | nil if no parameters

### Optional Parameters
Optional parameters are passed through a pointer to a AccessTokensApiGetAccessTokenByOwnerUsingGET1Opts struct
Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **getAll** | **optional.Bool**| getAll | [default to false]
 **owner** | **optional.String**| owner | 
 **pageNumber** | **optional.Int32**| pageNumber | [default to -1]
 **pageSize** | **optional.Int32**| pageSize | [default to -1]

### Return type

[**interface{}**](interface{}.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **UpdateAccessTokenByIdUsingPUT1**
> AccessTokenVo UpdateAccessTokenByIdUsingPUT1(ctx, body)
Update access token by id

### Required Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
  **body** | [**AccessTokenVo**](AccessTokenVo.md)| accessTokenVo | 

### Return type

[**AccessTokenVo**](AccessTokenVo.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

