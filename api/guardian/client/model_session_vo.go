/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type SessionVo struct {
	AdminPerms          []string     `json:"adminPerms,omitempty"`
	Authenticated       bool         `json:"authenticated,omitempty"`
	BeforeExpireSeconds int32        `json:"beforeExpireSeconds,omitempty"`
	Credential          *interface{} `json:"credential,omitempty"`
	SessionId           string       `json:"sessionId,omitempty"`
	UserId              string       `json:"userId,omitempty"`
	UserVo              *UserVo      `json:"userVo,omitempty"`
}
