/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

// Guardian server info
type GuardianServerVo struct {
	// user's accessToken
	AccessToken string `json:"accessToken,omitempty"`
	// user's password
	Password string `json:"password,omitempty"`
	// guardian server address
	ServerAddr string `json:"serverAddr"`
	// guardian server path
	ServerPath string `json:"serverPath,omitempty"`
	// guardian server port
	ServerPort int32 `json:"serverPort"`
	// whether tls is enabled
	TlsEnabled bool `json:"tlsEnabled,omitempty"`
	// user used to connect to guardian
	User string `json:"user,omitempty"`
}
