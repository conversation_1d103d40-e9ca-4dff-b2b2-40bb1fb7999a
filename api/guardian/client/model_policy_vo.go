/*
 * Guardian
 *
 * Guardian Swagger API /API/V2
 *
 * API version: /api/v2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package guardianv2

type PolicyVo struct {
	CreateTime  *Timestamp    `json:"createTime,omitempty"`
	Description string        `json:"description,omitempty"`
	Id          int64         `json:"id,omitempty"`
	Name        string        `json:"name,omitempty"`
	Owner       string        `json:"owner,omitempty"`
	Statements  []StatementVo `json:"statements,omitempty"`
	UpdateTime  *Timestamp    `json:"updateTime,omitempty"`
	Version     string        `json:"version,omitempty"`
}
