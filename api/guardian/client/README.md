# Go API client for swagger

Guardian Swagger API /API/V2

## Overview
This API client was generated by the [swagger-codegen](https://github.com/swagger-api/swagger-codegen) project.  By using the [swagger-spec](https://github.com/swagger-api/swagger-spec) from a remote server, you can easily generate an API client.

- API version: /api/v2
- Package version: 1.0.0
- Build package: io.swagger.codegen.v3.generators.go.GoClientCodegen

## Installation
Put the package under your project folder and add the following in import:
```golang
import "./swagger"
```

## Documentation for API Endpoints

All URIs are relative to *//*************/*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*AbacApi* | [**AddPolicyUsingPOST1**](docs/AbacApi.md#addpolicyusingpost1) | **Post** /api/v2/abac/policy | Create a policy
*AbacApi* | [**DeletePolicyByIdUsingDELETE1**](docs/AbacApi.md#deletepolicybyidusingdelete1) | **Delete** /api/v2/abac/policy/{id} | Delete policy by id
*AbacApi* | [**GetConditionKeyOpsUsingGET1**](docs/AbacApi.md#getconditionkeyopsusingget1) | **Get** /api/v2/abac/condition-key-op | List condition key and operators
*AbacApi* | [**GetConditionQualifierVoUsingGET1**](docs/AbacApi.md#getconditionqualifiervousingget1) | **Get** /api/v2/abac/condition-qualifier | List condition qualifiers
*AbacApi* | [**GetPoliciesUsingGET1**](docs/AbacApi.md#getpoliciesusingget1) | **Get** /api/v2/abac/policy | List policies
*AbacApi* | [**GetPolicyByIdUsingGET1**](docs/AbacApi.md#getpolicybyidusingget1) | **Get** /api/v2/abac/policy/{id} | Get policy by id
*AbacApi* | [**GetPolicyResultUsingPOST1**](docs/AbacApi.md#getpolicyresultusingpost1) | **Post** /api/v2/abac/check-policy | Check a policy
*AbacApi* | [**GetPolicyUpdateTimeUsingGET1**](docs/AbacApi.md#getpolicyupdatetimeusingget1) | **Get** /api/v2/abac/policy-update-time/{serviceName} | Get the policy update time for a service
*AbacApi* | [**GetStatementsUsingGET1**](docs/AbacApi.md#getstatementsusingget1) | **Get** /api/v2/abac/statement | List statements
*AbacApi* | [**RefreshPolicyUpdateTimeUsingPUT1**](docs/AbacApi.md#refreshpolicyupdatetimeusingput1) | **Put** /api/v2/abac/policy-update-time/{serviceName} | Refresh the policy update time for a service
*AbacApi* | [**UpdatePolicyByIdUsingPUT1**](docs/AbacApi.md#updatepolicybyidusingput1) | **Put** /api/v2/abac/policy/{id} | Update policy by id
*AccessTokensApi* | [**CreateAccessTokenUsingPOST1**](docs/AccessTokensApi.md#createaccesstokenusingpost1) | **Post** /api/v2/access-tokens | Create an access token
*AccessTokensApi* | [**DeleteAccessTokenByIdUsingDELETE1**](docs/AccessTokensApi.md#deleteaccesstokenbyidusingdelete1) | **Delete** /api/v2/access-tokens/{id} | Delete access token by id
*AccessTokensApi* | [**GetAccessTokenByIdUsingGET1**](docs/AccessTokensApi.md#getaccesstokenbyidusingget1) | **Get** /api/v2/access-tokens/{id} | Get access token by id
*AccessTokensApi* | [**GetAccessTokenByOwnerUsingGET1**](docs/AccessTokensApi.md#getaccesstokenbyownerusingget1) | **Get** /api/v2/access-tokens | Get access tokens by owner
*AccessTokensApi* | [**UpdateAccessTokenByIdUsingPUT1**](docs/AccessTokensApi.md#updateaccesstokenbyidusingput1) | **Put** /api/v2/access-tokens | Update access token by id
*AdminApi* | [**AddAdminRoleUsingPOST1**](docs/AdminApi.md#addadminroleusingpost1) | **Post** /api/v2/admin/admin-roles | Add admin role
*AdminApi* | [**AssignUsingPUT2**](docs/AdminApi.md#assignusingput2) | **Put** /api/v2/admin/admin-roles/assign | Assign admin role to user
*AdminApi* | [**DeassignUsingPUT2**](docs/AdminApi.md#deassignusingput2) | **Put** /api/v2/admin/admin-roles/deassign | Deassign admin role from user
*AdminApi* | [**DeleteAdminRoleUsingDELETE1**](docs/AdminApi.md#deleteadminroleusingdelete1) | **Delete** /api/v2/admin/admin-roles/{adminRoleName} | Delete admin role
*AdminApi* | [**GetAdminRolePermsUsingGET**](docs/AdminApi.md#getadminrolepermsusingget) | **Get** /api/v2/admin/admin-roles/{adminRoleName}/perms | Get admin role permissions
*AdminApi* | [**GetAdminRoleUsingGET1**](docs/AdminApi.md#getadminroleusingget1) | **Get** /api/v2/admin/admin-roles/{adminRoleName} | Get admin role
*AdminApi* | [**GetAdminRolesUsingGET**](docs/AdminApi.md#getadminrolesusingget) | **Get** /api/v2/admin/admin-roles | Get admin roles
*AdminApi* | [**GetAssignedAdminRolesUsingGET**](docs/AdminApi.md#getassignedadminrolesusingget) | **Get** /api/v2/admin/users/{username}/admin-roles | Get user assigned admin roles
*AdminApi* | [**GetAssignedUsersUsingGET1**](docs/AdminApi.md#getassignedusersusingget1) | **Get** /api/v2/admin/admin-roles/{adminRoleName}/assigned-users | Get admin role assigned users
*AdminApi* | [**GetPasswordPolicyUsingGET1**](docs/AdminApi.md#getpasswordpolicyusingget1) | **Get** /api/v2/admin/pwd-policy | Return password policy
*AdminApi* | [**GrantPermUsingPUT**](docs/AdminApi.md#grantpermusingput) | **Put** /api/v2/admin/admin-roles/grant | Grant admin role permission
*AdminApi* | [**HasRoleUsingGET1**](docs/AdminApi.md#hasroleusingget1) | **Get** /api/v2/admin/admin-roles/has-role | Check whether the user has all/any of the admin roles
*AdminApi* | [**RevokePermUsingPUT**](docs/AdminApi.md#revokepermusingput) | **Put** /api/v2/admin/admin-roles/revoke | Revoke admin role permission
*AdminApi* | [**UpdateAdminRoleUsingPUT1**](docs/AdminApi.md#updateadminroleusingput1) | **Put** /api/v2/admin/admin-roles | Update admin role
*AdminApi* | [**UpdatePasswordPolicyUsingPUT1**](docs/AdminApi.md#updatepasswordpolicyusingput1) | **Put** /api/v2/admin/pwd-policy | Update the password policy
*AuditApi* | [**ChangeAuditLevelUsingPUT1**](docs/AuditApi.md#changeauditlevelusingput1) | **Put** /api/v2/audit/audit-level | Change audit level
*AuditApi* | [**GetActiveAuditLevelUsingGET1**](docs/AuditApi.md#getactiveauditlevelusingget1) | **Get** /api/v2/audit/audit-level | Get audit level
*AuditApi* | [**GetAuditRecordsUsingGET1**](docs/AuditApi.md#getauditrecordsusingget1) | **Get** /api/v2/audit | Get audit records
*ChangesApi* | [**GetChangListUsingGET1**](docs/ChangesApi.md#getchanglistusingget1) | **Get** /api/v2/changes/{component} | Get component change list
*ChangesApi* | [**GetChangListUsingGET2**](docs/ChangesApi.md#getchanglistusingget2) | **Get** /api/v2/changes | Get change list
*ChangesApi* | [**GetChangeStartTimeUsingGET1**](docs/ChangesApi.md#getchangestarttimeusingget1) | **Get** /api/v2/changes/start-time | Get the start time of Change keeper
*ChangesApi* | [**InvalidateChangeUsingPOST1**](docs/ChangesApi.md#invalidatechangeusingpost1) | **Post** /api/v2/changes/invalidate | Invalidate all client caches
*ChangesApi* | [**InvalidateComponentUsingPOST1**](docs/ChangesApi.md#invalidatecomponentusingpost1) | **Post** /api/v2/changes/invalidate/{component} | Invalidate all client caches of component
*ConfigsApi* | [**ListConfigsUsingGET**](docs/ConfigsApi.md#listconfigsusingget) | **Get** /api/v2/configs | Get Guardian configuration properties
*GroupsApi* | [**AddGroupOwnerUsingPUT**](docs/GroupsApi.md#addgroupownerusingput) | **Put** /api/v2/groups/{groupName}/owner/{username} | Add owner to group
*GroupsApi* | [**AddGroupUsingPOST1**](docs/GroupsApi.md#addgroupusingpost1) | **Post** /api/v2/groups | Add group
*GroupsApi* | [**AssignGroupUsingPUT**](docs/GroupsApi.md#assigngroupusingput) | **Put** /api/v2/groups/assign | Assign group to user or group
*GroupsApi* | [**DeassignGroupUsingPUT**](docs/GroupsApi.md#deassigngroupusingput) | **Put** /api/v2/groups/deassign | Deassign group from user or group
*GroupsApi* | [**DeleteGroupOwnerUsingDELETE**](docs/GroupsApi.md#deletegroupownerusingdelete) | **Delete** /api/v2/groups/{groupName}/owner/{username} | Delete owner from group
*GroupsApi* | [**DeleteGroupUsingDELETE1**](docs/GroupsApi.md#deletegroupusingdelete1) | **Delete** /api/v2/groups/{groupName} | Delete group
*GroupsApi* | [**GetGroupLabelsUsingGET1**](docs/GroupsApi.md#getgrouplabelsusingget1) | **Get** /api/v2/groups/{groupName}/labels | Get group labels
*GroupsApi* | [**GetGroupOwnersUsingGET**](docs/GroupsApi.md#getgroupownersusingget) | **Get** /api/v2/groups/{groupName}/owners | Get group owners
*GroupsApi* | [**GetGroupUsingGET1**](docs/GroupsApi.md#getgroupusingget1) | **Get** /api/v2/groups/{groupName} | Get group
*GroupsApi* | [**GetGroupsUsingGET**](docs/GroupsApi.md#getgroupsusingget) | **Get** /api/v2/groups | Get groups
*GroupsApi* | [**TagGroupUsingPUT**](docs/GroupsApi.md#taggroupusingput) | **Put** /api/v2/groups/{groupName}/labels/{label} | Tag group
*GroupsApi* | [**UntagGroupUsingDELETE2**](docs/GroupsApi.md#untaggroupusingdelete2) | **Delete** /api/v2/groups/{groupName}/labels/{label} | Untag group
*GroupsApi* | [**UntagGroupUsingDELETE3**](docs/GroupsApi.md#untaggroupusingdelete3) | **Delete** /api/v2/groups/{groupName}/labels | Clear group labels
*GroupsApi* | [**UpdateGroupUsingPUT1**](docs/GroupsApi.md#updategroupusingput1) | **Put** /api/v2/groups/{groupName} | Update group
*HealthApi* | [**GetStatusUsingGET1**](docs/HealthApi.md#getstatususingget1) | **Get** /api/v2/health | Check health status
*LabelsApi* | [**ClearLabelPrincipalsUsingDELETE1**](docs/LabelsApi.md#clearlabelprincipalsusingdelete1) | **Delete** /api/v2/labels/{label}/principals | Remove all principals from label
*LabelsApi* | [**CreateLabelUsingPOST1**](docs/LabelsApi.md#createlabelusingpost1) | **Post** /api/v2/labels | Create a label
*LabelsApi* | [**DeleteLabelUsingDELETE1**](docs/LabelsApi.md#deletelabelusingdelete1) | **Delete** /api/v2/labels/{label} | Delete label by name
*LabelsApi* | [**GetLabelPrincipalsUsingGET1**](docs/LabelsApi.md#getlabelprincipalsusingget1) | **Get** /api/v2/labels/{label}/principals | Return principals by label
*LabelsApi* | [**GetLabelUsingGET1**](docs/LabelsApi.md#getlabelusingget1) | **Get** /api/v2/labels/{label} | Get label by name
*LabelsApi* | [**GetUserLabelsUsingGET2**](docs/LabelsApi.md#getuserlabelsusingget2) | **Get** /api/v2/labels | Return labels
*LabelsApi* | [**TagPrincipalUsingPOST1**](docs/LabelsApi.md#tagprincipalusingpost1) | **Post** /api/v2/labels/{label}/tag | Tag a principal with a label
*LabelsApi* | [**UntagPrincipalUsingPOST1**](docs/LabelsApi.md#untagprincipalusingpost1) | **Post** /api/v2/labels/{label}/untag | Untag a principal from a label
*LabelsApi* | [**UpdateLabelUsingPUT1**](docs/LabelsApi.md#updatelabelusingput1) | **Put** /api/v2/labels | Update label by name
*LoginApi* | [**AuthenticateUsingPOST**](docs/LoginApi.md#authenticateusingpost) | **Post** /api/v2/authenticate | Authenticate with Guardian account
*LoginApi* | [**GetAuthInfoUsingGET1**](docs/LoginApi.md#getauthinfousingget1) | **Get** /api/v2/auth-info | Return the authentication information
*LoginApi* | [**LoginUsingPOST**](docs/LoginApi.md#loginusingpost) | **Post** /api/v2/login | Login Guardian service
*LoginApi* | [**LogoutUsingPOST1**](docs/LoginApi.md#logoutusingpost1) | **Post** /api/v2/logout | Logout from Guardian Service
*LoginApi* | [**SingleLogoutUsingGET1**](docs/LoginApi.md#singlelogoutusingget1) | **Get** /api/v2/single-logout | Single logout
*LoginApi* | [**VerifyAccessTokenUsingPOST1**](docs/LoginApi.md#verifyaccesstokenusingpost1) | **Post** /api/v2/verify-access-token | Verify access token
*PermissionsApi* | [**AddPermUsingPOST**](docs/PermissionsApi.md#addpermusingpost) | **Post** /api/v2/perms | Add permission
*PermissionsApi* | [**CheckUsingPUT1**](docs/PermissionsApi.md#checkusingput1) | **Put** /api/v2/perms/check | Check permission
*PermissionsApi* | [**DeletePermUsingPOST**](docs/PermissionsApi.md#deletepermusingpost) | **Post** /api/v2/perms/delete | Delete permission
*PermissionsApi* | [**DeleteResourcePermsUsingPOST**](docs/PermissionsApi.md#deleteresourcepermsusingpost) | **Post** /api/v2/perms/resource-perms/delete | Delete resource permission
*PermissionsApi* | [**DeleteServicePermsUsingDELETE**](docs/PermissionsApi.md#deleteservicepermsusingdelete) | **Delete** /api/v2/perms/service-perms | Delete service permission
*PermissionsApi* | [**GetAuthorizedDataNodesUsingPOST**](docs/PermissionsApi.md#getauthorizeddatanodesusingpost) | **Post** /api/v2/perms/authorized-data-nodes | Get authorized data nodes
*PermissionsApi* | [**GetAuthorizedDataSourcesUsingPOST**](docs/PermissionsApi.md#getauthorizeddatasourcesusingpost) | **Post** /api/v2/perms/authorized-data-sources | Get authorized data sources
*PermissionsApi* | [**GetAuthorizedPermsUsingPOST**](docs/PermissionsApi.md#getauthorizedpermsusingpost) | **Post** /api/v2/perms/authorized-perms | Get authorized permissions based on principal
*PermissionsApi* | [**GetAuthorizedPrincipalsUsingPOST**](docs/PermissionsApi.md#getauthorizedprincipalsusingpost) | **Post** /api/v2/perms/authorized-principals | Get all principals to which a permission is granted
*PermissionsApi* | [**GetPermRelatedResourcesUsingPOST**](docs/PermissionsApi.md#getpermrelatedresourcesusingpost) | **Post** /api/v2/perms/resources | Get perm-related resources
*PermissionsApi* | [**GetPrincipalPermsUsingPOST**](docs/PermissionsApi.md#getprincipalpermsusingpost) | **Post** /api/v2/perms/principal-perms | Get principal permission relations based on resource
*PermissionsApi* | [**GetResourcePermActionsUsingPOST**](docs/PermissionsApi.md#getresourcepermactionsusingpost) | **Post** /api/v2/perms/resource-perm-actions | Get perm actions of a resource
*PermissionsApi* | [**GrantPermUsingPUT1**](docs/PermissionsApi.md#grantpermusingput1) | **Put** /api/v2/perms/grant | Grant permission
*PermissionsApi* | [**GrantPermsOnResourceUsingPUT**](docs/PermissionsApi.md#grantpermsonresourceusingput) | **Put** /api/v2/perms/grant-on-resource | Grant permission on one resource to specified user
*PermissionsApi* | [**ListPermissionsUsingPOST**](docs/PermissionsApi.md#listpermissionsusingpost) | **Post** /api/v2/perms/resource-perms | List permissions
*PermissionsApi* | [**RevokePermUsingPUT1**](docs/PermissionsApi.md#revokepermusingput1) | **Put** /api/v2/perms/revoke | Revoke permission
*PermissionsApi* | [**RevokePermsOnResourceUsingPUT**](docs/PermissionsApi.md#revokepermsonresourceusingput) | **Put** /api/v2/perms/revoke-on-resource | Revoke permission on one resource from specified user
*QuotasApi* | [**AddQuotaUsingPOST1**](docs/QuotasApi.md#addquotausingpost1) | **Post** /api/v2/quotas | Add quota
*QuotasApi* | [**DeleteQuotaUsingPOST**](docs/QuotasApi.md#deletequotausingpost) | **Post** /api/v2/quotas/delete | Delete quota
*QuotasApi* | [**ReadQuotaUsingPOST**](docs/QuotasApi.md#readquotausingpost) | **Post** /api/v2/quotas/read | Read quota
*QuotasApi* | [**SearchQuotasUsingPOST**](docs/QuotasApi.md#searchquotasusingpost) | **Post** /api/v2/quotas/search | Search quotas
*QuotasApi* | [**UpdateQuotaUsingPUT1**](docs/QuotasApi.md#updatequotausingput1) | **Put** /api/v2/quotas | Update quota
*ResourcesApi* | [**DeleteResourceUsingPOST**](docs/ResourcesApi.md#deleteresourceusingpost) | **Post** /api/v2/resources/delete | Delete resource
*ResourcesApi* | [**DeleteServiceResourcesUsingDELETE**](docs/ResourcesApi.md#deleteserviceresourcesusingdelete) | **Delete** /api/v2/resources/service-resources | Delete service resources
*ResourcesApi* | [**GetChildNodesUsingPOST**](docs/ResourcesApi.md#getchildnodesusingpost) | **Post** /api/v2/resources/child-nodes | Get child nodes of a resource
*ResourcesApi* | [**GetDescendantResourcesUsingPOST**](docs/ResourcesApi.md#getdescendantresourcesusingpost) | **Post** /api/v2/resources/descendants | Get descendant resources of a resource
*ResourcesApi* | [**RenameNodeUsingPUT**](docs/ResourcesApi.md#renamenodeusingput) | **Put** /api/v2/resources/rename | Rename one node of data source
*RolesApi* | [**AddRoleUsingPOST1**](docs/RolesApi.md#addroleusingpost1) | **Post** /api/v2/roles | Add role
*RolesApi* | [**AssignRoleUsingPUT**](docs/RolesApi.md#assignroleusingput) | **Put** /api/v2/roles/assign-group | Assign role to group
*RolesApi* | [**AssignRoleUsingPUT1**](docs/RolesApi.md#assignroleusingput1) | **Put** /api/v2/roles/assign-user | Assign role to user
*RolesApi* | [**DeassignRoleUsingPUT**](docs/RolesApi.md#deassignroleusingput) | **Put** /api/v2/roles/deassign-group | Assign role from group
*RolesApi* | [**DeassignRoleUsingPUT1**](docs/RolesApi.md#deassignroleusingput1) | **Put** /api/v2/roles/deassign-user | Assign role from user
*RolesApi* | [**DeleteRoleUsingDELETE1**](docs/RolesApi.md#deleteroleusingdelete1) | **Delete** /api/v2/roles/{roleName} | Delete role
*RolesApi* | [**GetRoleLabelsUsingGET1**](docs/RolesApi.md#getrolelabelsusingget1) | **Get** /api/v2/roles/{roleName}/labels | Get labels of role
*RolesApi* | [**GetRoleUsingGET1**](docs/RolesApi.md#getroleusingget1) | **Get** /api/v2/roles/{roleName} | Get role
*RolesApi* | [**GetRolesUsingGET1**](docs/RolesApi.md#getrolesusingget1) | **Get** /api/v2/roles | Get roles
*RolesApi* | [**TagRoleUsingPUT**](docs/RolesApi.md#tagroleusingput) | **Put** /api/v2/roles/{roleName}/labels/{label} | Tag role
*RolesApi* | [**UntagRoleUsingDELETE2**](docs/RolesApi.md#untagroleusingdelete2) | **Delete** /api/v2/roles/{roleName}/labels/{label} | Untag role
*RolesApi* | [**UntagRoleUsingDELETE3**](docs/RolesApi.md#untagroleusingdelete3) | **Delete** /api/v2/roles/{roleName}/labels | Clear all role labels
*RolesApi* | [**UpdateRoleUsingPUT1**](docs/RolesApi.md#updateroleusingput1) | **Put** /api/v2/roles/{roleName} | Update role
*SecurityContextApi* | [**GetSubjectUsingGET1**](docs/SecurityContextApi.md#getsubjectusingget1) | **Get** /api/v2/security-context/subject | Get subject for user
*ServicesApi* | [**DeleteServiceUsingDELETE**](docs/ServicesApi.md#deleteserviceusingdelete) | **Delete** /api/v2/services/{serviceName} | Delete service
*ServicesApi* | [**DeleteServicesUsingDELETE**](docs/ServicesApi.md#deleteservicesusingdelete) | **Delete** /api/v2/services | Delete listed services
*ServicesApi* | [**GenYarnSchedulerXmlConfUsingGET1**](docs/ServicesApi.md#genyarnschedulerxmlconfusingget1) | **Get** /api/v2/services/{yarnName}/scheduler-xml-confs/{fileName} | Generate yarn scheduler conf in xml format
*ServicesApi* | [**GetEffectiveServiceUsingGET**](docs/ServicesApi.md#geteffectiveserviceusingget) | **Get** /api/v2/services/{serviceName}/effective-service | Get effective service
*ServicesApi* | [**GetInactiveSchedulerNodesUsingGET1**](docs/ServicesApi.md#getinactiveschedulernodesusingget1) | **Get** /api/v2/services/{serviceName}/inactive-scheduler-nodes | Get inactive scheduler nodes
*ServicesApi* | [**GetSchedulerTypeUsingGET1**](docs/ServicesApi.md#getschedulertypeusingget1) | **Get** /api/v2/services/{serviceName}/scheduler-type | Get scheduler type
*ServicesApi* | [**GetServiceMappingUsingGET1**](docs/ServicesApi.md#getservicemappingusingget1) | **Get** /api/v2/services/service-mapping | Get service mapping
*ServicesApi* | [**ListServicesUsingGET1**](docs/ServicesApi.md#listservicesusingget1) | **Get** /api/v2/services | List services
*ServicesApi* | [**RefreshYarnQueuesUsingPOST1**](docs/ServicesApi.md#refreshyarnqueuesusingpost1) | **Post** /api/v2/services/{yarnName}/queues/refresh | Refresh yarn queues
*ServicesApi* | [**RegisterUsingPOST1**](docs/ServicesApi.md#registerusingpost1) | **Post** /api/v2/services/register | Register a service
*ServicesApi* | [**SearchResourceOwnersUsingPOST**](docs/ServicesApi.md#searchresourceownersusingpost) | **Post** /api/v2/services/resources/owners/search | Search resource owners
*ServicesApi* | [**SearchResourcesUsingPOST**](docs/ServicesApi.md#searchresourcesusingpost) | **Post** /api/v2/services/resources/search | Search service resources
*TrustRelationApi* | [**AddDomainUsingPOST1**](docs/TrustRelationApi.md#adddomainusingpost1) | **Post** /api/v2/trust-relations/domains | Add a domain
*TrustRelationApi* | [**AddTrustRelationUsingPOST1**](docs/TrustRelationApi.md#addtrustrelationusingpost1) | **Post** /api/v2/trust-relations | Add a trust relation
*TrustRelationApi* | [**DeleteDomainUsingDELETE1**](docs/TrustRelationApi.md#deletedomainusingdelete1) | **Delete** /api/v2/trust-relations/domains/{domainName} | Delete a domain
*TrustRelationApi* | [**DeleteTrustRelationV2UsingPOST**](docs/TrustRelationApi.md#deletetrustrelationv2usingpost) | **Post** /api/v2/trust-relations/delete | Delete a trust relation
*TrustRelationApi* | [**GetLocalDomainInfoUsingGET1**](docs/TrustRelationApi.md#getlocaldomaininfousingget1) | **Get** /api/v2/trust-relations/local-domain | Get local domain info
*TrustRelationApi* | [**ListTrustRelationsUsingGET1**](docs/TrustRelationApi.md#listtrustrelationsusingget1) | **Get** /api/v2/trust-relations | List trust relations
*TrustRelationApi* | [**UpdateDomainUsingPUT1**](docs/TrustRelationApi.md#updatedomainusingput1) | **Put** /api/v2/trust-relations/domains | update a domain
*UsersApi* | [**AddUserUsingPOST**](docs/UsersApi.md#adduserusingpost) | **Post** /api/v2/users | Add user
*UsersApi* | [**DeleteUserUsingDELETE1**](docs/UsersApi.md#deleteuserusingdelete1) | **Delete** /api/v2/users/{username} | Delete user
*UsersApi* | [**FindOwnedGroupUsingGET1**](docs/UsersApi.md#findownedgroupusingget1) | **Get** /api/v2/users/{username}/owned-groups | Get the groups owned by the user
*UsersApi* | [**GenerateKeytabUsingGET**](docs/UsersApi.md#generatekeytabusingget) | **Get** /api/v2/users/keytab | Generate user&#x27;s keytab
*UsersApi* | [**GenerateMultipleKeytabUsingPOST**](docs/UsersApi.md#generatemultiplekeytabusingpost) | **Post** /api/v2/users/multiple-keytab | Generate multiple users&#x27; keytab
*UsersApi* | [**GeneratePasswordUsingGET1**](docs/UsersApi.md#generatepasswordusingget1) | **Get** /api/v2/users/suggested-password | Generate user password
*UsersApi* | [**GetKrb5PrincipalsUsingGET**](docs/UsersApi.md#getkrb5principalsusingget) | **Get** /api/v2/users/principals | Get krb5 principals
*UsersApi* | [**GetUserLabelsUsingGET3**](docs/UsersApi.md#getuserlabelsusingget3) | **Get** /api/v2/users/{username}/labels | Get user labels
*UsersApi* | [**GetUserUsingGET1**](docs/UsersApi.md#getuserusingget1) | **Get** /api/v2/users/{username} | Get user
*UsersApi* | [**GetUsersUsingGET**](docs/UsersApi.md#getusersusingget) | **Get** /api/v2/users | Get users
*UsersApi* | [**LockUsingPUT**](docs/UsersApi.md#lockusingput) | **Put** /api/v2/users/{username}/lock | Lock user
*UsersApi* | [**ResetPasswordUsingPUT**](docs/UsersApi.md#resetpasswordusingput) | **Put** /api/v2/users/{username}/reset-password | Reset user password
*UsersApi* | [**TagUserUsingPUT**](docs/UsersApi.md#taguserusingput) | **Put** /api/v2/users/{username}/labels/{label} | Tag user
*UsersApi* | [**UnlockUsingPUT**](docs/UsersApi.md#unlockusingput) | **Put** /api/v2/users/{username}/unlock | Unlock user
*UsersApi* | [**UntagUserUsingDELETE2**](docs/UsersApi.md#untaguserusingdelete2) | **Delete** /api/v2/users/{username}/labels/{label} | Untag user
*UsersApi* | [**UntagUserUsingDELETE3**](docs/UsersApi.md#untaguserusingdelete3) | **Delete** /api/v2/users/{username}/labels | Clear user tags
*UsersApi* | [**UpdatePasswordUsingPUT1**](docs/UsersApi.md#updatepasswordusingput1) | **Put** /api/v2/users/{username}/update-password | Update user password
*UsersApi* | [**UpdateUserUsingPUT1**](docs/UsersApi.md#updateuserusingput1) | **Put** /api/v2/users/{username} | Update user

## Documentation For Models

 - [AccessTokenVo](docs/AccessTokenVo.md)
 - [ActionContainerVo](docs/ActionContainerVo.md)
 - [ActionGrantOptionVo](docs/ActionGrantOptionVo.md)
 - [AdminPermVo](docs/AdminPermVo.md)
 - [AdminRolePermVo](docs/AdminRolePermVo.md)
 - [AdminRoleVo](docs/AdminRoleVo.md)
 - [AuditRecord](docs/AuditRecord.md)
 - [AuthorizedPrincRequestVo](docs/AuthorizedPrincRequestVo.md)
 - [AuthorizedResourceRequestVo](docs/AuthorizedResourceRequestVo.md)
 - [ChangeAuditLevelRequestVo](docs/ChangeAuditLevelRequestVo.md)
 - [ChangeList](docs/ChangeList.md)
 - [CheckPrincPermVo](docs/CheckPrincPermVo.md)
 - [CheckResultVo](docs/CheckResultVo.md)
 - [ConditionKeyOpVo](docs/ConditionKeyOpVo.md)
 - [ConditionKeyVo](docs/ConditionKeyVo.md)
 - [ConditionOpVo](docs/ConditionOpVo.md)
 - [ConditionQualifierVo](docs/ConditionQualifierVo.md)
 - [ConditionVo](docs/ConditionVo.md)
 - [DomainVo](docs/DomainVo.md)
 - [EnvContextVo](docs/EnvContextVo.md)
 - [File](docs/File.md)
 - [GroupRoleVo](docs/GroupRoleVo.md)
 - [GroupVo](docs/GroupVo.md)
 - [GuardianServerVo](docs/GuardianServerVo.md)
 - [HealthStatus](docs/HealthStatus.md)
 - [InputStream](docs/InputStream.md)
 - [KeytabPrincipalVo](docs/KeytabPrincipalVo.md)
 - [LabelVo](docs/LabelVo.md)
 - [LoginRequestVo](docs/LoginRequestVo.md)
 - [NodeVo](docs/NodeVo.md)
 - [PermGrantOptionVo](docs/PermGrantOptionVo.md)
 - [PermVo](docs/PermVo.md)
 - [PolicyCheckVo](docs/PolicyCheckVo.md)
 - [PolicyVo](docs/PolicyVo.md)
 - [PrincGroupVo](docs/PrincGroupVo.md)
 - [PrincPermOnResourceVo](docs/PrincPermOnResourceVo.md)
 - [PrincPermRequestVo](docs/PrincPermRequestVo.md)
 - [PrincPermVo](docs/PrincPermVo.md)
 - [PrincipalContainerVo](docs/PrincipalContainerVo.md)
 - [PrincipalVo](docs/PrincipalVo.md)
 - [PwPolicyVo](docs/PwPolicyVo.md)
 - [QuotaVo](docs/QuotaVo.md)
 - [RenameNodeRequestVo](docs/RenameNodeRequestVo.md)
 - [Resource](docs/Resource.md)
 - [ResourceContainerVo](docs/ResourceContainerVo.md)
 - [ResourceEntry](docs/ResourceEntry.md)
 - [ResourceLookupVo](docs/ResourceLookupVo.md)
 - [ResourceOwnerEntry](docs/ResourceOwnerEntry.md)
 - [ResourceOwnerRequestVo](docs/ResourceOwnerRequestVo.md)
 - [ResourcePermRequestVo](docs/ResourcePermRequestVo.md)
 - [ResourceRequestVo](docs/ResourceRequestVo.md)
 - [ResourceSearchRequestVo](docs/ResourceSearchRequestVo.md)
 - [ResourceVo](docs/ResourceVo.md)
 - [RoleVo](docs/RoleVo.md)
 - [SearchResultAuditRecord](docs/SearchResultAuditRecord.md)
 - [SearchResultResourceOwnerEntry](docs/SearchResultResourceOwnerEntry.md)
 - [ServiceVo](docs/ServiceVo.md)
 - [SessionVo](docs/SessionVo.md)
 - [StatementVo](docs/StatementVo.md)
 - [SubjectVo](docs/SubjectVo.md)
 - [Timestamp](docs/Timestamp.md)
 - [TrustRelationVo](docs/TrustRelationVo.md)
 - [UpdatePasswordRequestVo](docs/UpdatePasswordRequestVo.md)
 - [Uri](docs/Uri.md)
 - [Url](docs/Url.md)
 - [UserAdminRoleVo](docs/UserAdminRoleVo.md)
 - [UserRoleVo](docs/UserRoleVo.md)
 - [UserVo](docs/UserVo.md)

## Documentation For Authorization
 Endpoints do not require authorization.


## Author


