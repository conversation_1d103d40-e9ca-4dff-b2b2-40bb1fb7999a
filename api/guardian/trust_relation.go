package trust

import (
	"github.com/antihax/optional"
)

type TrustParams struct {
	SourceGuardianPackage GuardianPackage
	TargetGuardianPackage GuardianPackage
	RelationType          string
}

type DeleteTrustRelationReq struct {
	TargetTenantName string `json:"target_tenant_name"`
	GuardianName     string `json:"guardian_name"`
	GuardianPassword string `json:"guardian_password"`
	Type             string `json:"relation_type"`
}

type DeleteParamReq struct {
	ResourceVo *ResourceVo `json:"resourceVo,omitempty"`
}

type AddPermsReq struct {
	ResourceVo       *ResourceVo `json:"resourceVo,omitempty"`
	GuardianUsername string      `json:"guardianUsername,omitempty"`
	GuardianPassword string      `json:"guardianPassword,omitempty"`
}

type DeletePermsReq struct {
	ResourceVo       *ResourceVo `json:"resourceVo,omitempty"`
	GuardianUsername string      `json:"guardianUsername,omitempty"`
	GuardianPassword string      `json:"guardianPassword,omitempty"`
}

type ResourceVo struct {
	// The NodeVo list representing the resource
	DataSource []NodeVo `json:"dataSource"`
	// Service name tdc tenantUid
	ServiceName string `json:"serviceName"`
}

type NodeVo struct {
	// Node value
	//Value string `json:"value"`
	ServiceSid  string `json:"serviceSid"`
	ClusterName string `json:"clusterName"`
	//tdh tenantuid
	TenantUid string `json:"tenantUid"`
}

type GrantReq struct {
	Perm *PermVo `json:"perm,omitempty"`
}

type PermVoResponse struct {
	ActionResources []ActionResource `json:"actionResources"`
}

type ActionResource struct {
	Action string `json:"action"`
	//带有AllData后缀代表返回值中，guardian已经返回了正确的值，但是为了提供给eco/tcc需要将最里面的value字段进行切割
	ResourceVo *ResourceAllData `json:"resourceVo,omitempty"`
}

type ResourceAllData struct {
	DataSource []NodeAllData `json:"dataSource"`
	// Service name
	ServiceName string `json:"serviceName"`
	ServiceType string `json:"serviceType"`
}

type NodeAllData struct {
	// Node value
	//Value string `json:"value"`
	Type string `json:"type"`
	//返回值需要将guardian的返回值进行拆分，value按照@符号切割成以下三个字段
	ServiceName string `json:"serviceName"`
	ClusterName string `json:"clusterName"`
	TenantUid   string `json:"tenantUid"`
	ServiceSid  string `json:"serviceSid"`
}

type PermVo struct {
	ResourceVo *ResourceVo `json:"resourceVo,omitempty"`
}

type GetDataSourceReq struct {
	ResourceVo *ResourceVo `json:"resourceVo,omitempty"`
}

type AddTrustRelationReq struct {
	Creator          string `json:"creator"`
	GuardianName     string `json:"guardian_name"`
	GuardianPassword string `json:"guardian_password"`
	TargetTenantName string `json:"target_tenant_name"`
	Type             string `json:"type"`
}

type GuardianPackage struct {
	client                   *GuardianClient
	Name                     string             `json:"name"`
	Namespace                string             `json:"namespace"`
	Application              string             `json:"application"`
	K8sGuardianServerAddress string             `json:"k8s_guardian_server_address"`
	GuardianServerAddress    string             `json:"guardian_server_address"`
	NetworkDomainSuffix      string             `json:"network_domain_suffix"`
	DSAdminUsername          string             `json:"ds_admin_username"`
	DSAdminPassword          string             `json:"ds_admin_password"`
	DSAccessToken            string             `json:"ds_access_token"`
	Trusted                  []GuardianPackage  `json:"trusted"`
	Trusting                 []GuardianPackage  `json:"trusting"`
	Kerberos                 Kerberos           `json:"kerberos"`
	SecurityUsersMeta        []UserSecurityMeta `json:"security_users_meta"`
}

func (p *GuardianPackage) GetGuardianClient() (*GuardianClient, error) {
	err := p.ensureInited()
	if err != nil {
		return nil, err
	}
	return p.client, nil
}

// init and login
func (p *GuardianPackage) ensureInited() error {
	if p.client == nil {
		c, err := InitGuardianV2Client(p.K8sGuardianServerAddress)
		if err != nil {
			return err
		}
		p.client = c
		//_, err = p.client.Login(
		//	guardianv2.LoginRequestVo{
		//		Username: p.DSAdminUsername,
		//		Password: p.DSAdminPassword,
		//	},
		//)
		//if err != nil {
		//	return err
		//}
	}
	return nil
}

// RequestKeyTab from this guardian package given a list of principals
// principals are like hive/tos_tenant-1
func (p *GuardianPackage) RequestKeyTab(principals []string) ([]byte, error) {
	if err := p.ensureInited(); err != nil {
		return nil, err
	}

	// we don't require explicit base64 encoding here
	// because golang marshals []byte to base64 automatically (when we apply secret to k8s)
	res, err := p.client.RequestKeytab(principals, false)
	return res, err
}

type Kerberos struct {
	TenantName          string       `json:"tenant_name"`
	Realm               string       `json:"realm"`
	PrincipalSuffix     string       `json:"principal_suffix"`
	KDCAddresses        []string     `json:"kdc_addresses"`
	Hosts               []string     `json:"hosts"`
	NetworkDomainSuffix string       `json:"network_domain_suffix"`
	TrustsKrbConfigs    []KerbConfig `json:"trusts_krb_configs"`
}

type KerberosServerConfig struct {
	TenantUid           string
	KdcAddress          []string
	Hosts               []string
	Realm               string
	NetworkDomainSuffix string
	TrustedKerberoses   []string
	PrincipalHost       string
}

type KerbConfig struct {
	TenantName          string   `json:"tenant_name"`
	Hosts               []string `json:"hosts"`
	Realm               string   `json:"realm"`
	PrincipalSuffix     string   `json:"principal_suffix"`
	KDCAddresses        []string `json:"kdc_addresses"`
	NetworkDomainSuffix string   `json:"network_domain_suffix"`
}

type UserSecurityMeta struct {
	UserName string   `json:"user_name"`
	Secrets  []string `json:"secrets"`
}

type TenantManagementKrb5 struct {
	SourceType  string   `json:"source_type"`
	Realm       string   `json:"realm"`
	Hosts       []string `json:"hosts"`
	GuardianUrl string   `json:"guardian_url"`
	Kdc         []string `json:"kdc"`
}

type Secret struct {
	Namespace string
	Name      string
	Data      map[string]string
}

type TDHGuardianKerberos struct {
	Username    string
	Password    string
	GuardianUrl string
	Kdc         []string
	Hosts       []string
	Realm       string
}

type DiscoverSystemGuardianJob struct {
	TenantUid                 string
	ProvidedMetaAttrValMap    map[string]string
	UsersSecurityMetaRequired bool
	TrustedRequired           bool
	TrustingRequired          bool
}

type DiscoverGuardianJob struct {
	Namespace      string
	ReleaseName    string
	StatusRequired bool
	ConfigRequired bool
}

type TrustRelation struct {
	SourceGuardianPackage GuardianPackage
	TargetGuardianPackage GuardianPackage
	TrustRelationType     string
	Creator               string
}

type TrustRelationsForTenant struct {
	TenantUid         string
	TargetTenantName  string
	Creators          string
	TrustRelationType string
	Username          string
	Password          string
}

type GuardianTrustRelation struct {
	TenantUid                  string
	ComponentInstanceName      string
	TrustTenantUid             string
	TrustComponentInstanceName string
	Creator                    string
	CreatedTime                string
	ModifiedTime               string
}

type KerberosPrincipal struct {
	// KerberosPrincipal的属性和方法
}

type KerberosSecretConfig struct {
	// KerberosSecretConfig的属性和方法
}

type KerberosApplicationServerConfig struct {
	// KerberosApplicationServerConfig的属性和方法
}

type KerberosConfig struct {
	AuthType          string
	Principals        []KerberosPrincipal
	SecretConfig      KerberosSecretConfig
	AsAppServerConfig KerberosApplicationServerConfig
	Server            KerberosServerConfig
	OperationServer   GuardianPackage
}

type SecretsKerberosConfig struct {
	GuardianPackage    GuardianPackage
	Modifier           string
	KerberosesToAdd    []KerberosServerConfig
	KerberosesToDelete []KerberosServerConfig
	RestartRequired    bool
}

type DataSource string

const (
	DataSourceGlobal DataSource = "GLOBAL"
	DataSourcePath   DataSource = "PATH"
)

type GlobalPermission string

const (
	GlobalPermissionRead    GlobalPermission = "READ"
	GlobalPermissionWrite   GlobalPermission = "WRITE"
	GlobalPermissionExecute GlobalPermission = "EXECUTE"
	GlobalPermissionAccess  GlobalPermission = "ACCESS"
	GlobalPermissionAdmin   GlobalPermission = "ADMIN"
)

type PermissionVo struct {
	Action         string   `json:"action"`
	Component      string   `json:"component"`
	DataSources    []string `json:"data_sources"`
	Administrative bool     `json:"administrative,omitempty"`
	Grantable      bool     `json:"grantable,omitempty"`
	Heritable      bool     `json:"heritable,omitempty"`
}

type EntityPermissionVo struct {
	Name          string       `json:"name"`
	PermissionVo  PermissionVo `json:"permission_vo"`
	PrincipalType string       `json:"principal_type,omitempty"`
}

type PrincipalsVo struct {
	// group principals
	Groups []string `json:"groups"`
	// role principals
	Roles []string `json:"roles"`
	// user principals
	Users []string `json:"users"`
}

type PermsApiDeletePermissionUsingDELETEOpts struct {
	Action     optional.String
	DataSource optional.Interface
}

type NGWalmException struct {
	Code      int    `json:"status_code"`
	ErrorCode string `json:"error_code"`
	Msg       string `json:"msg"`
	Details   string `json:"detail"`
	Request   string `json:"request"`
}
