package asset

import (
	"context"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/helper"
)

func (r *Resource) GetModuleData(request *restful.Request, response *restful.Response) {
	projectId := request.QueryParameter(ProjectIdParam)

	key := request.QueryParameter(KeyParam)
	if len(key) == 0 {
		helper.ErrorResponse(response, stderr.BadRequest.Error("缺少请求参数 key"))
		return
	}

	rscTypes := request.QueryParameter(RscTypesParam)

	assetReq := &pb.AssetReq{
		ProjectId: projectId,
		Key:       key,
	}

	assetResp, err := r.as.GetModuleData(assetReq, rscTypes)

	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取所有数据异常"))
		return
	}
	helper.SuccessStringResponse(response, assetResp)
}

func (r *Resource) Search(request *restful.Request, response *restful.Response) {
	key := request.QueryParameter(KeyParam)
	if len(key) == 0 {
		helper.ErrorResponse(response, stderr.BadRequest.Error("缺少请求参数 key"))
		return
	}

	username := helper.ParseUsername(request)
	projs, err := r.ps.ListProjects(username, "")
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取项目所有数据异常"))
		return
	}
	projIDs := make([]string, 0)
	for _, p := range projs {
		projIDs = append(projIDs, p.ProjectId)
	}

	searchResp, err := r.ss.Search(context.Background(), key, projIDs)

	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "执行搜索异常"))
		return
	}
	helper.SuccessStringResponse(response, searchResp)
}
