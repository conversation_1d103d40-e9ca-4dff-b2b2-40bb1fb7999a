package alerting

import (
	"net/http"

	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models/alerting"
	alsvc "transwarp.io/applied-ai/central-auth-service/service/alerting"
)

func (r *Resource) WebService() *restful.WebService {
	tags := []string{"服务预警"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(helper.PathAlertRules)
	ws.Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	projectIDQueryParam := ws.QueryParameter(QueryParamProjectId, "空间id").Required(true)

	ws.Route(ws.GET("/").To(r.ListRules).
		Doc("获取预警规则列表").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Param(ws.QueryParameter(QueryParamServiceId, "服务id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), alsvc.ListRulesRsp{}))

	ws.Route(ws.POST("/").To(r.CreateRule).
		Doc("创建预警规则").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Reads(alerting.AlertRule{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), alerting.AlertRule{}))
	ws.Route(ws.DELETE("/").To(r.DeleteRule).
		Doc("删除预警规则").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Param(ws.QueryParameter(QueryParamRuleId, "预警规则id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), alsvc.ListRulesRsp{}))

	ws.Route(ws.PUT("/").To(r.UpdateRule).
		Doc("更新预警规则").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Reads(alerting.AlertRule{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), alerting.AlertRule{}))

	ws.Route(ws.PATCH("/paused").To(r.PatchRulePaused).
		Doc("更新预警规则的停用状态").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Reads(PatchRulePausedReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), true))

	ws.Route(ws.POST("/exists").To(r.Exists).
		Doc("判断预警规则是否存在").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Reads(RuleExistsReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), RuleExistsRsp{}))

	return ws
}
