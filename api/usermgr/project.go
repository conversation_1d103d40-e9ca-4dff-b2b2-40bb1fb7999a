package usermgr

import (
	"encoding/json"
	"strconv"
	"time"

	"github.com/dlclark/regexp2"
	"github.com/emicklei/go-restful/v3"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/utils"
	"transwarp.io/applied-ai/central-auth-service/utils/auth/cas/client"
)

func (r *Projmgr) ListProjects(request *restful.Request, response *restful.Response) {
	permissionCode := request.QueryParameter(helper.ParamPermissionCode)
	currUsername := client.Username(request.Request)
	projects, err := r.ps.ListProjects(currUsername, permissionCode)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "列出所有空间失败"))
		return
	}
	helper.SuccessResponse(response, projects)
}

func (r *Projmgr) ListAllProjects(request *restful.Request, response *restful.Response) {
	query := make(map[string]interface{})
	tenantUid := request.QueryParameter(helper.TenantId)
	if tenantUid != "" {
		query["tenant_uid"] = tenantUid
	}

	projects, err := r.ps.QueryList(query)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "列出所有空间失败"))
		return
	}
	helper.SuccessResponse(response, projects)
}

func (r *Projmgr) CreateProject(request *restful.Request, response *restful.Response) {
	projectReq := new(models.ProjectReq)
	if err := request.ReadEntity(projectReq); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("请求体解析失败"))
		return
	}
	// if len(projectReq.Managers) == 0 {
	// 	helper.ErrorResponse(response, stderr.BadRequest.Errorf("空间管理员不能为空"))
	// 	return
	// }
	// todo 待添加锁实现并发安全性
	existProject, _ := r.ps.GetProjectByName(projectReq.Name)
	if existProject != nil && existProject.Id != 0 {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("%s空间已存在，请重新换个空间名称", projectReq.Name))
		return
	}
	re := regexp2.MustCompile(`^(?=.{1,63}$)[a-z]([-a-z0-9]*[a-z0-9])?$`, 0)
	match, err := re.MatchString(projectReq.ProjectId)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	if projectReq.ProjectId == "" || !match {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("%s空间id非法，请检查输入", projectReq.ProjectId))
		return
	}
	existProject, _ = r.ps.GetProjectById(projectReq.ProjectId)
	if existProject != nil && existProject.Id != 0 {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("%s空间id已存在，请重新输入", projectReq.ProjectId))
		return
	}
	if projectReq.CreateUser == "" {
		projectReq.CreateUser = client.Username(request.Request)
	}
	if err := r.ps.CreateProject(projectReq); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("空间创建失败"))
		return
	}
	project := &models.Project{
		ProjectId:   projectReq.ProjectId,
		Name:        projectReq.Name,
		Description: projectReq.Description,
		Labels:      utils.MapToJsonStr(projectReq.Labels),
		Industry:    projectReq.Industry,
		Logo:        projectReq.Logo,
		CreateUser:  projectReq.CreateUser,
		CreateTime:  time.Now(),
		UpdateTime:  time.Now(),
		TenantUid:   projectReq.TenantUid,
		Examine:     0,
	}
	helper.SuccessResponse(response, project)
}

func (r *Projmgr) GetProject(request *restful.Request, response *restful.Response) {
	projectId := request.PathParameter(helper.PathProjectID)
	if projectId == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的空间ID"))
		return
	}
	project, err := r.ps.GetProjectRespById(projectId)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "空间获取失败，无效的空间ID：%s", projectId))
		return
	}
	helper.SuccessResponse(response, project)
}

func (r *Projmgr) ExistsProject(request *restful.Request, response *restful.Response) {
	projectId := request.PathParameter(helper.PathProjectID)
	if projectId == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的空间ID"))
		return
	}
	exists, err := r.ps.ExistsProjectId(projectId)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "空间获取失败", projectId))
		return
	}
	helper.SuccessResponse(response, models.ProjectExistsRsp{Exists: exists})
}

func (r *Projmgr) UpdateProject(request *restful.Request, response *restful.Response) {
	projectId := request.PathParameter(helper.PathProjectID)
	if projectId == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的空间ID"))
	}
	projectReq := new(models.ProjectReq)
	if err := request.ReadEntity(projectReq); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "请求体解析失败"))
		return
	}

	project, err := r.ps.GetProjectById(projectId)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "查询空间失败"))
		return
	}
	project.Name = projectReq.Name
	project.Description = projectReq.Description
	labelsStr := utils.MapToJsonStr(projectReq.Labels)
	project.Labels = labelsStr
	project.Industry = projectReq.Industry
	project.Logo = projectReq.Logo
	project.UpdateTime = time.Now()
	if err := r.ps.SaveProject(project); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "更新空间失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Projmgr) DeleteProject(request *restful.Request, response *restful.Response) {
	projectId := request.PathParameter(helper.PathProjectID)
	if projectId == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的空间ID"))
	}
	if err := r.ps.DeleteProjectById(projectId); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "删除空间失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Projmgr) ListProjectMembers(request *restful.Request, response *restful.Response) {
	req := &models.ProjectMemberListReq{
		ProjectId:   request.PathParameter(helper.PathProjectID),
		PhoneNumber: request.QueryParameter("phone_number"),
		Name:        request.QueryParameter("name"),
		FullName:    request.QueryParameter("full_name"),
	}

	if req.ProjectId == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的空间ID"))
	}
	projectMembers, err := r.pms.ListProjectMembers(req)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "根据空间ID列出所有空间成员失败"))
		return
	}
	helper.SuccessResponse(response, projectMembers)
}

func (r *Projmgr) AddProjectMember(request *restful.Request, response *restful.Response) {
	projectId := request.PathParameter(helper.PathProjectID)
	if projectId == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的空间ID"))
	}
	projectMemberReqs := make([]*models.ProjectMemberReq, 0)
	decoder := json.NewDecoder(request.Request.Body)
	if err := decoder.Decode(&projectMemberReqs); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	if m, ok := models.IsMembersUniq(projectMemberReqs); !ok {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("成员出现多次: %s: %s", m.UserType, m.Name))
		return
	}
	currUsername := client.Username(request.Request)
	if err := r.pms.CreateProjectMembers(projectMemberReqs, projectId, currUsername); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("空间创建失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Projmgr) UpdateProjectMember(request *restful.Request, response *restful.Response) {
	projectId := request.PathParameter(helper.PathProjectID)
	if projectId == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的空间ID"))
	}
	projectMemberReqs := make([]*models.ProjectMemberReq, 0)
	decoder := json.NewDecoder(request.Request.Body)
	if err := decoder.Decode(&projectMemberReqs); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	if m, ok := models.IsMembersUniq(projectMemberReqs); !ok {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("成员出现多次: %s: %s", m.UserType, m.Name))
		return
	}

	currUsername := client.Username(request.Request)
	// 获取空间创建人
	projectResp, _ := r.ps.GetProjectRespById(projectId)
	if projectResp == nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("空间不存在，projectId: %s", projectResp.ProjectId))
		return
	}
	projectCreateUser := projectResp.CreateUser
	// 空间创建人不能编辑空间角色，不能从空间成员列表删除
	isCreateUserExist := false
	for _, projectMemberReq := range projectMemberReqs {
		if projectCreateUser == projectMemberReq.Name {
			isCreateUserExist = true
			if uint64(helper.ProjectOwnerID) != projectMemberReq.RoleId {
				helper.ErrorResponse(response, stderr.BadRequest.Error("空间创建人不能编辑空间角色，%s，%s",
					projectCreateUser, strconv.FormatUint(projectMemberReq.RoleId, 10)))
				return
			}
		}
	}
	if !isCreateUserExist {
		helper.ErrorResponse(response, stderr.BadRequest.Error("空间创建人不能从空间成员列表删除，%s", projectCreateUser))
		return
	}
	if err := r.pms.UpdateProjectMembers(projectMemberReqs, projectId, currUsername); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("空间编辑失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Projmgr) DeleteProjectMember(request *restful.Request, response *restful.Response) {
	projectId := request.PathParameter(helper.PathProjectID)
	if projectId == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的空间ID"))
	}
	projectMemberReq := new(models.ProjectMemberReq)
	if err := request.ReadEntity(projectMemberReq); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	// 获取空间创建人
	projectResp, _ := r.ps.GetProjectRespById(projectId)
	if projectResp == nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("空间不存在，projectId: %s", projectResp.ProjectId))
		return
	}
	projectCreateUser := projectResp.CreateUser
	// 空间创建人不能从空间成员列表删除
	if projectCreateUser == projectMemberReq.Name {
		helper.ErrorResponse(response, stderr.BadRequest.Error("空间创建人不能从空间成员列表删除，%s", projectCreateUser))
		return
	}
	if err := r.pms.DeleteProjectMemberById(projectMemberReq, projectId); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "删除空间失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Projmgr) BatchDeleteProjectMembers(request *restful.Request, response *restful.Response) {
	projectId := request.PathParameter(helper.PathProjectID)
	if projectId == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的空间ID"))
	}
	projectMemberReqs := make([]*models.ProjectMemberReq, 0)
	decoder := json.NewDecoder(request.Request.Body)
	if err := decoder.Decode(&projectMemberReqs); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	// 获取空间创建人
	projectResp, _ := r.ps.GetProjectRespById(projectId)
	if projectResp == nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("空间不存在，projectId: %s", projectResp.ProjectId))
		return
	}
	projectCreateUser := projectResp.CreateUser
	// 空间创建人不能从空间成员列表删除
	for _, projectMemberReq := range projectMemberReqs {
		if projectCreateUser == projectMemberReq.Name {
			helper.ErrorResponse(response, stderr.BadRequest.Error("%s空间创建人不能从空间成员列表删除，%s",
				projectCreateUser, projectMemberReq.RoleId))
			return
		}
	}
	if err := r.pms.BatchDeleteProjectMembers(projectMemberReqs, projectId); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "批量删除空间失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Projmgr) ListProjectUsers(request *restful.Request, response *restful.Response) {
	projectId := request.PathParameter(helper.PathProjectID)
	if projectId == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Error("空间ID不能为空"))
		return
	}
	permissionCode := request.QueryParameter(helper.ParamPermissionCode)
	permissionAction := request.QueryParameter(helper.ParamPermissionAction)
	projectUsers, err := r.pms.ListProjectUsers(projectId, permissionCode, permissionAction)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, projectUsers)
}

func (r *Projmgr) ListProjectGroups(request *restful.Request, response *restful.Response) {
	projectId := request.PathParameter(helper.PathProjectID)
	if projectId == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("空间ID不能为空"))
		return
	}
	code := request.QueryParameter(helper.ParamPermissionCode)
	act := request.QueryParameter(helper.ParamPermissionAction)
	groups, err := r.pms.ListProjectGroups(projectId, code, act)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, groups)
}

func (r *Projmgr) RebuildProjectCategory(request *restful.Request, response *restful.Response) {
	categories := new([]*models.ProjectCategory)
	if err := request.ReadEntity(categories); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}

	res, err := r.pcs.RebuildProjectCategory(*categories)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}

func (r *Projmgr) CreateProjectCategory(request *restful.Request, response *restful.Response) {
	categoryReq := new(models.ProjectCategory)
	if err := request.ReadEntity(categoryReq); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}

	category, err := r.pcs.CreateProjectCategory(categoryReq)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, category)
}

func (r *Projmgr) ListProjectCategories(request *restful.Request, response *restful.Response) {
	categories, err := r.pcs.ListProjectCategories()
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, categories)
}

func (r *Projmgr) GetProjectCategory(request *restful.Request, response *restful.Response) {
	idstr := request.PathParameter("id")
	id, err := strconv.ParseUint(idstr, 10, 64)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	projectCategory, err := r.pcs.GetProjectCategory(id)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, projectCategory)
}

func (r *Projmgr) UpdateProjectCategory(request *restful.Request, response *restful.Response) {
	idstr := request.PathParameter("id")
	id, err := strconv.ParseUint(idstr, 10, 64)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	projectCategoryReq := new(models.ProjectCategory)
	if err := request.ReadEntity(projectCategoryReq); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}

	err = r.pcs.UpdateProjectCategory(projectCategoryReq, id)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	projectCategory, err := r.pcs.GetProjectCategory(id)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, projectCategory)
}

func (r *Projmgr) ExamineSwitch(request *restful.Request, response *restful.Response) {
	projectId := request.PathParameter(helper.PathProjectID)
	if projectId == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Error("空间ID不能为空"))
		return
	}
	on := request.QueryParameter(helper.QueryOn)
	if on == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Error("need参数不能为空"))
		return
	}
	project, err := r.ps.GetProjectById(projectId)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("获取空间[%s]错误", projectId))
		return
	}
	project.Examine = func() int {
		if on == "true" {
			return 1
		} else {
			return 0
		}
	}()
	err = r.ps.SaveProject(project)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("更新空间[%s]错误", projectId))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Projmgr) DeleteProjectCategory(request *restful.Request, response *restful.Response) {
	idstr := request.PathParameter("id")
	id, err := strconv.ParseUint(idstr, 10, 64)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	projectCategory, err := r.pcs.GetProjectCategory(id)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	err = r.pcs.DeleteProjectCategory(id)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, projectCategory)
}
