package usermgr

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"path/filepath"
	"regexp"
	"strconv"
	"time"

	"github.com/emicklei/go-restful/v3"

	stdauth "transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	guardianv2 "transwarp.io/applied-ai/central-auth-service/api/guardian/client"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/rbac"
	"transwarp.io/applied-ai/central-auth-service/utils"
	"transwarp.io/applied-ai/central-auth-service/utils/auth/cas/client"
)

func (r *Usrmgr) ListUsers(request *restful.Request, response *restful.Response) {
	var userRespArr []*models.UserResp
	var err error
	username := helper.UsernameQP.GetValue(request)
	if username != "" {
		user := r.us.GetUserByName(username)
		if user == nil || user.Name == "" {
			helper.ErrorResponse(response, stderr.InvalidParam.Errorf("User %s not found", username))
			return
		}
		userRespArr, err = r.us.ConvertUsers([]*dao.User{user}, true, "")
	} else {
		req := &models.UserListReq{
			UsernameLike: request.QueryParameter("username_like"),
			FullName:     request.QueryParameter("full_name"),
			PhoneNumber:  request.QueryParameter("phone_number"),
			ProjectId:    request.QueryParameter("project_id"),
		}
		userRespArr, err = r.us.ListUsers(r.gus, r.sus, req)
	}
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取用户列表失败"))
		return
	}
	helper.SuccessResponse(response, userRespArr)
}

func (r *Usrmgr) CountUsers(request *restful.Request, response *restful.Response) {
	userRespArr, err := r.us.ListUsers(r.gus, r.sus, &models.UserListReq{})
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取用户数量"))
		return
	}
	helper.SuccessResponse(response, models.UserCountResp{Count: len(userRespArr)})
}

var isPhone = regexp.MustCompile(`^[0-9+-]+$`)

func (r *Usrmgr) CreateUser(request *restful.Request, response *restful.Response) {
	userReq := new(models.UserReq)
	if err := request.ReadEntity(userReq); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	if userReq.ExpirationTimeSelect == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Cause(nil, "有效期不能为空"))
		return
	}
	err := userReq.SetExpriationTime()
	if err != nil {
		helper.ErrorResponse(response, stderr.InvalidParam.Cause(err, "请求体解析失败"))
		return
	}
	for _, wip := range userReq.WhiteIps {
		ip := net.ParseIP(wip)
		if ip == nil {
			helper.ErrorResponse(response, stderr.InvalidParam.Cause(nil, "IP 格式异常"))
			return
		}
		if ip.IsLoopback() {
			helper.ErrorResponse(response, stderr.InvalidParam.Cause(nil, "IP 不能是环回地址"))
			return
		}
	}
	if userReq.DefaultProject == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Cause(nil, "默认空间不能为空"))
		return
	}
	if userReq.PhoneNumber != "" && !isPhone.MatchString(userReq.PhoneNumber) {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("手机号码格式错误"))
		return
	}

	currUsername := client.Username(request.Request)
	if conf.C.UserStore.Type == models.Local.String() {
		user := r.us.GetUserByName(userReq.UserName)
		if user != nil && user.ID != 0 {
			helper.ErrorResponse(response, stderr.BadRequest.Error("已经存在同名的用户%s", userReq.UserName))
			return
		}
		if userReq.Status != models.Enable && userReq.Status != models.Disable {
			helper.ErrorResponse(response, stderr.InvalidParam.Cause(nil, "无效的用户状态"))
			return
		}
	}
	if err := r.us.CreateUser(currUsername, userReq, r.gus); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "创建用户失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Usrmgr) UpdateUser(request *restful.Request, response *restful.Response) {
	userReq := new(models.UserReq)
	if err := request.ReadEntity(userReq); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	username := request.PathParameter(helper.FormUsername)
	if username == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户名"))
		return
	}
	if userReq.PhoneNumber != "" && !isPhone.MatchString(userReq.PhoneNumber) {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("手机号码格式错误"))
		return
	}
	if conf.C.UserStore.Type == models.Local.String() {
		user := r.us.GetUserByName(username)
		if user == nil {
			helper.ErrorResponse(response, stderr.Internal.Cause(nil, "%s用户不存在，无法编辑", username))
			return
		}
		if userReq.Uid == 0 {
			helper.ErrorResponse(response, stderr.InvalidParam.Cause(nil, "参数异常:用户ID为空"))
			return
		}
		userById := r.us.GetUserById(strconv.FormatUint(userReq.Uid, 10))
		if userById == nil {
			helper.ErrorResponse(response, stderr.Internal.Cause(nil, "用户不存在"))
			return
		}
		if updateUser := r.us.GetUserByName(userReq.UserName); updateUser != nil && updateUser.ID != 0 && updateUser.ID != uint(userReq.Uid) {
			helper.ErrorResponse(response, stderr.Internal.Cause(nil, "%s用户已经存在，请换一个用户名", userReq.UserName))
			return
		}
		if userReq.ExpirationTimeSelect != "" { // 如果修改有效期
			err := userReq.SetExpriationTime()
			if err != nil {
				helper.ErrorResponse(response, stderr.InvalidParam.Cause(err, "请求体解析失败"))
				return
			}
		} else { // 不修改有效期, 置空防止误修改
			userReq.ExpirationTime = 0
		}
		if userReq.Status != models.Enable && userReq.Status != models.Disable {
			helper.ErrorResponse(response, stderr.InvalidParam.Cause(nil, "无效的用户状态"))
			return
		}
		for _, wip := range userReq.WhiteIps {
			ip := net.ParseIP(wip)
			if ip == nil {
				helper.ErrorResponse(response, stderr.InvalidParam.Cause(nil, "IP 格式异常"))
				return
			}
			if ip.IsLoopback() {
				helper.ErrorResponse(response, stderr.InvalidParam.Cause(nil, "IP 不能是环回地址"))
				return
			}
		}
		if userReq.DefaultProject == "" {
			helper.ErrorResponse(response, stderr.InvalidParam.Cause(nil, "默认空间不能为空"))
			return
		}
	}
	if err := r.us.UpdateUser(client.Username(request.Request), username, userReq, r.gus); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "更新用户信息失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Usrmgr) UpdateUserPassword(request *restful.Request, response *restful.Response) {
	passwordReq := new(models.PasswordReq)
	if err := request.ReadEntity(passwordReq); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("请求体解析失败"))
		return
	}
	username := client.Username(request.Request)
	if err := r.us.UpdateUserPassword(username, passwordReq, r.gus); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Usrmgr) GetUserProfile(request *restful.Request, response *restful.Response) {
	username := client.Username(request.Request)
	projectId := request.QueryParameter(helper.ParamProjectId)
	profile, err := r.us.GetUserProfile(username, projectId, r.gus, stdsrv.GetLanguage(request))
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取用户信息失败"))
		return
	}
	if projectId != "" {
		ck := stdsrv.NewProjectCookie(projectId)
		ck.MaxAge = 0
		ck.Expires = time.Now().Add(7 * 24 * time.Hour)
		http.SetCookie(response.ResponseWriter, ck)
	}
	helper.SuccessResponse(response, profile)
}

func (r *Usrmgr) DeleteUser(request *restful.Request, response *restful.Response) {
	username := request.PathParameter(helper.FormUsername)
	if username == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户名，%s", username))
		return
	}
	if username == "admin" || username == "thinger" || username == "demo" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("%s为内置用户，无法进行删除", username))
		return
	}
	if err := r.us.DeleteUserByUsername(username, r.gus); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "删除用户失败，用户名：%s", username))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Usrmgr) BatchDeleteUsers(request *restful.Request, response *restful.Response) {
	usernames := make([]string, 0)
	decoder := json.NewDecoder(request.Request.Body)
	if err := decoder.Decode(&usernames); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	for _, username := range usernames {
		if username == "admin" || username == "thinger" || username == "demo" {
			helper.ErrorResponse(response, stderr.BadRequest.Error("%s为内置用户，无法进行删除", username))
			return
		}
	}
	if err := r.us.BatchDeleteUsersByUserIds(usernames, r.gus); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "删除用户信息失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Usrmgr) ListUserGroups(request *restful.Request, response *restful.Response) {
	groupResp, err := r.ugs.ListUserGroups(r.gas, request.QueryParameter("project_id"))
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取用户组失败"))
		return
	}
	helper.SuccessResponse(response, groupResp)
}

func (r *Usrmgr) CreateUserGroup(request *restful.Request, response *restful.Response) {
	username := client.Username(request.Request)
	groupReq := new(models.GroupReq)
	if err := request.ReadEntity(groupReq); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	if conf.C.UserStore.Type == models.Local.String() {
		group := r.ugs.GetGroupByGroupName(groupReq.Name)
		if group != nil && group.Id != 0 {
			helper.ErrorResponse(response, stderr.Internal.Cause(nil, "已经存在同名的用户组，组名为：%s",
				groupReq.Name))
			return
		}
	}
	if err := r.ugs.CreateUserGroup(groupReq, username, r.gas); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "创建用户组失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Usrmgr) UpdateUserGroup(request *restful.Request, response *restful.Response) {
	groupReq := new(models.GroupReq)
	if err := request.ReadEntity(groupReq); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	groupName := request.PathParameter(helper.PathRbacGroupName)
	if groupName == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户组，用户组名称：%s", groupName))
		return
	}
	if conf.C.UserStore.Type == models.Local.String() {
		existsGroup := r.ugs.GetGroupById(int64(groupReq.Gid))
		if existsGroup == nil || existsGroup.Id == 0 {
			helper.ErrorResponse(response, stderr.BadRequest.Error("%s用户组不存在，无法进行更新", groupName))
			return
		}
		group := r.ugs.GetGroupByGroupName(groupReq.Name)
		if group != nil && group.Id != 0 && group.Id != existsGroup.Id {
			helper.ErrorResponse(response, stderr.BadRequest.Error("%s用户组已经存在，请换一个用户组名", groupReq.Name))
			return
		}
	}
	username := client.Username(request.Request)
	if err := r.ugs.UpdateUserGroup(groupReq, groupName, username, r.gas); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "更新用户组失败，用户组名称：%s", groupName))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Usrmgr) DeleteUserGroup(request *restful.Request, response *restful.Response) {
	groupName := request.PathParameter(helper.PathRbacGroupName)
	if groupName == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户组名称 %s", groupName))
		return
	}
	if groupName == models.AllUsers.String() {
		helper.ErrorResponse(response, stderr.BadRequest.Error("%s用户组不能删除", groupName))
		return
	}
	if err := r.ugs.DeleteUserGroup(groupName, r.gas); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "删除用户组失败，用户组名称：%s", groupName))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Usrmgr) BatchDeleteUserGroups(request *restful.Request, response *restful.Response) {
	groupNames := make([]string, 0)
	decoder := json.NewDecoder(request.Request.Body)
	if err := decoder.Decode(&groupNames); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	for _, groupName := range groupNames {
		if groupName == "all_users" {
			helper.ErrorResponse(response, stderr.BadRequest.Error("%s用户组不能删除", groupName))
			return
		}
	}
	if err := r.ugs.BatchDeleteUserGroupsByGroupNames(groupNames, r.gas); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "批量删除用户组失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Usrmgr) ListRoles(request *restful.Request, response *restful.Response) {
	roleType := request.QueryParameter(helper.QueryType)
	if roleType != "" && roleType != "platform" && roleType != "project" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的角色类型type"))
		return
	}
	roles, err := r.rs.ListRoles(roleType)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "列出所有角色失败"))
		return
	}
	// 国际化处理
	loc := stdsrv.GetLanguage(request)
	if !loc.IsChinese() {
		for _, r := range roles {
			if name, ok := r.NameLocals[loc.String()]; ok && name != "" {
				r.Name = name
			}
			if desc, ok := r.DescLocals[loc.String()]; ok && desc != "" {
				r.Description = desc
			}
			for _, p := range r.Permissions {
				if name, ok := p.NameLocals[loc.String()]; ok && name != "" {
					p.Name = name
				}
			}
		}
	}
	helper.SuccessResponse(response, roles)
}

func (r *Usrmgr) CreateRole(request *restful.Request, response *restful.Response) {
	roleReq := new(models.RoleReq)
	if err := request.ReadEntity(roleReq); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	// 优先级 token  >>  param
	creator := stdauth.GetAuthContext(request)
	if creator != nil && len(creator.GetUsername()) != 0 {
		roleReq.Creator = creator.GetUsername()
	}
	if err := r.rs.CreateRole(roleReq); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("空间角色创建失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Usrmgr) UpdateRole(request *restful.Request, response *restful.Response) {
	rid := request.PathParameter(helper.PathRbacRoleID)
	if rid == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的角色ID"))
		return
	}
	roleReq := new(models.RoleReq)
	if err := request.ReadEntity(roleReq); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "请求体解析失败"))
		return
	}
	roleID, _ := strconv.ParseUint(rid, 10, 32)
	// todo 临时操作，1.3权限点改动完成后需要恢复该校验
	// if roleReq.Name == models.ProjectManager.String() {
	//	helper.ErrorResponse(response, stderr.BadRequest.Error("%s, 角色不能编辑", roleReq.Name))
	//	return
	// }
	if err := r.rs.UpdateRole(roleID, roleReq); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "更新角色失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Usrmgr) DeleteRole(request *restful.Request, response *restful.Response) {
	rid := request.PathParameter(helper.PathRbacRoleID)
	if rid == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的角色ID"))
		return
	}
	roleID, _ := strconv.ParseUint(rid, 10, 32)
	role, err := r.rs.GetRoleByRoleId(roleID)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "删除角色失败"))
		return
	}
	if role != nil && (uint64(helper.ProjectOwnerID) == role.Id || uint64(helper.DataShareUserID) == role.Id) {
		helper.ErrorResponse(response, stderr.BadRequest.Error("%s，角色不能删除", role.Name))
		return
	}
	if err := r.rs.DeleteRole(roleID); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "删除角色失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Usrmgr) BatchDeleteRoles(request *restful.Request, response *restful.Response) {
	roleIds := make([]uint64, 0)
	decoder := json.NewDecoder(request.Request.Body)
	if err := decoder.Decode(&roleIds); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	roles, err := r.rs.GetRolesByRoleIds(roleIds)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "删除角色失败"))
		return
	}
	for _, role := range roles {
		if role != nil && role.Id == uint64(helper.ProjectOwnerID) {
			helper.ErrorResponse(response, stderr.BadRequest.Error("%s，角色不能删除", role.Name))
			return
		}
	}
	if err := r.rs.BatchDeleteRolesByRoleIds(roleIds); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "批量删除角色失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Usrmgr) ListPermissions(request *restful.Request, response *restful.Response) {
	permissionType := request.QueryParameter(helper.QueryType)
	if permissionType != "" && permissionType != "platform" && permissionType != "project" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的权限类型"))
	}
	permissionTreeNodes, err := r.ps.ListPermissions(permissionType, stdsrv.GetLanguage(request).String())
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取用户组失败"))
		return
	}
	helper.SuccessResponse(response, permissionTreeNodes)
}

func (r *Usrmgr) NotImplementYet(request *restful.Request, response *restful.Response) {
	var err error
	var ret interface{}
	defer func() { helper.HandleErr(response, err) }()

	// do implement ...

	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, ret)
}

func (r *Usrmgr) GetGuardianAccessToken(request *restful.Request, response *restful.Response) {
	guardianAcessToken := models.GuardianAccessToken{Name: "default", Content: guardianv2.GuardianAccessToken}
	helper.SuccessResponse(response, guardianAcessToken)
}

func (r *Usrmgr) GetKeytab(request *restful.Request, response *restful.Response) {
	username := client.Username(request.Request)
	localVarReturnValue, _, err := r.gus.GenerateKeytabUsingGET(context.Background(), username)
	helper.WriteFileResponse(response, fmt.Sprintf("%s.%s", username, "keytab"), bytes.NewReader(localVarReturnValue))
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "下载keytab失败"))
	}

	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r Usrmgr) ImportUserValidate(request *restful.Request, response *restful.Response) {
	req := models.UserImportReq{}
	err := request.ReadEntity(&req)
	if err != nil {
		helper.ErrorResponse(response, stderr.Wrap(err, "获取参数失败"))
		return
	}
	msg, err := r.uis.ValidateCsv(filepath.Join(rbac.DirPrefix, req.Path))
	if err != nil {
		helper.ErrorResponse(response, stderr.Wrap(err, "校验失败"))
		return
	}
	rsp := models.ValidateUserImportResp{
		Success: msg == "",
		Message: msg,
	}
	helper.SuccessResponse(response, rsp)
}

func (r Usrmgr) ImportUser(request *restful.Request, response *restful.Response) {
	req := models.UserImportReq{}
	err := request.ReadEntity(&req)
	if err != nil {
		helper.ErrorResponse(response, stderr.Wrap(err, "获取参数失败"))
		return
	}
	username := client.Username(request.Request)
	err = r.uis.ImportCsv(username, filepath.Join(rbac.DirPrefix, req.Path))
	if err != nil {
		helper.ErrorResponse(response, stderr.Wrap(err, "修改失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r Usrmgr) UpdateUserStatus(request *restful.Request, response *restful.Response) {
	req := models.UserStatusReq{}
	err := request.ReadEntity(&req)
	if err != nil {
		helper.ErrorResponse(response, stderr.Wrap(err, "获取参数失败"))
		return
	}
	if req.Status != models.Enable && req.Status != models.Disable {
		helper.ErrorResponse(response, stderr.InvalidParam.Cause(nil, "无效的用户状态"))
		return
	}
	err = r.us.UpdateUserStatus(&req)
	if err != nil {
		helper.ErrorResponse(response, stderr.Wrap(err, "修改用户状态失败"))
		return
	}
	// TODO 测试是否可以删除此逻辑
	// 删除所有相关的session
	// if req.Status != models.Enable {
	// 	go func() {
	// 		user := r.us.GetUserById(strconv.FormatUint(req.Id, 10))
	// 		err := auth.GetAuthHandler().DeleteUserCacheSessions(user)
	// 		if err != nil {
	// 			stdlog.WithError(err).Errorf("Delete user [%s] sessions error.", user.Name)
	// 		}
	// 	}()
	// }
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Usrmgr) InWhiteIPs(request *restful.Request, response *restful.Response) {
	ip := request.QueryParameter("ip")
	if ip == "" {
		ip = utils.ClientIP(request)
	}
	passed, _, err := r.us.InWhiteIPs(request.Request.Context(), client.Username(request.Request), ip)
	if err != nil {
		helper.ErrorResponse(response, stderr.Wrap(err, "校验失败"))
		return
	}
	helper.SuccessResponse(response, passed)
}

func (r *Usrmgr) GetGroupByName(request *restful.Request, response *restful.Response) {
	groupName := request.PathParameter(helper.PathRbacGroupName)
	if groupName == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("缺少请求参数 groupName"))
		return
	}
	group, err := r.ugs.GetUserByGroup(request.Request.Context(), groupName)
	if err != nil {
		helper.ErrorResponse(response, stderr.Wrap(err, "获取用户组失败"))
		return
	}
	helper.SuccessResponse(response, group)
}
