package usermgr

import (
	"net/http"

	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"

	guardianv2 "transwarp.io/applied-ai/central-auth-service/api/guardian/client"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service"
	"transwarp.io/applied-ai/central-auth-service/service/project"
	"transwarp.io/applied-ai/central-auth-service/service/rbac"
)

func NewUserManagerAPI(root string, us *rbac.UserService, uis *rbac.UserImportService, ugs *rbac.UserGroupService, rs *rbac.RoleService,
	ps *rbac.PermissionService, gus *guardianv2.UsersApiService, gas *guardianv2.GroupsApiService, gat *guardianv2.AccessTokensApiService,
	sus *service.UserService,
) *restful.WebService {
	return (&Usrmgr{us, uis, ugs, rs, ps, gus, gas, gat, sus}).UserService(root)
}

type Usrmgr struct {
	us  *rbac.UserService
	uis *rbac.UserImportService
	ugs *rbac.UserGroupService
	rs  *rbac.RoleService
	ps  *rbac.PermissionService
	gus *guardianv2.UsersApiService
	gas *guardianv2.GroupsApiService
	gat *guardianv2.AccessTokensApiService
	sus *service.UserService
}

func (r *Usrmgr) UserService(root string) *restful.WebService {
	ws := new(restful.WebService)
	ws.Path(root + "/usermgr").Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)
	metaK := restfulspec.KeyOpenAPITags
	tags := []string{"LLM1.0用户管理"}

	ws.Route(ws.GET("/users").Doc("用户列表").
		To(r.ListUsers).Metadata(metaK, tags).
		Param(ws.QueryParameter("project_id", "空间id, 获取用户的空间角色时需要")).
		Param(ws.QueryParameter("username", "用户名").DataType("string").Required(false)).
		Param(ws.QueryParameter("username_like", "用户名模糊搜索").DataType("string").Required(false)).
		Param(ws.QueryParameter("full_name", "模糊搜索").DataType("string").Required(false)).
		Param(ws.QueryParameter("phone_number", "模糊搜索").DataType("string").Required(false)).
		Writes([]models.UserResp{}).
		Returns(http.StatusOK, "OK", []models.UserResp{}))
	ws.Route(ws.GET("/users/count").Doc("用户数量").
		To(r.CountUsers).Metadata(metaK, tags).
		Writes(models.UserCountResp{}).
		Returns(http.StatusOK, "OK", models.UserCountResp{}))
	ws.Route(ws.POST("/users").Doc("添加新用户").
		To(r.CreateUser).Metadata(metaK, tags).
		Reads(models.UserReq{}).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.PUT("/users/{username}").Doc("编辑用户信息").
		To(r.UpdateUser).Metadata(metaK, tags).
		Reads(models.UserReq{}).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.GET("/users/profile").
		Param(ws.QueryParameter("project_id", "空间id").DataType("int").Required(false)).
		Doc("获取当前登录用户的基本信息+权限列表，不传project_id，获取当前用户平台角色权限，"+
			"传project_id，获取当前用户平台角色权限+空间角色权限").
		To(r.GetUserProfile).Metadata(metaK, tags).
		Writes(models.UserProfileResp{}).
		Returns(http.StatusOK, "OK", []models.UserProfileResp{}))
	ws.Route(ws.DELETE("/users/{username}").Doc("删除指定用户").
		To(r.DeleteUser).Metadata(metaK, tags).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.POST("/users:batchDel").Doc("批量删除用户").
		To(r.BatchDeleteUsers).Metadata(metaK, tags).
		Reads([]int64{}).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.POST("/users/import/validate").Doc("用户导入校验").
		To(r.ImportUserValidate).Metadata(metaK, tags).
		Reads(&models.UserImportReq{}).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.POST("/users/import").Doc("用户导入").
		To(r.ImportUser).Metadata(metaK, tags).
		Reads(&models.UserImportReq{}).
		Returns(http.StatusOK, "OK", nil))

	ws.Route(ws.POST("/users/status").Doc("用户状态修改").
		To(r.UpdateUserStatus).Metadata(metaK, tags).
		Reads(&models.UserStatusReq{}).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.GET("/users:ipCheck").Doc("用户ip白名单校验").
		To(r.InWhiteIPs).Metadata(metaK, tags).
		Param(ws.QueryParameter("ip", "ip, 为空则从header获取").DataType("string").Required(false)).
		Returns(http.StatusOK, "OK", nil))

	ws.Route(ws.PUT("/users/-/password").Doc("编辑当前用户密码").
		To(r.UpdateUserPassword).Metadata(metaK, tags).
		Reads(models.PasswordReq{}).
		Returns(http.StatusOK, "OK", nil))

	ws.Route(ws.GET("/groups").Doc("获取用户组列表").
		To(r.ListUserGroups).Metadata(metaK, tags).
		Param(ws.QueryParameter("project_id", "空间id, 获取用户组的空间角色时需要")).
		Writes([]models.GroupResp{}).
		Returns(http.StatusOK, "OK", []models.GroupResp{}))
	ws.Route(ws.POST("/groups").Doc("添加新的用户组").
		To(r.CreateUserGroup).Metadata(metaK, tags).
		Reads(models.GroupReq{}).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.PUT("/groups/{groupName}").Doc("编辑已有用户组").
		To(r.UpdateUserGroup).Metadata(metaK, tags).
		Reads(models.GroupReq{}).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.DELETE("/groups/{groupName}").Doc("删除指定的用户组").
		To(r.DeleteUserGroup).Metadata(metaK, tags).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.POST("/groups:batchDel").Doc("批量删除用户组").
		To(r.BatchDeleteUserGroups).Metadata(metaK, tags).
		Reads([]int64{}).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.GET("/groups/{groupName}").Doc("获取指定name的用户组").
		To(r.GetGroupByName).Metadata(metaK, tags).
		Writes(models.GroupResp{}).
		Returns(http.StatusOK, "OK", models.GroupResp{}))

	ws.Route(ws.GET("/roles").
		Param(ws.QueryParameter("type", "角色类型").DataType("string").Required(false)).
		Doc("角色列表，type=platform，表示获取所有平台角色，type=project，表示获取所有空间角色，type不传表示获取所有角色").
		To(r.ListRoles).Metadata(metaK, tags).
		Writes([]models.Role{}).
		Returns(http.StatusOK, "OK", []models.RoleResp{}))
	ws.Route(ws.POST("/roles").Doc("添加空间角色").
		To(r.CreateRole).Metadata(metaK, tags).
		Reads(models.RoleReq{}).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.PUT("/roles/{rid}").Doc("编辑空间角色").
		To(r.UpdateRole).Metadata(metaK, tags).
		Reads(models.RoleReq{}).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.DELETE("/roles/{rid}").Doc("删除空间角色").
		To(r.DeleteRole).Metadata(metaK, tags).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.POST("/roles:batchDel").Doc("批量删除空间角色").
		To(r.BatchDeleteRoles).Metadata(metaK, tags).
		Reads([]int64{}).
		Returns(http.StatusOK, "OK", nil))

	ws.Route(ws.GET("/permissions").
		Doc("获取系统中所有权限点定义（树形）,type=platform，表示获取所有平台角色的权限点，type=project，表示获取所有空间角色的权限点，type不传表示获取所有权限点").
		Param(ws.QueryParameter("type", "权限类型").DataType("string").Required(false)).
		To(r.ListPermissions).Metadata(metaK, tags).
		Writes([]models.PermissionTreeNode{}).
		Returns(http.StatusOK, "OK", []models.PermissionTreeNode{}).
		Writes([]models.PermissionTreeNode{}).
		Returns(http.StatusOK, "OK", []models.PermissionTreeNode{}))

	ws.Route(ws.GET("/guardianAccessToken").
		Doc("获取guardian access token").
		To(r.GetGuardianAccessToken).Metadata(metaK, tags).
		Writes([]models.GuardianAccessToken{}).
		Returns(http.StatusOK, "OK", []models.GuardianAccessToken{}))
	ws.Route(ws.GET("/kerberos-conf/{fileName}").
		Doc("获取 keytab").
		Param(ws.PathParameter("fileName", "文件名").DataType("string").Required(true)).
		To(r.GetKeytab).Metadata(metaK, tags).
		Writes([]models.GuardianAccessToken{}).
		Returns(http.StatusOK, "OK", helper.EmptyRsp{}))

	return ws
}

func NewProjectManagerAPI(root string, ps *project.ProjectService, pms *project.ProjectMemberService,
	rs *rbac.RoleService, pcs *project.ProjectCategoryService,
) *restful.WebService {
	return (&Projmgr{ps, pms, rs, pcs}).ProjectService(root)
}

type Projmgr struct {
	ps  *project.ProjectService
	pms *project.ProjectMemberService
	rs  *rbac.RoleService
	pcs *project.ProjectCategoryService
}

func (r *Projmgr) ProjectService(root string) *restful.WebService {
	ws := new(restful.WebService)
	ws.Path(root + "/projmgr").Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)
	metaK := restfulspec.KeyOpenAPITags
	tags := []string{"LLM1.0空间管理"}
	ws.Route(ws.GET("/projects").Doc("空间列表").
		Param(ws.QueryParameter("permission_code", "权限点编码").DataType("string").Required(false)).
		Doc("permission_code 权限点编码，不传获取用户所有的空间列表，传的话，获取用户有该权限点的空间列表").
		To(r.ListProjects).Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes([]models.ProjectResp{}).
		Returns(http.StatusOK, "OK", []models.ProjectResp{}))
	ws.Route(ws.GET("/projects/list").Doc("空间列表").
		Param(ws.QueryParameter(helper.TenantId, helper.TenantIdDesc).DataType("string").Required(false)).
		Doc("返回所有空间列表，仅返回name、projectId和tenant_uid字段").
		To(r.ListAllProjects).Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes([]models.ProjectResp{}).
		Returns(http.StatusOK, "OK", []models.ProjectResp{}))
	ws.Route(ws.POST("/projects").Doc("新建空间").
		To(r.CreateProject).Metadata(metaK, tags).
		Consumes(restful.MIME_JSON).Reads(models.ProjectReq{}).
		Returns(http.StatusOK, "OK", models.Project{}))
	ws.Route(ws.GET("/projects/{pid}").Doc("空间详情").
		To(r.GetProject).Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes(models.ProjectResp{}).
		Returns(http.StatusOK, "OK", models.ProjectResp{}))
	ws.Route(ws.GET("/projects/{pid}/exists").Doc("空间id是否已存在").
		To(r.ExistsProject).Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes(models.ProjectExistsRsp{}).
		Returns(http.StatusOK, "OK", models.ProjectExistsRsp{}))
	ws.Route(ws.PUT("/projects/{pid}").Doc("编辑空间").
		To(r.UpdateProject).Metadata(metaK, tags).
		Consumes(restful.MIME_JSON).Reads(models.ProjectReq{}).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.DELETE("/projects/{pid}").Doc("删除空间").
		To(r.DeleteProject).Metadata(metaK, tags).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.GET("/projects/{pid}/members").Doc("空间成员列表").
		To(r.ListProjectMembers).Metadata(metaK, tags).
		Param(ws.QueryParameter("name", "用户/用户组名模糊搜索").DataType("string")).
		Param(ws.QueryParameter("full_name", "模糊搜索").DataType("string")).
		Param(ws.QueryParameter("phone_number", "模糊搜索").DataType("string")).
		Produces(restful.MIME_JSON).Writes([]models.ProjectMemberResp{}).
		Returns(http.StatusOK, "OK", []models.ProjectMemberResp{}))
	ws.Route(ws.POST("/projects/{pid}/members").Doc("添加空间成员").
		To(r.AddProjectMember).Metadata(metaK, tags).
		Consumes(restful.MIME_JSON).Reads([]models.ProjectMemberReq{}).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.PUT("/projects/{pid}/members").Doc("编辑空间成员").
		To(r.UpdateProjectMember).Metadata(metaK, tags).
		Consumes(restful.MIME_JSON).Reads([]models.ProjectMemberReq{}).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.DELETE("/projects/{pid}/members").Doc("删除空间成员").
		To(r.DeleteProjectMember).Metadata(metaK, tags).
		Consumes(restful.MIME_JSON).Reads(models.ProjectMemberReq{}).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.POST("/projects/{pid}/members:batchDel").Doc("批量删除空间成员").
		To(r.BatchDeleteProjectMembers).Metadata(metaK, tags).
		Consumes(restful.MIME_JSON).Reads([]models.ProjectMemberReq{}).
		Returns(http.StatusOK, "OK", nil))
	ws.Route(ws.GET("/projects/{pid}/users").Doc("获取空间下拥有某一权限的用户列表").
		To(r.ListProjectUsers).Metadata(metaK, tags).
		Param(ws.QueryParameter("permission_code", "权限点编码").DataType("string").Required(false)).
		Param(ws.QueryParameter("permission_action", "操作权限").DataType("string").Required(false)).
		Produces(restful.MIME_JSON).Writes([]string{}).
		Returns(http.StatusOK, "OK", []string{}))
	ws.Route(ws.GET("/projects/{pid}/groups").Doc("获取空间下的用户组列表").
		To(r.ListProjectGroups).Metadata(metaK, tags).
		Param(ws.QueryParameter("permission_code", "拥有指定 权限点: manage-center.role.*").DataType("string")).
		Param(ws.QueryParameter("permission_action", "拥有指定 操作权限: read").DataType("string")).
		Returns(http.StatusOK, "OK", []models.GroupResp{}))
	ws.Route(ws.POST("/projects/{pid}/examine").Doc("开启或关闭该空间的审批").
		To(r.ExamineSwitch).Metadata(metaK, tags).
		AllowedMethodsWithoutContentType([]string{http.MethodPost}).
		Param(ws.PathParameter("pid", "空间id")).
		Param(ws.QueryParameter(helper.QueryOn, "是否需要开启或关闭审批,true | false")).
		Consumes(restful.MIME_JSON).
		Returns(http.StatusOK, "OK", helper.EmptyRsp{}))

	// project category
	ws.Route(ws.POST("/categories").Doc("新建空间类别").
		To(r.CreateProjectCategory).Metadata(metaK, tags).
		Consumes(restful.MIME_JSON).Reads(models.ProjectCategory{}).
		Returns(http.StatusOK, "OK", models.ProjectCategory{}))
	ws.Route(ws.POST("/categories/rebuild").Doc("重建空间类别").
		To(r.RebuildProjectCategory).Metadata(metaK, tags).
		Consumes(restful.MIME_JSON).Reads([]models.ProjectCategory{}).
		Returns(http.StatusOK, "OK", []models.ProjectCategory{}))
	ws.Route(ws.GET("/categories").Doc("获取空间类别列表").
		To(r.ListProjectCategories).Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes([]models.ProjectCategory{}).
		Returns(http.StatusOK, "OK", []models.ProjectCategory{}))
	ws.Route(ws.GET("/categories/{id}").Doc("获取空间类别").
		Param(ws.PathParameter("id", "租户类别id").DataType("string").Required(true)).
		To(r.GetProjectCategory).Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes(models.ProjectCategory{}).
		Returns(http.StatusOK, "OK", models.ProjectCategory{}))
	ws.Route(ws.DELETE("/categories/{id}").Doc("删除空间类别").
		Param(ws.PathParameter("id", "租户类别id").DataType("string").Required(true)).
		To(r.DeleteProjectCategory).Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes(models.ProjectCategory{}).
		Returns(http.StatusOK, "OK", models.ProjectCategory{}))
	ws.Route(ws.PUT("/categories/{id}").Doc("更新空间类别").
		Param(ws.PathParameter("id", "租户类别id").DataType("string").Required(true)).
		To(r.UpdateProjectCategory).Metadata(metaK, tags).
		Consumes(restful.MIME_JSON).Reads(models.ProjectCategory{}).
		Returns(http.StatusOK, "OK", models.ProjectCategory{}))

	// .
	// 	To(r.NotImplementYet).
	// 	Returns(http.StatusOK, http.StatusText(http.StatusOK), nil))
	//
	return ws
}
