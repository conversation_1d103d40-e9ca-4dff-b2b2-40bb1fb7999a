package metrics

import (
	"github.com/emicklei/go-restful/v3"
	"strconv"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/service/metrics"
)

const (
	PathParamStartTimeSecond = "start_time_second"
	PathParamEndTimeSecond   = "end_time_second"
)

func NewMetricsAPI(root string, ms *metrics.MetricsStatsService) *restful.WebService {
	return (&Resource{ms: ms}).WebService(root)
}

type Resource struct {
	ms *metrics.MetricsStatsService
}

func (r *Resource) DailyStats(request *restful.Request, response *restful.Response) {
	startTime, err := strconv.Atoi(request.QueryParameter(PathParamStartTimeSecond))
	if err != nil {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("startTime is invalid"))
		return
	}

	endTime, err := strconv.Atoi(request.QueryParameter(PathParamEndTimeSecond))
	if err != nil {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("endTime is invalid"))
		return
	}

	rsp, err := r.ms.ListAllMetrics(request.Request.Context(), startTime, endTime)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}
