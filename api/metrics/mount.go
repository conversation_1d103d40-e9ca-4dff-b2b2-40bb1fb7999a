package metrics

import (
	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"
	"net/http"
	"transwarp.io/applied-ai/central-auth-service/service/metrics/client"
)

func (r *Resource) WebService(root string) *restful.WebService {
	tags := []string{"总体资产变化统计"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(root + "/assets-stats")
	ws.Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.GET("/daily-stats").To(r.DailyStats).
		Doc("统计各资产总量随日期的变化").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(ws.QueryParameter(PathParamStartTimeSecond, "时间戳，单位秒。开始日期的最后一秒 xxx-23:59:59")).
		Param(ws.QueryParameter(PathParamEndTimeSecond, "时间戳，单位秒。结束日期的最后一秒 xxx-23:59:59")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []*client.StatsInfoResp{}))
	return ws
}
