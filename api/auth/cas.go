package auth

import (
	"bytes"
	"encoding/base64"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"text/template"

	"github.com/emicklei/go-restful/v3"

	std_auth "transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/utils"

	"transwarp.io/applied-ai/central-auth-service/utils/auth"
	"transwarp.io/applied-ai/central-auth-service/utils/auth/cas/server"
)

const (
	tmplExt           = ".tmpl"
	loginTmplDir      = "./public/templates"
	loginHtmlFile     = "login.html"
	loginJsFile       = "login.js"
	loginHtmlTmplFile = loginHtmlFile + tmplExt
	loginJsTmplFile   = loginJsFile + tmplExt
)

var (
	renderCfg     *conf.LoginPageConfig
	loginJsTmpl   *template.Template
	loginHtmlTmpl *template.Template
)

func initTmpl(p string) (*template.Template, error) {
	tmpl, err := template.New(p).ParseFiles(filepath.Join(loginTmplDir, p))
	if err != nil {
		return nil, stderr.Wrap(err, "new template")
	}
	return tmpl, nil
}

// initRenderCfg 用于初始化登录页的一些动态配置项，如 logo, 提示语
func initRenderCfg() error {
	var err error

	// 初始化静态文件渲染模板
	loginHtmlTmpl, err = initTmpl(loginHtmlTmplFile)
	if err != nil {
		return stderr.Wrap(err, "init html template for login page")
	}
	loginJsTmpl, err = initTmpl(loginJsTmplFile)
	if err != nil {
		return stderr.Wrap(err, "init js template for login page")
	}

	// 初始化动态渲染配置(或者从用户自定义配置中获取)
	renderCfg = conf.C.LoginPage // 默认从配置文件读取
	// renderCfg.OAuth2AuthUrl = renderCfg.OAuth2.ToOAuth2Config().AuthCodeURL("")
	cfg, err := dao.MustCheckAndGetCustomConfigService().GetConfigValue()
	if err != nil {
		return stderr.Wrap(err, "get custom config values'")
	}

	if title, _ := cfg.GetStringValue(dao.LoginTitleID); title != "" {
		renderCfg.TitleZH = title
		renderCfg.TitleEN = title
	}

	// js 文件仅初始化一次即可， 写回文件
	f, err := os.OpenFile(filepath.Join(loginTmplDir, loginJsFile), os.O_CREATE|os.O_TRUNC|os.O_RDWR, os.ModePerm)
	if err != nil {
		return stderr.Wrap(err, "open login.js")
	}
	if err := loginJsTmpl.Execute(f, renderCfg); err != nil {
		return stderr.Wrap(err, "render login.js")
	}

	// 后续HTML每次动态进行渲染
	return nil
}

func (r *Resource) CASGetLoginForm(request *restful.Request, response *restful.Response) {
	service := helper.ParseServiceFromRequest(request)
	// 如果是外部认证，则重定向到外部认证的登录页
	if conf.C.Auth.CAS.External() {
		redirect, err := GetAuthHandler().(*auth.CASHandler).LoginUrl(request.Request)
		if err != nil {
			helper.ErrorResponse(response, stderr.Internal.Cause(err, "fail to get the login url to redirect"))
			return
		}
		helper.Redirect(request, response, redirect)
		return
	}

	var logined bool
	defer func() {
		if !logined {
			// 登录失败，重定向到登录页
			r.redirect2Login(request, response, http.StatusOK, &LoginPageRenderParams{
				PostUrl: fmt.Sprintf("%s?service=%s", helper.PathAuthCASLogin, service),
			})
		}
	}()

	// 判断是否存在已经登录过的凭证TGT
	tgt, ok := r.checkTGTCookie(request)
	if !ok {
		// 没有登录过 或 登录过期，则重定向到登录界面重新获取 TGC
		return
	}

	st, err := r.as.CreateServiceTicket(tgt, service, request.Request.Host)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "generate new ServiceTicket by TGC"))
		return
	}

	s, err := url.Parse(service)
	if err != nil {
		stdlog.WithError(err).Errorf("construct the redirect url with ticket")
		return
	}

	logined = true
	q := s.Query()
	q.Set("ticket", st.Ticket)
	s.RawQuery = q.Encode()
	helper.Redirect(request, response, s.String())
	return

}

// checkTGTCookie 检查是否已经登录过，如果登录过且仍在有效期, 则返回TGT + True, 否则返回空串 + False
func (r *Resource) checkTGTCookie(request *restful.Request) (tgt string, ok bool) {
	// 是否存在已经登录过的凭证TGT
	tgtc, err := request.Request.Cookie(server.TGTCookieName)
	if err != nil {
		if errors.Is(err, http.ErrNoCookie) {
			return "", false
		} else {
			stdlog.WithError(err).Errorf("should never happened, got an unexpected error while getting TGT cookit")
			return "", false
		}
	}
	tgt = tgtc.Value
	if !r.as.CheckTicketGrantingTicketExistence(tgt) {
		return "", false
	}
	return tgt, ok
}

func (r *Resource) CASPostLoginForm(request *restful.Request, response *restful.Response) {
	service := helper.ParseServiceFromRequest(request)

	username := request.Request.PostFormValue(helper.FormUsername)
	if username == "" {
		r.redirect2Login(request, response, http.StatusBadRequest, &LoginPageRenderParams{
			PostUrl: fmt.Sprintf("%s?service=%s", helper.PathAuthCASLogin, service),
			Notice:  "用户名不能为空",
		})
		return
	}
	password := request.Request.PostFormValue(helper.FormPassword)
	if password == "" {
		r.redirect2Login(request, response, http.StatusBadRequest, &LoginPageRenderParams{
			PostUrl: fmt.Sprintf("%s?service=%s", helper.PathAuthCASLogin, service),
			Notice:  "密码不能为空",
		})
		return
	}

	user := &models.UserReq{
		UserName: username,
		Password: password,
	}
	if tgt, st, err := r.as.Login(user, service, request.Request.Host, utils.ClientIP(request), stdsrv.GetLanguage(request)); err != nil {
		r.redirect2Login(request, response, http.StatusUnauthorized, &LoginPageRenderParams{
			PostUrl:  fmt.Sprintf("%s?service=%s", helper.PathAuthCASLogin, service),
			Notice:   err.Error(),
			CurtUser: user.UserName,
		})
		return
	} else { // success
		http.SetCookie(response.ResponseWriter, server.NewTGTCookie(tgt))

		s, err := url.Parse(service)
		if err != nil {
			helper.ErrorResponse(response, stderr.Internal.Cause(err, "fail to construct the redirect url with ticket"))
			return
		}
		q := s.Query()
		q.Set("ticket", st.Ticket)
		s.RawQuery = q.Encode()
		helper.Redirect(request, response, s.String())
	}
}

func (r *Resource) CASLogout(request *restful.Request, response *restful.Response) {
	for _, cookie := range request.Request.Cookies() {
		cookie.MaxAge = -1
		http.SetCookie(response.ResponseWriter, cookie)
	}

	if conf.C.Auth.CAS.External() {
		redirect, err := GetAuthHandler().(*auth.CASHandler).LogoutUrl(request.Request)
		if err != nil {
			helper.ErrorResponse(response, stderr.Internal.Cause(err, "fail to get the login url to redirect"))
			return
		}
		helper.Redirect(request, response, redirect)
		return
	}

	tgc, err := request.Request.Cookie(server.TGTCookieName)
	if err == nil {
		if err = r.as.Logout(tgc.Value); err != nil {
			helper.ErrorResponse(response, stderr.Internal.Cause(err, "fail to clear the TGT"))
			return
		}
		http.SetCookie(response.ResponseWriter, server.NewExpiredTGTCookie())
	}

	service := helper.ParseServiceFromRequest(request)
	helper.Redirect(request, response, service)
}

func (r *Resource) CAS3ValidateServiceTicket(request *restful.Request, response *restful.Response) {
	service := request.QueryParameter(helper.QueryService)
	if service == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("the query parameter '%s' must be given", helper.QueryService))
		return
	}
	service, _ = url.PathUnescape(service)
	ticket := request.QueryParameter(helper.QueryTicket)
	if ticket == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("the query parameter '%s' must be given", helper.QueryTicket))
		return
	}

	if conf.C.Auth.CAS.External() {
		redirect, err := GetAuthHandler().(*auth.CASHandler).ServiceValidateUrl(request.Request)
		if err != nil {
			helper.ErrorResponse(response, stderr.Internal.Cause(err, "fail to get the login url to redirect"))
			return
		}
		helper.Redirect(request, response, redirect)
		return
	}

	data := r.as.ValidateServiceTicket(service, ticket)
	response.Header().Add("Content-Type", "text/xml")
	_, _ = response.ResponseWriter.Write(data)
}

func (r *Resource) AclAllow(request *restful.Request, response *restful.Response) {
	if !GetAuthHandler().IsAuthenticated(response.ResponseWriter, request.Request) {
		helper.ErrorResponse(response, stderr.Unauthenticated.Errorf("fail to pass the authentication"))
		return
	}
	token, err := std_auth.ParseTokenFromRequest(request.Request)
	if err != nil {
		helper.ErrorResponse(response, stderr.Unauthenticated.Errorf(err.Error()))
		return
	}
	uri := request.QueryParameter("uri")
	if uri == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("uri must be given"))
		return
	}
	u, err := url.ParseRequestURI(uri)
	if err != nil {
		helper.ErrorResponse(response, stderr.InvalidParam.Cause(err, "uri parse error"))
		return
	}

	meth := request.QueryParameter("method")
	if meth == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("method must be given"))
		return
	}
	uri, err = url.QueryUnescape(u.Path)
	if err != nil {
		helper.ErrorResponse(response, stderr.InvalidParam.Cause(err, "uri QueryUnescape error"))
		return
	}
	_, f, ok := strings.Cut(uri, "gateway")
	if ok {
		uri = f
	}
	rsp, err := r.as.AclAllow(request.Request.Context(), token.GetUsername(), strings.ToUpper(meth), uri)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "fail to check the access"))
		return
	}
	helper.SuccessResponse(response, rsp)
}

type LoginPageRenderParams struct {
	PostUrl string
	Notice  string
	conf.LoginPageConfig
	CurtUser      string
	EnableOauth2  bool   `json:"enable_oauth2" yaml:"enable_oauth2"`
	OAuth2AuthUrl string `json:"oauth2_auth_url" yaml:"oauth2_auth_url"`
	// LogoSvg string
}

func (r *Resource) redirect2Login(req *restful.Request, rsp *restful.Response, code int, params *LoginPageRenderParams) {
	if err := initRenderCfg(); err != nil {
		helper.ErrorResponse(rsp, stderr.Internal.Cause(err, "render the login page js"))
		return
	}
	if renderCfg != nil {
		params.LoginPageConfig = *renderCfg
	}
	if conf.C.Auth.Mode == conf.AuthModeMixed {
		params.EnableOauth2 = true
		params.OAuth2AuthUrl = conf.C.Auth.OAuth2.ToOAuth2Config().AuthCodeURL(base64.StdEncoding.EncodeToString([]byte(serviceQP.GetValue(req))))
	} else {
		params.EnableOauth2 = false
		params.OAuth2AuthUrl = ""
	}
	buf := bytes.NewBuffer(nil)
	if err := loginHtmlTmpl.Execute(buf, params); err != nil {
		helper.ErrorResponse(rsp, stderr.Internal.Cause(err, "render the login form page"))
		return
	}
	rsp.ResponseWriter.WriteHeader(code)
	if _, err := rsp.ResponseWriter.Write(buf.Bytes()); err != nil {
		stdlog.WithError(err).Errorf("write response while redirecting to login page")
	}
	return
}
