package auth

import (
	"net/http"
	"path"

	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"
	"github.com/unrolled/render"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/service"
)

func NewAuthAPI(prefix string, as *service.AuthService, us *service.UserService, ts *service.AccessTokenService) *restful.WebService {
	if err := initAuthHandler(); err != nil {
		panic(err)
	}
	if err := initRenderCfg(); err != nil {
		if conf.IsDevMode() {
			stdlog.Warnf("!!!! skipping deps (mysql) initiation in development mode")
		} else {
			panic(err)
		}
	}
	return (&Resource{
		as: as,
		us: us,
		ts: ts,
	}).WebService(prefix)
}

type Resource struct {
	as         *service.AuthService
	us         *service.UserService
	ts         *service.AccessTokenService
	pageRender *render.Render
}

func (r *Resource) WebService(prefix string) *restful.WebService {
	tags := []string{"认证管理"}
	casTags := []string{"认证管理 - CAS 3.0"}
	metaK := restfulspec.KeyOpenAPITags

	ws := new(restful.WebService)
	ws.Path(prefix)
	ws.
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	queryParamToken := ws.QueryParameter(helper.QueryToken, helper.QueryToken).
		DataType(helper.ParamTypeString).Required(true)
	queryParamTicket := ws.QueryParameter(helper.QueryTicket, helper.QueryTicketDesc).
		DataType(helper.ParamTypeString).Required(true)
	queryParamService := ws.QueryParameter(helper.QueryService, helper.QueryServiceDesc).
		DataType(helper.ParamTypeString).Required(false)
	queryParamRedirect := ws.QueryParameter(helper.QueryRedirect, helper.QueryRedirectDesc).
		DataType(helper.ParamTypeString).Required(false)
	formParamUsername := ws.FormParameter(helper.FormUsername, helper.FormUsernameDesc).
		DataType(helper.ParamTypeString).Required(true)
	formParamPassword := ws.FormParameter(helper.FormPassword, helper.FormPasswordDesc).
		DataType(helper.ParamTypeString).Required(true)

	ws.Route(ws.GET("/loginPath").Param(queryParamRedirect).
		To(r.Redirect2LoginPage).
		Doc("借助浏览器根据认证方式重定向到对应的登录页面").Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes(helper.EmptyRsp{}).
		Returns(http.StatusFound, http.StatusText(http.StatusFound), helper.EmptyRsp{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET("/logout").Param(queryParamRedirect).
		To(r.Logout).
		Doc("注销用户会话").Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes(helper.EmptyRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET("/loginCheck").Param(queryParamRedirect).Param(queryParamTicket).
		To(r.LoginCheck).
		Doc("用户登录校验").Metadata(metaK, tags).
		Produces(restful.MIME_JSON).Writes(helper.EmptyRsp{}).
		Returns(http.StatusFound, http.StatusText(http.StatusFound), helper.EmptyRsp{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.GET("/login:password-free").Param(queryParamToken).
		To(r.LoginWithToken).
		Doc("使用 TOKEN 免密登录").Metadata(metaK, tags).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.GET("/cas/login").Param(queryParamService).
		To(r.CASGetLoginForm).
		Doc("获取 SSO(Single Sign On) 表单").Metadata(metaK, casTags).
		Produces(restful.MIME_XML).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET("/cas/{subpath}").Param(restful.PathParameter("subpath", "获取前端界面静态资源")).To(GetUIResources))
	ws.Route(ws.POST("/cas/login").Param(queryParamService).
		To(r.CASPostLoginForm).
		Doc("提交 SSO(Single Sign On) 表单").Metadata(metaK, casTags).
		Consumes("application/x-www-form-urlencoded").Param(formParamUsername).Param(formParamPassword). // 接收表单数据
		Produces(restful.MIME_JSON).
		Returns(http.StatusFound, http.StatusText(http.StatusFound), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET("/cas/logout").Param(queryParamService).
		To(r.CASLogout).
		Doc("注销 CAS 会话 - SLO(Single Logout)").Metadata(metaK, casTags).
		Produces(restful.MIME_JSON).Writes(helper.EmptyRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET("/cas/p3/serviceValidate").Param(queryParamService).Param(queryParamTicket).
		To(r.CAS3ValidateServiceTicket).
		Doc("验证 ServiceTicket 有效性").Metadata(metaK, casTags).
		Produces("text/xml").
		Returns(http.StatusOK, http.StatusText(http.StatusOK), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET("/acl/allow").To(r.AclAllow).
		Doc("校验用户对指定接口是否有访问权限").
		Metadata(metaK, casTags).
		Param(ws.QueryParameter("method", "请求方式").Required(true)).
		Param(ws.QueryParameter("uri", "目标接口").Required(true)).
		Param(ws.QueryParameter("project_id", "空间id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	return ws
}

func GetUIResources(request *restful.Request, response *restful.Response) {
	actual := path.Join("public/templates", request.PathParameter("subpath"))
	http.ServeFile(
		response.ResponseWriter,
		request.Request, actual)
}
