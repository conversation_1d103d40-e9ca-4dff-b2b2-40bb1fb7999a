package auth

import (
	"net/url"

	"github.com/emicklei/go-restful/v3"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/utils/auth"
)

var authHandler auth.Handler

var serviceQP = stdsrv.NewQueryParam(auth.FinalRedirectServiceQueryParamKey, "登录成功后返回时, 携带的登录前原始浏览器地址")

func initAuthHandler() error {
	var err error
	authHandler, err = auth.NewAuthHandler(conf.C.Auth, conf.C.SessionStore)
	if err != nil {
		return stderr.Wrap(err, "NewAuthHandler")
	}
	return nil
}
func GetAuthHandler() auth.Handler {
	return authHandler
}

func (r *Resource) Redirect2LoginPage(request *restful.Request, response *restful.Response) {
	svc := request.QueryParameter(helper.QueryService)
	if svc == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("缺少请求参数 service"))
		return
	}

	loginUrl, err := GetAuthHandler().RedirectBeforeLoginForRequest(request.Request, svc)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "登录页面地址获取失败，无法重定向"))
		return
	}
	if conf.C.Auth.CAS.EmbeddedServerExternalHost != "" {
		URL, _ := url.Parse(loginUrl)
		URL.Host = conf.C.Auth.CAS.EmbeddedServerExternalHost
		loginUrl = URL.String()
	}
	helper.Redirect(request, response, loginUrl)

}
func (r *Resource) LoginCheck(request *restful.Request, response *restful.Response) {
	if !GetAuthHandler().IsAuthenticated(response.ResponseWriter, request.Request) {
		helper.ErrorResponse(response, stderr.Unauthenticated.Error("用户认证失败！"))
		return
	}
	user, err := GetAuthHandler().GetAuthenticationUser(request.Request)
	if err != nil {
		helper.ErrorResponse(response, stderr.Unauthenticated.Error("用户获取失败！"))
		return
	}
	if err := GetAuthHandler().SaveSession(response.ResponseWriter, request.Request, user); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("用户认证信息缓存失败"))
		return
	}

	// redirect
	redirect := helper.ParseRedirectFromRequest(request)
	helper.Redirect(request, response, redirect)
}
func (r *Resource) Logout(request *restful.Request, response *restful.Response) {
	if err := GetAuthHandler().ClearSession(response.ResponseWriter, request.Request); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "用户认证信息清除失败"))
		return
	}

	// redirect
	redirect := helper.ParseRedirectFromRequest(request)
	logoutUrl, err := GetAuthHandler().RedirectAfterLogoutForRequest(request.Request, redirect)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "登出界面地址获取失败，无法重定向"))
		return
	}
	if conf.C.Auth.CAS.EmbeddedServerExternalHost != "" {
		URL, _ := url.Parse(logoutUrl)
		URL.Host = conf.C.Auth.CAS.EmbeddedServerExternalHost
		logoutUrl = URL.String()
	}
	helper.Redirect(request, response, logoutUrl)
}

func (r *Resource) LoginWithToken(request *restful.Request, response *restful.Response) {
	token := request.QueryParameter(helper.QueryToken)
	if token == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("缺少查询参数 %s", helper.QueryToken))
		return
	}

	// if at, err := r.ts.GetAccessTokenByToken(token); err != nil {
	// 	helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "TOKEN 无法被系统识别"))
	// 	return
	// } else if !at.Valid() {
	// 	helper.ErrorResponse(response, stderr.Unauthorized.Cause(fmt.Errorf("TOKEN 已被系统禁用"), "免密登录失败"))
	// 	return
	// }
	if err := GetAuthHandler().LoginWithToken(response.ResponseWriter, request.Request, token,
		conf.C.Auth.PasswordFreeSetCookie); err != nil {
		helper.ErrorResponse(response, stderr.Unauthenticated.Cause(err, "免密登录失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}
