// package rbac 基于角色的访问控制, 目前用于文件资产与用户/用户组之间的访问控制
package rbac

import (
	"net/http"
	"path"

	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/rbac"
)

type rbacApi struct {
	rbacSvc *rbac.RbacService
}

func NewRbacApi(root string) *restful.WebService {
	return (&rbacApi{rbac.NewrbacService()}).WebService(root)
}

func (r *rbacApi) WebService(root string) *restful.WebService {
	ws := new(restful.WebService)
	ws.Path(path.Join(root, "/rbac")).
		Consumes(restful.MIME_JSON).
		Produces(restful.MIME_JSON)
	metaK := restfulspec.KeyOpenAPITags
	tags := []string{"角色访问控制"}

	// 对象
	{
		ws.Route(ws.PUT("/objects/{objId}").To(r.PutObject).
			Doc("添加一个对象与其访问策略, 已存在的对象将会更新").
			Param(ws.PathParameter("objId", "对象id")).
			Param(ws.QueryParameter("objType", "对象类型")).
			Reads([]models.PutObjectReq{}).
			Metadata(metaK, tags).
			Returns(http.StatusOK, "OK", []models.PutObjectReq{}))

		ws.Route(ws.DELETE("/objects/{objId}").To(r.DelObject).
			Doc("删除一个对象与其访问策略").
			Param(ws.PathParameter("objId", "对象id")).
			Param(ws.QueryParameter("objType", "对象类型")).
			Metadata(metaK, tags).
			Returns(http.StatusOK, "OK", nil))

		ws.Route(ws.GET("/objects/{objId}").To(r.GetObject).
			Doc("获取一个对象与其访问策略").
			Param(ws.PathParameter("objId", "对象id")).
			Param(ws.QueryParameter("objType", "对象类型")).
			Param(ws.QueryParameter("project_id", "空间id, 只返回该空间内的访问策略")).
			Metadata(metaK, tags).
			Returns(http.StatusOK, "OK", models.GetObjectRsp{}))

		ws.Route(ws.GET("/objects").To(r.ListObject).
			Doc("获取用户有权操作的对象列表和用户拥有的操作权限").
			Param(ws.QueryParameter("username", "用户名筛选, 只返回用户有权限的对象, 同时策略只会返回与用户有关(继承自用户组)的数据").Required(true)).
			Param(ws.QueryParameter("project_id", "空间id")).
			Param(ws.QueryParameter("objType", "对象类型")).
			Param(ws.QueryParameter("obj_id", "对象id")).
			Param(ws.QueryParameter("acts", "指定期望具有的权限, 逗号分隔")).
			Metadata(metaK, tags).
			Returns(http.StatusOK, "OK", []models.GetObjectRsp{}))
	}
	// 策略
	{
		ws.Route(ws.GET("/policies").To(r.ListPolicy).
			Doc("获取访问策略列表").
			Param(ws.QueryParameter("obj_ids", "对象id数组,分隔")).
			Param(ws.QueryParameter("obj_type", "对象类型")).
			Param(ws.QueryParameter("project_id", "空间id")).
			Param(ws.QueryParameter("act", "readonly,all")).
			Param(ws.QueryParameter("sub_type", "user,group")).
			Param(ws.QueryParameter("username", "用户名,不继承来自用户组的权限")).
			Param(ws.QueryParameter("group_id", "用户组id")).
			Metadata(metaK, tags).
			Returns(http.StatusOK, "OK", []models.Policy{}))
	}
	return ws
}
