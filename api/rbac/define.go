package rbac

import (
	"fmt"
	"strings"

	"github.com/emicklei/go-restful/v3"
	"github.com/samber/lo"
	"transwarp.io/applied-ai/aiot/vision-std/clients/cas"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func (r *rbacApi) PutObject(request *restful.Request, response *restful.Response) {
	objid := request.PathParameter("objId")
	if objid == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("objId: %s", objid))
		return
	}
	objt := request.QueryParameter("objType")
	if objt == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("objType: %s", objt))
		return
	}
	req := make([]*models.PutObjectReq, 0)
	err := request.ReadEntity(&req)
	if err != nil {
		helper.ErrorResponse(response, stderr.Unmarshal.Cause(err, ""))
		return
	}
	uniq := make(map[string]struct{}, len(req))
	var key string
	for _, v := range req {
		if v.SubType == "" ||
			(v.Username == "" && v.GroupId == 0) ||
			v.Act == "" {
			helper.ErrorResponse(response, stderr.InvalidParam.Errorf("参数不能为空: %+v", v))
			return
		}
		if (v.SubType == cas.SubType_User && v.Username == "") ||
			(v.SubType == cas.SubType_Group && v.GroupId == 0) {
			helper.ErrorResponse(response, stderr.InvalidParam.Errorf("用户或用户组参数与SubType不匹配: %+v", v))
			return
		}
		key = fmt.Sprintf("%s-%s-%d-%s-%s-%s", v.SubType, v.Username, v.GroupId, v.ProjectId, objt, objid)
		if _, ok := uniq[key]; !ok {
			uniq[key] = struct{}{}
		} else {
			helper.ErrorResponse(response, stderr.InvalidParam.Errorf("每个用户或用户组只能在同一个空间下配置一条权限: %+v", v))
			return
		}
	}
	err = r.rbacSvc.PutObject(request.Request.Context(), cas.ObjType(objt), objid, req)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, ""))
		return
	}
	helper.SuccessResponse(response, req)
}

func (r *rbacApi) DelObject(request *restful.Request, response *restful.Response) {
	objid := request.PathParameter("objId")
	if objid == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("objId: %s", objid))
		return
	}
	objt := request.QueryParameter("objType")
	if objt == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("objType: %s", objt))
		return
	}
	err := r.rbacSvc.DelObject(request.Request.Context(), cas.ObjType(objt), objid)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, ""))
		return
	}
	helper.SuccessResponse(response, nil)
}

func (r *rbacApi) GetObject(request *restful.Request, response *restful.Response) {
	objid := request.PathParameter("objId")
	if objid == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("objId: %s", objid))
		return
	}
	objt := request.QueryParameter("objType")
	if objt == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("objType: %s", objt))
		return
	}
	rsp, err := r.rbacSvc.GetObject(request.Request.Context(), cas.ObjType(objt), objid)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, ""))
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *rbacApi) ListObject(request *restful.Request, response *restful.Response) {
	username := request.QueryParameter("username")
	if username == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("username: %s", username))
		return
	}
	objt := request.QueryParameter("objType")
	objId := request.QueryParameter("obj_id")
	projectId := request.QueryParameter("project_id")
	act := request.QueryParameter("acts")
	acts := make([]cas.Act, 0, strings.Count(act, ",")+1)
	if act != "" {
		acts = lo.UniqMap(strings.Split(act, ","), func(e string, _ int) cas.Act { return cas.Act(e) })
	}
	rsp, err := r.rbacSvc.ListObject(request.Request.Context(), username, projectId, cas.ObjType(objt), objId, acts...)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, ""))
		return
	}
	helper.SuccessResponse(response, rsp)
}
