/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// OperandDefinition defines operand identity, which could be at least one Static or Runtime Meta object(s) or Meta type(s) like Component, ComponentInstance, etc..
type OperandDefinition struct {
	Kind string `json:"kind"`
}

// OperationDefinitionSpec defines the desired state of OperationDefinition
type OperationDefinitionSpec struct {
	Operand OperandDefinition `json:"operand,omitempty"`

	// Operator should be "start", "stop", "snapshot", "restore", etc..
	Operator string `json:"operator,omitempty"`

	Policies []PolicyDefinition `json:"policies,omitempty"`
}

// PolicyDefinition defines policy's metadata, OperationTrait schematic and OperationPlan schematic
type PolicyDefinition struct {
	Name           string    `json:"name,omitempty"`
	ShortName      string    `json:"shortName,omitempty"`
	Content        string    `json:"content,omitempty"`
	OperationTrait Schematic `json:"operationTrait,omitempty"`
	OperationPlan  Schematic `json:"operationPlan,omitempty"`
}

// CUE defines the encapsulation of one capability in CUE format
type CUE struct {
	Template string `json:"template,omitempty"`
}

// Schematic defines the encapsulation of one capability.
//
//	Struct field definition should be in format of <field name> <schematic> `impl:<SchematicImplementationKey>`
//	Struct field's tag "impl:<SchematicImplementationKey>" refers to a concrete implementation.
//	Ordered by field index, the first non-nil-valued struct field determines which concrete implementation is chosen.
type Schematic struct {
	CUE *CUE `json:"cue,omitempty" impl:"cue"`
	// Insert other encapsulation formats such as other interpretive languages here...
}

// OperationDefinitionStatus defines the observed state of OperationDefinition
type OperationDefinitionStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file
}

// OperationDefinition is the Schema for the operationdefinitions API
// +genclient
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:metadata:labels="group=oam.tdc.transwarp.io"
// +kubebuilder:resource:shortName=opdef
type OperationDefinition struct {
	metav1.TypeMeta `json:",inline"`

	// ObjectMeta Name's syntax might be [Spec.Operator]-[Spec.Operand.Kind] in lowercase like "start-component-instance"
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   OperationDefinitionSpec   `json:"spec,omitempty"`
	Status OperationDefinitionStatus `json:"status,omitempty"`
}

//+kubebuilder:object:root=true

// OperationDefinitionList contains a list of OperationDefinition
type OperationDefinitionList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []OperationDefinition `json:"items"`
}

func init() {
	SchemeBuilder.Register(&OperationDefinition{}, &OperationDefinitionList{})
}
