package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"transwarp.io/applied-ai/central-auth-service/api/apis/oam.tdc.transwarp.io/common"
)

// OperationTrait describes a trait of an operation policy on a specific operand
// +genclient
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:metadata:labels="group=oam.tdc.transwarp.io"
// +kubebuilder:resource:shortName=optrait
type OperationTrait struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              OperationTraitSpec `json:"spec,omitempty"`
}

// OperationTraitList list of op trait
// +kubebuilder:object:root=true
type OperationTraitList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []OperationTrait `json:"items,omitempty"`
}

type OperationTraitRef struct {
	// OperationDefinition refers to an operation definition instance
	OperationDefinition string `json:"operationDefinition"`
	// Policy refers to a specific policy in operation definition
	Policy string `json:"policy"`
	// OperandReference refers to OAM object(s)
	// +kubebuilder:pruning:PreserveUnknownFields
	OperandReference common.RawReference `json:"operand"`
}

type OperationTraitSpec struct {
	OperationTraitRef `json:",inline"`
	DependsOn         []OperationTraitRef `json:"dependsOn,omitempty"`
	Includes          []OperationTraitRef `json:"includes,omitempty"`
}

func init() {
	SchemeBuilder.Register(&OperationTrait{}, &OperationTraitList{})
}
