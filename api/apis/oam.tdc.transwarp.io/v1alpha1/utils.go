package v1alpha1

import (
	"transwarp.io/applied-ai/central-auth-service/api/apis/oam.tdc.transwarp.io/common"
)

func ConvertOperationDefinitionToReference(definition *OperationDefinition) *common.OperationDefinitionReference {
	return &common.OperationDefinitionReference{Name: definition.Name, Namespace: definition.Namespace}
}

//func NewOperationTraitSpec() *OperationTraitSpec {
//	return &OperationTraitSpec{
//		OperationTraitRef: OperationTraitRef{},
//		DependsOn:         nil,
//		Includes:          nil,
//	}
//}
