//go:build !ignore_autogenerated
// +build !ignore_autogenerated

/*
Copyright 2023.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by controller-gen. DO NOT EDIT.

package v1alpha1

import (
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CUE) DeepCopyInto(out *CUE) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CUE.
func (in *CUE) DeepCopy() *CUE {
	if in == nil {
		return nil
	}
	out := new(CUE)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OperandDefinition) DeepCopyInto(out *OperandDefinition) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OperandDefinition.
func (in *OperandDefinition) DeepCopy() *OperandDefinition {
	if in == nil {
		return nil
	}
	out := new(OperandDefinition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OperationDefinition) DeepCopyInto(out *OperationDefinition) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	out.Status = in.Status
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OperationDefinition.
func (in *OperationDefinition) DeepCopy() *OperationDefinition {
	if in == nil {
		return nil
	}
	out := new(OperationDefinition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *OperationDefinition) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OperationDefinitionList) DeepCopyInto(out *OperationDefinitionList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]OperationDefinition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OperationDefinitionList.
func (in *OperationDefinitionList) DeepCopy() *OperationDefinitionList {
	if in == nil {
		return nil
	}
	out := new(OperationDefinitionList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *OperationDefinitionList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OperationDefinitionSpec) DeepCopyInto(out *OperationDefinitionSpec) {
	*out = *in
	out.Operand = in.Operand
	if in.Policies != nil {
		in, out := &in.Policies, &out.Policies
		*out = make([]PolicyDefinition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OperationDefinitionSpec.
func (in *OperationDefinitionSpec) DeepCopy() *OperationDefinitionSpec {
	if in == nil {
		return nil
	}
	out := new(OperationDefinitionSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OperationDefinitionStatus) DeepCopyInto(out *OperationDefinitionStatus) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OperationDefinitionStatus.
func (in *OperationDefinitionStatus) DeepCopy() *OperationDefinitionStatus {
	if in == nil {
		return nil
	}
	out := new(OperationDefinitionStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OperationTrait) DeepCopyInto(out *OperationTrait) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OperationTrait.
func (in *OperationTrait) DeepCopy() *OperationTrait {
	if in == nil {
		return nil
	}
	out := new(OperationTrait)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *OperationTrait) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OperationTraitList) DeepCopyInto(out *OperationTraitList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]OperationTrait, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OperationTraitList.
func (in *OperationTraitList) DeepCopy() *OperationTraitList {
	if in == nil {
		return nil
	}
	out := new(OperationTraitList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *OperationTraitList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OperationTraitRef) DeepCopyInto(out *OperationTraitRef) {
	*out = *in
	in.OperandReference.DeepCopyInto(&out.OperandReference)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OperationTraitRef.
func (in *OperationTraitRef) DeepCopy() *OperationTraitRef {
	if in == nil {
		return nil
	}
	out := new(OperationTraitRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OperationTraitSpec) DeepCopyInto(out *OperationTraitSpec) {
	*out = *in
	in.OperationTraitRef.DeepCopyInto(&out.OperationTraitRef)
	if in.DependsOn != nil {
		in, out := &in.DependsOn, &out.DependsOn
		*out = make([]OperationTraitRef, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Includes != nil {
		in, out := &in.Includes, &out.Includes
		*out = make([]OperationTraitRef, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OperationTraitSpec.
func (in *OperationTraitSpec) DeepCopy() *OperationTraitSpec {
	if in == nil {
		return nil
	}
	out := new(OperationTraitSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PolicyDefinition) DeepCopyInto(out *PolicyDefinition) {
	*out = *in
	in.OperationTrait.DeepCopyInto(&out.OperationTrait)
	in.OperationPlan.DeepCopyInto(&out.OperationPlan)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PolicyDefinition.
func (in *PolicyDefinition) DeepCopy() *PolicyDefinition {
	if in == nil {
		return nil
	}
	out := new(PolicyDefinition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProductInstance) DeepCopyInto(out *ProductInstance) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	out.Spec = in.Spec
	out.Status = in.Status
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProductInstance.
func (in *ProductInstance) DeepCopy() *ProductInstance {
	if in == nil {
		return nil
	}
	out := new(ProductInstance)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ProductInstance) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProductInstanceList) DeepCopyInto(out *ProductInstanceList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ProductInstance, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProductInstanceList.
func (in *ProductInstanceList) DeepCopy() *ProductInstanceList {
	if in == nil {
		return nil
	}
	out := new(ProductInstanceList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ProductInstanceList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProductInstanceSpec) DeepCopyInto(out *ProductInstanceSpec) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProductInstanceSpec.
func (in *ProductInstanceSpec) DeepCopy() *ProductInstanceSpec {
	if in == nil {
		return nil
	}
	out := new(ProductInstanceSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProductInstanceStatus) DeepCopyInto(out *ProductInstanceStatus) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProductInstanceStatus.
func (in *ProductInstanceStatus) DeepCopy() *ProductInstanceStatus {
	if in == nil {
		return nil
	}
	out := new(ProductInstanceStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Schematic) DeepCopyInto(out *Schematic) {
	*out = *in
	if in.CUE != nil {
		in, out := &in.CUE, &out.CUE
		*out = new(CUE)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Schematic.
func (in *Schematic) DeepCopy() *Schematic {
	if in == nil {
		return nil
	}
	out := new(Schematic)
	in.DeepCopyInto(out)
	return out
}
