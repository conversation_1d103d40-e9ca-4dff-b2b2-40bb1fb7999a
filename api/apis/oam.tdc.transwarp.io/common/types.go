package common

import (
	"fmt"
	"k8s.io/apimachinery/pkg/runtime"
)

// SystemDefinitionNamespace is the system definition namespace.
// To make life easier, temporarily use constant instead of config or variable. Optimize if necessary.
const SystemDefinitionNamespace string = "tdcsys"

// NamespacedName the namespace and name model
type NamespacedName struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
}

type MetaDataBase struct {
	Kind      string `json:"kind"`
	Namespace string `json:"namespace,omitempty"`
	Name      string `json:"name,omitempty"`
	Id        uint64 `json:"id,omitempty"`
	UUID      string `json:"uuid,omitempty"`
}

//+kubebuilder:object:generate=true

type MetaData struct {
	Kind            string        `json:"kind"`
	Namespace       string        `json:"namespace,omitempty"`
	Name            string        `json:"name,omitempty"`
	Id              uint64        `json:"id,omitempty"`
	UUID            string        `json:"uuid,omitempty"`
	Parent          *MetaDataBase `json:"parent,omitempty"`
	ResourceVersion string        `json:"resourceVersion,omitempty"`
}

func (m *MetaData) IsValidMeta() bool {
	// id and uuid must not be empty at same time
	return m.Id != 0 || m.UUID != ""
}

func (m *MetaData) AsParent() *MetaDataBase {
	return &MetaDataBase{
		Kind:      m.Kind,
		Namespace: m.Namespace,
		Name:      m.Name,
		Id:        m.Id,
		UUID:      m.UUID,
	}
}

func (m *MetaData) String() string {
	return fmt.Sprintf("MetaData[kind: %s, namespace: %s, name: %s, id: %d, uuid: %s]",
		m.Kind, m.Namespace, m.Name, m.Id, m.UUID)
}

// MetaDataList is a list of MetaData of OAM objects of the same kind
type MetaDataList struct {
	Kind  string     `json:"kind"`
	Items []MetaData `json:"items"`
}

// RawFeature describes a feature of an OAM object
type RawFeature struct {
	Name      string      `json:"name"`
	Supported bool        `json:"supported"`
	Content   interface{} `json:"content"`
}

// RawProperty describes OAM object(s) of the same kind sharing the same properties
type RawProperty struct {
	Kind       string      `json:"kind"`
	Properties interface{} `json:"properties"`
}

// +kubebuilder:object:generate=true

// RawReference is reference to an OAM object where runtime.RawExtension.Raw bytes Must be JSON-encoded data where
//
//	the JSON MUST containing a required name "kind" with other custom names whose struct
//	the JSON will look something like this:
//	{
//		"kind":"MyKind",  				 // field for every kind
//		"name": "MyKind object name"			 // field for every "MyKind" object
//		"namespace": "MyKind object namespace"  // field for every "MyKind" object
//	}
type RawReference struct {
	runtime.RawExtension `json:"-"`
}

//+kubebuilder:object:generate=true

type OperationDefinitionReference struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
}

type PolicyDefinitionReference struct {
	OpDefRef OperationDefinitionReference `json:"operationDefinitionRef"`
	Policy   string                       `json:"policy"`
}

type OperatorWithPolicy struct {
	Operator string `json:"operator"`
	Policy   string `json:"policy"`
}
