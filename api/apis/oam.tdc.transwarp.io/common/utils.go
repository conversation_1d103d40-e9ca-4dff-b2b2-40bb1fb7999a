package common

import "fmt"

func NewOperationDefinitionReference(name string, namespace *string) *OperationDefinitionReference {
	if namespace == nil {
		return &OperationDefinitionReference{Name: name, Namespace: SystemDefinitionNamespace}
	}
	return &OperationDefinitionReference{Name: name, Namespace: *namespace}
}

func GenOperationDefinitionObjectKey(reference OperationDefinitionReference) string {
	return fmt.Sprintf("%s/%s", SystemDefinitionNamespace, reference.Name)
}
