package gpu_spec

import (
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
)

func (r *Resource) ListAllGpuSpecs(request *restful.Request, response *restful.Response) {
	gpuSpecs, err := r.gs.ListGpuSpecs()
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, gpuSpecs)
}

func (r *Resource) GetGpuSpec(request *restful.Request, response *restful.Response) {
	id := request.PathParameter(QueryParamGpuSpecId)
	gpuSpec, err := r.gs.GetGpuSpecByID(id)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, gpuSpec)
}

func (r *Resource) CreateGpuSpec(request *restful.Request, response *restful.Response) {
	gpuSpec := new(dao.GpuSpec)
	if err := request.ReadEntity(gpuSpec); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	if err := r.gs.CreateGpuSpec(gpuSpec); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, nil)
}

func (r *Resource) UpdateGpuSpec(request *restful.Request, response *restful.Response) {
	id := request.PathParameter(QueryParamGpuSpecId)
	gpuSpec := new(dao.GpuSpec)
	if err := request.ReadEntity(gpuSpec); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	if err := r.gs.UpdateGpuSpec(gpuSpec, id); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, nil)
}

func (r *Resource) DeleteGpuSpec(request *restful.Request, response *restful.Response) {
	id := request.PathParameter(QueryParamGpuSpecId)
	if err := r.gs.DeleteGpuSpecByID(id); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, nil)
}
