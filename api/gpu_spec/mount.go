package gpu_spec

import (
	"fmt"
	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"
	"net/http"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/service"
)

const (
	QueryParamGpuSpecId = "gpu_spec_id"
)

func NewGpuSpecAPI(root string, gs *service.GpuSpec) *restful.WebService {
	return (&Resource{gs}).WebService(root)
}

type Resource struct {
	gs *service.GpuSpec
}

func (r *Resource) WebService(root string) *restful.WebService {
	tags := []string{"GPU规格管理"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(root + "/gpumgr")
	ws.Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)
	GpuSpecIdPathParam := ws.PathParameter(QueryParamGpuSpecId, "GPU配置id")

	ws.Route(ws.GET("/gpu_specs").To(r.ListAllGpuSpecs).
		Doc("获取所有GPU配置列表").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []*dao.GpuSpec{}))

	ws.Route(ws.GET(fmt.Sprintf("/gpu_specs/{%s}", QueryParamGpuSpecId)).To(r.GetGpuSpec).
		Doc("获取单个GPU配置").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(GpuSpecIdPathParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.GpuSpec{}))

	ws.Route(ws.POST("/gpu_specs").To(r.CreateGpuSpec).
		Doc("创建一个GPU配置").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Reads(dao.GpuSpec{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), nil))

	ws.Route(ws.PUT(fmt.Sprintf("/gpu_specs/{%s}", QueryParamGpuSpecId)).To(r.UpdateGpuSpec).
		Doc("更新一个GPU配置").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(GpuSpecIdPathParam).
		Reads(dao.GpuSpec{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), nil))

	ws.Route(ws.DELETE(fmt.Sprintf("/gpu_specs/{%s}", QueryParamGpuSpecId)).To(r.DeleteGpuSpec).
		Doc("删除一个GPU配置").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(GpuSpecIdPathParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), nil))
	return ws
}
