package portal

import (
	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"
	"net/http"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/service"
)

func NewPortalInfoAPI(root string, ps *service.PortalInfoService) *restful.WebService {
	return (&Resource{ps}).WebService(root)
}

type Resource struct {
	ps *service.PortalInfoService
}

func (r *Resource) WebService(root string) *restful.WebService {
	tags := []string{"首页信息管理"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(root + "/portalmgr")
	ws.Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	projectIDQueryParam := ws.QueryParameter(helper.QueryParamProjectId, "空间id").Required(true)
	tenantIDQueryParam := ws.QueryParameter(helper.QueryParamTenantId, "租户id").Required(true)

	ws.Route(ws.GET("/portal").To(r.GetPortalInfo).
		Doc("获取某个空间的首页信息").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Param(tenantIDQueryParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.PortalInfoDO{}))

	ws.Route(ws.POST("/portal").To(r.UpsertPortalInfo).
		Doc("更新某个空间的首页信息").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Param(tenantIDQueryParam).
		Reads(dao.PortalInfoDO{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}))

	return ws
}
