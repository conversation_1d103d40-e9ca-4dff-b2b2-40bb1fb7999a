package statistics

import (
	"net/http"

	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/central-auth-service/models"
	service "transwarp.io/applied-ai/central-auth-service/service/statistics"
)

func NewStatisticsAPI(root string, ss *service.StatisticsService) *restful.WebService {
	return (&Resource{ss: ss}).WebService(root)
}

type Resource struct {
	ss *service.StatisticsService
}

func (r *Resource) WebService(root string) *restful.WebService {
	tags := []string{"资产统计信息"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(root + "/stats")
	ws.Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.GET("/project/summary").To(r.GetProjectSummary).
		Doc("get project summary").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.ProjectSummary{}))

	// call cvat applet and  api directly
	// ws.Route(ws.POST("/assets/overview").To(r.GetAssetsOverview).
	// 	Doc("get assets overview").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
	// 	Returns(http.StatusOK, http.StatusText(http.StatusOK), models.AssetsOverview{}))

	ws.Route(ws.GET("/static-assets").To(r.GetStaticAssetsDetails).
		Doc("get static assets details").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(ws.QueryParameter("page", "page number").Required(false)).
		Param(ws.QueryParameter("page_size", "page size").Required(false)).
		Param(ws.QueryParameter("project_name", "fuzzy project name").Required(false)).
		Param(ws.QueryParameter("creator", "fuzzy creator").Required(false)).
		Param(ws.QueryParameter("assets_subtype", "filter by assets subtype").Required(false)).
		Param(ws.QueryParameter("sort_by", "sort by filed").DataType("string").DefaultValue("update_time")).
		Param(ws.QueryParameter("sort_order", "sort order descend or ascend").DataType("string").DefaultValue("descend")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.StaticAssetsResp{}))

	// call serving api directly
	// ws.Route(ws.POST("/dynamic-assets").To(r.GetDynamicAssetsDetails).
	// 	Doc("get dynamic assets details").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
	// 	Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.ServiceBaseInfoList{}))

	return ws
}
