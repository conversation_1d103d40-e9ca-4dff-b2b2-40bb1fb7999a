package statistics

import (
	"strconv"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func (r *Resource) GetProjectSummary(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	summary, err := r.ss.GetProjectSummary(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
	}
	helper.SuccessResponse(response, summary)
}

func (r *Resource) GetAssetsOverview(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	assetsOverview, err := r.ss.GetAssetsOverview(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
	}
	helper.SuccessResponse(response, assetsOverview)
}

func (r *Resource) GetStaticAssetsDetails(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	page, _ := strconv.ParseInt(request.QueryParameter("page"), 10, 64)
	pageSize, _ := strconv.ParseInt(request.QueryParameter("page_size"), 10, 64)
	fuzzyProjectName := request.QueryParameter("project_name")
	fuzzyCreator := request.QueryParameter("creator")
	assetsTypesArr := request.QueryParameters("assets_types")
	var assetsTypes []models.AssetsType
	for _, typeStr := range assetsTypesArr {
		switch models.AssetsType(typeStr) {
		case models.Model, models.Application, models.Corups, models.Knowledge:
			assetsTypes = append(assetsTypes, models.AssetsType(typeStr))
		}
	}

	queryReq := &models.StaticAssetsQueryReq{
		AssetsTypes: assetsTypes,
		ProjectName: fuzzyProjectName,
		Creator:     fuzzyCreator,
		Page:        int(page),
		PageSize:    int(pageSize),
	}
	details, err := r.ss.GetStaticAssetsDetail(ctx, queryReq)
	if err != nil {
		helper.ErrorResponse(response, err)
	}
	helper.SuccessResponse(response, details)
}

func (r *Resource) GetDynamicAssetsDetails(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	pageSize, _ := strconv.ParseInt(request.QueryParameter("page_size"), 10, 64)
	page, _ := strconv.ParseInt(request.QueryParameter("page"), 10, 64)
	OrderBy := request.QueryParameter("order_by")

	details, err := r.ss.GetDynamicAssetsDetails(ctx, OrderBy, int(page), int(pageSize))
	if err != nil {
		helper.ErrorResponse(response, err)
	}

	stdsrv.SuccessResponseMixWithProto(response, details)
}
