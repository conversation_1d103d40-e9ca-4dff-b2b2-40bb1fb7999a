package statistics

import (
	"strconv"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func (r *Resource) GetProjectSummary(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	summary, err := r.ss.GetProjectSummary(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
	}
	helper.SuccessResponse(response, summary)
}

func (r *Resource) GetAssetsOverview(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	assetsOverview, err := r.ss.GetAssetsOverview(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
	}
	helper.SuccessResponse(response, assetsOverview)
}

func (r *Resource) GetStaticAssetsDetails(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	page, _ := strconv.ParseInt(request.QueryParameter("page"), 10, 64)
	pageSize, _ := strconv.ParseInt(request.QueryParameter("page_size"), 10, 64)
	fuzzyProjectName := request.QueryParameter("project_name")
	fuzzyCreator := request.QueryParameter("creator")
	assetsSubtypesArr := request.QueryParameters("assets_subtype")
	var assetsSubtypes []models.AssetsSubtype
	for _, typeStr := range assetsSubtypesArr {
		switch models.AssetsSubtype(typeStr) {
		case models.MwhTypeModel, models.MwhTypeRemoteService, models.CorpusTypeDataset, models.CorpusTypeFileAsset, models.KbsTypeText:
			assetsSubtypes = append(assetsSubtypes, models.AssetsSubtype(typeStr))
		case models.KbsTypeTable, models.AppletTypeChain:
			assetsSubtypes = append(assetsSubtypes, models.AssetsSubtype(typeStr))
		case models.AppletTypeAssistant, models.AppletTypeExternalRegister, models.AppletTypeTaskChain, models.AppletTypeCustomDeployed:
			assetsSubtypes = append(assetsSubtypes, models.AssetsSubtype(typeStr))
		}
	}
	sortBy := request.QueryParameter("sort_by")
	sortOrder := request.QueryParameter("sort_order")

	queryReq := &models.StaticAssetsQueryReq{
		AssetsSubtypes: assetsSubtypes,
		ProjectName:    fuzzyProjectName,
		Creator:        fuzzyCreator,
		Page:           int(page),
		PageSize:       int(pageSize),
		SortBy:         sortBy,
		SortOrder:      sortOrder,
	}
	details, err := r.ss.GetStaticAssetsDetail(ctx, queryReq)
	if err != nil {
		helper.ErrorResponse(response, err)
	}
	helper.SuccessResponse(response, details)
}

func (r *Resource) GetDynamicAssetsDetails(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	pageSize, _ := strconv.ParseInt(request.QueryParameter("page_size"), 10, 64)
	page, _ := strconv.ParseInt(request.QueryParameter("page"), 10, 64)
	OrderBy := request.QueryParameter("order_by")

	details, err := r.ss.GetDynamicAssetsDetails(ctx, OrderBy, int(page), int(pageSize))
	if err != nil {
		helper.ErrorResponse(response, err)
	}

	stdsrv.SuccessResponseMixWithProto(response, details)
}
