package custom

import (
	"net/http"

	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"
	"gorm.io/gorm"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/custom"
)

func NewCustomAPI(prefix string, db *gorm.DB) *restful.WebService {
	custom.Init(db)
	return (&Resource{
		cs: custom.GetConfigService(),
		ls: custom.GetLogoService(),
		ns: custom.GetNoticeService(),
		is: custom.GetIconService(),
	}).WebService(prefix)
}

type Resource struct {
	cs dao.CustomConfigService
	ls custom.LogoService
	ns custom.NoticeService
	is custom.IconService
}

func (r *Resource) WebService(prefix string) *restful.WebService {
	tags := []string{"配置管理"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags

	ws := new(restful.WebService)
	ws.Path(prefix)
	ws.Consumes(restful.MIME_JSON, restful.MIME_OCTET).Produces(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.GET("/config/defines").To(r.ListConfigs).
		Doc("获取系统配置列表").Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []*pb.DynamicParam{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.GET("/config/values").To(r.GetConfigValue).
		Doc("获取系统配置以及logo").Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), &models.CustomConfig{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.PUT("/config/values").To(r.UpdateConfigValue).
		Doc("修改系统配置的值").Metadata(metaK, metaV).
		Writes(&models.CustomConfig{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.GET("/logo").To(r.GetLogo).
		Doc("获取系统logo").Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), &models.CustomLogo{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.PUT("/logo").To(r.UpdateLogo).
		Doc("修改系统logo").Metadata(metaK, metaV).
		Writes(&models.CustomLogo{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.GET("/icon").To(r.GetIcon).
		Doc("获取系统icon").Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), &models.CustomLogo{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.PUT("/icon").To(r.UpdateIcon).
		Doc("修改系统icon").Metadata(metaK, metaV).
		Writes(&models.CustomLogo{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.GET("/notices").To(r.ListNotices).
		Doc("获取公告列表").Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []*models.CustomNotice{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.POST("/notices").To(r.CreateNotice).
		Doc("新增公告").Metadata(metaK, metaV).
		Writes(&models.CustomNotice{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.PUT("/notices/{id}").To(r.UpdateNotice).
		Doc("编辑公告").Metadata(metaK, metaV).
		Writes(&models.CustomNotice{}).
		Param(ws.PathParameter("id", "公告id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.DELETE("/notices").To(r.DeleteNotice).
		Doc("批量删除公告，ids传用逗号分割的id").Metadata(metaK, metaV).
		Param(ws.QueryParameter("ids", "逗号分割的id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.POST("/notices/{id}/online").To(r.OnlineNotice).
		AllowedMethodsWithoutContentType([]string{http.MethodPost}).
		Doc("上线公告").Metadata(metaK, metaV).
		Param(ws.PathParameter("id", "公告id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.POST("/notices/{id}/offline").To(r.OfflineNotice).
		AllowedMethodsWithoutContentType([]string{http.MethodPost}).
		Doc("下线公告").Metadata(metaK, metaV).
		Param(ws.PathParameter("id", "公告id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	return ws
}
