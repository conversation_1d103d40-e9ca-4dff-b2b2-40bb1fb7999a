package custom

import (
	"strconv"
	"strings"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func (r *Resource) ListConfigs(request *restful.Request, response *restful.Response) {
	lang := stdsrv.GetLanguage(request)
	result := r.cs.ListConfigs(lang)
	stdsrv.SuccessResponseMixWithProto(response, result)
}

func (r *Resource) GetConfigValue(request *restful.Request, response *restful.Response) {
	//username := helper.ParseUsername(request)
	// 合并一下返回
	config, err := r.cs.GetConfigValue()
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	logo, err := r.ls.GetLogo()
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	icon, err := r.is.GetIcon()
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, map[string]interface{}{
		"logo":   logo,
		"icon":   icon,
		"config": config,
	})
}

func (r *Resource) UpdateConfigValue(request *restful.Request, response *restful.Response) {
	username := helper.ParseUsername(request)
	body := new(models.CustomConfig)
	err := request.ReadEntity(body)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "read body error."))
		return
	}
	body.UpdatedBy = username
	err = r.cs.UpdateConfigValue(body)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) GetLogo(request *restful.Request, response *restful.Response) {
	result, err := r.ls.GetLogo()
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, result)
}

func (r *Resource) UpdateLogo(request *restful.Request, response *restful.Response) {
	username := helper.ParseUsername(request)
	body := new(models.CustomLogo)
	err := request.ReadEntity(body)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "read body error."))
		return
	}
	body.UpdatedBy = username
	err = r.ls.UpdateLogo(body)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) GetIcon(request *restful.Request, response *restful.Response) {
	result, err := r.is.GetIcon()
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, result)
}

func (r *Resource) UpdateIcon(request *restful.Request, response *restful.Response) {
	username := helper.ParseUsername(request)
	body := new(models.CustomIcon)
	err := request.ReadEntity(body)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "read body error."))
		return
	}
	body.UpdatedBy = username
	err = r.is.UpdateIcon(body)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) CreateNotice(request *restful.Request, response *restful.Response) {
	username := helper.ParseUsername(request)
	body := new(models.CustomNotice)
	err := request.ReadEntity(body)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "read body error."))
		return
	}
	body.CreatedBy = username
	body.UpdatedBy = username
	err = r.ns.CreateNotice(body)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) ListNotices(request *restful.Request, response *restful.Response) {
	res, err := r.ns.ListNotices()
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}

func (r *Resource) UpdateNotice(request *restful.Request, response *restful.Response) {
	id := request.PathParameter("id")
	uid, err := strconv.ParseUint(id, 10, 64)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "parse id error."))
		return
	}
	username := helper.ParseUsername(request)
	body := new(models.CustomNotice)
	err = request.ReadEntity(body)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "read body error."))
		return
	}
	body.ID = uint(uid)
	body.UpdatedBy = username
	err = r.ns.UpdateNotice(body)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) GetNotice(request *restful.Request, response *restful.Response) {
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) DeleteNotice(request *restful.Request, response *restful.Response) {
	ids := request.QueryParameter("ids")
	uids := make([]uint, 0)
	for _, id := range strings.Split(ids, ",") {
		uid, err := strconv.ParseUint(id, 10, 64)
		if err != nil {
			helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "parse ids error."))
			return
		}
		uids = append(uids, uint(uid))
	}
	if len(uids) == 0 {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("ids is empty."))
		return
	}
	err := r.ns.DeleteNotice(uids...)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) OnlineNotice(request *restful.Request, response *restful.Response) {
	id := request.PathParameter("id")
	uid, err := strconv.ParseUint(id, 10, 64)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "parse id error."))
		return
	}
	err = r.ns.OnlineNotice(uint(uid))
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) OfflineNotice(request *restful.Request, response *restful.Response) {
	id := request.PathParameter("id")
	uid, err := strconv.ParseUint(id, 10, 64)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "parse id error."))
		return
	}
	err = r.ns.OfflineNotice(uint(uid))
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}
