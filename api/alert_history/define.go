package alert_history

import (
	"context"
	"github.com/aws/smithy-go/ptr"
	"github.com/emicklei/go-restful/v3"
	"strconv"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/dao/alert_history"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/alerting"
	"transwarp.io/applied-ai/central-auth-service/utils/auth/cas/client"
)

func (r *Resource) ReceiveAlertHistoryFromGrafana(request *restful.Request, response *restful.Response) {
	req := &models.AlertsHistory{}
	if err := request.ReadEntity(&req); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "parse req  err"))
		return
	}
	ruleID, err := req.GetRuleID()
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "parse rule if  err"))
		return
	}
	stdlog.Infof("receive from grafana :%v", ruleID)
	ctx := context.Background()
	ID, err := alerting.GetAlertHistoryMgrInstance().Create(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "create alert err"))
		return
	}
	helper.SuccessResponse(response, struct{ ID string }{ID: ID})
}

func (r *Resource) List(request *restful.Request, response *restful.Response) {
	svcID, userName := "", ""
	filterType := request.QueryParameter("filter_type")
	if filterType == models.QueryTypeUser {
		userName = client.Username(request.Request)
	} else {
		svcID = request.QueryParameter("service_id")
	}
	ctx := context.Background()
	if userName != "" {
		userName = strconv.Quote(userName)
	}
	alerts, err := alerting.GetAlertHistoryMgrInstance().List(ctx, alert_history.ListAlertReq{
		SvcID:    svcID,
		UserName: userName,
	})
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "parse req err"))
		return
	}
	res := make([]*models.AlertsHistoryDTO, 0)
	for _, a := range alerts {
		dto, err := a.TODTO()
		if err != nil {
			helper.ErrorResponse(response, err)
			return
		}
		res = append(res, dto)
	}
	helper.SuccessResponse(response, res)
}

func (r *Resource) DelByIDs(request *restful.Request, response *restful.Response) {
	IDs := &models.IDs{}
	ctx := context.Background()
	if err := request.ReadEntity(&IDs); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "parse req  err"))
		return
	}
	err := alerting.GetAlertHistoryMgrInstance().DeleteByIDs(ctx, IDs.IDs)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, struct{}{})
}

func (r *Resource) MarkAsRead(request *restful.Request, response *restful.Response) {
	ID := request.PathParameter("id")
	ctx := context.Background()
	if ID == "" {
		helper.ErrorResponse(response, stderr.Internal.Errorf("no alert id"))
		return
	}
	if err := alerting.GetAlertHistoryMgrInstance().MarkStateAsRead(ctx, ID); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, struct{}{})
}

// gorm create &struct 时，会忽略零值，需要把零值变成非零值
func updateReceiverZeroValue(cfg *models.AlertReceiverConfig) {
	if cfg.ServiceCreator == nil {
		cfg.ServiceCreator = ptr.String("")
	}
	if cfg.TargetReceivers == nil {
		cfg.TargetReceivers = []string{}
	}
	if cfg.ProjectManager == nil {
		cfg.ProjectManager = []string{}
	}
}

func (r *Resource) UpdateReceiverCfg(request *restful.Request, response *restful.Response) {
	svcID := request.PathParameter("svc_id")
	if svcID == "" {
		helper.ErrorResponse(response, stderr.Internal.Errorf("no svc id"))
		return
	}
	cfg := &models.AlertReceiverConfig{}
	if err := request.ReadEntity(&cfg); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	ctx := context.Background()
	cfg.ServiceID = svcID
	updateReceiverZeroValue(cfg)
	if err := alerting.GetAlertReceiverMgrInstance().Upsert(ctx, cfg); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, struct{}{})
}

func (r *Resource) GetReceiverCfg(request *restful.Request, response *restful.Response) {
	ctx := context.Background()
	svcID := request.PathParameter("svc_id")
	if svcID == "" {
		helper.ErrorResponse(response, stderr.Internal.Errorf("no svc id"))
		return
	}
	res, err := alerting.GetAlertReceiverMgrInstance().Get(ctx, svcID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	// 默认服务创建人
	if res == nil {
		res = &models.AlertReceiverConfig{
			ServiceID:      svcID,
			ServiceCreator: ptr.String("${service_creator}"),
		}
	}
	helper.SuccessResponse(response, res)
}
