package alert_history

import (
	"net/http"
	"transwarp.io/applied-ai/central-auth-service/models"

	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"
)

func NewAlertHistoryAPI(prefix string) *restful.WebService {
	return (&Resource{}).WebService(prefix)
}

type Resource struct {
}

func (r *Resource) WebService(prefix string) *restful.WebService {
	tags := []string{"预警历史"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags

	ws := new(restful.WebService)
	ws.Path(prefix)
	ws.Consumes(restful.MIME_JSON, restful.MIME_OCTET).Produces(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.POST("/receive").
		Doc("接受预警信息").
		Metadata(metaK, metaV).
		To(r.Receive<PERSON>tHistoryFromGrafana).
		Returns(http.StatusOK, "OK", struct{}{}))

	ws.Route(ws.GET("").
		Doc("获取预警列表").
		Param(ws.QueryParameter("filter_type", "筛选类型，可选user/service，分别表示按用户过滤/按服务过滤，默认service").
			DataType("string").Required(false)).
		Param(ws.QueryParameter("project_id", "空间id，如果filter_type为service则此参数必填").DataType("string").Required(false)).
		Param(ws.QueryParameter("service_id", "服务id，如果filter_type为service则此参数必填").DataType("string").Required(false)).
		Metadata(metaK, metaV).
		To(r.List).
		Returns(http.StatusOK, "OK", []models.AlertsHistoryDTO{}))

	ws.Route(ws.POST("batch-delete").
		Doc("批量删除预警消息").
		Reads(models.IDs{}).
		Metadata(metaK, metaV).
		To(r.DelByIDs).
		Returns(http.StatusOK, "OK", struct{}{}))

	ws.Route(ws.POST("/{id}/mark-as-read").
		Doc("消息已读").
		Param(ws.PathParameter("id", "预警历史id").DataType("string").Required(true)).
		Metadata(metaK, metaV).
		To(r.MarkAsRead).
		Returns(http.StatusOK, "OK", struct{}{}))

	ws.Route(ws.PUT("/receiver-cfg/{svc_id}").
		Doc("修改消息设置").
		Param(ws.PathParameter("svc_id", "服务id").DataType("string").Required(true)).
		Reads(models.AlertReceiverConfig{}).
		Metadata(metaK, metaV).
		To(r.UpdateReceiverCfg).
		Returns(http.StatusOK, "OK", struct{}{}))

	ws.Route(ws.GET("/receiver-cfg/{svc_id}").
		Doc("获取消息配置").
		Param(ws.PathParameter("svc_id", "服务id").DataType("string").Required(true)).
		Metadata(metaK, metaV).
		To(r.GetReceiverCfg).
		Returns(http.StatusOK, "OK", models.AlertReceiverConfig{}))

	return ws
}
