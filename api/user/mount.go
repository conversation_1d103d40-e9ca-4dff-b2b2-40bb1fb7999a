package user

import (
	"fmt"
	"net/http"

	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"

	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/service"
)

func NewUserAPI(prefix string, us *service.UserService, ts *service.AccessTokenService) *restful.WebService {
	return (&Resource{us: us, ts: ts}).WebService(prefix)
}

type Resource struct {
	us *service.UserService
	ts *service.AccessTokenService
}

func (r *Resource) WebService(prefix string) *restful.WebService {
	tags := []string{"用户管理"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags

	ws := new(restful.WebService)
	ws.Path(prefix)
	ws.
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	pathParamRoleID := ws.PathParameter(helper.PathRoleID, helper.PathRoleIDDesc).
		DataType(helper.ParamTypeString).Required(true)
	pathParamUserID := ws.PathParameter(helper.PathUserID, helper.PathUserIDDesc).
		DataType(helper.ParamTypeString).Required(true)
	queryParamRoleName := ws.QueryParameter(helper.QueryRoleName, helper.QueryRoleNameDesc).
		DataType(helper.ParamTypeString).Required(false)

	ws.Route(ws.GET("/-/profile").To(r.GetProfile).
		Doc("获取当前用户信息").Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.User{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.POST("/-/profile").To(r.UpdateProfile).
		Doc("更新当前用户信息").Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.User{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.POST("/").
		To(r.CreateUser).
		Doc("创建新的用户").Metadata(metaK, metaV).
		Consumes(restful.MIME_JSON).Reads(dao.User{}).
		Produces(restful.MIME_JSON).Writes(dao.User{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.User{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.DELETE(fmt.Sprintf("/{%s}", helper.PathUserID)).Param(pathParamUserID).
		To(r.DeleteUser).
		Doc("根据用户 ID 删除用户").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(helper.EmptyRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET("/").Param(queryParamRoleName).
		To(r.ListUsers).
		Doc("获取所有用户").Metadata(metaK, metaV).
		Param(helper.UsernameQP.Param()).
		Produces(restful.MIME_JSON).Writes([]dao.User{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []dao.User{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET(fmt.Sprintf("/{%s}", helper.PathUserID)).Param(pathParamUserID).
		To(r.GetUser).
		Doc("根据用户 ID 获取用户").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(dao.User{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.User{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.POST(fmt.Sprintf("/{%s}/roles/{%s}", helper.PathUserID, helper.PathRoleID)).
		Param(pathParamUserID).Param(pathParamRoleID).
		To(r.AppendRoleToUser).
		Doc("为指定用户附加指定角色").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(helper.EmptyRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.DELETE(fmt.Sprintf("/{%s}/roles/{%s}", helper.PathUserID, helper.PathRoleID)).
		Param(pathParamUserID).Param(pathParamRoleID).
		To(r.RemoveRoleFromUser).
		Doc("为指定用户删除指定角色").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(helper.EmptyRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.POST("/-/roles").
		To(r.CreateRole).
		Doc("创建新的角色").Metadata(metaK, metaV).
		Consumes(restful.MIME_JSON).Reads(dao.Role{}).
		Produces(restful.MIME_JSON).Writes(dao.Role{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.Role{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.DELETE(fmt.Sprintf("/-/roles/{%s}", helper.PathRoleID)).Param(pathParamRoleID).
		To(r.DeleteRole).
		Doc("根据角色 ID 删除角色（会删除当前角色下的所有用户）").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(helper.EmptyRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET("/-/roles").
		To(r.ListRoles).
		Doc("获取所有角色").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes([]dao.Role{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []dao.Role{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET(fmt.Sprintf("/-/roles/{%s}", helper.PathRoleID)).Param(pathParamRoleID).
		To(r.GetRole).
		Doc("根据角色 ID 获取角色").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(dao.Role{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.Role{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.POST(fmt.Sprintf("/-/tokens")).
		To(r.CreateAccessToken).
		Doc("生成用户 TOKEN").Metadata(metaK, metaV).
		Consumes(restful.MIME_JSON).Reads(dao.AccessToken{}).
		Produces(restful.MIME_JSON).Writes(dao.AccessToken{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.AccessToken{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.DELETE(fmt.Sprintf("/-/tokens")).
		Param(ws.QueryParameter(helper.QueryUsername, helper.QueryUsernameDesc).DataType(helper.ParamTypeString).Required(true)).
		To(r.DeleteAccessTokenByUsername).
		Doc("删除指定用户的 TOKEN").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(helper.EmptyRsp{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.EmptyRsp{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.DELETE(fmt.Sprintf("/-/tokens/{%s}", helper.PathTokenID)).
		Param(ws.PathParameter(helper.PathTokenID, helper.PathTokenIDDesc).DataType(helper.ParamTypeString)).
		To(r.DeleteAccessTokenByID).
		Doc("删除指定的用户 TOKEN").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(dao.AccessToken{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.AccessToken{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.PUT(fmt.Sprintf("/-/tokens/{%s}", helper.PathTokenID)).
		Param(ws.PathParameter(helper.PathTokenID, helper.PathTokenIDDesc).DataType(helper.ParamTypeString)).
		To(r.UpdateAccessToken).
		Doc("更新指定的用户 TOKEN").Metadata(metaK, metaV).
		Consumes(restful.MIME_JSON).Reads(dao.AccessToken{}).
		Produces(restful.MIME_JSON).Writes(dao.AccessToken{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.AccessToken{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.DELETE(fmt.Sprintf("/-/tokens/-/validity")).
		Param(ws.QueryParameter(helper.QueryUsername, helper.QueryUsernameDesc).DataType(helper.ParamTypeString).Required(true)).
		To(r.DisableAccessTokenByUsername).
		Doc("禁用指定用户的 TOKEN").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes([]dao.AccessToken{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []dao.AccessToken{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.DELETE(fmt.Sprintf("/-/tokens/{%s}/validity", helper.PathTokenID)).
		Param(ws.PathParameter(helper.PathTokenID, helper.PathTokenIDDesc).DataType(helper.ParamTypeString)).
		To(r.DisableAccessTokenByID).
		Doc("禁用指定的用户 TOKEN").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(dao.AccessToken{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.AccessToken{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET(fmt.Sprintf("/-/tokens")).
		Param(ws.QueryParameter(helper.QueryUsername, helper.QueryUsernameDesc).DataType(helper.ParamTypeString).Required(true)).
		To(r.GetAccessTokenByUsername).
		Doc("获取指定用户的 TOKEN").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes([]dao.AccessToken{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []dao.AccessToken{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	ws.Route(ws.GET(fmt.Sprintf("/-/tokens/{%s}", helper.PathTokenID)).
		Param(ws.PathParameter(helper.PathTokenID, helper.PathTokenIDDesc).DataType(helper.ParamTypeString)).
		To(r.GetAccessTokenByID).
		Doc("获取指定的用户 TOKEN").Metadata(metaK, metaV).
		Produces(restful.MIME_JSON).Writes(dao.AccessToken{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), dao.AccessToken{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	return ws
}
