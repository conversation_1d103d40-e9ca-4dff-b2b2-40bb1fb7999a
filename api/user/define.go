package user

import (
	"time"

	"github.com/emicklei/go-restful/v3"

	utils "transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/api/auth"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
)

func (r *Resource) GetProfile(request *restful.Request, response *restful.Response) {
	if !auth.GetAuthHandler().IsAuthenticated(response.ResponseWriter, request.Request) {
		helper.ErrorResponse(response, stderr.Unauthenticated.Error("用户未登录"))
		return
	}
	user, err := auth.GetAuthHandler().GetAuthenticationUser(request.Request)
	if err != nil {
		helper.ErrorResponse(response, stderr.Unauthenticated.Error("用户信息获取失败！"))
		return
	}
	helper.SuccessResponse(response, user)
}

func (r *Resource) UpdateProfile(request *restful.Request, response *restful.Response) {
	// TODO persist profile into db
	helper.ErrorResponse(response, stderr.Unsupported.Error("暂未实现"))
}

func (r *Resource) CreateRole(request *restful.Request, response *restful.Response) {
	role := new(dao.Role)
	if err := request.ReadEntity(role); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	if err := r.us.CreateRole(role); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("角色创建失败"))
		return
	}
	helper.SuccessResponse(response, role)
}

func (r *Resource) DeleteRole(request *restful.Request, response *restful.Response) {
	roleID := request.PathParameter(helper.PathRoleID)
	if roleID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的角色 ID"))
		return
	}
	if err := r.us.DeleteRoleByID(roleID); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("角色删除失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) ListRoles(request *restful.Request, response *restful.Response) {
	roles, err := r.us.ListRoles()
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "列出所有角色失败"))
		return
	}
	helper.SuccessResponse(response, roles)
}

func (r *Resource) GetRole(request *restful.Request, response *restful.Response) {
	roleID := request.PathParameter(helper.PathRoleID)
	if roleID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的角色 ID"))
		return
	}
	role, err := r.us.GetRoleByID(roleID)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "角色获取失败，无效的角色 ID：%s", roleID))
		return
	}
	helper.SuccessResponse(response, role)
}

func (r *Resource) CreateAccessToken(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	token := new(dao.AccessToken)
	if err := request.ReadEntity(token); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "请求体解析失败"))
		return
	}
	// 为指定用户创建 TOKEN
	if token.Username == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户"))
		return
	}
	user, err := r.us.GetUserByName(token.Username)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "获取用户失败"))
		return
	}
	token.Token = utils.JWTokenBuilder{}.Username(user.GetUsername()).Roles(user.GetRoles()...).
		TimeToLive(36500 * 24 * time.Hour).Scope(utils.JWTTokenScopeExternal).Build().Token()

	if err := r.ts.CreateAccessToken(creator.GetUsername(), token); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "用户 TOKEN 创建失败"))
		return
	}
	helper.SuccessResponse(response, token)
}

func (r *Resource) DeleteAccessTokenByUsername(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	username := request.QueryParameter(helper.QueryUsername)
	if username == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户名"))
		return
	}
	if err := r.ts.DeleteAccessTokenByUsername(username); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "删除用户 TOKEN 失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) DeleteAccessTokenByID(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	tokenID := request.PathParameter(helper.PathTokenID)
	if tokenID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的 TOKEN ID"))
		return
	}
	if err := r.ts.DeleteAccessTokenByID(tokenID); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "删除用户 TOKEN 失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) UpdateAccessToken(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	tokenID := request.PathParameter(helper.PathTokenID)
	if tokenID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的 TOKEN ID"))
		return
	}
	token := new(dao.AccessToken)
	if err := request.ReadEntity(token); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "请求体解析失败"))
		return
	}
	token.ID = tokenID

	if err := r.ts.UpdateAccessToken(token); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "更新用户 TOKEN 失败"))
		return
	}
	helper.SuccessResponse(response, token)
}

func (r *Resource) DisableAccessTokenByUsername(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	username := request.QueryParameter(helper.QueryUsername)
	if username == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户名"))
		return
	}
	if err := r.ts.DisableAccessTokenByUsername(username); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "禁用用户 TOKEN 失败"))
		return
	}

	tokens, err := r.ts.GetAccessTokenByUsername(username)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取指定用户的 TOKEN 失败"))
		return
	}
	helper.SuccessResponse(response, tokens)
}

func (r *Resource) DisableAccessTokenByID(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	tokenID := request.PathParameter(helper.PathTokenID)
	if tokenID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的 TOKEN ID"))
		return
	}
	if err := r.ts.DisableAccessTokenByID(tokenID); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "禁用用户 TOKEN 失败"))
		return
	}

	token, err := r.ts.GetAccessTokenByID(tokenID)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取用户 TOKEN 失败"))
		return
	}
	helper.SuccessResponse(response, token)
}

func (r *Resource) GetAccessTokenByUsername(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	username := request.QueryParameter(helper.QueryUsername)
	if username == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户名"))
		return
	}
	tokens, err := r.ts.GetAccessTokenByUsername(username)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取指定用户的 TOKEN 失败"))
		return
	}
	helper.SuccessResponse(response, tokens)
}

func (r *Resource) GetAccessTokenByID(request *restful.Request, response *restful.Response) {
	creator := utils.GetAuthContext(request)
	if !creator.IsAdmin() {
		helper.ErrorResponse(response, stderr.Unauthorized.Error("只有管理员才可以执行 TOKEN 相关操作"))
		return
	}

	tokenID := request.PathParameter(helper.PathTokenID)
	if tokenID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的 TOKEN ID"))
		return
	}
	token, err := r.ts.GetAccessTokenByID(tokenID)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取用户 TOKEN 失败"))
		return
	}
	helper.SuccessResponse(response, token)
}

func (r *Resource) CreateUser(request *restful.Request, response *restful.Response) {
	user := new(struct {
		dao.User
		Password string `json:"password"`
	})
	if err := request.ReadEntity(user); err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Error("请求体解析失败"))
		return
	}
	if err := r.us.CreateUser(&user.User, user.Password); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("用户创建失败"))
		return
	}
	helper.SuccessResponse(response, user)
}

func (r *Resource) DeleteUser(request *restful.Request, response *restful.Response) {
	userID := request.PathParameter(helper.PathUserID)
	if userID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户 ID"))
		return
	}
	if err := r.us.DeleteUserByID(userID); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("用户删除失败"))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) ListUsers(request *restful.Request, response *restful.Response) {
	role := request.QueryParameter(helper.QueryRoleName)
	if role != "" {
		users, err := r.us.ListUsersByRoleName(role)
		if err != nil {
			helper.ErrorResponse(response, stderr.Internal.Cause(err, "列出所有 %s 角色的用户失败", role))
			return
		}
		helper.SuccessResponse(response, users)
		return
	}

	username := helper.UsernameQP.GetValue(request)
	if username != "" {
		user, err := r.us.GetUserByName(username)
		if err != nil {
			helper.ErrorResponse(response, stderr.Internal.Cause(err, "User %s not found", username))
			return
		}
		helper.SuccessResponse(response, []*dao.User{user})
		return
	}

	users, err := r.us.ListUsers(nil)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "列出所有用户失败"))
		return
	}
	helper.SuccessResponse(response, users)
}

func (r *Resource) GetUser(request *restful.Request, response *restful.Response) {
	userID := request.PathParameter(helper.PathUserID)
	if userID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户 ID"))
		return
	}
	user, err := r.us.GetUserByID(userID)
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Error("用户获取失败，无效的用户 ID：%s", userID))
		return
	}
	helper.SuccessResponse(response, user)
}

func (r *Resource) AppendRoleToUser(request *restful.Request, response *restful.Response) {
	userID := request.PathParameter(helper.PathUserID)
	if userID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户 ID"))
		return
	}
	roleID := request.PathParameter(helper.PathRoleID)
	if roleID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的角色 ID"))
		return
	}

	if err := r.us.AppendRoleToUser(userID, roleID); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "为用户 %s 附加角色 %s 失败", userID, roleID))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}

func (r *Resource) RemoveRoleFromUser(request *restful.Request, response *restful.Response) {
	userID := request.PathParameter(helper.PathUserID)
	if userID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的用户 ID"))
		return
	}
	roleID := request.PathParameter(helper.PathRoleID)
	if roleID == "" {
		helper.ErrorResponse(response, stderr.BadRequest.Error("无效的角色 ID"))
		return
	}

	if err := r.us.RemoveRoleFromUser(userID, roleID); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "为用户 %s 移除角色 %s 失败", userID, roleID))
		return
	}
	helper.SuccessResponse(response, helper.EmptyRsp{})
}
