package audit

import (
	"net/http"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/central-auth-service/service"

	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"
)

const (
	QueryParamProjectId  = "project_id"
	QueryParamUser       = "user_id"
	QueryParamSince      = "since"
	QueryParamModules    = "modules"
	QueryParamSubModules = "sub_modules"
	QueryParamOpTypes    = "op_types"
	QueryParamLimit      = "limit"
)

type ID struct {
	ID string `json:"id"`
}

func NewAuditAPI(root string, as *service.AuditRecordService) *restful.WebService {
	return (&Resource{as}).WebService(root)
}

type Resource struct {
	ars *service.AuditRecordService
}

func (r *Resource) WebService(root string) *restful.WebService {
	tags := []string{"审计事件"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(root + "/audit")
	ws.Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	projectIDQueryParam := ws.QueryParameter(QueryParamProjectId, "空间id").Required(true)

	ws.Route(ws.GET("/records").To(r.ListRecords).
		Doc("获取审计事件列表").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Param(projectIDQueryParam).
		Param(ws.QueryParameter(QueryParamSince, "开始时间, unix milliseconds")).
		Param(ws.QueryParameter(QueryParamUser, "操作用户")).
		Param(ws.QueryParameter(QueryParamModules, "模块, 逗号分割的列表")).
		Param(ws.QueryParameter(QueryParamSubModules, "子模块(功能菜单), 逗号分割的列表")).
		Param(ws.QueryParameter(QueryParamOpTypes, "操作类型, 逗号分割的列表")).
		Param(ws.QueryParameter(QueryParamLimit, "限制最大返回条数, 不指定或小于等于0时不限制")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), pb.ListAuditRecordsRsp{}))

	ws.Route(ws.GET("/records/-/modules").To(r.ListModules).
		Doc("获取审计事件的模块列表").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), map[string][]string{}))

	return ws
}
