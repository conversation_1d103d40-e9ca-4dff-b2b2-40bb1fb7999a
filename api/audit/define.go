package audit

import (
	"strconv"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/central-auth-service/helper"
)

var moduleToSubModules = map[pb.AuditRecordModule][]pb.AuditRecordSubModule{
	pb.AuditRecordModule_AuditRecordModule_SPACE: {
		pb.AuditRecordSubModule_AuditRecordSubModule_SPACE_INFO_MANAGEMENT,
		pb.AuditRecordSubModule_AuditRecordSubModule_SPACE_MEMBER_MANAGEMENT,
	},
	pb.AuditRecordModule_AuditRecordModule_MODEL: {
		pb.AuditRecordSubModule_AuditRecordSubModule_MODEL_MANAGEMENT,
		pb.AuditRecordSubModule_AuditRecordSubModule_MODEL_EXPERIENCE,
		pb.AuditRecordSubModule_AuditRecordSubModule_MODEL_TRAINING,
		pb.AuditRecordSubModule_AuditRecordSubModule_MODEL_EVALUATION,
		pb.AuditRecordSubModule_AuditRecordSubModule_PROMPT_ENGINEERING,
	},
	pb.AuditRecordModule_AuditRecordModule_APP: {
		pb.AuditRecordSubModule_AuditRecordSubModule_APP_MANAGEMENT,
		pb.AuditRecordSubModule_AuditRecordSubModule_APP_EXPERIENCE,
		pb.AuditRecordSubModule_AuditRecordSubModule_APP_PLUGIN_MANAGEMENT,
		pb.AuditRecordSubModule_AuditRecordSubModule_CUSTOM_OPERATOR,
		pb.AuditRecordSubModule_AuditRecordSubModule_APP_EVALUATION,
	},
	pb.AuditRecordModule_AuditRecordModule_KNOWLEDGE: {
		pb.AuditRecordSubModule_AuditRecordSubModule_KNOWLEDGE_MANAGEMENT,
		pb.AuditRecordSubModule_AuditRecordSubModule_KNOWLEDGE_EXPERIENCE,
	},
	pb.AuditRecordModule_AuditRecordModule_CORPUS: {
		pb.AuditRecordSubModule_AuditRecordSubModule_CORPUS_MANAGEMENT,
		pb.AuditRecordSubModule_AuditRecordSubModule_CORPUS_PROCESSING,
		pb.AuditRecordSubModule_AuditRecordSubModule_CORPUS_LABELING,
	},
	pb.AuditRecordModule_AuditRecordModule_TOOL: {
		pb.AuditRecordSubModule_AuditRecordSubModule_SERVICE_DEPLOYMENT,
		pb.AuditRecordSubModule_AuditRecordSubModule_SECURITY_CENTER,
		pb.AuditRecordSubModule_AuditRecordSubModule_CODE_EXAMPLES,
		pb.AuditRecordSubModule_AuditRecordSubModule_WORKFLOW_MANAGEMENT,
	},
}

func (r *Resource) ListRecords(request *restful.Request, response *restful.Response) {
	projectId := request.QueryParameter(QueryParamProjectId)
	user := request.QueryParameter(QueryParamUser)
	sinceStr := request.QueryParameter(QueryParamSince)
	modules := request.QueryParameter(QueryParamModules)
	subModules := request.QueryParameter(QueryParamSubModules)
	opTypes := request.QueryParameter(QueryParamOpTypes)
	limitStr := request.QueryParameter(QueryParamLimit)

	var since, limit int
	var err error
	if sinceStr == "" {
		since = 0
	} else if since, err = strconv.Atoi(sinceStr); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	if limitStr == "" {
		limit = 0
	} else if limit, err = strconv.Atoi(limitStr); err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	req := &pb.ListAuditRecordsReq{
		UserContext: &pb.UserContext{ProjectId: projectId},
		Since:       int64(since),
		UserId:      user,
		Modules:     helper.StringToEnumSlice[pb.AuditRecordModule](modules, pb.AuditRecordModule_value),
		SubModules:  helper.StringToEnumSlice[pb.AuditRecordSubModule](subModules, pb.AuditRecordSubModule_value),
		OpTypes:     helper.StringToEnumSlice[pb.AuditRecordOperateType](opTypes, pb.AuditRecordOperateType_value),
		Limit:       int64(limit),
	}
	rsp, err := r.ars.ListAuditRecords(request.Request.Context(), req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) ListModules(request *restful.Request, response *restful.Response) {
	mp := make(map[string][]string)
	for k, v := range moduleToSubModules {
		sub := make([]string, len(v))
		for i, s := range v {
			sub[i] = s.String()
		}
		mp[k.String()] = sub
	}
	helper.SuccessResponse(response, mp)
}
