package notice

import (
	"context"
	"github.com/emicklei/go-restful/v3"
	"sync"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/dao/alert_history"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func (r *Resource) CountMessages(request *restful.Request, response *restful.Response) {
	projectID := request.QueryParameter(helper.ParamProjectId)
	username := helper.ParseUsername(request)
	// 分别计算每个消息的数量
	wg := sync.WaitGroup{}
	wg.Add(3)
	result := &models.Count{
		Alert:       &models.ReadStatus{},
		ExamineFlow: &models.ReadStatus{},
	}
	go func() {
		defer wg.Done()
		req := &models.ListFlowReq{
			User:  username,
			Types: []models.ListType{models.ListTypeWaiting},
		}
		c, err := r.efs.CountFlows(projectID, req)
		if err != nil {
			stdlog.WithError(err).Error("count examine flow waiting error")
		}
		result.ExamineFlow.UnRead = c
	}()
	go func() {
		defer wg.Done()
		req := &models.ListFlowReq{
			User:  username,
			Types: []models.ListType{models.ListTypeProceeded},
		}
		c, err := r.efs.CountFlows(projectID, req)
		if err != nil {
			stdlog.WithError(err).Error("count examine flow proceeded error")
		}
		result.ExamineFlow.Read = c
	}()
	go func() {
		defer wg.Done()
		req := alert_history.ListAlertReq{
			UserName: username,
		}
		lists, err := r.am.List(context.Background(), req)
		if err != nil {
			stdlog.WithError(err).Error("list alert history error")
		}
		read, unread := 0, 0
		for _, h := range lists {
			if h.HasBeenRead {
				read++
			} else {
				unread++
			}
		}
		result.Alert.Read = read
		result.Alert.UnRead = unread
	}()
	wg.Wait()
	helper.SuccessResponse(response, result)
}
