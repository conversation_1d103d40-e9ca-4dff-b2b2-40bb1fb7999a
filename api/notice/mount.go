package notice

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"
	"gorm.io/gorm"
	"net/http"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/alerting"
	"transwarp.io/applied-ai/central-auth-service/service/examine"
)

func NewNoticeAPI(prefix string, db *gorm.DB) *restful.WebService {
	examine.Init(db)
	return (&Resource{
		am:  alerting.GetAlertHistoryMgrInstance(),
		efs: examine.GetExamineFlowService(db),
	}).WebService(prefix)
}

type Resource struct {
	am  *alerting.AlertHistoryMgr
	efs examine.ExamineFlowService
}

func (r *Resource) WebService(root string) *restful.WebService {
	tags := []string{"消息通知"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(root)
	ws.Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.GET("/messages/count").To(r.CountMessages).
		Doc("统计我的消息数量").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []*models.Count{}))
	return ws
}
