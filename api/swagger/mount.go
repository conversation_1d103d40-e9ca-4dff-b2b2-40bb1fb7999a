package swagger

import (
	"fmt"
	"net/http"
	"path"
	"sort"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"

	"github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"
	"github.com/go-openapi/spec"
	"github.com/spf13/viper"

	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/version"
)

var UIRoot = "public/swagger-ui"

func NewSwaggerAPI(root string) *restful.WebService {
	api := new(restful.WebService)
	api.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)
	api.Route(api.GET("/version.json").To(GetModelWareHouseVersion))
	api.Route(api.GET("/environments").To(ListConfigEnvKeys).Doc("环境变量").Writes([]string{}))
	api.Route(api.GET("/swagger.json").To(stdsrv.GetSwagger))
	api.Route(api.GET("/").To(GetSwaggerUI))
	api.Route(api.GET("{subpath}").Param(restful.PathParameter("subpath", "sub path")).To(GetSwaggerUIResources))
	return api
}

func GetSwagger(request *restful.Request, response *restful.Response) {
	config := restfulspec.Config{
		WebServices: restful.RegisteredWebServices(),
	}
	swagger := restfulspec.BuildSwagger(config)
	swagger.Info = &spec.Info{
		InfoProps: spec.InfoProps{
			Title:       conf.C.Server.Name,
			Description: fmt.Sprintf("HTTP APIs for %s", conf.C.Server.Name),
			Version:     "0.0.0",
		},
	}
	swagger.Schemes = []string{"https", "http"}

	response.WriteEntity(swagger)
}

func ListConfigEnvKeys(request *restful.Request, response *restful.Response) {
	keys := viper.AllKeys()
	sort.Strings(keys)
	for i, key := range keys {
		keys[i] = strings.ToUpper(conf.EnvPrefix) + "_" + strings.ToUpper(key)
	}
	helper.SuccessResponse(response, keys)
}

func GetModelWareHouseVersion(request *restful.Request, response *restful.Response) {
	helper.SuccessResponse(response, GetVersion())
}

type VersionInfo struct {
	BuildName    string
	BuildVersion string
	BuildTime    string
	CommitId     string
}

func GetVersion() map[string]*VersionInfo {
	appVersion := make(map[string]*VersionInfo)
	appVersion[conf.C.Server.Name] = &VersionInfo{
		BuildName:    version.BuildName,
		BuildVersion: version.BuildVersion,
		BuildTime:    version.BuildTime,
		CommitId:     version.CommitID,
	}
	return appVersion
}

func GetSwaggerUI(request *restful.Request, response *restful.Response) {
	http.ServeFile(
		response.ResponseWriter,
		request.Request,
		UIRoot+"/index.html")
}

func GetSwaggerUIResources(req *restful.Request, resp *restful.Response) {
	actual := path.Join(UIRoot, req.PathParameter("subpath"))
	http.ServeFile(
		resp.ResponseWriter,
		req.Request, actual)
}
