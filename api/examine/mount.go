package examine

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"
	"gorm.io/gorm"
	"net/http"
	examinestd "transwarp.io/applied-ai/aiot/vision-std/examine"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/examine"
)

func NewExamineAPI(prefix string, db *gorm.DB) *restful.WebService {
	examine.Init(db)
	return (&Resource{
		efs: examine.GetExamineFlowService(db),
	}).WebService(prefix)
}

type Resource struct {
	efs examine.ExamineFlowService
}

func (r *Resource) WebService(prefix string) *restful.WebService {
	tags := []string{"审批管理"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags

	ws := new(restful.WebService)
	ws.Path(prefix)
	ws.Consumes(restful.MIME_JSON, restful.MIME_OCTET).Produces(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.GET("/flows").To(r.ListFlows).
		Doc("获取审批列表").Metadata(metaK, metaV).
		Param(ws.QueryParameter(helper.QueryType, "获取列表的筛选条件：[created, proceeded, waiting], 多个同时可以逗号分割，传空表示获取所有")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []*models.FlowInfo{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.GET("/flows/{instance-id}").To(r.GetFlow).
		Doc("获取审批单详情").Metadata(metaK, metaV).
		Param(ws.PathParameter(helper.PathFlowInstanceID, "审批流程单id")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.FlowInfo{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.POST("/flows").
		To(r.CreateFlow).
		Doc("创建审批流程").Metadata(metaK, metaV).
		Consumes(restful.MIME_JSON).Reads(examinestd.CreateExamineFlowReq{}).
		Produces(restful.MIME_JSON).Writes(examinestd.CreateExamineFlowReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.FlowInfo{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.POST("/flows/{instance-id}/node:approve").
		To(r.ApproveFlow).
		Doc("通过审批流程的当前节点").Metadata(metaK, metaV).
		Param(ws.PathParameter(helper.PathFlowInstanceID, "审批流程单id")).
		Consumes(restful.MIME_JSON).Reads(models.NodeReq{}).
		Produces(restful.MIME_JSON).Writes(models.NodeReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.FlowInfo{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.POST("/flows/{instance-id}/node:reject").
		To(r.RejectFlow).
		Doc("拒绝审批流程的当前节点").Metadata(metaK, metaV).
		Param(ws.PathParameter(helper.PathFlowInstanceID, "审批流程单id")).
		Consumes(restful.MIME_JSON).Reads(models.NodeReq{}).
		Produces(restful.MIME_JSON).Writes(models.NodeReq{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.FlowInfo{}).
		Returns(http.StatusBadRequest, http.StatusText(http.StatusBadRequest), nil).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	ws.Route(ws.GET("/flows/count").To(r.CountFlows).
		Doc("获取列表数量").Metadata(metaK, metaV).
		Param(ws.QueryParameter(helper.QueryType, "获取列表的筛选条件：[created, proceeded, waiting], 多个同时可以逗号分割，传空表示获取所有")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), models.Count{}).
		Returns(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), nil).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	return ws
}
