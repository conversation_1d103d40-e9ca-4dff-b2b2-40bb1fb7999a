package examine

import (
	"github.com/emicklei/go-restful/v3"
	"strconv"
	"strings"
	"transwarp.io/applied-ai/aiot/vision-std/examine"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func (r *Resource) ListFlows(request *restful.Request, response *restful.Response) {
	projectID := request.QueryParameter(helper.ParamProjectId)
	username := helper.ParseUsername(request)
	t := request.QueryParameter(helper.QueryType)
	req := &models.ListFlowReq{
		User:  username,
		Types: make([]models.ListType, 0),
	}
	if t != "" {
		ts := strings.Split(t, ",")
		for _, qt := range ts {
			req.Types = append(req.Types, models.ListType(qt))
		}
	}
	result, err := r.efs.ListFlows(projectID, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, result)
}

func (r *Resource) CountFlows(request *restful.Request, response *restful.Response) {
	projectID := request.QueryParameter(helper.ParamProjectId)
	username := helper.ParseUsername(request)
	t := request.QueryParameter(helper.QueryType)
	req := &models.ListFlowReq{
		User:  username,
		Types: make([]models.ListType, 0),
	}
	if t != "" {
		ts := strings.Split(t, ",")
		for _, qt := range ts {
			req.Types = append(req.Types, models.ListType(qt))
		}
	}
	result, err := r.efs.CountFlows(projectID, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, &models.Count{Num: result})
}

func (r *Resource) GetFlow(request *restful.Request, response *restful.Response) {
	username := helper.ParseUsername(request)
	id := request.PathParameter(helper.PathFlowInstanceID)
	ID, err := strconv.Atoi(id)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "id should be a number"))
		return
	}
	result, err := r.efs.GetFlow(uint(ID), username)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, result)
}

func (r *Resource) CreateFlow(request *restful.Request, response *restful.Response) {
	projectID := request.QueryParameter(helper.ParamProjectId)
	username := helper.ParseUsername(request)
	body := new(examine.CreateExamineFlowReq)
	err := request.ReadEntity(body)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "body struct is not valid"))
		return
	}
	token := request.HeaderParameter("Authorization")
	body.User = username
	if body.HttpInfo != nil {
		if body.HttpInfo.Token == "" {
			body.HttpInfo.Token = token
		}
	}
	if body.RejectHttpInfo != nil {
		if body.RejectHttpInfo.Token == "" {
			body.RejectHttpInfo.Token = token
		}
	}
	result, err := r.efs.CreateFlow(projectID, body)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, result)
}

func (r *Resource) ApproveFlow(request *restful.Request, response *restful.Response) {
	username := helper.ParseUsername(request)
	id := request.PathParameter(helper.PathFlowInstanceID)
	ID, err := strconv.Atoi(id)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "id should be a number"))
		return
	}
	body := new(models.NodeReq)
	err = request.ReadEntity(body)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "body struct is not valid"))
		return
	}
	body.ID = uint(ID)
	body.User = username
	result, err := r.efs.Approve(body)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, result)
}

func (r *Resource) RejectFlow(request *restful.Request, response *restful.Response) {
	username := helper.ParseUsername(request)
	id := request.PathParameter(helper.PathFlowInstanceID)
	ID, err := strconv.Atoi(id)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "id should be a number"))
		return
	}
	body := new(models.NodeReq)
	err = request.ReadEntity(body)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Cause(err, "body struct is not valid"))
		return
	}
	body.ID = uint(ID)
	body.User = username
	result, err := r.efs.Reject(body)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, result)
}
