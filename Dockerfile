# https://github.com/taosdata/driver-go/issues/114
# 目前 TDengine 官方没有提供 alpine 系统下的 taosc，并且使用官方提供的安装脚本也没有成功，所以先使用 ubuntu:18.04 作为基础镜像
# FROM harbor.transwarp.io/aip/base/golang:1.18-alpine3.15 AS build
# harbor.transwarp.io/aip/base/vision-builder:ubuntu18.04-go1.18
FROM --platform=${BUILDPLATFORM} harbor.transwarp.io/gold/aip-mm/base/golang-builder:1.23-ubuntu24.04 AS build

ARG TARGETARCH

ARG PACKAGE=transwarp.io/applied-ai/central-auth-service
ARG BUILD_VERSION=0.0.0
ARG BUILD_TIME=99999999
ARG CI_COMMIT_SHA=00000000
ARG CHART_VERSION=0.0.0-dev
ARG DEFULT_CHART_VERSION=0.0.0-dev
ARG ARCH=${TARGETARCH}

WORKDIR /build

ENV CHART_VERSION=${CHART_VERSION}
ENV HARBOR_USER ciuser
ENV HARBOR_PASSWORD !QAZ2wsx
ENV HARBOR_ADDR https://*************:30004/chartrepo/sophonedge

RUN helm repo add --debug --insecure-skip-tls-verify --username ${HARBOR_USER} --password "${HARBOR_PASSWORD}" edge-harbor ${HARBOR_ADDR} && \
    helm repo update && \
    helm pull edge-harbor/hippo --version ${DEFULT_CHART_VERSION} --insecure-skip-tls-verify && \
    helm pull edge-harbor/llmops-basic --version ${DEFULT_CHART_VERSION} --insecure-skip-tls-verify && \
    helm pull edge-harbor/llmops-queue --version ${DEFULT_CHART_VERSION} --insecure-skip-tls-verify && \
    helm pull edge-harbor/hippo --version ${CHART_VERSION} --insecure-skip-tls-verify && \
    helm pull edge-harbor/llmops-basic --version ${CHART_VERSION} --insecure-skip-tls-verify && \
    helm pull edge-harbor/llmops-queue --version ${CHART_VERSION} --insecure-skip-tls-verify

COPY central-auth-service central-auth-service
COPY vision-std vision-std
COPY llmops-common llmops-common
COPY grafana-openapi-client-go grafana-openapi-client-go
COPY kube-nodexpu-manager kube-nodexpu-manager

RUN envsubst '${CAHRT_VERSION}' < ./central-auth-service/etc/charts/charts.yaml.template > ./central-auth-service/etc/charts/charts.yaml

ENV GOPATH /root/go
RUN if [ "$ARCH" = "arm64" ]; then \
        export CC=aarch64-linux-gnu-gcc; \
        export CXX=aarch64-linux-gnu-g++; \
    else \
        export CC=gcc; \
        export CXX=g++; \
    fi && \
    export PATH=$PATH && cd central-auth-service &&\
    go env -w GOPROXY=http://*************:1111,https://goproxy.io/,https://mirrors.aliyun.com/goproxy/,https://goproxy.cn/,direct && go mod tidy &&\
        GO111MODULE=on \
        GOOS=linux \
        CGO_ENABLED=1 \
        CC=${CC} \
        CXX=${CXX} \
        GOARCH=${ARCH} \
        go build -ldflags "-X ${PACKAGE}/version.BuildName=${PACKAGE} -X ${PACKAGE}/version.BuildVersion=${BUILD_VERSION} -X ${PACKAGE}/version.BuildTime=${BUILD_TIME} -X ${PACKAGE}/version.CommitID=${CI_COMMIT_SHA}" \
        -o cas ./main.go

RUN mkdir -p /build/gojieba && \
    cp -r $(find $GOPATH/pkg/mod/github.com/yanyiwu -type d -name "gojieba*" | head -n 1)/deps/cppjieba/dict /build/gojieba/dict


FROM harbor.transwarp.io/gold/aip-mm/base/ubuntu-runner:24.04

ARG TARGETARCH

ARG ARCH=${TARGETARCH}
ARG CHART_VERSION=0.0.0-dev
ARG DEFULT_CHART_VERSION=0.0.0-dev

ENV CHART_VERSION=${CHART_VERSION}

WORKDIR /cas

COPY --from=build /build/central-auth-service/bin/boot.sh /bin/boot.sh
COPY --from=build /build/central-auth-service/etc etc
COPY --from=build /build/central-auth-service/public public
# 镜像构建后会尝试获取二进制
COPY --from=build /build/central-auth-service/cas central-auth-service

RUN mkdir -p /cas/gojieba
COPY --from=build /build/gojieba/dict /cas/gojieba/dict
ENV JIEBA_DIC_DIR /cas/gojieba/dict

RUN chmod 777 -R ./ && chmod +x /bin/boot.sh

# https://github.com/taosdata/TDengine/issues/4151
# copy all dependencies. View dependencies: "ldd /edge/edm"
# COPY --from=build /usr/lib/libtaos.so.1 /usr/lib/libtaos.so.1
#COPY --from=build /lib/x86_64-linux-gnu/libpthread.so.0 /lib/x86_64-linux-gnu/libpthread.so.0
#COPY --from=build /lib/x86_64-linux-gnu/libc.so.6 /lib/x86_64-linux-gnu/libc.so.6
#COPY --from=build /lib64/ld-linux-x86-64.so.2 /lib64/ld-linux-x86-64.so.2
#COPY --from=build /lib/x86_64-linux-gnu/libm.so.6 /lib/x86_64-linux-gnu/libm.so.6
#COPY --from=build /lib/x86_64-linux-gnu/librt.so.1 /lib/x86_64-linux-gnu/librt.so.1
#COPY --from=build /lib/x86_64-linux-gnu/libdl.so.2 /lib/x86_64-linux-gnu/libdl.so.2
RUN mkdir -p /opt/charts
COPY --from=build /build/hippo-${DEFULT_CHART_VERSION}.tgz /opt/charts/hippo-${DEFULT_CHART_VERSION}.tgz
COPY --from=build /build/llmops-basic-${DEFULT_CHART_VERSION}.tgz /opt/charts/llmops-basic-${DEFULT_CHART_VERSION}.tgz
COPY --from=build /build/llmops-queue-${DEFULT_CHART_VERSION}.tgz /opt/charts/llmops-queue-${DEFULT_CHART_VERSION}.tgz
COPY --from=build /build/hippo-${CHART_VERSION}.tgz /opt/charts/hippo-${CHART_VERSION}.tgz
COPY --from=build /build/llmops-basic-${CHART_VERSION}.tgz /opt/charts/llmops-basic-${CHART_VERSION}.tgz
COPY --from=build /build/llmops-queue-${CHART_VERSION}.tgz /opt/charts/llmops-queue-${CHART_VERSION}.tgz

CMD ["/bin/boot.sh"]
