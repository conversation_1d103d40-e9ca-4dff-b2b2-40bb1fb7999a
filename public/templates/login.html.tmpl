<!DOCTYPE html>
<!-- saved from url=(0175)https://172.16.251.85:32708/api/v1/auth/cas/login?service=https%3A%2F%2F172.16.251.83%3A30443%2Fgateway%2Fcas%2Fapi%2Flogin%3Fredirect%3Dhttps%3A%2F%2F172.16.251.83%3A30443%2F -->
<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{ .TitleZH }}</title>
  <link rel="stylesheet" type="text/css" href="./login.css">
</head>

<body>
  <div class="login-page">
    <div class="stars" id="stars"></div>
    <div class="login-box">
      <div class="box-show">
        <img src="./logo.svg" class="logo" width="80" height="80">
        <h1 id="text-1">Sophon LLMOps</h1>
      </div>
      <div class="login-modal">
        <div class="login-form-box">
          <div class="header"> <img src="./logo.svg" class="logo" width="64" height="64"></div>
          <div class="title-container">
            <h2 class="title" id="text-3">欢迎登录</h2>
            {{ if .EnableOauth2 }}
            <a href="{{ .OAuth2AuthUrl }}" id="sso-login-link" class="reset">内部SSO登录</a>
            {{ end }}
          </div>
          <div class="ant-form ant-form-horizontal login-form">
            <form id="form" name="form" action="{{ .PostUrl }}" method="POST"
              encrypt="application/x-www-form-urlencoded">
              {{ if ne .Notice "" }}
              <div class="form-notice">{{ .Notice }}</div>
              {{ end }}
              <div id="username-form-item" class="form-item">
                <p><label id="user-name">用户名</label></p>
                <input name="username" class="login-form-item" id="username-input" placeholder="请输入用户名" ,
                 value="{{.CurtUser}}">
                <div id="username-error-msg" class="error-msg">用户名不能为空</div>
              </div>
              <div id="password-form-item" class="form-item">
                <p class="password-box"><label id="password">密码</label><span id="reset-password"
                    class="reset">重置密码</span></p>
                <div class="ant-row login-form-item">
                  <input name="password" id="password-input" type="password" placeholder="请输入密码">
                  <span role="img" id="soicon-eye" class="soicon soicon-eye">
                    <svg viewBox="64 64 896 896" focusable="false" data-icon="eye-invisible" width="1em" height="1em"
                      fill="currentColor" aria-hidden="true">
                      <path
                        d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z">
                      </path>
                      <path
                        d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z">
                      </path>
                    </svg>
                  </span>
                </div>
                <div id="password-error-msg" class="error-msg">密码不能为空</div>
              </div>
              <input id="login-btn" class="login-form-btn" type="submit" value="登录">
            </form>
          </div>
          <div id="overlay"></div>
          <div id="modal">
            <button class="close-btn" id="closeModal">&times;</button>
            <h2 class="title" id="reset">重置密码</h2>
            <div class="input-group" id="user-form-item">
              <input name="username" class="login-form-item" id="username" placeholder="请输入用户名">
              <div id="username-error-msg1" class="error-msg">用户名不能为空</div>
            </div>
            <p class="desc" id="desc">请输入您需要重置密码的用户名信息，并点击确认按钮，平台将向该用户的绑定邮箱发送重置密码的链接。</p>
            <button class="confirm-btn" id="confirmBtn">确定</button>
          </div>
          <div id="tooltip">✅ 已收到您的密码重置申请，请前往绑定邮箱查看链接，并按指引操作。</div>
        </div>
      </div>
      <script>
        document.addEventListener("DOMContentLoaded", () => {
          const canvas = document.createElement("canvas");
          document.body.appendChild(canvas);
          const ctx = canvas.getContext("2d");

          function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
          }
          resizeCanvas();
          window.addEventListener("resize", resizeCanvas);

          const stars = [];
          const numStars = 200;

          for (let i = 0; i < numStars; i++) {
            stars.push({
              x: parseInt(Math.random() * canvas.width),
              y: parseInt(Math.random() * canvas.height),
              radius: Math.random() * 1.6,
              alpha: Math.random() + 0.2,
              delta: Math.random() * 0.02 - 0.01,
            });
          }

          function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            stars.forEach((star) => {
              star.alpha += star.delta;
              if (star.alpha <= 0 || star.alpha >= 1) star.delta *= -1;
              ctx.beginPath();
              ctx.arc(star.x, star.y, star.radius, 0, Math.PI * 2);
              ctx.fillStyle = `rgba(255, 255, 255, ${star.alpha})`;
              ctx.fill();
            });
            requestAnimationFrame(animate);
          }
          animate();
        });

      </script>
    </div>
  </div>
  </div>
  <script src="./login.js"></script>


</body>

</html>