body {
  overflow: hidden;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Microsoft Yahei",
    Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  margin: 0;
  height: 100vh;
  background: linear-gradient(45deg, #0f1016 0%, #232741 100%);
  overflow: hidden;
}

canvas {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  border: 0;
  box-sizing: border-box;
  font-size: 100%;
  margin: 0;
  padding: 0;
  vertical-align: baseline;
}

.stars {
  width: 100%;
  height: 100%;
  position: fixed;
}

.star {
  position: absolute;
  background: white;
  border-radius: 50%;
  animation: twinkle var(--duration) infinite;
}

@keyframes twinkle {

  0%,
  100% {
    opacity: 0;
    transform: scale(0.3);
  }

  50% {
    opacity: 1;
    transform: scale(1);
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  color: #fff;
  border: 0;
  box-sizing: border-box;
  font-size: 100%;
  margin: 0;
  padding: 0;
  vertical-align: baseline;
}

h3 {
  font-size: 18px;
  line-height: 25px;
}

.login-page {
  align-items: center;
  display: flex;
  height: 100vh;
  justify-content: center;
  position: relative;
}

.login-box {
  border-radius: 6px;
  display: flex;
  height: 720px;
  position: relative;
  width: 1200px;
  z-index: 10;
  background-color: #f6f8fe;
}

.box-show {
  padding: 30px 60px 30px 0px;
  position: relative;
  background: url("./<EMAIL>") 0 0 no-repeat;
  background-size: cover;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
  width: 650px;
  text-align: center;
}

.logo {
  margin-top: 22%;
  margin-bottom: 20px;
}

h2,
h3 {
  color: #fff;
}

h1 {
  font-size: 32px;
}

h2 {
  font-size: 26px;
  line-height: 37px;
}

h3 {
  font-size: 18px;
  line-height: 25px;
}

.radius {
  position: absolute;
}

.radius svg {
  display: block;
  fill: #fff;
  height: 100%;
  opacity: 0.05;
  width: 100%;
}

.radius-box1 {
  height: 110px;
  left: 44px;
  top: -75px;
  width: 65px;
}

.radius-box2 {
  bottom: 50px;
  height: 130px;
  right: -100px;
  width: 140px;
}

.radius-box3 {
  bottom: -70px;
  height: 130px;
  left: -50px;
  width: 130px;
}

.circle-box1,
.circle-box2 {
  position: absolute;
}

.circle-box1 {
  bottom: -180px;
  right: -300px;
}

.circle-box1 svg {
  height: 365px;
  width: 365px;
}

.circle-box2 {
  bottom: -155px;
  left: -145px;
}

.circle-box2 svg {
  height: 188px;
  width: 188px;
}

.login-header {
  color: #4ea5f2;
  left: 0;
  line-height: 32px;
  position: absolute;
  top: 0;
}

.login-header .soicon-logo {
  color: #fff;
  font-size: 65px;
  vertical-align: middle;
}

.login-header .soicon-logo svg {
  width: 4em;
}

.login-header .title {
  font-size: 20px;
  margin-left: 9px;
  vertical-align: middle;
}

@media (max-width: 1200px) {
  .login-modal {
    min-width: 360px;
  }
}

.header {
  display: none;
}

@media (max-width: 960px) {
  .login-box {
    height: 600px;
  }

  .box-show {
    display: none;
  }

  .login-box {
    margin: 0 30px;
    width: 600px;
  }

  .login-modal .login-form-box>.title {
    margin-top: 60px;
  }

  .header {
    display: block;
    text-align: center;
    position: absolute;
    top: 8%;
    left: 50%;
    margin-left: -32px;
  }
}

.login-modal {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.login-modal .border {
  background-color: #4ea5f2;
  border-radius: 2px;
  height: 15px;
  width: 5px;
}

.login-modal .title {
  font-size: 28px;
  color: #2d364d;
  line-height: 32px;
  font-weight: 700;
  margin-bottom: 10px;
}

.login-form-box {
  width: 320px;
}

.login-form.ant-form .login-form-item {
  background-color: transparent;
  border-color: #9bafd1;
  width: 320px;
}

.login-form.ant-form .login-form-item:focus {
  border: 1px solid #086df4;
}

input {
  background-color: transparent;
  color: #7988a1;
  padding: 0;
  border: none;
  outline: none;
  text-overflow: ellipsis;
  box-sizing: border-box;
  margin: 0;
  font-variant: tabular-nums;
  list-style: none;
  font-feature-settings: "tnum";
  position: relative;
  display: inline-block;
  width: 100%;
  min-width: 0;
  font-size: 14px;
  border-radius: 4px;
  transition: all 0.3s;
}

input:placeholder-shown {
  text-overflow: ellipsis;
}

input:hover {
  border-right-width: 1px !important;
}

input:-internal-autofill-selected {
  background-color: transparent !important;
}

input:-webkit-input-placeholder,
input:-moz-placeholder,
input:-ms-input-placeholder {
  color: #bfbfbf;
}

.soicon {
  color: #7988a1;
  font-size: 20px;
}

.ant-input-password-icon {
  color: #7988a1;
}

.login-form-btn {
  line-height: 1.5715;
  position: relative;
  display: inline-block;
  white-space: nowrap;
  text-align: center;
  background-image: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  user-select: none;
  touch-action: manipulation;
  margin-top: 26px;
  width: 100%;
  color: #fff;
  background: #086df4;
  height: 36px;
  padding: 7px 12px;
  font-size: 14px;
  border-radius: 6px;
}

.login-form-btn:hover {
  border-color: #7ac6ff;
  background: #3091ff;
}

.login-form-item {
  background-color: #fff;
  width: 320px;
  padding: 7.3px 11px;
  font-size: 14px;
  position: relative;
  min-width: 0;
  color: #4b5668;
  line-height: 22px;
  border: 1px solid #9bafd1;
  border-radius: 6px;
  transition: all 0.3s;
  display: inline-flex;
  margin-bottom: 30px;
}

.login-form-item:focus {
  border: 1px solid #086df4;
}

.login-form-item .soicon {
  margin-right: 2px;
  display: flex;
  align-items: center;
}

.soicon-eye {
  cursor: pointer;
}

.password-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-item {
  position: relative;
}

.form-item>p>label {
  font-size: 14px;
  color: #6d7587;
  line-height: 22px;
}

.error-msg {
  display: none;
  color: #ec2a2a;
  font-size: 12px;
  line-height: 22px;
  position: absolute;
  bottom: 0;
  left: 0;
}

.form-item-err .login-form-item {
  border-color: #ec2a2a;
  margin-bottom: 22px;
}

.form-item-err .error-msg {
  display: block;
}

.form-item-err .soicon {
  color: #ef7070;
}

.form-notice {
  padding: 4px 12px;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  color: #721c24;
  background: #f8d7da;
  margin-bottom: 10px;
  margin-top: -5px;
}

.reset {
  text-align: right;
  cursor: pointer;
  font-size: 14px;
  color: #086df4;
  line-height: 22px;
}

#overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1000;
}

#modal {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 20px;
  border-radius: 6px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  z-index: 1001;
  width: 500px;
}

.close-btn {
  background: transparent;
  border: 0;
  color: #2d364d;
  cursor: pointer;
  line-height: 1;
  position: absolute;
  right: 6px;
  top: 6px;
  transition: color 0.3s;
  z-index: 10;
  display: block;
  font-size: 24px;
  font-style: normal;
  height: 40px;
  line-height: 40px;
  text-align: center;
  text-rendering: auto;
  text-transform: none;
  width: 40px;
}

.confirm-btn {
  background: #086df4;
  color: white;
  border: none;
  padding: 4px 15px;
  cursor: pointer;
  border-radius: 6px;
  height: 30px;
  width: 20%;
  margin-left: 80%;
  margin-top: 16px;
}

.input-group {
  display: flex;
  align-items: start;
}

label {
  margin-bottom: 5px;
  display: block;
}

/* 提示框样式 */
#tooltip {
  display: none;
  position: fixed;
  left: 50%;
  bottom: 20px;
  transform: translateX(-50%);
  background: #f0f0f0;
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  z-index: 2000;
  width: 420px;
  text-align: left;
}

.desc {
  color: #6d7588;
  font-size: 14px;
}

.title {
  word-wrap: break-word;
  color: #202340;
  font-size: 15px;
  font-weight: 500;
  line-height: 20px;
  margin: 0;
  padding-bottom: 16px;
}
/* login.css */
.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reset {
  color: #1890ff; /* 蓝色链接颜色 */
  text-decoration: none; /* 去掉下划线 */
  cursor: pointer;
}

.reset:hover {
  text-decoration: underline; /* 鼠标悬停时显示下划线 */
}