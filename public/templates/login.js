const soiconEye = document.getElementById("soicon-eye");
const usernameInput = document.getElementById("username-input");
const usernameInput1 = document.getElementById("username");
const passwordInput = document.getElementById("password-input");
const loginBtn = document.getElementById("login-btn");
const usernameFormItem = document.getElementById("username-form-item");
const passwordFormItem = document.getElementById("password-form-item");
const overlay = document.getElementById("overlay");
const modal = document.getElementById("modal");
const reset = document.getElementById("reset-password");
const reset1 = document.getElementById("reset");
const userInput = document.getElementById("username");
const userFormItem = document.getElementById("user-form-item");
const closeModalButton = document.getElementById("closeModal");
const confirmButton = document.getElementById("confirmBtn");

const en = {
  "Enterprise Computer Vision Analysis Platform":
    "Enterprise Computer Vision Analysis Platform",
  "Welcome To Login": "Welcome To Login",
  "Please enter username": "Please enter username",
  "Please enter password": "Please enter password",
  Login: "Login",
  "Username cannot be empty": "Username cannot be empty",
  "Password cannot be empty": "Password cannot be empty",
  "Reset password": "Reset password",
  desc: "Please enter the username information for which you need to reset your password and click the confirm button. The platform will send a link to reset the password to the user's bound email.",
  tooltip:
    "✅ We have received your password reset request. Please go to the bound email to view the link and follow the instructions.",
  Confirm: "Confirm",
  "User Name": "User Name",
  Password: "Password",
  "Sophon LLMOps": "Sophon LLMOps",
  "SSO Login": "Internal SSO login",
};
const zh = {
  "Enterprise Computer Vision Analysis Platform": "企业级计算机视觉分析平台",
  "Welcome To Login": "欢迎登录",
  "Please enter username": "请输入用户名",
  "Please enter password": "请输入密码",
  Login: "登录",
  "Username cannot be empty": "用户名不能为空",
  "Password cannot be empty": "密码不能为空",
  "Reset password": "重置密码",
  desc: "请输入您需要重置密码的用户名信息，并点击确认按钮，平台将向该用户的绑定邮箱发送重置密码的链接。",
  tooltip: "✅ 已收到您的密码重置申请，请前往绑定邮箱查看链接，并按指引操作。",
  Confirm: "确定",
  "User Name": "用户名",
  Password: "密码",
  "Sophon LLMOps": "Sophon LLMOps",
  "SSO Login": "内部SSO登录",
};
const baseUrl = "";
soiconEye.addEventListener("click", () => {
  if (passwordInput.type === "password") {
    passwordInput.type = "text";
    soiconEye.innerHTML = `<svg viewBox="64 64 896 896" focusable="false" data-icon="eye" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"></path></svg>`;
  } else {
    passwordInput.type = "password";
    soiconEye.innerHTML = `<svg viewBox="64 64 896 896" focusable="false" data-icon="eye-invisible" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"></path><path d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"></path></svg>`;
  }
});
const setError = (dom, isError) => {
  if (isError) {
    dom.className = "form-item form-item-err";
  } else {
    dom.className = "form-item ";
  }
};
const login = () => {
  const username = usernameInput.value;
  const password = passwordInput.value;
  setError(usernameFormItem, !username);
  setError(passwordFormItem, !password);
  if (username && password) {
    form.submit();
  }
};
usernameInput.addEventListener("input", () => {
  setError(usernameFormItem, !usernameInput.value);
});
passwordInput.addEventListener("input", () => {
  setError(passwordFormItem, !passwordInput.value);
});
passwordInput.addEventListener("keyup", (e) => {
  if (e.key === "Enter") login();
});
loginBtn?.addEventListener("click", login);
reset.addEventListener("click", () => {
  overlay.style.display = "block";
  modal.style.display = "block";
});
userInput.addEventListener("input", () => {
  setError(userFormItem, !userInput.value);
});

closeModalButton.addEventListener("click", () => {
  userInput.value = "";
  overlay.style.display = "none";
  modal.style.display = "none";
});

overlay.addEventListener("click", () => {
  userInput.value = "";
  overlay.style.display = "none";
  modal.style.display = "none";
});

confirmButton.addEventListener("click", () => {
  const username = userInput.value;
  if (username) {
    // 显示提示
    tooltip.style.display = "block";
    setTimeout(() => {
      tooltip.style.display = "none";
    }, 3000); // 3秒后隐藏
    overlay.style.display = "none";
    modal.style.display = "none";
    userInput.value = ""; // 清空输入框
  } else {
    setError(userFormItem, !userInput.value);
  }
});

const search = location.search
  .slice(1)
  .split("&")
  .reduce((res, item) => {
    const [key, value] = item.split("=");
    res[key] = value;
    return res;
  }, {});
const { token } = search;
// ajax
const axios = {
  get: ({ path, onSuccess }) => {
    var xhr = new XMLHttpRequest();
    xhr.open("GET", `${baseUrl}${path}`, true);
    token && xhr.setRequestHeader("Authorization", search.token);
    xhr.send();
    xhr.onreadystatechange = function (e) {
      const { readyState, responseText } = xhr;
      if (readyState == 4) {
        let data = {};
        try {
          data = JSON.parse(responseText);
        } catch (e) { }
        onSuccess(data);
      }
    };
  },
  post: ({ path, value, onSuccess }) => {
    const keys = Object.keys(value || {});
    const sendMsg = keys
      .map((key) => {
        return `${key}=${value[key]}`;
      })
      .join("&");
    var xhr = new XMLHttpRequest();
    xhr.open("POST", `${baseUrl}${path}`, true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    token && xhr.setRequestHeader("Authorization", search.token);
    xhr.send(sendMsg);
    xhr.onreadystatechange = function (e) {
      const { status, responseText } = xhr;
      if (status === 200) {
        location.href = "";
      } else if (status === 401) {
        let data = {};
        try {
          data = JSON.parse(responseText);
          const { msg } = data;
          window.alert(msg);
        } catch (e) { }
      }
    };
  },
};
// getTranslation
let locale = navigator.language.split("-")[0];
try {
  const LOCALE = JSON.parse(localStorage.getItem("LOCALE"));
  locale = LOCALE.content;
} catch (e) { }
const getTranslation = (key) => {
  let result = null;
  if (locale === "en") {
    result = en[key] || key;
  } else {
    result = zh[key] || key;
  }
  return result;
};

// set oauth2 url
window.addEventListener('DOMContentLoaded', function () {
  const link = document.getElementById('sso-login-link');
  if (!link) return;

  const currentParams = window.location.search;
  if (currentParams) {
    try {
      const url = new URL(link.href);
      const currentUrlParams = new URLSearchParams(currentParams);
      const oauthParams = new URLSearchParams(url.search);

      currentUrlParams.forEach((value, key) => {
        oauthParams.set(key, value); // 合并参数
      });

      url.search = oauthParams.toString();
      link.href = url.toString();
    } catch (e) {
      console.error('URL 处理失败:', e);
    }
  }
});
const text1 = document.getElementById("text-1");
text1.innerText = getTranslation("Sophon LLMOps");
const text3 = document.getElementById("text-3");
text3.innerText = getTranslation("Welcome To Login");
const userName = document.getElementById("user-name");
userName.innerText = getTranslation("User Name");
const password = document.getElementById("password");
password.innerText = getTranslation("Password");
reset.innerText = getTranslation("Reset password");
reset1.innerHTML = getTranslation("Reset password");
usernameInput1.placeholder = getTranslation("Please enter username");
usernameInput.placeholder = getTranslation("Please enter username");
passwordInput.placeholder = getTranslation("Please enter password");
loginBtn.value = getTranslation("Login");
const usernameErrorMsg = document.getElementById("username-error-msg");
const usernameErrorMsg1 = document.getElementById("username-error-msg1");
usernameErrorMsg.innerText = getTranslation("Username cannot be empty");
usernameErrorMsg1.innerText = getTranslation("Username cannot be empty");
const passwordErrorMsg = document.getElementById("password-error-msg");
passwordErrorMsg.innerText = getTranslation("Password cannot be empty");
const desc = document.getElementById("desc");
desc.innerHTML = getTranslation("desc");
confirmButton.innerHTML = getTranslation("Confirm");
const link = document.getElementById('sso-login-link');
link.innerHTML = getTranslation("SSO Login");
