package search

import (
	"context"
	"sync"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"

	"transwarp.io/aip/llmops-common/pb"
)

var indexMap = make(map[pb.Module]Index)
var moduleRscTypeMap = map[pb.Module]pb.RscType{
	pb.Module_mlops:     pb.RscType_CHAIN,
	pb.Module_mw:        pb.RscType_MODEL,
	pb.Module_sample:    pb.RscType_DATASET,
	pb.Module_knowledge: pb.RscType_KNOWLEDGE_BASE,
}

func init() {
	if conf.IsDevMode() {
		stdlog.Warnf("!!!! skipping deps (search) initiation in development mode")
		return
	}
	indexDaemon := NewIndexDaemon()
	// exec per min
	// spec := "*/5 * * * * *"
	// spec := "0 * * * * ?"
	spec := "* */3 * * * ?"

	appletIndex, _ := NewIndex(pb.Module_mlops.String())
	indexMap[pb.Module_mlops] = appletIndex
	indexDaemon.AddSyncTask(spec, AppletDataPollerFunc(appletIndex))

	modelIndex, _ := NewIndex(pb.Module_mw.String())
	indexMap[pb.Module_mw] = modelIndex
	indexDaemon.AddSyncTask(spec, ModelDataPollerFunc(modelIndex))

	corupsIndex, _ := NewIndex(pb.Module_sample.String())
	indexMap[pb.Module_sample] = corupsIndex
	indexDaemon.AddSyncTask(spec, CorupsDataPollerFunc(corupsIndex))

	knowledgeBaseIndex, _ := NewIndex(pb.Module_knowledge.String())
	indexMap[pb.Module_knowledge] = knowledgeBaseIndex
	indexDaemon.AddSyncTask(spec, KnowlegeBaseDataPollerFunc(knowledgeBaseIndex))

	indexDaemon.Start()
}

type SearchSerivce struct {
}

func NewSearchService() *SearchSerivce {
	return &SearchSerivce{}
}

func (s *SearchSerivce) Search(ctx context.Context, keyword string, projIDs []string) (*pb.AssetResp, error) {
	var mu sync.Mutex
	groups := make([]*pb.Group, 0)
	wg := sync.WaitGroup{}
	wg.Add(len(indexMap))
	for k, index := range indexMap {
		go func(m pb.Module, index Index) {
			defer wg.Done()

			docs, err := index.Search(keyword, projIDs)
			if err != nil {
				stdlog.Errorf("search %s failed: %+v", m.String(), err)
			}

			group := pb.Group{
				Module:  m,
				RscType: moduleRscTypeMap[m],
				Items:   docs,
			}
			mu.Lock()
			groups = append(groups, &group)
			mu.Unlock()
		}(k, index)
	}
	wg.Wait()
	return &pb.AssetResp{
		Key:    keyword,
		Groups: groups,
	}, nil
}
