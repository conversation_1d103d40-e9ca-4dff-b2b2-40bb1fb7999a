package search

import (
	"context"
	"fmt"
	"path"
	"strconv"
	"strings"

	"github.com/blevesearch/bleve/v2"
	"github.com/blevesearch/bleve/v2/analysis"
	"github.com/blevesearch/bleve/v2/analysis/analyzer/custom"
	"github.com/blevesearch/bleve/v2/analysis/token/lowercase"
	"github.com/blevesearch/bleve/v2/analysis/token/ngram"
	"github.com/blevesearch/bleve/v2/analysis/token/stop"
	"github.com/blevesearch/bleve/v2/registry"
	"github.com/blevesearch/bleve/v2/search/query"
	index_api "github.com/blevesearch/bleve_index_api"
	"github.com/redis/rueidis"
	"github.com/yanyiwu/gojieba"
	"google.golang.org/protobuf/encoding/protojson"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/utils"
)

const (
	CustomAnalyzerName = "gojieba"
	JIEBA_DIC_DIR      = "JIEBA_DIC_DIR"
)

const (
	StopTokenFilterName = "stop_en"
	StopTokenMapName    = "english_stop_token_map"

	SubWordTokenFilterName = "ngram3_5"
)

func init() {
	// gojieba.DICT_DIR = utils.GetEnvWithDefault(JIEBA_DIC_DIR, "/tmp/gojieba/dict")
	// 本地调试注释
	gojieba.DICT_DIR = utils.GetEnvWithDefault(JIEBA_DIC_DIR, "/cas/gojieba/dict")

	gojieba.DICT_PATH = path.Join(gojieba.DICT_DIR, "jieba.dict.utf8")
	gojieba.HMM_PATH = path.Join(gojieba.DICT_DIR, "hmm_model.utf8")
	gojieba.USER_DICT_PATH = path.Join(gojieba.DICT_DIR, "user.dict.utf8")
	gojieba.IDF_PATH = path.Join(gojieba.DICT_DIR, "idf.utf8")
	gojieba.STOP_WORDS_PATH = path.Join(gojieba.DICT_DIR, "stop_words.utf8")

	registerTokenMap()
}

type Index interface {
	Search(keyword string, projIDs []string) ([]*pb.Item, error)
	AddOrUpdateDocument(doc *pb.Item) error
	DelDocument(doc *pb.Item) error
}

type RedisSearchIndex struct {
	client    rueidis.Client
	indexName string
}

func NewRedisSearchIndex(client rueidis.Client, indexName string) (*RedisSearchIndex, error) {
	index := &RedisSearchIndex{
		client:    client,
		indexName: indexName,
	}
	err := index.InitIndex()
	return index, err
}

func (r *RedisSearchIndex) InitIndex() error {
	ctx := context.Background()
	cmd := r.client.B().FtCreate().
		Index(r.indexName).OnJson().
		Prefix(1).Prefix(r.indexName + ":").
		Language("chinese").
		Schema().
		FieldName("$.id").As("id").Text().Weight(5.0).Sortable().
		FieldName("$.name").As("name").Text().Weight(4.0).Sortable().
		FieldName("$.desc").As("desc").Text().Weight(3.0).Sortable().
		FieldName("$.project_id").As("project_id").Text().
		FieldName("$.labels").As("labels").Text().
		FieldName("$.update_time").As("update_time").Numeric().
		Build()
	err := r.client.Do(ctx, cmd).Error()
	if err != nil {
		stdlog.Errorf("failed to create index %s: %+v", r.indexName, err)
		return err
	}
	return nil
}

func (r *RedisSearchIndex) Search(keyword string, projIDs []string) ([]*pb.Item, error) {
	// rueidis @colors:(white|light)
	ctx := context.Background()
	projIDFilter := ""
	if nil != projIDs && len(projIDs) > 0 {
		projIDFilter = fmt.Sprintf("@proj_id:(%s)", strings.Join(projIDs, "|"))
	}
	query := strings.Trim(fmt.Sprintf("%s %s", projIDFilter, keyword), " ")
	cmd := r.client.B().FtSearch().Index(r.indexName).Query(query).
		Limit().OffsetNum(0, 100).
		Build()
	_, ftRes, err := r.client.Do(ctx, cmd).AsFtSearch()
	if err != nil {
		stdlog.Errorf("failed to search: %+v", err)
		return nil, err
	}
	docs := make([]*pb.Item, 0)
	for _, v := range ftRes {
		docStr := v.Doc["$"]
		doc := &pb.Item{}
		protojson.Unmarshal([]byte(docStr), doc)
		docs = append(docs, doc)
	}
	return docs, nil
}

// if key exists, jsonset will overwrite, and if no exists will just add
func (r *RedisSearchIndex) AddOrUpdateDocument(doc *pb.Item) error {
	ctx := context.Background()
	key := fmt.Sprintf("%s:%s", r.indexName, doc.Id)
	docByte, err := protojson.Marshal(doc)
	if err != nil {
		return err
	}
	addCmd := r.client.B().JsonSet().Key(key).Path("$").Value(string(docByte)).Build()
	err = r.client.Do(ctx, addCmd).Error()
	if err != nil {
		return err
	}
	return nil
}

func (r *RedisSearchIndex) DelDocument(doc *pb.Item) error {
	ctx := context.Background()
	key := fmt.Sprintf("%s:%s", r.indexName, doc.Id)
	delCmd := r.client.B().JsonDel().Key(key).Build()
	err := r.client.Do(ctx, delCmd).Error()
	if err != nil {
		return err
	}
	return nil
}

type BleveSearchIndex struct {
	indexName string
	client    bleve.Index
}

func NewBleveSearchIndex(indexName string) (*BleveSearchIndex, error) {
	index := &BleveSearchIndex{
		indexName: indexName,
	}
	err := index.InitIndex()
	return index, err
}

func registerTokenMap() {
	englishStopWords := analysis.TokenMap{
		"a": true, "an": true, "and": true, "are": true, "as": true, "at": true, "be": true, "but": true, "by": true,
		"for": true, "if": true, "in": true, "into": true, "is": true, "it": true, "no": true, "not": true, "of": true,
		"on": true, "or": true, "such": true, "that": true, "the": true, "their": true, "then": true, "there": true,
		"these": true, "they": true, "this": true, "to": true, "was": true, "will": true, "with": true,
	}
	registry.RegisterTokenMap(StopTokenMapName,
		func(config map[string]interface{}, cache *registry.Cache) (analysis.TokenMap, error) {
			return englishStopWords, nil
		},
	)
}

func (b *BleveSearchIndex) InitIndex() error {
	indexMapping := bleve.NewIndexMapping()

	// docMapping := bleve.NewDocumentMapping()
	// docMapping.AddFieldMappingsAt("project_id", bleve.NewKeywordFieldMapping())
	// docMapping.AddFieldMappingsAt("name", bleve.NewTextFieldMapping())
	// docMapping.AddFieldMappingsAt("desc", bleve.NewTextFieldMapping())
	// docMapping.AddFieldMappingsAt("labels", bleve.NewTextFieldMapping())
	// indexMapping.AddDocumentMapping(b.indexName, docMapping)

	err := indexMapping.AddCustomTokenizer(JiebaTokenizerName, map[string]interface{}{
		"type": JiebaTokenizerType,
	})
	if err != nil {
		return err
	}

	indexMapping.AddCustomTokenFilter(SubWordTokenFilterName, map[string]interface{}{
		"type": ngram.Name,
		"min":  3,
		"max":  64,
	})

	err = indexMapping.AddCustomTokenFilter(StopTokenFilterName, map[string]interface{}{
		"type":           stop.Name,
		"stop_token_map": StopTokenMapName,
	})
	if err != nil {
		return err
	}

	err = indexMapping.AddCustomAnalyzer(CustomAnalyzerName, map[string]interface{}{
		"type":      custom.Name,
		"tokenizer": JiebaTokenizerName,
		"token_filters": []string{
			lowercase.Name,
			StopTokenFilterName,
			SubWordTokenFilterName,
		},
	})
	if err != nil {
		return err
	}
	indexMapping.DefaultAnalyzer = CustomAnalyzerName

	index, err := bleve.NewMemOnly(indexMapping)
	if err != nil {
		return err
	}
	b.client = index
	return nil
}

func (b *BleveSearchIndex) Search(keyword string, projIDs []string) ([]*pb.Item, error) {
	queryCondition := func(keyword string, projIDs []string) query.Query {
		var keywordQuery query.Query

		keywordQuery = bleve.NewQueryStringQuery(keyword)

		if nil == projIDs || len(projIDs) == 0 {
			return keywordQuery
		}

		projQueries := make([]query.Query, 0, len(projIDs))
		for _, v := range projIDs {
			q := bleve.NewTermQuery(v)
			q.SetField("project_id")
			projQueries = append(projQueries, q)
		}
		projIDsQuery := bleve.NewDisjunctionQuery(projQueries...)

		return bleve.NewConjunctionQuery(projIDsQuery, keywordQuery)
	}
	// max return 25
	searchReq := bleve.NewSearchRequest(queryCondition(keyword, projIDs))
	searchReq.Size = 25
	searchRsp, err := b.client.Search(searchReq)
	if err != nil {
		stdlog.Errorf("bleve search %s failed: %+v", keyword, err)
		return nil, err
	}
	rsp := make([]*pb.Item, 0)
	for _, v := range searchRsp.Hits {
		doc, _ := b.client.Document(v.ID)
		item := &pb.Item{}
		labels := make(map[string]string)
		doc.VisitFields(func(f index_api.Field) {
			switch {
			case strings.EqualFold(f.Name(), "id"):
				item.Id = string(f.Value())
			case strings.EqualFold(f.Name(), "name"):
				item.Name = string(f.Value())
			case strings.EqualFold(f.Name(), "desc"):
				item.Desc = string(f.Value())
			case strings.EqualFold(f.Name(), "project_id"):
				item.ProjectId = string(f.Value())
			case strings.HasPrefix(f.Name(), "labels."):
				labels[strings.TrimPrefix(f.Name(), "labels.")] = string(f.Value())
			case strings.EqualFold(f.Name(), "module"):
				value := pb.Module_value[f.Name()]
				item.Module = pb.Module(value)
			case strings.EqualFold(f.Name(), "rsc_type"):
				value := pb.RscType_value[f.Name()]
				item.RscType = pb.RscType(value)
			case strings.EqualFold(f.Name(), "creator"):
				item.Creator = string(f.Value())
			case strings.EqualFold(f.Name(), "update_time"):
				if time, err := strconv.ParseInt(string(f.Value()), 10, 64); err == nil {
					item.UpdateTime = time
				}
			default:
			}
		})
		item.Labels = labels
		rsp = append(rsp, item)
	}
	return rsp, nil
}
func (b *BleveSearchIndex) AddOrUpdateDocument(doc *pb.Item) error {
	key := fmt.Sprintf("%s:%s", b.indexName, doc.Id)
	return b.client.Index(key, doc)
}
func (b *BleveSearchIndex) DelDocument(doc *pb.Item) error {
	key := fmt.Sprintf("%s:%s", b.indexName, doc.Id)
	return b.client.Delete(key)
}
