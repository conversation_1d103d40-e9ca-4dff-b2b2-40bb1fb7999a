package search

import (
	"context"
	"fmt"
	"testing"
	"time"

	"google.golang.org/protobuf/encoding/protojson"
	"transwarp.io/aip/llmops-common/pb"
)

func TestInitIndex(t *testing.T) {
	NewIndex("test")
}

func TestAddOrCreateDoc(t *testing.T) {
	index, err := NewIndex("test")
	if err != nil {
		fmt.Println(err.Error())
	}
	doc := &pb.Item{
		Id:   "1",
		Name: "test",
		Desc: "this is a test document, 新建图知识库, 小明硕士毕业于中国科学院计算所，后在日本京都大学深造",
		//Labels:    "{\"pub\": \"true\"}",
		Labels:     map[string]string{"pub": "true"},
		ProjectId:  "1",
		Module:     pb.Module_mlops,
		RscType:    pb.RscType_CHAIN,
		UpdateTime: time.Now().UnixMicro(),
	}
	index.AddOrUpdateDocument(doc)
	index.AddOrUpdateDocument(&pb.Item{
		Id:         "2",
		Name:       "test",
		Desc:       "this is a test document, 新图表测试务删, 我来到北京清华大学",
		Labels:     map[string]string{"pub": "false"},
		ProjectId:  "2",
		Module:     pb.Module_mw,
		RscType:    pb.RscType_MODEL,
		UpdateTime: time.Now().UnixMicro(),
	})
	rsp, err := index.Search("大", []string{"1", "2"})
	if err != nil {
		fmt.Println(err.Error())
	}
	options := protojson.MarshalOptions{
		EmitUnpopulated: true,
	}
	for _, v := range rsp {
		b, err := options.Marshal(v)
		if err != nil {
			fmt.Println(err.Error())
		}
		fmt.Println(string(b))
	}
}

func TestSearchService(t *testing.T) {
	time.Sleep(10 * time.Second)
	ss := SearchSerivce{}
	rsp, err := ss.Search(context.Background(), "图表", nil)
	if err != nil {
		fmt.Println(err.Error())
	}
	options := protojson.MarshalOptions{
		EmitUnpopulated: true, // 输出未填充的字段（即默认值）
	}
	b, err := options.Marshal(rsp)
	if err != nil {
		fmt.Println(err.Error())
	}
	fmt.Println(string(b))
}

// FT.CREATE test:doc ON JSON PREFIX 1 "test:" SCHEMA $.id AS id TEXT $.name AS name TEXT $.desc AS desc TEXT
