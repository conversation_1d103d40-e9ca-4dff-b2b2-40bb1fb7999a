package search

import (
	"strings"

	"github.com/blevesearch/bleve/v2/analysis"
	"github.com/blevesearch/bleve/v2/registry"
	"github.com/yanyiwu/gojieba"

	"transwarp.io/applied-ai/central-auth-service/conf"
)

const (
	JiebaTokenizerName = "gojieba"
	JiebaTokenizerType = "gojieba"
)

type JiebaTokenizer struct {
	jieba     *gojieba.Jieba
	stopWords map[string]struct{}
}

func init() {
	if conf.IsDevMode() {
		return
	}
	registry.RegisterTokenizer(JiebaTokenizerName, TokenizerConstructor)
}

func (t *JiebaTokenizer) Tokenize(input []byte) analysis.TokenStream {
	text := strings.ToLower(string(input))
	tokenStream := make(analysis.TokenStream, 0)

	tokenStream = append(tokenStream, &analysis.Token{
		Term:     input,
		Start:    0,
		End:      len(text),
		Position: 1,
		Type:     analysis.AlphaNumeric,
	})

	start := 0
	segments := t.jieba.Cut(text, true)
	for _, segment := range segments {
		token := &analysis.Token{
			Term:     []byte(segment),
			Start:    start,
			End:      start + len(segment),
			Position: len(tokenStream) + 1,
			Type:     analysis.AlphaNumeric,
		}
		tokenStream = append(tokenStream, token)
		start += len(segment)
	}

	return tokenStream
}

func TokenizerConstructor(config map[string]interface{}, cache *registry.Cache) (analysis.Tokenizer, error) {
	jieba := gojieba.NewJieba()

	return &JiebaTokenizer{
		jieba: jieba,
	}, nil
}
