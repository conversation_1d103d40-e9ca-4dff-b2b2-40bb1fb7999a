package search

import (
	"context"
	"fmt"
	"testing"
	"time"
)

func TestSearch(t *testing.T) {
	projIDs := []string{"default", "assets"}
	ss := NewSearchService()
	time.Sleep(30 * time.Second)
	resp, err := ss.Search(context.TODO(), "test", projIDs)
	if err != nil {
		fmt.Printf("err: %+v", err)
	}
	fmt.Println(len(resp.Groups))
	resp, err = ss.Search(context.TODO(), "t032", projIDs)
	if err != nil {
		fmt.Printf("err: %+v", err)
	}
	fmt.Println(len(resp.Groups))
	fmt.Println(len(resp.Groups))
	resp, err = ss.Search(context.TODO(), "0325", projIDs)
	if err != nil {
		fmt.Printf("err: %+v", err)
	}
	fmt.Println(len(resp.Groups))
	resp, err = ss.Search(context.TODO(), "dEep", projIDs)
	if err != nil {
		fmt.Printf("err: %+v", err)
	}
	fmt.Println(len(resp.Groups))
	resp, err = ss.Search(context.TODO(), "DeEPsEek", projIDs)
	if err != nil {
		fmt.Printf("err: %+v", err)
	}
	fmt.Println(len(resp.Groups))
}
