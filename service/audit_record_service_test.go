package service

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"testing"
	"time"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/helper"
)

var entryJson = `{"api_module":"mw","headers":{"sec-ch-ua-platform":"\"Windows\"","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","x-forwarded-proto":"https","sec-fetch-dest":"empty","x-forwarded-for":"***********","referer":"https:\/\/**************:30443\/project\/llmops\/mw\/atom\/MWH-MODEL-cppshfou859fcqgrr7k0\/model-tabs\/%E6%B5%8B%E8%AF%95\/versions","accept-encoding":"gzip, deflate, br, zstd","accept-language":"zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6","priority":"u=1, i","cookie":"SOPHONID=070fdd19268ea292a6211f4aed307d25; token=e32bdf68-33be-493b-89cd-16303c46cef2","x-forwarded-port":"443","connection":"upgrade","authorization":"Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MTk1NDY4OTgsImlhdCI6MTcxOTI4NzY5OCwiaXNzIjoidHJhbnN3YXJwIiwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInN1YiI6ImxsbW9wcyIsInVzZXJuYW1lIjoidGhpbmdlciJ9.et4bqQMKRkNzvjrQz52B0TTdqTSxel475gnMkKbSzcPHwg8IxRiUGwd8t9yVD7KnrDDeKciFvTN1xZmd9V2smw","origin":"https:\/\/**************:30443","sec-ch-ua":"\"Not\/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Microsoft Edge\";v=\"126\"","host":"**************:30443","content-length":"335","sec-ch-ua-mobile":"?0","content-type":"application\/json","accept":"application\/json, text\/plain, *\/*","user-agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********"},"req_body":"{\"target_project\":\"assets\",\"source_project\":\"llmops\",\"cloned_models\":[{\"id\":\"MWH-MODEL-cppshfou859fcqgrr7k0\",\"desc_after_clone\":\"\",\"name_after_clone\":\"测试_1719393504555\",\"label_after_clone\":{}}],\"cloned_specific_releases\":[{\"id\":\"MWH-MODEL-RELEASE-cppshp8u859fcqgrr7l0\",\"desc_after_clone\":\"\",\"name_after_clone\":\"22_1719393504555\"}]}","remote_addr":"***********","method":"POST","timestamp":"1719393635.981","uri":"\/api\/v1\/mwh\/rscmgr\/resources:clone","status":"200","resp_body":"{\"examineFlag\":true,\"config\":{\"flowName\":\"project_assets_flow\",\"type\":\"共享模型\",\"rule\":{\"key\":\"examine\",\"value\":\"model_share\"},\"extraRule\":{\"or\":[{\"and\":[{\"type\":\"body\",\"name\":[\"target_project\"],\"operator\":\"eq\",\"value\":\"assets\"}]}]}}}","query_args":{"project_id":"llmops"}}`

func TestMain(m *testing.M) {
	nlp = &NgxLogProcessor{
		apiConfigs: conf.C.AuditRecord.APIs,
	}
	nlp.Init()

	m.Run()
}
func TestMatchEntryToAPI(t *testing.T) {
	entry := new(NgxLogEntry)
	if err := json.Unmarshal([]byte(entryJson), entry); err != nil {
		t.Error(err)
	}
	fmt.Println(entry)

	data := map[string]any{
		ReqBody:     entry.ReqBody,
		RespBody:    entry.RespBody,
		Headers:     entry.Headers,
		QueryParams: entry.QueryArgs,
	}
	api := matchEntryToAPI(entry, nlp.apiConfigs, data)
	if api == nil {
		panic("api is nil")
	}
	fmt.Println(api)
	headers := entry.Headers
	authorization, ok := headers["authorization"]
	if !ok {
		panic(nil)
	}
	jwt, err := auth.ParseToken(authorization)
	if err != nil {
		panic(nil)
	}
	details := map[string]string{
		ReqBody:      entry.ReqBody,
		RespBody:     entry.RespBody,
		Headers:      mustMarshal(headers),
		QueryParams:  mustMarshal(entry.QueryArgs),
		MatchedPaths: mustMarshal(data[MatchedPaths]),
	}

	// 默认API都通过query参数指定空间id
	var projectId string
	if v, ok := entry.QueryArgs[ProjectId]; ok {
		if vv, ok := v.(string); ok {
			projectId = vv
		}
	}

	if projectId == "" && api.SubModule == "SPACE_MEMBER_MANAGEMENT" { // 成员管理的projectId参数在请求路径中
		if paths, ok := data[MatchedPaths].([]string); ok && len(paths) > 1 {
			projectId = paths[1]
		}
	}

	var timeMills int64
	// nginx中的timestamp格式为1717064096.366
	timeMills, err = strconv.ParseInt(strings.Replace(entry.Timestamp, ".", "", 1), 10, 64)
	if err != nil {
		stdlog.Warnf("parse unix mill of ngx log, err:%v", err)
		timeMills = time.Now().UnixMilli()
	}

	event, err := render(api.GoTemplate, data, true)
	if err != nil {
		t.Error(err)
	}

	r := &pb.AuditRecord{
		Module:     helper.StringToEnum[pb.AuditRecordModule]("AuditRecordModule_"+api.Module, pb.AuditRecordModule_value),
		SubModule:  helper.StringToEnum[pb.AuditRecordSubModule]("AuditRecordSubModule_"+api.SubModule, pb.AuditRecordSubModule_value),
		OpType:     helper.StringToEnum[pb.AuditRecordOperateType](api.OpType, pb.AuditRecordOperateType_value),
		Event:      event,
		User:       jwt.GetUsername(),
		TimeMills:  timeMills,
		MatchedApi: api.ToPb(),
		ReqPath:    entry.URI,
		Details:    details,
		ProjectId:  projectId,
	}
	fmt.Println(r)
}
