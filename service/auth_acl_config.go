package service

import (
	"errors"
	"fmt"
	"io/fs"
	"os"
	"path"
	"slices"
	"strings"

	"github.com/go-playground/validator/v10"
	"gopkg.in/yaml.v3"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type AclConfig struct {
	Module      string `yaml:"module"`
	Route       string `validate:"required"              yaml:"route"`
	Method      string `validate:"required,uppercase"    yaml:"method"`
	Uri         string `validate:"required,startswith=/" yaml:"uri"`
	Description string `yaml:"description"`
	PermCode    string `validate:"required"              yaml:"perm_code"`
	PermAction  string `validate:"required"              yaml:"perm_action"`
}

var (
	validate   = validator.New(validator.WithRequiredStructEnabled())
	AclService = newAcl()
)

func init() {
	err := AclService.loadConfig()
	if err != nil {
		panic(err)
	}
}

type acl struct {
	aclConfigs    []*AclConfig
	methUriConfig map[string]*AclConfig
	router        *router
}

func newAcl() *acl {
	return &acl{
		methUriConfig: make(map[string]*AclConfig),
		aclConfigs:    []*AclConfig{},
		router:        newRouter(),
	}
}

func (a *acl) loadConfig() error {
	_, err := os.Stat("./etc/acl_config.yaml")
	if errors.Is(err, fs.ErrNotExist) {
		stdlog.Warn("acl_config.yaml not found, skipping")
		return nil
	}

	b, err := os.ReadFile("./etc/acl_config.yaml")
	if err != nil {
		return fmt.Errorf("read file: %w", err)
	}

	a.aclConfigs = make([]*AclConfig, 0)

	err = yaml.Unmarshal(b, &a.aclConfigs)
	if err != nil {
		return fmt.Errorf("unmarshal yaml: %w", err)
	}

	file, err := os.ReadFile("./etc/permission.yaml")
	if err != nil {
		return fmt.Errorf("read permission.yaml: %w", err)
	}
	perms := make([]*models.Permission, 0)
	err = yaml.Unmarshal(file, &perms)
	if err != nil {
		return fmt.Errorf("unmarshal permissions: %w", err)
	}
	permMap := make(map[string][]string)
	for _, p := range perms {
		permMap[p.Code] = append(permMap[p.Code], p.Action)
	}

	a.router = newRouter()
	for _, v := range a.aclConfigs {
		err = validate.Struct(v)
		if err != nil {
			return fmt.Errorf("validate: %w: %#v", err, v)
		}
		// 校验配置的权限是否存在
		// DEBUG 暂时跳过 corpus.annotation.*
		if v.PermCode != "corpus.annotation.*" {
			if as, ok := permMap[v.PermCode]; !ok {
				stdlog.Warnf("permission not found: %s", v.PermCode)
			} else if !slices.Contains(as, v.PermAction) {
				stdlog.Warnf("permission action not found: %s %s", v.PermCode, v.PermAction)
			}
		}

		a.router.addRoute(v.Method, path.Join("/", v.Route, v.Uri), v)
	}
	return nil
}

// Match 路由匹配
//
//	@param m method: GET, POST, ...
//	@param uri /<route>/uri
//	@return bool 是否匹配成功
func (a *acl) Match(m, uri string) (*AclConfig, bool) {
	return a.router.match(m, uri)
}

// nodeType 路由节点类型
type nodeType uint8

const (
	static   nodeType = iota // 静态路由
	param                    // 参数路由: {}
	catchAll                 // 通配符路由: *, TODO
)

type node struct {
	path     string // 当前节点路径片段
	fullpath string // 当前节点完整路径
	acl      *AclConfig
	nodeType nodeType
	children []*node
}

type router struct {
	trees map[string]*node // [method]*node
}

func newRouter() *router {
	return &router{
		trees: make(map[string]*node, 10),
	}
}

// search 搜索下一个节点
//
//	@param part 要搜索的节点path片段
//	@param skip 是否跳过 [param] 节点
func (n *node) search(part string) (*node, bool) {
	for _, child := range n.children {
		if child.nodeType == static && child.path == part {
			return child, true
		}
	}

	for _, child := range n.children {
		if child.nodeType == param {
			return child, true
		}
	}

	return nil, false
}

// isParam 是否是参数节点
func isParam(part string) bool {
	return strings.HasPrefix(part, "{") && strings.HasSuffix(part, "}")
}

// addRoute 添加到路由树中
//
//	@param method 请求方式
//	@param path 路由: /<route>/uri
//
// TODO 路由冲突处理, 目前是覆盖
func (r *router) addRoute(method, path string, acl *AclConfig) {
	if _, ok := r.trees[method]; !ok {
		r.trees[method] = &node{}
	}

	n := r.trees[method]

	fulls := make([]string, 0)
	parts := strings.Split(path, "/")
	if len(parts) == 0 {
		stdlog.Warnf("add route: path is empty: %s %s: %+v", method, path, acl)
		return
	}
	for _, part := range parts[1:] {
		if isParam(part) {
			fulls = append(fulls, "{var}")
		} else {
			fulls = append(fulls, part)
		}

		if part == "" {
			continue
		}

		nextNode, found := n.search(part)

		if !found {
			nextNode = &node{path: part, fullpath: strings.Join(fulls, "/")}
			idx := -1
			if isParam(part) {
				nextNode.path = ""
				nextNode.nodeType = param
				idx = slices.IndexFunc(n.children, func(e *node) bool { return e.nodeType == param })
				if idx >= 0 {
					// 已经有 参数节点, 共用
					nextNode = n.children[idx]
				}
			}
			if idx < 0 {
				n.children = append(n.children, nextNode)
			}
		}

		n = nextNode
	}

	n.acl = acl
}

func (r *router) match(method, path string) (*AclConfig, bool) {
	root, ok := r.trees[method]
	if !ok {
		return nil, false
	}

	n := root
	parts := strings.Split(path, "/")
	if len(parts) == 0 {
		stdlog.Warnf("match: path is empty: %s %s", method, path)
		return nil, false
	}

	for _, part := range parts[1:] {
		if part == "" {
			continue
		}

		nextNode, found := n.search(part)

		if !found {
			return nil, false
		}

		n = nextNode
	}

	if n == nil || n.acl == nil {
		return nil, false
	}

	stdlog.Infof("uri: %s match node: %+v: acl: %+v", path, n, n.acl)

	return n.acl, true
}
