package service

import (
	"context"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/samber/lo"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/dao/rbac"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/utils"
	"transwarp.io/applied-ai/central-auth-service/utils/auth/cas"
)

// LockInfo stores information about a locked user account
type LockInfo struct {
	LockedUntil time.Time
	Attempts    int
}

// 定义错误代码常量
const (
	ErrInvalidCredentials = iota + 1 // 用户名或密码错误
	ErrUserNotFound                  // 用户不存在
	ErrUserNotAvailable              // 用户被禁用
	ErrRemainingAttempts             // 密码错误，剩余尝试次数
	ErrTooManyAttempts               // 错误次数过多，账户被锁定
	ErrAccountLocked                 // 账户已被锁定
	ErrInternalError                 // 系统内部错误
	ErrUsernameEmpty                 // 系统内部错误
	ErrPasswordEmpty                 // 系统内部错误
	ErrWhiteIP                       // ip 不在白名单内
)

// 定义错误类型
type LoginError struct {
	Code    int    // 错误代码
	Message string // 错误消息
}

// 实现 Error 方法使 LoginError 满足 error 接口
func (e *LoginError) Error() string {
	return e.Message
}

// NewLoginError 创建错误函数，根据 lang 返回不同语言的错误消息
func NewLoginError(code int, lang stdsrv.Language, remainingAttempts int, dur time.Duration) error {
	isZh := lang == stdsrv.LanguageChinese
	var message string
	switch code {
	case ErrInvalidCredentials:
		if isZh {
			message = "用户名或密码错误，请检查后重新尝试。"
		} else {
			message = "Incorrect username or password. Please check and try again."
		}
	case ErrUserNotFound:
		if isZh {
			message = "用户不存在，请检查用户名。"
		} else {
			message = "User does not exist. Please check the username."
		}
	case ErrUserNotAvailable:
		if isZh {
			message = "该用户已被禁用，请联系管理员。"
		} else {
			message = "This user is disabled. Please contact the administrator."
		}
	case ErrRemainingAttempts:
		if isZh {
			message = fmt.Sprintf("用户名或密码错误，请检查后重新尝试。您还有 %d 次尝试机会，超过5次错误将锁定您的账户。", remainingAttempts)
		} else {
			message = fmt.Sprintf("Incorrect username or password. Please check and try again. You have %d attempts remaining. After 5 incorrect attempts, your account will be locked.", remainingAttempts)
		}
	case ErrTooManyAttempts:
		if isZh {
			message = fmt.Sprintf("错误次数过多，您的账户已被锁定。请 %s 钟后再试。", dur.String())
		} else {
			message = fmt.Sprintf("Too many incorrect attempts. Your account is locked. Please try again in %s.", dur.String())
		}
	case ErrAccountLocked:
		if isZh {
			message = fmt.Sprintf("您的账户已被锁定，请 %s 后再试。", dur.String())
		} else {
			message = fmt.Sprintf("Your account is locked. Please try again in %s.", dur.String())
		}
	case ErrUsernameEmpty:
		if isZh {
			message = "用户名不能为空。"
		} else {
			message = "The username cannot be empty."
		}
	case ErrInternalError:
		if isZh {
			message = "密码不能为空。"
		} else {
			message = "The password cannot be empty"
		}
	case ErrWhiteIP:
		if isZh {
			message = "您的IP地址不允许使用此账户"
		} else {
			message = "Your IP address is not allowed to use this account."
		}
	default:
		message = "Unknown error."
	}

	return &LoginError{
		Code:    code,
		Message: message,
	}
}

func (s *AuthService) Login(u *models.UserReq, svc, host, clientip string, lang stdsrv.Language) (
		tgt *dao.TicketGrantingTicket, st *dao.ServiceTicket, err error,
) {
	const maxFailedAttempts = 5
	const lockoutDuration = 30 * time.Minute

	s.lockMutex.Lock()
	defer s.lockMutex.Unlock()

	var exist *dao.User
	exist, err = dao.GetUserByName(s.db, u.UserName)
	if err != nil {
		stdlog.WithError(err).Errorf("Failed to get user by name: %s", u.UserName)
		err = NewLoginError(ErrInternalError, lang, 0, 0)
		return

	}
	if exist.Name == "" {
		err = NewLoginError(ErrUserNotFound, lang, 0, 0)
		return
	}
	if exist.Status != models.Enable {
		err = NewLoginError(ErrUserNotAvailable, lang, 0, 0)
		return
	}

	// Check if the account is locked
	if lockInfo, ok := s.lockoutInfo[u.UserName]; ok && time.Now().Before(lockInfo.LockedUntil) {
		dur := time.Duration(lockInfo.LockedUntil.Sub(time.Now()).Seconds()) * time.Second
		err = NewLoginError(ErrAccountLocked, lang, 0, dur)
		return
	}

	if exist.Secret != (&dao.User{Name: u.UserName}).GenSecret(u.Password) {
		// Increment failed login attempts
		lockInfo, ok := s.lockoutInfo[u.UserName]
		if !ok {
			lockInfo = &LockInfo{}
			s.lockoutInfo[u.UserName] = lockInfo
		}
		lockInfo.Attempts++

		// Check if the maximum number of failed attempts is reached
		if lockInfo.Attempts >= maxFailedAttempts {
			// Lock the account
			lockInfo.LockedUntil = time.Now().Add(lockoutDuration)
			lockInfo.Attempts = 0 // Reset failed attempts count

			err = NewLoginError(ErrTooManyAttempts, lang, 0, lockoutDuration)
			return
		}

		err = NewLoginError(ErrRemainingAttempts, lang, maxFailedAttempts-lockInfo.Attempts, lockoutDuration)
		return
	}
	// check white ip
	if !exist.CheckIP(exist.WhiteIps, clientip) {
		err = NewLoginError(ErrWhiteIP, lang, 0, 0)
		return
	}
	// Reset failed attempts on successful login
	delete(s.lockoutInfo, u.UserName)

	// if not allow multiclient login at the same time then kickoff logined
	if !s.allowMultiClientLoginAtSameTime {
		dao.DeleteTicketGrantingTicketByUsername(s.db, u.UserName)
	}

	// Continue with the rest of the login logic...
	// (Generate TGT, ST, etc.)
	tgt = dao.NewTicketGrantingTicket(u.UserName, host)
	if err = dao.CreateTicketGrantingTicket(s.db, tgt); err != nil {
		return
	}
	st = dao.NewServiceTicket(tgt.Ticket, svc, false)
	if err = dao.CreateServiceTicket(s.db, st); err != nil {
		return
	}

	return tgt, st, nil
}

func (s *AuthService) Logout(ticket string) (err error) {
	return dao.DeleteTicketGrantingTicket(s.db, ticket)
}

func (s *AuthService) CheckTicketGrantingTicketExistence(ticket string) bool {
	if tgt, err := dao.GetTicketGrantingTicket(s.db, ticket); err != nil || tgt.Ticket != ticket {
		return false
	}
	return true
}

func (s *AuthService) ValidateServiceTicket(service, ticket string) []byte {
	var err error

	var st *dao.ServiceTicket
	st, err = dao.GetServiceTicket(s.db, ticket)
	if err != nil {
		err = stderr.Database.Cause(err, "ServiceTicket not found: %s", ticket)
		return cas.NewFailureServiceResponse("ST_NOT_FOUND", err.Error())
	}
	// if st.Service != s.trimServiceQueries(service) {
	//	err = fmt.Errorf("invalid service: the ServiceTicket was used for another service: %s", st.Service)
	//	return cas.NewFailureServiceResponse("INVALID_SERVICE", err.Error())
	// }
	if st.Validity.Before(time.Now()) {
		err = fmt.Errorf("ServiceTicket is expired")
		return cas.NewFailureServiceResponse("ST_EXPIRED", err.Error())
	}
	if err = dao.DeleteServiceTicket(s.db, ticket); err != nil {
		err = stderr.Database.Cause(err, "fail to remove ServiceTicket after using")
		return cas.NewFailureServiceResponse("INTERNAL_ERROR", err.Error())
	}

	var tgt *dao.TicketGrantingTicket
	tgt, err = dao.GetTicketGrantingTicket(s.db, st.Tgt)
	if err != nil {
		err = stderr.Database.Cause(err, "TicketGrantingTicket not found, Tgt:%s", st.Tgt)
		return cas.NewFailureServiceResponse("TGT_NOT_FOUND", err.Error())
	}

	var user *dao.User
	user, err = dao.GetUserByName(s.db, tgt.Username)
	if err != nil {
		err = stderr.Database.Cause(err, "user[%s] not found", tgt.Username)
		return cas.NewFailureServiceResponse("USER_NOT_FOUND", err.Error())
	}
	return cas.NewSuccessServiceResponse(user, "")
}

// CreateServiceTicket 基于TGT(单点登录界面长有效期)创建ST(临时登录验证,短有效期)
func (s *AuthService) CreateServiceTicket(ticket string, svc string, host string) (st *dao.ServiceTicket, err error) {
	var tgt *dao.TicketGrantingTicket
	tgt, err = dao.GetTicketGrantingTicket(s.db, ticket)
	if err != nil {
		err = stderr.Wrap(err, "get tgt from db by %s", ticket)
		return
	}
	if tgt.Validity.Before(time.Now()) {
		err = stderr.Unauthenticated.Error("TGT was expired, please re-login")
		return
	}
	if tgt.Host != utils.IPAddr(host) {
		err = stderr.Unauthenticated.Error("Unauthenticated IP Address: %s", host)
		return
	}

	st = dao.NewServiceTicket(ticket, s.trimServiceQueries(svc), false)
	if err = dao.CreateServiceTicket(s.db, st); err != nil {
		return
	}
	return st, nil
}

func (s *AuthService) trimServiceQueries(svc string) string {
	idx := strings.Index(svc, "?")
	if idx < 0 {
		return svc
	}
	return svc[:idx]
}

func (s *AuthService) AclAllow(ctx context.Context, username, method, uri string) (*models.AclAllowRsp, error) {
	user, err := dao.GetUserByName(s.db, username)
	if err != nil {
		return nil, err
	}

	if user.ID == 0 {
		return nil, fmt.Errorf("%s 用户不存在", username)
	}

	userGroups, err := rbac.BatchGetUserGroupsByUserNames(s.db, []string{username})
	if err != nil {
		return nil, err
	}

	groupNames := lo.Map(userGroups, func(e *models.UserGroup, _ int) string { return e.GroupName })

	// 根据用户和用户组获取平台角色和空间角色信息
	userRoles := make([]*models.UserRole, 0)

	err = s.db.Where("name in (?)", append(groupNames, username)).Find(&userRoles).Error
	if err != nil {
		return nil, err
	}
	// 如果包括超管
	if slices.ContainsFunc(userRoles, func(userRole *models.UserRole) bool {
		return userRole.RoleId == uint64(helper.SuperAdminUserID)
	}) {
		stdlog.Infof("%s 用户是超管，直接放行", username)

		return &models.AclAllowRsp{Allow: true}, nil
	}

	roleIds := lo.Map(userRoles, func(e *models.UserRole, _ int) uint64 { return e.RoleId })

	acl, ok := AclService.Match(method, uri)
	if !ok {
		stdlog.Infof("acl not found: %s %s", method, uri)

		return &models.AclAllowRsp{Allow: true}, nil
	}

	var t int64

	err = s.db.Model(&models.RolePermission{}).
		Where("role_id in (?)", roleIds).
		Where("permission_id in (?)",
			s.db.Model(&models.Permission{}).
				Select("id").
				Where("code = ?", acl.PermCode).
				Where("action = ?", acl.PermAction)).
		Count(&t).Error
	if err != nil {
		return nil, fmt.Errorf("count roles with perm_action: %w", err)
	}

	if t > 0 {
		return &models.AclAllowRsp{Allow: true}, nil
	}

	stdlog.Infof("acl not allow: %s %s: acl: %+v", method, uri, acl)

	return &models.AclAllowRsp{Allow: false}, nil
}
