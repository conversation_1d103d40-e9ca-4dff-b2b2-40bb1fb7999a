package service

import (
	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/dao"
)

type GpuSpec struct {
	db *gorm.DB
}

func NewGpuSpecService(db *gorm.DB) *GpuSpec {
	gs := &GpuSpec{db: db.Session(&gorm.Session{NewDB: true})}
	return gs
}

func (s *GpuSpec) CreateGpuSpec(gpuSpec *dao.GpuSpec) error {
	return dao.CreateGpuSpec(s.db, gpuSpec)
}

func (s *GpuSpec) GetGpuSpecByID(gpuSpecID string) (*dao.GpuSpec, error) {
	gpuSpec, err := dao.GetGpuSpec(s.db, gpuSpecID)
	if err != nil {
		return nil, err
	}
	return gpuSpec, nil
}

func (s *GpuSpec) ListGpuSpecs() ([]*dao.GpuSpec, error) {
	gpuSpecs, err := dao.ListGpuSpecs(s.db)
	if err != nil {
		return nil, err
	}
	return gpuSpecs, nil
}

func (s *GpuSpec) DeleteGpuSpecByID(gpuSpecID string) error {
	return dao.DeleteGpuSpec(s.db, gpuSpecID)
}

func (s *GpuSpec) UpdateGpuSpec(gpuSpec *dao.GpuSpec, id string) error {
	return dao.UpdateGpuSpec(s.db, gpuSpec, id)
}
