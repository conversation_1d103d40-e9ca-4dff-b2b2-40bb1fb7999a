package rbac

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"net"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/saintfish/chardet"
	"golang.org/x/exp/slices"
	"golang.org/x/text/encoding"
	"golang.org/x/text/encoding/ianaindex"
	"golang.org/x/text/encoding/unicode"
	"golang.org/x/text/encoding/unicode/utf32"
	"golang.org/x/text/transform"
	"gorm.io/gorm"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/project"
)

const DirPrefix = "/sfs"

const (
	TemplFileName         = "templ.csv"
	TemplFilePath         = "tenants/dev-assets/projs/assets/center-manager"
	UsernameColumn        = "用户名"
	PasswordColumn        = "密码"
	ConfirmPasswordColumn = "确认密码"
	NameColumn            = "全名"
	EmailColumn           = "邮箱"
	PhoneColumn           = "手机"
	PlatformRoleName      = "平台角色"
	DefaultProjectName    = "默认空间"
	WhiteIPs              = "IP白名单"
)

var (
	// 表头
	TitleRow = []string{
		UsernameColumn, PasswordColumn, ConfirmPasswordColumn, NameColumn,
		EmailColumn, PhoneColumn, PlatformRoleName, DefaultProjectName, WhiteIPs,
	}
	TitleRowEn = []string{
		"Username", "password", "confirm password", "full name",
		"email", "phone", "platform role", "default space", "IP whitelist",
	}
	// 平台角色待选项
	PlatformRoleValues   = []string{"普通用户", "管理员", "超级管理员"}
	PlatformRoleValuesEn = []string{"Regular User", "Administrator", "Super Administrator"}
	// 用户名校验正则
	regUserName = regexp.MustCompile(`^[a-zA-Z0-9][a-zA-Z0-9-_\\.]{0,54}[a-zA-Z0-9]$`)
	regPhone    = regexp.MustCompile(`^[0-9-+]+$`)
	valida      = validator.New()
	detector    = chardet.NewTextDetector() // 用于字符编码检测
)

type UserImportService struct {
	db *gorm.DB
}

func NewUserImportService(db *gorm.DB) *UserImportService {
	s := &UserImportService{
		db: db,
	}
	s.CreateTemplateFile()
	return s
}

type CsvUserModel struct {
	UserName           string
	Password           string
	ConfirmPassword    string
	Name               string
	Email              string
	Phone              string
	PlatformRoleName   string
	DefaultProjectName string
	WhiteIPs           []string
}

// CreateTemplateFile 生成用户导入模板 现在是放在了前端
func (s *UserImportService) CreateTemplateFile() {
	// TODO 文件已存在则直接返回
	// file, err := os.Open(TemplFileName)
	// if err != nil {
	// 	if os.IsNotExist(err) {
	// 		stdlog.Info("模板文件不存在")
	// 	} else {
	// 		stdlog.Errorf("打开文件 %s 时发生错误: %v", TemplFileName, err)
	// 	}
	// } else {
	// 	return file
	// }
	err := os.Mkdir(filepath.Join(DirPrefix, TemplFilePath), os.ModeDir)
	if err != nil {
		stdlog.Warnf("create template file: %s", err)
	}
	fileName := filepath.Join(DirPrefix, TemplFilePath, TemplFileName)
	stdlog.Infof("create template file: %s", fileName)
	file, err := os.Create(filepath.Join(DirPrefix, TemplFilePath, TemplFileName))
	if err != nil {
		stdlog.Errorf("create template file: %s", err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 字段说明
	desc := []string{`【字段说明】
1、用户名：必填；长度不超过56个字符；仅支持英文、数字、短划线、下划线和小数点，必须以英文或数字开头和结尾。
2、密码/确认密码：必填；长度及格式无限制，请确保两次输入的密码保持一致。
3、全名：选填，可为空；长度不超过40个字符，格式无限制。
4、邮箱：必填；请确保邮箱格式输入正确。
5、平台角色：必填；枚举值，仅支持填写“普通用户”、“管理员”或“超级管理员”，其他任何输入都将无法通过校验。
6、默认空间：必填；请严格匹配空间列表中已存在的空间名称，否则将无法通过校验。`, "", "", "", "", "", ""}
	// 写入字段说明与表头
	err = writer.Write(desc)
	if err != nil {
		stdlog.Errorf("write to template file: %s", err)
	}
	err = writer.Write(TitleRow)
	if err != nil {
		stdlog.Errorf("write template file: %s", err)
	}
}

// readCsvFile 读取 csv 文件返回数据
func (s *UserImportService) readCsvFile(path string) ([]*CsvUserModel, string, error) {
	file, err := os.Open(path)
	if err != nil {
		stdlog.Errorf("无法打开文件 %s: %v", path, err)
		return nil, "", err
	}
	defer file.Close()
	fileInfo, err := file.Stat()
	if err != nil {
		stdlog.Errorf("无法获取文件信息 %s: %v", path, err)
		return nil, "", err
	}
	// 如果 == 0, 再次尝试读取文件
	// AIP-106620: 从文件系统创建文件时, 会首先创建空文件占位而后写入内容, 读取时需要延迟一下
	for i := 0; fileInfo.Size() == 0 && i < 4; i++ {
		stdlog.Warnf("文件 %s 为空，正在重试...", file.Name())
		err = file.Close()
		if err != nil {
			stdlog.Errorf("无法关闭文件: %s: %v", path, err)
			return nil, "", err
		}
		time.Sleep(time.Second << i)
		file, err = os.Open(path)
		if err != nil {
			stdlog.Errorf("无法打开文件 %s: %v", path, err)
			return nil, "", err
		}
		fileInfo, err = file.Stat()
		if err != nil {
			stdlog.Errorf("无法获取文件信息 %s: %v", path, err)
			return nil, "", err
		}
	}
	// 校验文件大小
	if fileInfo.Size() == 0 {
		return nil, "读取到空文件", errors.New("读取到空文件")
	}
	if fileInfo.Size() > 10*1024*1024 {
		return nil, "文件大小超过 10MB", errors.New("文件大小超过 10MB")
	}

	buf, err := io.ReadAll(file) // 获取数据 用于判断字符编码
	if err != nil {
		return nil, "读取文件失败", err
	}
	_, _ = file.Seek(0, io.SeekStart) // 文件指针重置
	// 进行编码检测 获取置信度最高的结果
	result, err := detector.DetectBest(buf)
	if err != nil {
		return nil, "获取文件编码失败", err
	}
	result.Charset = strings.ToUpper(result.Charset)
	if result.Charset == "GB-18030" { // 命名不一致 see: https://github.com/saintfish/chardet/issues/2
		result.Charset = "GB18030"
	}
	stdlog.Info("导入文件的字符编码, 预测结果: ", result.Charset)
	var trsfer *transform.Reader
	switch {
	case result.Charset == "UTF-8": // with bom 需额外处理
		trsfer = transform.NewReader(file, unicode.UTF8BOM.NewDecoder())

	case strings.HasPrefix(result.Charset, "UTF-16"): // with bom 需额外处理
		enc, err := ianaindex.MIME.Encoding(result.Charset)
		if err != nil {
			stdlog.Errorln(err)
			return nil, "", errors.New("不支持的文件编码")
		}
		trsfer = transform.NewReader(file, unicode.BOMOverride(enc.NewDecoder()))

	case strings.HasPrefix(result.Charset, "UTF-32"): // iana 中不存在
		var enc encoding.Encoding
		switch strings.TrimPrefix(result.Charset, "UTF-32") {
		case "BE":
			enc = utf32.UTF32(utf32.BigEndian, utf32.UseBOM)
		case "LE":
			enc = utf32.UTF32(utf32.LittleEndian, utf32.UseBOM)
		default:
			return nil, "未知的文件编码", nil
		}
		trsfer = transform.NewReader(file, enc.NewDecoder())

	default: // 其他类型通过 ianaindex 获取默认 encoding
		enc, err := ianaindex.MIME.Encoding(result.Charset)
		if err != nil {
			stdlog.Errorln(err)
			return nil, "", errors.New("不支持的文件编码")
		}
		if enc == nil {
			return nil, "", errors.New("未知的文件编码")
		}
		trsfer = transform.NewReader(file, enc.NewDecoder())
	}

	reader := csv.NewReader(trsfer)
	_, _ = reader.Read() // 跳过字段提醒
	reader.FieldsPerRecord = len(TitleRow)
	title, err := reader.Read() // 校验表头
	if err != nil {
		stdlog.Errorf("读取 CSV 文件表头: %v", err)
		return nil, "", err
	}
	for i := range title {
		title[i] = strings.TrimSpace(title[i])
	}
	if !slices.Equal(TitleRow, title) && !slices.Equal(TitleRowEn, title) {
		stdlog.Error("表头校验失败: ", title)
		return nil, "上传文件的表头字段与模板配置不一致，请修改后重新上传", nil
	}

	rows := make([]*CsvUserModel, 0, 10)
	// 逐行读取记录
	for {
		row, err := reader.Read()
		if err != nil {
			if errors.Is(err, io.EOF) {
				break // 读取完毕
			}
			stdlog.Errorf("读取 CSV 文件时发生错误: %v", err)
			return nil, "", err
		}
		// 处理当前行数据
		stdlog.Infof("当前行: %v", row)
		ips := make([]string, 0, 1)
		for _, v := range strings.Split(row[8], ";") { // XXX 前端模板中使用了 ; 作为分隔
			if strings.TrimSpace(v) == "" {
				continue
			}
			ips = append(ips, strings.TrimSpace(v))
		}
		rows = append(rows, &CsvUserModel{
			UserName:           strings.TrimSpace(row[0]),
			Password:           strings.TrimSpace(row[1]),
			ConfirmPassword:    strings.TrimSpace(row[2]),
			Name:               strings.TrimSpace(row[3]),
			Email:              strings.TrimSpace(row[4]),
			Phone:              strings.TrimSpace(row[5]),
			PlatformRoleName:   strings.TrimSpace(row[6]),
			DefaultProjectName: strings.TrimSpace(row[7]),
			WhiteIPs:           ips,
		})
	}
	return rows, "", nil
}

// ValidateCsv 校验文件内容，返回文字信息或者 error 报错信息
func (s *UserImportService) ValidateCsv(path string) (string, error) {
	rows, msg, err := s.readCsvFile(path)
	if err != nil || msg != "" {
		return msg, err
	}
	// 获取默认空间列表
	pros, err := project.NewProjectService(s.db).QueryList(nil)
	if err != nil {
		return "", err
	}
	prosNameMap := make(map[string]struct{}, len(pros))
	for _, pro := range pros {
		prosNameMap[pro.Name] = struct{}{}
	}

	var (
		username, passwd, cfmPasswd, name, email, phone, plt, defPro string

		usernameMap = make(map[string]struct{}, 10) // 用于判断 csv 内是否有重复的 username
		errs        = make([]string, 0)
		rownum      = 0 // 行号

	)
	// 忽略字段提示和表头的行号
	rownum += 2
	// 逐行读取记录
	for _, row := range rows {
		rownum++

		// 处理当前行数据
		stdlog.Infof("当前行: %d: %v", rownum, row)
		username, passwd, cfmPasswd, name, email, phone, plt, defPro = row.UserName, row.Password, row.ConfirmPassword, row.Name, row.Email, row.Phone, row.PlatformRoleName, row.DefaultProjectName
		// 用户名 必填；长度不超过56个字符；仅支持英文、数字、短划线、下划线和小数点，必须以英文或数字开头和结尾。
		if username == "" || !regUserName.MatchString(username) {
			errs = append(errs, fmt.Sprintf("第 %d 行: 用户名格式错误", rownum))
			continue
		}
		// 用户名是否已在系统内存在
		u := NewUserService(s.db).GetUserByName(username)
		if u != nil && u.Name != "" {
			errs = append(errs, fmt.Sprintf("第 %d 行: 用户名已存在", rownum))
			continue
		}
		// 用户名是否已在 csv 内存在
		if _, ok := usernameMap[username]; ok {
			errs = append(errs, fmt.Sprintf("第 %d 行: 用户名与文件内其他用户重复", rownum))
			continue
		} else {
			usernameMap[username] = struct{}{}
		}
		// 密码&确认密码 必填；长度及格式无限制，请确保两次输入的密码保持一致。
		if passwd == "" || cfmPasswd == "" || passwd != cfmPasswd {
			errs = append(errs, fmt.Sprintf("第 %d 行: 密码/确认密码格式错误", rownum))
			continue
		}
		// 全名 选填，可为空；长度不超过40个字符，格式无限制。
		if len(name) > 40 {
			errs = append(errs, fmt.Sprintf("第 %d 行: 长度超过40个字符", rownum))
			continue
		}
		// 邮箱 必填；请确保邮箱格式输入正确。
		if email == "" || valida.Var(email, "email") != nil {
			errs = append(errs, fmt.Sprintf("第 %d 行: 邮箱格式错误", rownum))
			continue
		}
		// 手机 选填: 只支持数字,-,+
		if phone != "" && !regPhone.MatchString(phone) {
			errs = append(errs, fmt.Sprintf("第 %d 行: 手机格式错误", rownum))
			continue
		}
		// 平台角色 必填；枚举值，仅支持填写“普通用户”、“管理员”或“超级管理员”，其他任何输入都将无法通过校验。
		if plt == "" || !slices.Contains(append(PlatformRoleValues, PlatformRoleValuesEn...), plt) {
			errs = append(errs, fmt.Sprintf("第 %d 行: 平台角色格式错误", rownum))
			continue
		}
		// 默认空间 必填；请严格匹配空间列表中已存在的空间名称，否则将无法通过校验。
		if _, ok := prosNameMap[defPro]; defPro == "" || !ok {
			errs = append(errs, fmt.Sprintf("第 %d 行: 默认空间格式错误", rownum))
			continue
		}
		// IP白名单 选填
		for i, v := range row.WhiteIPs {
			ip := net.ParseIP(v)
			if ip == nil {
				errs = append(errs, fmt.Sprintf("第 %d 行, 第 %d 个IP: 格式错误", rownum, i+1))
				continue
			}
			if ip.IsLoopback() {
				errs = append(errs, fmt.Sprintf("第 %d 行, 第 %d 个IP: 不能是环回地址", rownum, i+1))
				continue
			}
		}
	}
	if len(errs) > 0 {
		stdlog.Warn(errs)
		return "存在不满足字段校验条件的数据，请修改后重新上传。\n对应行数为:\n" + strings.Join(errs, "\n"), nil
	}
	return "", nil
}

func (s *UserImportService) ImportCsv(username string, path string) error {
	rows, _, err := s.readCsvFile(path)
	if err != nil {
		return err
	}

	roleMap := map[string]uint64{
		string(models.GeneralUser):        1001,
		string(models.Administrator):      1002,
		string(models.SuperAdministrator): 1003,
	}
	return s.db.Transaction(func(tx *gorm.DB) error {
		us := NewUserService(tx)
		for _, row := range rows {
			exists, err := dao.GetUserByName(tx, row.UserName)
			if err != nil {
				return err
			}
			if exists != nil && exists.ID != 0 {
				return fmt.Errorf("用户%s已存在", row.UserName)
			}

			pj := models.Project{}
			err = tx.Session(&gorm.Session{NewDB: true}).Where("name", row.DefaultProjectName).Find(&pj).Error
			if err != nil {
				return err
			}
			if pj.Id == 0 {
				return fmt.Errorf("项目名称%s不存在", row.DefaultProjectName)
			}

			user := &models.UserReq{
				UserName:             row.UserName,
				FullName:             row.Name,
				Email:                row.Email,
				Password:             row.Password,
				UserGroupNames:       []string{"all_users"},
				PlatformRoleId:       roleMap[row.PlatformRoleName],
				DefaultProject:       pj.ProjectId,
				ExpirationTimeSelect: models.Nolimit,
				WhiteIps:             row.WhiteIPs,
				PhoneNumber:          row.Phone,
			}
			err = us.CreateUser(username, user, nil)
			if err != nil {
				return err
			}
		}
		return nil
	})
}
