package rbac

import (
	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/dao/rbac"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type RoleService struct {
	db *gorm.DB
}

func NewRoleService(db *gorm.DB) *RoleService {
	rs := &RoleService{db: db.Session(&gorm.Session{NewDB: true})}
	return rs
}

func (s *RoleService) ListRoles(roleType string) ([]*models.RoleResp, error) {
	roles, err := rbac.ListRoles(s.db, roleType)
	if err != nil {
		return nil, err
	}
	var (
		roleIds     = make([]uint64, 0, len(roles))
		roleRespArr = make([]*models.RoleResp, 0, len(roles))
	)

	for _, role := range roles {
		roleIds = append(roleIds, role.Id)
	}

	// 获取每个角色的权限点列表
	rolePermissionsMap, err := GetPermissionsMapByRoleIDs(s.db, roleIds)

	for _, role := range roles {
		roleRespArr = append(roleRespArr, &models.RoleResp{
			Role:        *role,
			Permissions: rolePermissionsMap[role.Id],
		})
	}
	return roleRespArr, nil
}

func (s *RoleService) GetRolesByNameAndType(name string, roleType models.RoleType) (*models.Role, error) {
	role, err := rbac.GetRoleByNameAndType(s.db, name, roleType)
	if err != nil {
		return nil, err
	}
	return role, nil
}

func (s *RoleService) CreateRole(roleReq *models.RoleReq) error {
	role := new(models.Role)
	role.Name = roleReq.Name
	role.Type = models.ProjectRoleType
	role.Description = roleReq.Description
	role.CreateUser = roleReq.Creator
	if err := rbac.CreateRole(s.db, role); err != nil {
		return err
	}
	roleID := role.Id
	err := rbac.SavePermissionsByRoleID(s.db, roleID, &roleReq.Permissions)
	if err != nil {
		return err
	}
	return nil
}

func (s *RoleService) UpdateRole(roleID uint64, roleReq *models.RoleReq) error {
	role := new(models.Role)
	role.Id = roleID
	role.Name = roleReq.Name
	role.Description = roleReq.Description
	if err := rbac.UpdateRole(s.db, role); err != nil {
		return err
	}
	// 管理员的空间管理权限要在数据库中持久化, 防止更新权限时丢失
	if roleID == uint64(helper.AdminUserID) {
		acts := make([]string, 0, 3)
		for _, p := range roleReq.Permissions {
			if p.Code == "project.manage.*" {
				acts = append(acts, p.Action)
			}
		}
		err := dao.UpdateProjectPermission(dao.GetCustomConfigService(), acts)
		if err != nil {
			return err
		}
	}
	err := rbac.SavePermissionsByRoleID(s.db, roleID, &roleReq.Permissions)
	if err != nil {
		return err
	}
	return nil
}

func (s *RoleService) GetRoleByRoleId(roleID uint64) (*models.Role, error) {
	role, err := rbac.GetRoleByRoleId(s.db, roleID)
	if err != nil {
		return nil, err
	}
	return role, nil
}

func (s *RoleService) GetRolesByRoleIds(roleIDs []uint64) ([]*models.Role, error) {
	roles, err := rbac.GetRolesByRoleIds(s.db, roleIDs)
	if err != nil {
		return nil, err
	}
	return roles, nil
}

func (s *RoleService) DeleteRole(roleID uint64) error {
	if err := rbac.DeleteRoleById(s.db, roleID); err != nil {
		return err
	}
	if err := rbac.DeleteRolePermissionsByRoleId(s.db, roleID); err != nil {
		return err
	}
	if err := rbac.DeleteUserRolesByRoleId(s.db, roleID); err != nil {
		return err
	}
	return nil
}

func (s *RoleService) BatchDeleteRolesByRoleIds(roleIds []uint64) error {
	if err := rbac.BatchDeleteRolesByIds(s.db, roleIds); err != nil {
		return err
	}
	if err := rbac.BatchDeleteRolePermissionsByRoleIds(s.db, roleIds); err != nil {
		return err
	}
	if err := rbac.BatchDeleteUserRolesByRoleIds(s.db, roleIds); err != nil {
		return err
	}
	return nil
}
