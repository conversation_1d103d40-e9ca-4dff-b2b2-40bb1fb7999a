package rbac

import (
	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/dao/rbac"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type PermissionService struct {
	db *gorm.DB
}

func NewPermissionService(db *gorm.DB) *PermissionService {
	ps := &PermissionService{db: db.Session(&gorm.Session{NewDB: true})}
	return ps
}

func (s *PermissionService) ListPermissions(permissionType, locale string) ([]*models.PermissionTreeNode, error) {
	permissions, err := rbac.ListPermissions(s.db, permissionType)
	if err != nil {
		return nil, err
	}
	permissionTreeNodes := make([]*models.PermissionTreeNode, 0)
	codeActionNodeMap := map[string]*models.PermissionTreeNode{}

	// 提前建立 map 与权限点顺序解耦
	for _, p := range permissions {
		if name, ok := p.NameLocals[locale]; ok && name != "" {
			p.Name = name
		}
		codeActionNodeMap[rbac.FormatCodeAction(p.Code, p.Action)] = &models.PermissionTreeNode{
			ID:     p.ID,
			Code:   p.Code,
			Name:   p.Name,
			Action: p.Action,
		}
	}
	for _, p := range permissions {
		pNode := codeActionNodeMap[rbac.FormatCodeAction(p.Code, p.Action)]
		if p.Parent == "" {
			permissionTreeNodes = append(permissionTreeNodes, pNode)
			continue
		}

		parent := codeActionNodeMap[rbac.FormatCodeAction(p.Parent, models.Access.String())]
		parent.Children = append(parent.Children, pNode)
	}

	return permissionTreeNodes, nil
}
