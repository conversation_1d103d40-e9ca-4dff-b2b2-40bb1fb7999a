package rbac

import (
	"fmt"
	"testing"

	conf2 "transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao"
)

func Test_checkUserSuperAdminRole(t *testing.T) {
	dbConfig := conf.DBConfig{
		Type:   "mysql",
		SQLite: nil,
		MySQL: &conf2.MysqlConfig{
			Username:       "root",
			Password:       "236512",
			Host:           "localhost",
			Port:           "3306",
			DBName:         "central_auth_service",
			MaxIdle:        10,
			MaxConn:        50,
			NotPrintSql:    false,
			NotCreateTable: false,
		},
		Debug: true,
	}
	db, err := dao.ConnectDB(&dbConfig)
	if err != nil {
		fmt.Println(err)
	}
	tests := []struct {
		name     string
		username string
		want     bool
		wantErr  bool
	}{
		{"超级管理员", "1", true, false},
		{"非超级管理员", "2", false, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := checkUserSuperAdminRole(tt.username, db)
			if (err != nil) != tt.wantErr {
				t.Errorf("checkUserSuperAdminRole() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("checkUserSuperAdminRole() = %v, want %v", got, tt.want)
			}
		})
	}
}
