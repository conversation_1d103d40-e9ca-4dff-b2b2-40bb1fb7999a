package rbac

import (
	"fmt"
	"os"
	"path/filepath"
	"testing"

	conf2 "transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao"
	_ "transwarp.io/applied-ai/central-auth-service/migrations"
)

func TestCreateTemplateFile(t *testing.T) {
	dbConfig := conf.DBConfig{
		Type:   "mysql",
		SQLite: nil,
		MySQL: &conf2.MysqlConfig{
			Username:       "root",
			Password:       "123456",
			Host:           "localhost",
			Port:           "3306",
			DBName:         "metastore_cas_rbac",
			MaxIdle:        10,
			MaxConn:        50,
			NotPrintSql:    false,
			NotCreateTable: false,
		},
		Debug: true,
	}
	db, err := dao.ConnectDB(&dbConfig)
	if err != nil {
		fmt.Println(err)
	}
	NewUserImportService(db).CreateTemplateFile()
}

func TestRegexpUserName(t *testing.T) {
	testStrings := []string{
		"Hello-World_123.45",
		"你好-世界_123.45",
		"123-Hello-World_",
		"-Hello-World_123",
		"123.123",
		"123-123",
		"123-你好-世界_123-你好-世界_123-你好-世界_123-你好-世界_123-你好-世界_123-你好-世界_123",
		"123-你好-世界_123-你好-世界_123-你好-世界_123-你好-世界_123-你好-世界_123-你好-世界_123-你好-世界_123",
	}

	for _, s := range testStrings {
		if regUserName.MatchString(s) {
			fmt.Printf("'%s' 符合要求\n", s)
		} else {
			fmt.Printf("'%s' 不符合要求\n", s)
		}
	}
}

func TestVaildcsv(t *testing.T) {
	dbConfig := conf.DBConfig{
		Type:   "mysql",
		SQLite: nil,
		MySQL: &conf2.MysqlConfig{
			Username:       "root",
			Password:       "123456",
			Host:           "localhost",
			Port:           "3306",
			DBName:         "metastore_cas_rbac",
			MaxIdle:        10,
			MaxConn:        50,
			NotPrintSql:    false,
			NotCreateTable: false,
		},
		Debug: true,
	}
	db, err := dao.ConnectDB(&dbConfig)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(NewUserImportService(db).ValidateCsv(filepath.Join(DirPrefix, "test.csv")))
}

func TestUserImportService_readCsvFile(t *testing.T) {
	dbConfig := conf.DBConfig{
		Type:   "mysql",
		SQLite: nil,
		MySQL: &conf2.MysqlConfig{
			Username:       "root",
			Password:       "123456",
			Host:           "localhost",
			Port:           "3306",
			DBName:         "metastore_cas_rbac",
			MaxIdle:        10,
			MaxConn:        50,
			NotPrintSql:    false,
			NotCreateTable: false,
		},
		Debug: true,
	}
	db, err := dao.ConnectDB(&dbConfig)
	if err != nil {
		fmt.Println(err)
	}
	res, _, err := NewUserImportService(db).readCsvFile(filepath.Join(DirPrefix, "test.csv"))
	if err != nil {
		t.Fatal(err)
		return
	}
	for _, v := range res {
		fmt.Printf("%#v\n", v)
	}
}

func TestUserImportService_readCsvFile_empty(t *testing.T) {
	dbConfig := conf.DBConfig{
		Type:   "mysql",
		SQLite: nil,
		MySQL: &conf2.MysqlConfig{
			Username:       "root",
			Password:       "123456",
			Host:           "localhost",
			Port:           "3306",
			DBName:         "metastore_cas_rbac",
			MaxIdle:        10,
			MaxConn:        50,
			NotPrintSql:    false,
			NotCreateTable: false,
		},
		Debug: true,
	}
	db, err := dao.ConnectDB(&dbConfig)
	if err != nil {
		fmt.Println(err)
	}
	f, err := os.Create(filepath.Join(DirPrefix, "empty.csv"))
	if err != nil {
		t.Fatal(err)
	}
	f.Close()
	res, info, err := NewUserImportService(db).readCsvFile(filepath.Join(DirPrefix, "empty.csv"))
	if err != nil {
		t.Fatal(err)
		return
	}
	if info != "" {
		t.Fatal(err)
		return
	}
	for _, v := range res {
		fmt.Printf("%#v\n", v)
	}
}

func TestUserImportService_ImportCsv(t *testing.T) {
	dbConfig := conf.DBConfig{
		Type: "mysql",
		MySQL: &conf2.MysqlConfig{
			Username:       "root",
			Password:       "123456",
			Host:           "localhost",
			Port:           "3306",
			DBName:         "metastore_cas_rbac",
			MaxIdle:        10,
			MaxConn:        50,
			NotPrintSql:    false,
			NotCreateTable: false,
		},
		Debug: true,
	}
	db, err := dao.ConnectDB(&dbConfig)
	if err != nil {
		t.Fatal(err)
	}
	err = NewUserImportService(db.Debug()).ImportCsv("thinger", filepath.Join(DirPrefix, "test.csv"))
	if err != nil {
		t.Fatal(err)
	}
}
