package rbac

import (
	"context"
	"encoding/json"
	"errors"
	"slices"
	"strconv"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdfs"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/central-auth-service/api/auth"
	guardianv2 "transwarp.io/applied-ai/central-auth-service/api/guardian/client"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/dao/project"
	"transwarp.io/applied-ai/central-auth-service/dao/rbac"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service"
	"transwarp.io/applied-ai/central-auth-service/utils"
)

type UserService struct {
	db *gorm.DB
}

func NewUserService(db *gorm.DB) *UserService {
	us := &UserService{db: db.Session(&gorm.Session{NewDB: true})}
	return us
}

func (s *UserService) GetUserProfile(username string, projectID string, gus *guardianv2.UsersApiService, lc stdsrv.Language) (*models.UserProfileResp, error) {
	userProfileResp := new(models.UserProfileResp)
	groupNames := make([]string, 0)
	if conf.C.UserStore.Type == models.Guardian.String() {
		ctx := context.Background()
		localVarReturnValue, localVarHttpResponse, err := gus.GetUserUsingGET1(ctx, username)
		if err != nil || localVarHttpResponse == nil || localVarHttpResponse.StatusCode >= 300 {
			return nil, err
		}
		groupNames = localVarReturnValue.Groups
		userProfileResp.UserName = localVarReturnValue.UserName
		userProfileResp.FullName = localVarReturnValue.FullName
		userProfileResp.Uid = localVarReturnValue.UidNumber
		userProfileResp.Email = localVarReturnValue.UserEmail
		userProfileResp.UserGroupNames = localVarReturnValue.Groups
		userProfileResp.CreateUser = "admin"
		userProfileResp.CreateTime = time.Now()
	} else {
		user, err := dao.GetUserByName(s.db, username)
		if err != nil {
			return nil, err
		}
		userGroups, err := rbac.BatchGetUserGroupsByUserNames(s.db, []string{username})
		if err != nil {
			return nil, err
		}
		for _, userGroup := range userGroups {
			groupNames = append(groupNames, userGroup.GroupName)
		}
		userProfileResp.Uid = int64(user.ID)
		userProfileResp.UserName = user.Name
		userProfileResp.FullName = user.FullName
		userProfileResp.Email = user.Email
		userProfileResp.UserGroupNames = groupNames
		userProfileResp.CreateUser = user.CreateUser
		userProfileResp.CreateTime = user.CreatedAt
		userProfileResp.DefaultProject = user.DefaultProject
	}
	// 根据用户名获取角色信息
	userRoles, err := rbac.GetUserRoles(s.db, []string{username}, models.UserType, projectID)
	if err != nil {
		return nil, err
	}
	// 根据用户组名称获取角色信息
	groupRoles, err := rbac.GetUserRoles(s.db, groupNames, models.UserGroupType, projectID)
	if err != nil {
		return nil, err
	}
	userRoles = append(userRoles, groupRoles...)
	roleIds := make([]uint64, 0)
	// 获取平台角色id
	projectRoleIds := []uint64{}
	for _, userRole := range userRoles {
		roleIds = append(roleIds, userRole.RoleId)
		if userRole.ProjectId == "" && userRole.BindType == models.UserType {
			// 用户平台角色
			userProfileResp.PlatformRoleId = userRole.RoleId
		} else if userRole.ProjectId != "" {
			// 空间角色
			projectRoleIds = append(projectRoleIds, userRole.RoleId)
		}
	}

	// 查看空间权限且当前用户平台角色是超级管理员时，默认可以操作空间下所有权限
	// 来自用户组的超管角色也具有同等权限
	if projectID != "" && slices.Contains(roleIds, uint64(helper.SuperAdminUserID)) {
		roleIds = append(roleIds, uint64(helper.ProjectOwnerID))
	}
	if projectID != "" {
		proj, err := project.GetProjectById(s.db, projectID)
		if err != nil {
			return nil, err
		}
		pl, err := stdfs.NewProjectLocation(proj.TenantUid, proj.ProjectId, false)
		if err != nil {
			return nil, err
		}
		userProfileResp.ProjectLocation = string(pl)
	}

	permissionRespArr, err := GetUserPermissionsByRoleIDs(s.db, roleIds, lc)
	if err != nil {
		return nil, err
	}
	// 获取平台角色名称
	roles, err := rbac.GetRolesByRoleIds(s.db, []uint64{userProfileResp.PlatformRoleId})
	if err != nil {
		return nil, err
	}
	if len(roles) > 0 {
		userProfileResp.PlatformRoleName = roles[0].GetNameWithLocal(lc)
	}
	// 获取空间角色名称
	projectRoleNames := []string{}
	userProfileResp.ProjectRoleIds = []uint64{}
	projectRoles, err := rbac.GetRolesByRoleIds(s.db, projectRoleIds)
	for _, role := range projectRoles {
		projectRoleNames = append(projectRoleNames, role.GetNameWithLocal(lc))
		userProfileResp.ProjectRoleIds = append(userProfileResp.ProjectRoleIds, role.Id)
	}
	userProfileResp.ProjectRoleNames = projectRoleNames
	userProfileResp.UserGroupNames = groupNames
	userProfileResp.Permissions = permissionRespArr
	return userProfileResp, err
}

func (s *UserService) GetUserRoles(names []string, bindType models.BindType) (map[string]string, map[string]uint64, error) {
	userRoles, err := rbac.GetUserRoles(s.db, names, bindType, "")
	if err != nil {
		return nil, nil, err
	}
	roleIds := make([]uint64, 0)
	nameRoleIdMap := make(map[string]uint64)
	for _, userRole := range userRoles {
		roleIds = append(roleIds, userRole.RoleId)
		nameRoleIdMap[userRole.Name] = userRole.RoleId
	}
	roles, err := rbac.GetRolesByRoleIds(s.db, roleIds)
	if err != nil {
		return nil, nil, err
	}
	roleIdRoleNameMap := make(map[uint64]string)
	for _, role := range roles {
		roleIdRoleNameMap[role.Id] = role.Name
	}

	usernameRoleNameMap := make(map[string]string)
	for name, roleId := range nameRoleIdMap {
		usernameRoleNameMap[name] = roleIdRoleNameMap[roleId]
	}
	return usernameRoleNameMap, nameRoleIdMap, err
}

func (s *UserService) GetUserGroups(usernames []string) (map[string][]string, error) {
	userGroups, err := rbac.BatchGetUserGroupsByUserNames(s.db, usernames)
	if err != nil {
		return nil, err
	}
	usernameGroupNamesMap := make(map[string][]string)
	for _, userGroup := range userGroups {
		if usernameGroupNamesMap[userGroup.Username] == nil {
			groupNames := make([]string, 0)
			groupNames = append(groupNames, userGroup.GroupName)
			usernameGroupNamesMap[userGroup.Username] = groupNames
		} else {
			usernameGroupNamesMap[userGroup.Username] = append(usernameGroupNamesMap[userGroup.Username], userGroup.GroupName)
		}
	}
	return usernameGroupNamesMap, nil
}

func (s *UserService) ListUsers(gus *guardianv2.UsersApiService, sus *service.UserService, projectid string) ([]*models.UserResp, error) {
	if conf.C.UserStore.Type == models.Guardian.String() {
		return s.ListUsersFromGuardian(gus, true)
	}
	users, err := sus.ListUsers()
	if err != nil {
		return nil, err
	}
	return s.ConvertUsers(users, true, projectid)
}

func (s *UserService) ListUsersFromGuardian(gus *guardianv2.UsersApiService, withRole bool) ([]*models.UserResp, error) {
	userRespArr := make([]*models.UserResp, 0)
	if conf.C.UserStore.Type != models.Guardian.String() {
		return nil, stderr.InvalidParam.Errorf("user store type is not guardian")
	}
	ctx := context.Background()
	localVarReturnValue, localVarHttpResponse, err := gus.GetUsersUsingGET(ctx, nil)
	if err != nil || localVarHttpResponse == nil || localVarHttpResponse.StatusCode >= 300 {
		return nil, err
	}
	users := localVarReturnValue.([]interface{})
	for _, user := range users {
		userResp := new(models.UserResp)
		userVo := new(guardianv2.UserVo)
		bytes, _ := json.Marshal(user)
		err = json.Unmarshal(bytes, &userVo)
		userResp.UserName = userVo.UserName
		userResp.FullName = userVo.FullName
		userResp.Uid = uint64(userVo.UidNumber)
		userResp.Email = userVo.UserEmail
		userResp.UserGroupNames = userVo.Groups
		userResp.CreateTime = time.Now()
		userResp.CreateUser = "admin"
		userRespArr = append(userRespArr, userResp)
	}
	if withRole {
		if err = s.FillUserRoles("", userRespArr); err != nil {
			return nil, stderr.Wrap(err, "fill users' roles")
		}
	}
	return userRespArr, nil
}

func (s *UserService) FillUserRoles(projID string, userRespArr []*models.UserResp) error {
	usernameArr := make([]string, len(userRespArr))
	for i, userResp := range userRespArr {
		usernameArr[i] = userResp.UserName
	}
	usernameRoleNameMap, usernameRoleIdMap, err := s.GetUserRoles(usernameArr, models.UserType)
	if err != nil {
		return err
	}
	usernameGroupNamesMap, err := s.GetUserGroups(usernameArr)
	if err != nil {
		return err
	}
	userProRoleMap := make(map[string][]uint64)
	if projID != "" {
		userProRoles, err := rbac.GetUserRoles(s.db, usernameArr, models.UserType, projID)
		if err != nil {
			return err
		}
		for _, ur := range userProRoles {
			userProRoleMap[ur.Name] = append(userProRoleMap[ur.Name], ur.RoleId)
		}
	}

	// 用户在不同空间下具备的所有角色(继承用户组)
	userProsMap, err := s.getUserProjects(usernameGroupNamesMap)
	if err != nil {
		return err
	}

	for _, userResp := range userRespArr {
		userResp.PlatformRoleName = usernameRoleNameMap[userResp.UserName]
		userResp.PlatformRoleId = usernameRoleIdMap[userResp.UserName]
		userResp.ProjectRoleIds = userProRoleMap[userResp.UserName]
		userResp.Projects = userProsMap[userResp.UserName]
		if usernameGroupNamesMap[userResp.UserName] == nil {
			userResp.UserGroupNames = make([]string, 0)
		} else {
			userResp.UserGroupNames = usernameGroupNamesMap[userResp.UserName]
		}
	}
	return nil
}

// fillUserProjects 填充用户在不同空间下的角色信息(继承用户组角色)
//
//	@param userGroupsMap map[username][]groupnames
func (s *UserService) getUserProjects(userGroupsMap map[string][]string) (map[string][]*models.UserProject, error) {
	roles, err := rbac.ListRoles(s.db, "")
	if err != nil {
		return nil, err
	}
	projects, err := project.ListProjects(s.db, "")
	if err != nil {
		return nil, err
	}
	roleIdNameMap := lo.SliceToMap(roles, func(e *models.Role) (uint64, string) { return e.Id, e.Name })
	proIdNameMap := lo.SliceToMap(projects, func(e *models.Project) (string, string) { return e.ProjectId, e.Name })
	// 用户的空间角色, 继承来自用户组的
	userRoles := make([]*models.UserRole, 0)
	if err = s.db.Where("project_id != ''").Find(&userRoles).Error; err != nil {
		return nil, err
	}
	userProsMap := make(map[string][]*models.UserProject) // map[username][]
	// 用户角色
	ursMap := lo.GroupBy(lo.Filter(userRoles, func(e *models.UserRole, _ int) bool { return e.BindType == models.UserType }),
		func(ur *models.UserRole) string {
			return ur.Name
		})
	// 用户组角色
	ugrsMap := lo.GroupBy(lo.Filter(userRoles, func(e *models.UserRole, _ int) bool { return e.BindType == models.UserGroupType }),
		func(ur *models.UserRole) string {
			return ur.Name
		})

	for username, groups := range userGroupsMap {
		urs, ok := ursMap[username]
		if !ok {
			continue
		}
		for _, g := range groups {
			ugrs, ok := ugrsMap[g]
			if !ok {
				continue
			}
			urs = append(urs, ugrs...)
		}

		for _, ur := range urs {
			up, ok := lo.Find(userProsMap[username], func(e *models.UserProject) bool {
				return e.Id == ur.ProjectId
			})
			if !ok {
				up = &models.UserProject{
					Id:   ur.ProjectId,
					Name: proIdNameMap[ur.ProjectId],
					Roles: []*models.UserProjectRole{{
						RoleId:   ur.RoleId,
						RoleName: roleIdNameMap[ur.RoleId],
						BindType: ur.BindType,
						GroupName: func() string {
							if ur.BindType == models.UserGroupType {
								return ur.Name
							}
							return ""
						}(),
					}},
				}
				userProsMap[username] = append(userProsMap[username], up)
			} else {
				up.Roles = append(up.Roles, &models.UserProjectRole{
					RoleId:   ur.RoleId,
					RoleName: roleIdNameMap[ur.RoleId],
					BindType: ur.BindType,
					GroupName: func() string {
						if ur.BindType == models.UserGroupType {
							return ur.Name
						}
						return ""
					}(),
				})
			}
		}
	}
	return userProsMap, nil
}

func (s *UserService) ConvertUsers(users []*dao.User, withRole bool, projID string) ([]*models.UserResp, error) {
	var (
		userRespArr    = make([]*models.UserResp, 0)
		usernameArr    = make([]string, 0)
		disableUserIds = make([]uint, 0)
		now            = time.Now()
	)
	for _, user := range users {
		userResp := &models.UserResp{
			Uid:                  uint64(user.ID),
			UserName:             user.Name,
			FullName:             user.FullName,
			Email:                user.Email,
			CreateTime:           user.CreatedAt,
			CreateUser:           user.CreateUser,
			DefaultProject:       user.DefaultProject,
			Status:               user.Status,
			ExpirationTimeSelect: user.ExpirationTimeSelect,
			ExpirationTime:       user.ExpirationTime,
			WhiteIps:             user.WhiteIps,
		}
		if userResp.ExpirationTimeSelect == models.Nolimit {
			userResp.ExpirationTime = 0
		} else if userResp.Status == models.Enable && time.Unix(user.ExpirationTime, 0).Before(now) { // 不是无限制 && 已到过期时间
			// 先判断状态是否不一致
			userResp.Status = models.Disable
			disableUserIds = append(disableUserIds, user.ID)
		}
		userRespArr = append(userRespArr, userResp)
		usernameArr = append(usernameArr, user.Name)
	}
	err := s.db.Session(&gorm.Session{NewDB: true}).Model(&dao.User{}).Where("id in (?)", disableUserIds).Update("status", models.Disable).Error
	if err != nil {
		return nil, stderr.Wrap(err, "update disabled users status")
	}
	if !withRole {
		return userRespArr, nil
	}
	if err = s.FillUserRoles(projID, userRespArr); err != nil {
		return nil, stderr.Wrap(err, "FillUserRoles")
	}
	return userRespArr, nil
}

func (s *UserService) CreateUser(creator string, userReq *models.UserReq, gus *guardianv2.UsersApiService) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		if conf.C.UserStore.Type == models.Guardian.String() {
			userVo := guardianv2.UserVo{
				UserEmail:    userReq.Email,
				UserName:     userReq.UserName,
				UserPassword: userReq.Password,
				FullName:     userReq.FullName,
				Groups:       userReq.UserGroupNames,
			}
			ctx := context.Background()
			_, localVarHttpResponse, err := gus.AddUserUsingPOST(ctx, userVo)
			if err != nil || localVarHttpResponse == nil || localVarHttpResponse.StatusCode >= 300 {
				return err
			}
		} else {
			if userReq.PlatformRoleId == uint64(helper.SuperAdminUserID) {
				hasRole, err := checkUserSuperAdminRole(creator, tx)
				if err != nil {
					return err
				}
				if !hasRole {
					return errors.New("当前用户没有权限创建超级管理员用户")
				}
			}
			// 创建用户
			user := &dao.User{
				Name:                 userReq.UserName,
				FullName:             userReq.FullName,
				Email:                userReq.Email,
				CreateUser:           creator,
				DefaultProject:       userReq.DefaultProject,
				Status:               userReq.Status,
				ExpirationTimeSelect: userReq.ExpirationTimeSelect,
				ExpirationTime:       userReq.ExpirationTime,
				WhiteIps:             userReq.WhiteIps,
			}
			if err := dao.CreateUser(tx, user, userReq.Password); err != nil {
				return err
			}
		}

		// 删除重复的用户组名称
		groupNames := utils.RemoveDupElements(userReq.UserGroupNames)
		// 将用户-用户组关系进行绑定
		for _, groupName := range groupNames {
			userGroup := new(models.UserGroup)
			userGroup.Username = userReq.UserName
			userGroup.GroupName = groupName
			userGroup.CreateTime = time.Now()
			if err := rbac.CreateUserGroup(tx, userGroup); err != nil {
				return err
			}
		}
		// 将用户-角色信息进行绑定
		platformRoleId := userReq.PlatformRoleId
		userRole := new(models.UserRole)
		userRole.Name = userReq.UserName
		userRole.RoleId = platformRoleId
		userRole.BindType = models.UserType
		userRole.CreateTime = time.Now()
		userRole.UpdateTime = time.Now()
		if err := rbac.CreateUserRole(tx, userRole); err != nil {
			return err
		}

		projectMember := new(models.ProjectMember)
		projectMember.ProjectId = userReq.DefaultProject
		projectMember.Name = userReq.UserName
		projectMember.UserType = models.UserType
		projectMember.CreateUser = creator
		projectMember.CreateTime = time.Now()
		projectMember.UpdateTime = time.Now()
		if err := project.CreateProjectMember(tx, projectMember); err != nil {
			return err
		}

		proUserRole := new(models.UserRole)
		proUserRole.Name = userReq.UserName
		proUserRole.RoleId = uint64(helper.CustomUserID)
		proUserRole.BindType = models.UserType
		proUserRole.ProjectId = userReq.DefaultProject
		proUserRole.UpdateTime = time.Now()
		proUserRole.CreateTime = time.Now()
		if err := rbac.CreateUserRole(tx, proUserRole); err != nil {
			return err
		}
		return nil
	})
}

func (s *UserService) UpdateUser(createUser, oldName string, userReq *models.UserReq, gus *guardianv2.UsersApiService) error {
	now := time.Now()
	return s.db.Transaction(func(tx *gorm.DB) error {
		if conf.C.UserStore.Type == models.Guardian.String() {
			userVo := guardianv2.UserVo{
				UserEmail:    userReq.Email,
				UserName:     userReq.UserName,
				UserPassword: userReq.Password,
				FullName:     userReq.FullName,
				Groups:       userReq.UserGroupNames,
			}
			ctx := context.Background()
			_, localVarHttpResponse, err := gus.UpdateUserUsingPUT1(ctx, userVo, oldName)
			if err != nil || localVarHttpResponse == nil || localVarHttpResponse.StatusCode >= 300 {
				return err
			}
		} else {
			if userReq.PlatformRoleId == uint64(helper.SuperAdminUserID) {
				hasRole, err := checkUserSuperAdminRole(createUser, tx)
				if err != nil {
					return err
				}
				if !hasRole {
					return errors.New("当前用户没有权限编辑超级管理员用户")
				}
			}
			// 更新用户
			user := &dao.User{
				Model:                gorm.Model{ID: uint(userReq.Uid)},
				Name:                 userReq.UserName,
				FullName:             userReq.FullName,
				Email:                userReq.Email,
				DefaultProject:       userReq.DefaultProject,
				Status:               userReq.Status,
				ExpirationTimeSelect: userReq.ExpirationTimeSelect,
				ExpirationTime:       userReq.ExpirationTime,
				WhiteIps:             userReq.WhiteIps,
			}
			// 如果从禁用到启用, 则重新计算有效期
			get := &dao.User{Model: gorm.Model{ID: user.ID}}
			err := tx.Clauses(clause.Locking{Strength: clause.LockingStrengthUpdate}).Take(get).Error
			if err != nil {
				return stderr.Errorf("获取用户信息失败: %v", err)
			}
			if userReq.Status == models.Enable && get.Status == models.Disable {
				if userReq.ExpirationTimeSelect == "" { // 如果本次不修改有效期, 则使用之前的有效期, 否则使用本次修改的
					if slices.Contains([]models.ExpirationTimeSelect{models.Day1, models.Day7, models.Day15, models.Day30}, get.ExpirationTimeSelect) {
						d, _ := strconv.Atoi(string(get.ExpirationTimeSelect))
						user.ExpirationTime = time.Now().AddDate(0, 0, d).Unix()
					}
				}
			}
			if err = dao.UpdateUser(tx, user, userReq.Password); err != nil {
				return err
			}
			// 如果密码有变动, 则需要被修改的用户重新登录
			if get.Secret != user.GenSecret(userReq.Password) {
				go func(uname string) {
					err := auth.GetAuthHandler().ClearSessionByUsername(uname)
					if err != nil {
						stdlog.Errorf("被修改密码后强制登出, 删除 session: %s: %v", uname, err)
					}
					err = dao.DeleteTicketGrantingTicketByUsername(s.db.Session(&gorm.Session{NewDB: true}), uname)
					if err != nil {
						stdlog.Errorf("被修改密码后强制登出, 删除 tgt: %s: %v", uname, err)
					}
				}(user.Name)
			}
		}
		// 删除用户-用户组绑定关系
		if err := rbac.DeleteUserGroupsByUsername(tx, oldName); err != nil {
			return err
		}
		// 删除重复的用户组名称
		groupNames := utils.RemoveDupElements(userReq.UserGroupNames)
		// 将用户-用户组关系进行绑定
		for _, groupName := range groupNames {
			userGroup := new(models.UserGroup)
			userGroup.Username = userReq.UserName
			userGroup.GroupName = groupName
			userGroup.CreateTime = now
			if err := rbac.CreateUserGroup(tx, userGroup); err != nil {
				return err
			}
		}
		// 将用户-角色信息进行绑定
		userRole := &models.UserRole{
			UserRoleReq: models.UserRoleReq{
				RoleId:    userReq.PlatformRoleId,
				BindType:  models.UserType,
				ProjectId: "",
				Name:      userReq.UserName,
			},
		}
		if conf.C.UserStore.Type == models.Guardian.String() {
			userRole.Name = oldName
		}
		// 不存在则创建, 存在则更新
		err := tx.Model(userRole).Where(userRole, "BindType", "ProjectId").Where("name = ?", oldName).
			Assign(&models.UserRole{
				UserRoleReq: models.UserRoleReq{
					RoleId: userReq.PlatformRoleId,
					Name:   userReq.UserName,
				},
				CreateTime: now,
				UpdateTime: now,
			}).FirstOrCreate(userRole).Error
		if err != nil {
			return err
		}

		// 判断是否存在默认空间的成员中
		projectIds, err := project.GetProjectIdsByNameAndUserType(tx, []string{userReq.UserName}, models.UserType)
		if err != nil {
			return err
		}
		for _, projectId := range projectIds {
			if projectId == userReq.DefaultProject {
				return nil
			}
		}

		projectMember := new(models.ProjectMember)
		projectMember.ProjectId = userReq.DefaultProject
		projectMember.Name = userReq.UserName
		projectMember.UserType = models.UserType
		projectMember.CreateUser = createUser
		projectMember.CreateTime = now
		projectMember.UpdateTime = now
		if err := project.CreateProjectMember(tx, projectMember); err != nil {
			return err
		}

		proUserRole := new(models.UserRole)
		proUserRole.Name = userReq.UserName
		proUserRole.RoleId = uint64(helper.CustomUserID)
		proUserRole.BindType = models.UserType
		proUserRole.ProjectId = userReq.DefaultProject
		proUserRole.UpdateTime = now
		proUserRole.CreateTime = now
		if err := rbac.CreateUserRole(tx, proUserRole); err != nil {
			return err
		}
		return nil
	})
}

// UpdateUserPassword 仅支持当前用户修改自己的密码
func (s *UserService) UpdateUserPassword(username string, passwordReq *models.PasswordReq, gus *guardianv2.UsersApiService) error {
	if conf.C.UserStore.Type == models.Guardian.String() {
		userVo := guardianv2.UserVo{
			// FIXME 理论上应该要先校验，不过guardian逻辑先不做
			UserPassword: passwordReq.Password,
		}
		ctx := context.Background()
		_, localVarHttpResponse, err := gus.UpdateUserUsingPUT1(ctx, userVo, username)
		if err != nil || localVarHttpResponse == nil || localVarHttpResponse.StatusCode >= 300 {
			return err
		}
	} else {
		// 校验旧密码是否正确
		exist, err := dao.GetUserByName(s.db, username)
		if err != nil {
			return errors.New("用户不存在")
		}
		user := &dao.User{
			Model: gorm.Model{ID: exist.ID},
			Name:  username,
		}
		if exist.Secret != user.GenSecret(passwordReq.OldPassword) {
			return stderr.NotAllowed.Errorf("当前密码不正确")
		}
		if err := dao.UpdateUser(s.db, user, passwordReq.Password); err != nil {
			return stderr.Internal.Cause(err, "update user password error")
		}
	}
	return nil
}

func (s *UserService) GetUserByName(username string) *dao.User {
	user, _ := dao.GetUserByName(s.db, username)
	return user
}

func (s *UserService) GetUserById(userId string) *dao.User {
	user, _ := dao.GetUserByID(s.db, userId)
	return user
}

func (s *UserService) DeleteUserByUsername(username string, gus *guardianv2.UsersApiService) error {
	err := s.db.Transaction(func(tx *gorm.DB) error {
		var err error
		if err = rbac.DeleteUserRolesByName(tx, username, models.UserType.String()); err != nil {
			return err
		}
		if err = project.DeleteProjectMembersByNameAndUserType(tx, username, models.UserType.String()); err != nil {
			return err
		}
		if conf.C.UserStore.Type == models.Guardian.String() {
			ctx := context.Background()
			localVarHttpResponse, err := gus.DeleteUserUsingDELETE1(ctx, username)
			if err != nil || localVarHttpResponse == nil || localVarHttpResponse.StatusCode >= 300 {
				return err
			}
		} else {
			if err = dao.DeleteUserByName(tx, username); err != nil {
				return err
			}
			if err = rbac.DeleteUserGroupsByUsername(tx, username); err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	// 登出被删除的用户
	go func() {
		err := auth.GetAuthHandler().ClearSessionByUsername(username)
		if err != nil {
			stdlog.Errorf("被删除后强制登出, 删除 session: %s: %v", username, err)
		}
		err = dao.DeleteTicketGrantingTicketByUsername(s.db.Session(&gorm.Session{NewDB: true}), username)
		if err != nil {
			stdlog.Errorf("被删除后强制登出, 删除 tgt: %s: %v", username, err)
		}
	}()
	return nil
}

func (s *UserService) BatchDeleteUsersByUserIds(usernames []string, gus *guardianv2.UsersApiService) error {
	if conf.C.UserStore.Type == models.Guardian.String() {
		ctx := context.Background()
		for _, username := range usernames {
			localVarHttpResponse, err := gus.DeleteUserUsingDELETE1(ctx, username)
			if err != nil || localVarHttpResponse == nil || localVarHttpResponse.StatusCode >= 300 {
				return err
			}
		}
	} else {
		if err := dao.BatchDeleteUsersByNames(s.db, usernames); err != nil {
			return err
		}
	}
	if err := rbac.BatchDeleteUserRolesByNames(s.db, usernames, models.UserType.String()); err != nil {
		return err
	}
	if err := rbac.BatchDeleteUserGroupsByUsernames(s.db, usernames); err != nil {
		return err
	}
	if err := project.BatchDeleteProjectMembersByNamesAndUserType(s.db, usernames, models.UserType.String()); err != nil {
		return err
	}
	return nil
}

// checkUserSuperAdminRole 校验用户是否有超级管理员角色.
// 如果有 return true
func checkUserSuperAdminRole(username string, tx *gorm.DB) (bool, error) {
	scheduler, err := dao.GetUserByName(tx, username)
	if err != nil {
		return false, err
	}
	if scheduler == nil || scheduler.ID == 0 {
		return false, errors.New("scheduler not found")
	}
	schedulerRole := &models.UserRole{}
	err = tx.Session(&gorm.Session{NewDB: true}).Debug().Where("project_id is null or project_id = ''").
		Where("bind_type", models.UserType).
		Where("name", username).
		Find(schedulerRole).Error
	if err != nil {
		return false, err
	}
	if schedulerRole.RoleId != uint64(helper.SuperAdminUserID) {
		return false, nil
	}
	return true, nil
}

func (s *UserService) UpdateUserStatus(req *models.UserStatusReq) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		user := &dao.User{
			Model: gorm.Model{ID: uint(req.Id)},
		}
		err := tx.Take(user).Error
		if err != nil {
			return err
		}
		sels := make([]any, 0, 1)
		user.Status = req.Status
		// 如果要改为启用, 需重置过期时间
		if req.Status == models.Enable &&
			slices.Contains([]models.ExpirationTimeSelect{models.Day1, models.Day7, models.Day15, models.Day30}, user.ExpirationTimeSelect) {
			d, _ := strconv.Atoi(string(user.ExpirationTimeSelect))
			user.ExpirationTime = time.Now().AddDate(0, 0, d).Unix()
			sels = append(sels, "ExpirationTime")
		}
		return tx.Model(user).Select("Status", sels...).Updates(user).Error
	})
}

// UpdateUserStatusByExp 通过过期时间更新用户状态
func (s *UserService) UpdateUserStatusByExp(name, sessionid, ticket string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		user := &dao.User{Name: name}
		err := tx.Model(user).Clauses(clause.Locking{Strength: clause.LockingStrengthUpdate}).Where(user, "Name").Take(user).Error
		if err != nil {
			stdlog.Errorln(err)
			return err
		}
		// 禁用状态不 return, 列表接口会更新状态, 但不会清除用户的登录状态, 被禁用用户在请求接口时清除
		// if user.Status == models.UserStatusDisable {
		// 	return nil
		// }
		if user.ExpirationTime == 0 || time.Unix(user.ExpirationTime, 0).After(time.Now()) { // 未过期
			return nil
		}
		// 已过期 ↓
		stdlog.Debugf("用户: %s 已过期", name)
		if user.Status != models.Disable {
			err = tx.Model(user).Update("Status", models.Disable).Error
			if err != nil {
				return err
			}
		}
		go func() {
			err := auth.GetAuthHandler().GetSessionStore().Delete(sessionid)
			if err != nil {
				stdlog.WithError(err).Errorf("Delete user [%s] sessions error.", user.Name)
			}
			err = dao.DeleteTicketGrantingTicket(s.db, ticket)
			if err != nil {
				stdlog.WithError(err).Errorf("Delete user [%s] ticket error.", user.Name)
			}
		}()
		return nil
	})
}

// InWhiteIPs 是否可以通过 ip 白名单校验
//
//	@param ctx
//	@param uname
//	@param clientIP 客户 ip: ***********, 为空拦截
//	@return bool true: 通过校验
//	@return []string 已配置白名单的ip
func (s *UserService) InWhiteIPs(ctx context.Context, uname, clientIP string) (bool, []string, error) {
	user := &dao.User{Name: uname}
	err := s.db.Session(&gorm.Session{Context: ctx, NewDB: true}).Model(user).Where(user, "Name").Take(user).Error
	if err != nil {
		stdlog.Errorf("ip whitelist: username: %s, clientip: %s: %v", uname, clientIP, err)
		// 用户不存在时放行
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return true, []string{}, nil
		}
		return false, nil, err
	}
	return user.CheckIP(user.WhiteIps, clientIP), user.WhiteIps, nil
}
