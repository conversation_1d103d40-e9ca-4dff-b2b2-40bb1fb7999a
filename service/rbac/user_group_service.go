package rbac

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"time"

	"gorm.io/gorm"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	guardianv2 "transwarp.io/applied-ai/central-auth-service/api/guardian/client"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao/project"
	"transwarp.io/applied-ai/central-auth-service/dao/rbac"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/utils"
)

type UserGroupService struct {
	db *gorm.DB
}

func NewUserGroupService(db *gorm.DB) *UserGroupService {
	ugs := &UserGroupService{db: db.Session(&gorm.Session{NewDB: true})}
	return ugs
}

func (s *UserGroupService) ListUserGroups(gas *guardianv2.GroupsApiService, projectid string) ([]*models.GroupResp, error) {
	groupRespArr := make([]*models.GroupResp, 0)
	groupNames := make([]string, 0)

	if conf.C.UserStore.Type == models.Guardian.String() {
		ctx := context.Background()
		localVarReturnValue, localVarHttpResponse, err := gas.GetGroupsUsingGET(ctx, nil)
		if err != nil || localVarHttpResponse == nil || localVarHttpResponse.StatusCode >= 300 {
			return nil, err
		}
		fmt.Printf("%s", reflect.TypeOf(localVarReturnValue))
		groupMap := localVarReturnValue.(map[string]interface{})
		for _, value := range groupMap {
			fmt.Printf("%s", reflect.TypeOf(localVarReturnValue))
			groups := value.([]interface{})
			for _, group := range groups {
				groupVo := new(guardianv2.GroupVo)
				bytes, _ := json.Marshal(group)
				err = json.Unmarshal(bytes, &groupVo)
				groupResp := new(models.GroupResp)
				groupResp.Name = groupVo.GroupName
				groupResp.UserNames = groupVo.Users
				groupResp.Description = groupVo.GroupDescription
				groupResp.Gid = uint64(groupVo.GidNumber)
				groupResp.CreateTime = time.Now()
				groupResp.CreateUser = "admin"
				groupNames = append(groupNames, groupVo.GroupName)
				groupRespArr = append(groupRespArr, groupResp)
			}
		}
	} else {
		groups, err := rbac.ListGroups(s.db)
		if err != nil {
			return nil, err
		}
		for _, group := range groups {
			groupResp := new(models.GroupResp)
			groupResp.Gid = group.Id
			groupResp.Name = group.Name
			groupResp.Description = group.Description
			groupResp.CreateUser = group.CreateUser
			groupResp.CreateTime = group.CreateTime
			groupNames = append(groupNames, group.Name)
			groupRespArr = append(groupRespArr, groupResp)
		}
		userGroups, err := rbac.BatchGetUserGroupsByGroupNames(s.db, groupNames)
		if err != nil {
			return nil, err
		}
		groupNameUsernamesMap := make(map[string][]string)
		for _, userGroup := range userGroups {
			if groupNameUsernamesMap[userGroup.GroupName] == nil {
				usernames := make([]string, 0)
				usernames = append(usernames, userGroup.Username)
				groupNameUsernamesMap[userGroup.GroupName] = usernames
			} else {
				groupNameUsernamesMap[userGroup.GroupName] = append(groupNameUsernamesMap[userGroup.GroupName], userGroup.Username)
			}
		}
		for _, groupResp := range groupRespArr {
			if groupNameUsernamesMap[groupResp.Name] == nil {
				groupResp.UserNames = make([]string, 0)
			} else {
				groupResp.UserNames = groupNameUsernamesMap[groupResp.Name]
			}
		}
	}

	userRoles, err := rbac.GetUserRoles(s.db, groupNames, models.UserGroupType, "")
	if err != nil {
		return nil, err
	}
	groupNameRoleIdMap := make(map[string]uint64)
	roleIds := make([]uint64, 0)
	for _, userRole := range userRoles {
		groupNameRoleIdMap[userRole.Name] = userRole.RoleId
		roleIds = append(roleIds, userRole.RoleId)
	}
	roles, err := rbac.GetRolesByRoleIds(s.db, roleIds)
	if err != nil {
		return nil, err
	}
	roleIdRoleNameMap := make(map[uint64]string)
	for _, role := range roles {
		roleIdRoleNameMap[role.Id] = role.Name
	}
	groupNameRoleNameMap := make(map[string]string)
	for name, roleId := range groupNameRoleIdMap {
		groupNameRoleNameMap[name] = roleIdRoleNameMap[roleId]
	}
	groupProRoleMap := make(map[string][]uint64)
	if projectid != "" {
		proRoles, err := rbac.GetUserRoles(s.db, groupNames, models.UserGroupType, projectid)
		if err != nil {
			return nil, err
		}
		for _, ur := range proRoles {
			groupProRoleMap[ur.Name] = append(groupProRoleMap[ur.Name], ur.RoleId)
		}
	}

	for _, groupResp := range groupRespArr {
		groupResp.ProjectRoleIds = groupProRoleMap[groupResp.Name]
		groupResp.PlatformRoleName = groupNameRoleNameMap[groupResp.Name]
		groupResp.PlatformRoleId = groupNameRoleIdMap[groupResp.Name]
	}
	return groupRespArr, nil
}

func (s *UserGroupService) CreateUserGroup(groupReq *models.GroupReq, createUser string, gas *guardianv2.GroupsApiService) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		if conf.C.UserStore.Type == models.Guardian.String() {
			ctx := context.Background()
			groupVo := guardianv2.GroupVo{
				GroupName:        groupReq.Name,
				GroupDescription: groupReq.Description,
				Users:            groupReq.UserNames,
			}
			_, localVarHttpResponse, err := gas.AddGroupUsingPOST1(ctx, groupVo)
			if err != nil || localVarHttpResponse == nil || localVarHttpResponse.StatusCode >= 300 {
				return err
			}
		} else {
			if groupReq.PlatformRoleId == uint64(helper.SuperAdminUserID) {
				hasRole, err := checkUserSuperAdminRole(createUser, tx)
				if err != nil {
					return err
				}
				if !hasRole {
					return errors.New("当前用户没有权限创建超级管理员用户组")
				}
			}
			// 创建用户组记录
			group := new(models.Group)
			group.Name = groupReq.Name
			group.Description = groupReq.Description
			group.CreateUser = createUser
			group.CreateTime = time.Now()
			if err := rbac.CreateGroup(tx, group); err != nil {
				return err
			}
			userGroups := make([]*models.UserGroup, 0)
			usernames := utils.RemoveDupElements(groupReq.UserNames)
			// 用户组-用户关系绑定
			for _, username := range usernames {
				userGroup := new(models.UserGroup)
				userGroup.GroupName = groupReq.Name
				userGroup.Username = username
				userGroup.CreateTime = time.Now()
				userGroups = append(userGroups, userGroup)
				if err := rbac.CreateUserGroup(tx, userGroup); err != nil {
					return err
				}
			}
		}
		// 用户-平台角色关系绑定
		platformRoleId := groupReq.PlatformRoleId
		userRole := new(models.UserRole)
		userRole.Name = groupReq.Name
		userRole.RoleId = platformRoleId
		userRole.BindType = models.UserGroupType
		userRole.ProjectId = ""
		userRole.CreateTime = time.Now()
		userRole.UpdateTime = time.Now()
		if err := rbac.CreateUserRole(tx, userRole); err != nil {
			return err
		}
		return nil
	})
}

func (s *UserGroupService) UpdateUserGroup(groupReq *models.GroupReq, groupName string, username string, gas *guardianv2.GroupsApiService) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		if conf.C.UserStore.Type == models.Guardian.String() {
			ctx := context.Background()
			groupVo := guardianv2.GroupVo{
				GroupName:        groupReq.Name,
				GroupDescription: groupReq.Description,
				Users:            groupReq.UserNames,
			}
			_, localVarHttpResponse, err := gas.UpdateGroupUsingPUT1(ctx, groupVo, groupName)
			if err != nil || localVarHttpResponse == nil || localVarHttpResponse.StatusCode >= 300 {
				return err
			}
		} else {
			if groupReq.PlatformRoleId == uint64(helper.SuperAdminUserID) {
				hasRole, err := checkUserSuperAdminRole(username, tx)
				if err != nil {
					return err
				}
				if !hasRole {
					return errors.New("当前用户没有权限编辑超级管理员用户组")
				}
			}
			// 更新用户组信息
			exists, err := rbac.GetGroupById(tx, int64(groupReq.Gid))
			if err != nil {
				return stderr.Wrap(err, "获取用户组失败，id：%d", groupReq.Gid)
			}
			exists.Name = groupReq.Name
			exists.Description = groupReq.Description
			if err := rbac.UpdateGroup(tx, exists); err != nil {
				return stderr.Wrap(err, "更新用户组失败，id：%d", groupReq.Gid)
			}
			// 重新绑定用户组-用户关系
			if err := rbac.DeleteUserGroupsByGroupName(tx, groupName); err != nil {
				return err
			}
			userGroups := make([]*models.UserGroup, 0)
			usernames := utils.RemoveDupElements(groupReq.UserNames)
			for _, username := range usernames {
				userGroup := new(models.UserGroup)
				userGroup.GroupName = groupReq.Name
				userGroup.Username = username
				userGroup.CreateTime = time.Now()
				userGroups = append(userGroups, userGroup)
				if err := rbac.CreateUserGroup(tx, userGroup); err != nil {
					return err
				}
			}
		}
		// 绑定用户组-平台角色关系
		platformRoleId := groupReq.PlatformRoleId
		userRole := new(models.UserRole)
		// guardian不能更改用户组名称
		if conf.C.UserStore.Type == models.Guardian.String() {
			userRole.Name = groupName
		} else {
			userRole.Name = groupReq.Name
		}
		userRole.RoleId = platformRoleId
		userRole.BindType = models.UserGroupType
		userRole.ProjectId = ""
		if err := rbac.UpdateUserRoleByName(tx, userRole, groupName); err != nil {
			return err
		}
		return nil
	})
}

func (s *UserGroupService) DeleteUserGroup(groupName string, gas *guardianv2.GroupsApiService) error {
	if conf.C.UserStore.Type == models.Guardian.String() {
		ctx := context.Background()
		localVarHttpResponse, err := gas.DeleteGroupUsingDELETE1(ctx, groupName)
		if err != nil || localVarHttpResponse == nil || localVarHttpResponse.StatusCode >= 300 {
			return err
		}
	} else {
		// 根据用户组名称删除用户组信息
		if err := rbac.DeleteGroupByName(s.db, groupName); err != nil {
			return err
		}
		// 根据用户组名称删除用户组-用户关联关系
		if err := rbac.DeleteUserGroupsByGroupName(s.db, groupName); err != nil {
			return err
		}
	}
	if err := rbac.DeleteUserRolesByName(s.db, groupName, models.UserGroupType.String()); err != nil {
		return err
	}
	if err := project.DeleteProjectMembersByNameAndUserType(s.db, groupName, models.UserGroupType.String()); err != nil {
		return err
	}
	return nil
}

func (s *UserGroupService) BatchDeleteUserGroupsByGroupNames(groupNames []string, gas *guardianv2.GroupsApiService) error {
	if conf.C.UserStore.Type == models.Guardian.String() {
		ctx := context.Background()
		for _, groupName := range groupNames {
			localVarHttpResponse, err := gas.DeleteGroupUsingDELETE1(ctx, groupName)
			if err != nil || localVarHttpResponse == nil || localVarHttpResponse.StatusCode >= 300 {
				return err
			}
		}
	} else {
		// 根据用户组名称删除用户组信息
		if err := rbac.BatchDeleteGroupsByNames(s.db, groupNames); err != nil {
			return err
		}
		// 根据用户组名称删除用户组-用户关联关系
		if err := rbac.BatchDeleteUserGroupsByGroupNames(s.db, groupNames); err != nil {
			return err
		}
	}
	if err := rbac.BatchDeleteUserRolesByNames(s.db, groupNames, models.UserGroupType.String()); err != nil {
		return err
	}
	if err := project.BatchDeleteProjectMembersByNamesAndUserType(s.db, groupNames, models.UserGroupType.String()); err != nil {
		return err
	}
	return nil
}

func (s *UserGroupService) GetGroupByGroupName(groupName string) *models.Group {
	group, _ := rbac.GetGroup(s.db, groupName)
	return group
}

func (s *UserGroupService) GetGroupById(id int64) *models.Group {
	group, _ := rbac.GetGroupById(s.db, id)
	return group
}

func (s *UserGroupService) GetUserGroupsByUsername(username string) ([]string, error) {
	userGroups, err := rbac.BatchGetUserGroupsByUserNames(s.db, []string{username})
	if err != nil {
		return nil, err
	}
	groupNames := make([]string, 0)
	for _, userGroup := range userGroups {
		groupNames = append(groupNames, userGroup.GroupName)
	}
	return groupNames, err
}

func (s *UserGroupService) GetUserByGroup(ctx context.Context, groupName string) (*models.GroupResp, error) {
	group, err := rbac.GetGroup(s.db, groupName)
	if err != nil {
		return nil, err
	}
	groupResp := &models.GroupResp{
		Gid:         group.Id,
		Name:        group.Name,
		Description: group.Description,
		CreateUser:  group.CreateUser,
		CreateTime:  group.CreateTime,
	}
	if err := s.db.Model(new(models.UserGroup)).
		Where("group_name = ?", groupName).
		Pluck("username", &groupResp.UserNames).Error; err != nil {
		return nil, err
	}
	return groupResp, nil
}
