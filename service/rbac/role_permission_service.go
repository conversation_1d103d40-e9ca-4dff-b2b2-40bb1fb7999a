package rbac

import (
	"fmt"
	"strings"

	"gorm.io/gorm"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/central-auth-service/dao/rbac"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func GetPermissionsMapByRoleIDs(db *gorm.DB, roleIDs []uint64) (map[uint64][]*models.Permission, error) {
	rolePermissions, err := rbac.BatchGetRolePermissionsByRoleIds(db, roleIDs)
	if err != nil {
		return nil, err
	}

	permissionMap, _, _, err := rbac.AllPermissionMap(db)
	if err != nil {
		return nil, err
	}
	roleIDPermissionsMap := map[uint64][]*models.Permission{}
	for _, rolePermission := range rolePermissions {
		if permission, permissionExists := permissionMap[rolePermission.PermissionId]; permissionExists {
			if permissions, ok := roleIDPermissionsMap[rolePermission.RoleId]; ok {
				roleIDPermissionsMap[rolePermission.RoleId] = append(permissions, permission)
			} else {
				roleIDPermissionsMap[rolePermission.RoleId] = append([]*models.Permission{}, permission)
			}
		}
	}

	return roleIDPermissionsMap, nil
}

func setActionsMap(actionsMap *map[string]*map[string]string, permission *models.Permission, allCodeActionMap map[string]*models.Permission) {

	// 添加对应的action
	if actionMap, ok := (*actionsMap)[permission.Code]; ok {
		(*actionMap)[permission.Action] = ""
	} else {
		(*actionsMap)[permission.Code] = &map[string]string{permission.Action: ""}
	}
	if permission.Parent != "" {

		// 添加上级菜单的access action
		if actionMap, ok := (*actionsMap)[permission.Parent]; ok {
			(*actionMap)[models.Access.String()] = ""
		} else {
			(*actionsMap)[permission.Parent] = &map[string]string{models.Access.String(): ""}
		}
		setActionsMap(actionsMap, allCodeActionMap[rbac.FormatCodeAction(permission.Parent, models.Access.String())], allCodeActionMap)
	}

}

func GetUserPermissionsByRoleIDs(db *gorm.DB, roleIDs []uint64, lc stdsrv.Language) ([]*models.PermissionResp, error) {
	// 根据角色id获取权限信息
	rolePermissions, err := rbac.BatchGetRolePermissionsByRoleIds(db, roleIDs)
	if err != nil {
		return nil, err
	}

	allPermissionIdMap, _, allPermissionCodeActionMap, err := rbac.AllPermissionMap(db)
	if err != nil {
		return nil, err
	}

	/**
	  结构：
	  {
	      code:{
	          action1:""
	          action2:""
	          action3:""
	      }
	  }
	  例：
	  {
	      “project.resource.*”:{
	          "access":"",
	          "read":""
	      }
	  }
	  表示对应code下所有有权限的权限点
	*/
	actionsMap := map[string]*map[string]string{}

	for _, rolePermission := range rolePermissions {
		permission := allPermissionIdMap[rolePermission.PermissionId]
		if permission == nil {
			continue
		}
		setActionsMap(&actionsMap, permission, allPermissionCodeActionMap)
	}

	var result []*models.PermissionResp
	for code, actionMap := range actionsMap {
		// 权限名称
		var permissionName string
		if accessPermission := allPermissionCodeActionMap[rbac.FormatCodeAction(code, models.Access.String())]; accessPermission != nil {
			// 取对应二级菜单的名称(action=access)
			permissionName = accessPermission.GetNameWithLocal(lc)
		} else {
			// 部分权限点直接在一级菜单下，没有二级菜单，直接取一级菜单的名称
			permissionName = allPermissionCodeActionMap[fmt.Sprintf("%s:%s", strings.Split(code, ".")[0], models.Access.String())].GetNameWithLocal(lc)
		}

		var actions []string
		for key, _ := range *actionMap {
			actions = append(actions, key)
		}

		result = append(result, &models.PermissionResp{
			Code:   code,
			Name:   permissionName,
			Action: actions,
		})
	}

	return result, nil
}
