package rbac

import (
	"context"
	"fmt"
	"slices"

	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"transwarp.io/applied-ai/aiot/vision-std/clients/cas"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/dao/rbac"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type RbacService struct{}

func NewrbacService() *RbacService {
	return &RbacService{}
}

// PutObject 添加一个对象与其访问策略, 已存在的对象将会更新
func (r *RbacService) PutObject(ctx context.Context, objt cas.ObjType, objId string, req []*models.PutObjectReq) error {
	log := stdlog.WithFields(
		"objType", string(objt),
		"objExId", objId,
	)
	return dao.GetDB().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		obj := &rbac.RbacObject{
			ObjType:    objt,
			ExternalId: objId,
		}
		err := tx.Model(obj).
			Where(obj).
			Omit("RbacPolicies").
			FirstOrCreate(obj).Error
		if err != nil {
			log.Errorf("FirstOrCreate obj: %v", err)
			return fmt.Errorf("create obj: %w", err)
		}
		// 策略的更新: 删除与创建
		poc := &rbac.RbacPolicy{
			ObjId: obj.Id,
		}
		oldPocs := make([]*rbac.RbacPolicy, 0)
		err = tx.Clauses(clause.Locking{Strength: clause.LockingStrengthUpdate}).
			Model(poc).
			Where(poc, "ObjId").
			Find(&oldPocs).Error
		if err != nil {
			log.WithField("objId", obj.Id).Errorf("find policies: %v", err)
			return fmt.Errorf("find policies: %w", err)
		}
		reqPocs := lo.Map(req, func(e *models.PutObjectReq, _ int) *rbac.RbacPolicy {
			return &rbac.RbacPolicy{
				SubType:   e.SubType,
				Username:  e.Username,
				GroupId:   e.GroupId,
				ObjId:     obj.Id,
				ProjectId: e.ProjectId,
				Act:       e.Act,
			}
		})
		// 先删除旧的, 再创建新的
		if len(oldPocs) > 0 {
			err = tx.Delete(oldPocs).Error
			if err != nil {
				log.WithField("policies", oldPocs).Errorf("delete policies: %v", err)
				return fmt.Errorf("delete policies: %w", err)
			}
		}
		if len(reqPocs) > 0 {
			err = tx.Create(reqPocs).Error
			if err != nil {
				log.WithField("policies", reqPocs).Errorf("create policies: %v", err)
				return fmt.Errorf("create policies: %w", err)
			}
		}
		return nil
	})
}

// DelObject 删除一个对象与其访问策略
func (r *RbacService) DelObject(ctx context.Context, objt cas.ObjType, objId string) error {
	log := stdlog.WithFields(
		"objType", string(objt),
		"objExId", objId,
	)
	return dao.GetDB().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		obj := &rbac.RbacObject{
			ObjType:    objt,
			ExternalId: objId,
		}
		err := tx.Clauses(clause.Locking{Strength: clause.LockingStrengthUpdate}).
			Where(obj).Take(obj).Error
		if err != nil {
			log.Errorf("take obj: %v", err)
			return fmt.Errorf("take obj: %w", err)
		}
		// 同时删除关联的策略
		err = tx.Select("RbacPolicies").Delete(obj).Error
		if err != nil {
			log.WithField("objId", obj.Id).Errorf("delete obj and policies: %v", err)
			return fmt.Errorf("delete obj and policies: %w", err)
		}
		return nil
	})
}

// GetObject 获取一个对象与其访问策略
func (r *RbacService) GetObject(ctx context.Context, objt cas.ObjType, objId string) (*models.GetObjectRsp, error) {
	obj := &rbac.RbacObject{
		ExternalId: objId,
		ObjType:    objt,
	}
	err := dao.GetDB().WithContext(ctx).Model(obj).
		Preload("RbacPolicies").
		Where(obj).
		Take(obj).Error
	if err != nil {
		stdlog.WithFields(
			"objType", string(objt),
			"objExId", objId,
		).Errorf("get obj: %v", err)
		return nil, fmt.Errorf("get obj: %w", err)
	}
	rsp := &models.GetObjectRsp{
		ObjType:  objt,
		ObjId:    objId,
		Policies: make([]*models.Policy, len(obj.RbacPolicies)),
	}
	for i, v := range obj.RbacPolicies {
		rsp.Policies[i] = &models.Policy{
			SubType:   v.SubType,
			Username:  v.Username,
			GroupId:   v.GroupId,
			ProjectId: v.ProjectId,
			Act:       v.Act,
		}
	}
	return rsp, nil
}

// ListObject 获取对象与其访问策略列表, 用户的权限包含了从用户组继承而来的部分.
//
//	@param username 用户名, 获取用户拥有权限的对象
//	@param projectId 空间id
//	@param objt 对象类型
//	@param acts 指定操作
func (r *RbacService) ListObject(ctx context.Context, username, projectId string, objt cas.ObjType, objId string, acts ...cas.Act) (*models.ListObjectRsp, error) {
	log := stdlog.WithFields(
		"username", username,
		"projectId", projectId,
		"objt", string(objt),
		"objId", objId,
	).WithField("acts", acts)
	pocs := make([]*rbac.RbacPolicy, 0)
	sql := dao.GetDB().WithContext(ctx).
		Model(new(rbac.RbacPolicy)).
		Joins("RbacObject")

	// 获取用户所属的用户组, 包括平台的和指定空间的
	gs, err := listGroupsByUser(ctx, username, projectId)
	if err != nil {
		log.Errorf("get groups: %v", err)
		return nil, fmt.Errorf("get groups: %w", err)
	}
	gnames := lo.UniqMap(gs, func(g *models.Group, _ int) string { return g.Name })

	// 超管, 空间管理员, 默认有 [全部] 权限
	// 如何用户符合上述需求 直接返回特殊标识

	// 获取用户角色, 继承来自用户组的角色
	exsql := dao.GetDB().WithContext(ctx).Model(new(models.UserRole)).
		Where("name IN ?", append(gnames, username))
	if projectId != "" {
		// TODO llm2.0 空间内用户组 需要修改这里
		exsql = exsql.Where("project_id = '' OR project_id = ?", projectId)
	} else {
		exsql = exsql.Where("project_id = ''")
	}
	roleIds := make([]int, 0, 10)
	err = exsql.Pluck("role_id", &roleIds).Error
	if err != nil {
		return nil, fmt.Errorf("list user role: %w", err)
	}
	if slices.Contains(roleIds, int(helper.SuperAdminUserID)) ||
		slices.Contains(roleIds, int(helper.ProjectOwnerID)) {
		return &models.ListObjectRsp{
			AccessType: cas.AccessType_Unrestricted,
		}, nil
	}

	gids := lo.UniqMap(gs, func(g *models.Group, _ int) uint64 { return g.Id })
	sql = sql.Where(
		// 用户和用户组
		dao.GetDB().
			Where("username = ? AND sub_type = ?", username, cas.SubType_User).
			Or("group_id IN ? AND sub_type = ?", gids, cas.SubType_Group),
	)
	if projectId != "" {
		sql = sql.Where("project_id = ?", projectId)
	}
	if objt != "" {
		sql = sql.Where("obj_type = ?", objt)
	}
	if objId != "" {
		sql = sql.Where("external_id = ?", objId)
	}
	if len(acts) > 0 {
		sql = sql.Where("act IN ?", acts)
	}
	err = sql.Find(&pocs).Error
	if err != nil {
		log.Errorf("get policies: %v", err)
		return nil, fmt.Errorf("get policies: %w", err)
	}
	objPocs := lo.GroupBy(pocs, func(e *rbac.RbacPolicy) string { return e.RbacObject.ExternalId })
	rsp := &models.ListObjectRsp{
		Objects: make([]*models.Object, 0, len(objPocs)),
	}
	for k, v := range objPocs {
		// 挑选权限最高的
		p := slices.MinFunc(v, func(a, b *rbac.RbacPolicy) int { return actMap[a.Act] - actMap[b.Act] })
		obj := &models.Object{
			ObjId:   k,
			ObjType: p.RbacObject.ObjType,
			Act:     p.Act,
		}

		rsp.Objects = append(rsp.Objects, obj)
	}

	return rsp, nil
}

var actMap = map[cas.Act]int{
	cas.Act_All:      0,
	cas.Act_ReadOnly: 1,
}

func listGroupsByUser(ctx context.Context, username, projectId string) ([]*models.Group, error) {
	// 获取用户所属的用户组, 包括平台的和指定空间的
	gs := make([]*models.Group, 0)
	err := dao.GetDB().WithContext(ctx).Table("`groups` g").
		Select("g.*").
		Joins("JOIN user_group ug ON ug.group_name=g.name").
		Where("ug.username = ?", username).
		// TODO llm2.0 空间内用户组 需要修改这里
		// Where(
		// 	// 指定空间
		// 	dao.GetDB().Where("ug.project_id = ?", projectId).
		// 		// 平台的
		// 		Or("ug.project_id = ''"),
		// ).
		Find(&gs).Error
	if err != nil {
		return nil, fmt.Errorf("get groups: %w", err)
	}
	return gs, nil
}
