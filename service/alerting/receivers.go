package alerting

import (
	"context"
	"sync"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type AlertReceiverSvc struct {
	upsertHooks []UpdateReceiverHook
}

var (
	ar     *AlertReceiverSvc
	arOnce sync.Once
)

func GetAlertReceiverMgrInstance() *AlertReceiverSvc {
	arOnce.Do(func() {
		ar = &AlertReceiverSvc{
			upsertHooks: []UpdateReceiverHook{&UpdateAlertMsgHook{}},
		}
	})
	return ar
}

func (a AlertReceiverSvc) Upsert(ctx context.Context, cfg *models.AlertReceiverConfig) error {
	if err := dao.AlertReceiverDAO.UpsertAlertReceiverCfg(cfg); err != nil {
		return err
	}
	for _, hook := range a.upsertHooks {
		if err := hook.Handle(ctx, cfg); err != nil {
			return err
		}

	}
	return nil
}

func (a AlertReceiverSvc) Get(ctx context.Context, svcID string) (*models.AlertReceiverConfig, error) {
	svc, err := dao.AlertReceiverDAO.GetAlertReceiverCfg(svcID)
	if err != nil {
		return nil, err
	}
	return svc, nil
}

type UpdateReceiverHook interface {
	Handle(ctx context.Context, cfg *models.AlertReceiverConfig) error
}

type UpdateAlertMsgHook struct {
}

func (u UpdateAlertMsgHook) Handle(ctx context.Context, cfg *models.AlertReceiverConfig) error {
	return GetAlertHistoryMgrInstance().UpdateReceiversBySvc(ctx, cfg.ServiceID, cfg.GetReceiver())
}
