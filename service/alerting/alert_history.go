package alerting

import (
	"context"
	"strconv"
	"sync"
	"time"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/dao/alert_history"
	"transwarp.io/applied-ai/central-auth-service/dao/project"
	"transwarp.io/applied-ai/central-auth-service/models"
)

var (
	ah     *AlertHistoryMgr
	ahOnce sync.Once
)

func GetAlertHistoryMgrInstance() *AlertHistoryMgr {
	ahOnce.Do(func() {
		ah = &AlertHistoryMgr{}
	})
	return ah
}

type AlertHistoryMgr struct {
}

func (a AlertHistoryMgr) Create(ctx context.Context, alert *models.AlertsHistory) (string, error) {
	ruleID, err := alert.GetRuleID()
	if err != nil {
		return "", err
	}
	rule, err := NewAlertService().GetRule(ctx, ruleID)
	if err != nil {
		return "", err
	}
	alert.ID = strconv.Itoa(int(time.Now().UnixNano()))
	alert.ServiceID = rule.ServiceId
	receiverCfg, err := GetAlertReceiverMgrInstance().Get(ctx, rule.ServiceId)
	if err != nil {
		return "", err
	}
	receivers := make([]string, 0)
	if receiverCfg != nil {
		receivers = receiverCfg.GetReceiver()
	}
	if len(receivers) == 0 {
		receivers = []string{rule.ServiceCreator}
	}
	alert.Receivers = receivers
	alert.ServiceName = rule.ServiceName
	alert.Level = int(rule.Level)
	alert.RelatedMetrics = rule.RelatedMetrics
	alert.ConditionDesc = rule.Description
	alert.AlertCreator = rule.Creator
	alert.AlertSubject = rule.NotificationMessage
	alert.RuleCreateTimeSec = rule.CreateTime.Unix()
	alert.AlertName = rule.Name
	alert.ProjectID = rule.ProjectId
	proj, err := project.GetProjectById(dao.DB, rule.ProjectId)
	if err != nil {
		return "", err
	}
	alert.ProjectName = proj.Name
	alertPO, err := alert.TOPO()
	if err != nil {
		return "", err
	}
	return alertPO.ID, dao.AlertHistoryDAO.Create(ctx, alertPO)
}

func (a AlertHistoryMgr) List(ctx context.Context, req alert_history.ListAlertReq) ([]*models.AlertsHistory, error) {
	alerts := make([]*models.AlertsHistory, 0)
	alertsPO, err := dao.AlertHistoryDAO.ListBySvcID(ctx, req)
	if err != nil {
		return nil, err
	}
	for _, a := range alertsPO {
		alert, err := a.ToDO()
		if err != nil {
			return nil, err
		}
		alerts = append(alerts, alert)
	}
	return alerts, nil
}

func (a AlertHistoryMgr) DeleteByIDs(ctx context.Context, IDs []string) error {
	return dao.AlertHistoryDAO.DelByIDs(ctx, IDs)
}

func (a AlertHistoryMgr) MarkStateAsRead(ctx context.Context, ID string) error {
	return dao.AlertHistoryDAO.UpdateReadStateByID(ctx, ID, true)
}

func (a AlertHistoryMgr) UpdateReceiversBySvc(ctx context.Context, SvcID string, receivers []string) error {
	return dao.AlertHistoryDAO.UpdateAlertingReceiversBySvcID(ctx, SvcID, receivers)
}
