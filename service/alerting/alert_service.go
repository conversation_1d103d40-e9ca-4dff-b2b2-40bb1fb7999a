package alerting

import (
	"context"
	"sync"
	"time"

	"gorm.io/gorm"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models/alerting"
)

var (
	nilRuleErr   = stderr.Errorf("rule is nil")
	projEmptyErr = stderr.InvalidParam.Errorf("project_id is empty")
)

type AlertService struct {
	db *gorm.DB
}

func (s *AlertService) CreateRule(ctx context.Context, rule *alerting.AlertRule) (*alerting.AlertRule, error) {
	if rule == nil {
		return nil, nilRuleErr
	}
	rule.ProjectId = helper.GetProjectID(ctx)
	if rule.ProjectId == "" {
		return nil, projEmptyErr
	}
	rule.CreateTime = time.Now()
	rule.OnUpdate()
	if conf.C.Alerting.DefaultRuleIsPaused != nil {
		rule.IsPaused = *conf.C.Alerting.DefaultRuleIsPaused
	}

	err := dao.CreateRule(ctx, s.db, rule)
	if err != nil {
		return nil, err
	}
	return rule, nil
}

func (s *AlertService) UpdateRule(ctx context.Context, rule *alerting.AlertRule) (*alerting.AlertRule, error) {
	if rule == nil {
		return nil, nilRuleErr
	}
	if rule.ProjectId == "" {
		return nil, projEmptyErr
	}
	
	rule.OnUpdate()
	err := dao.UpdateRule(ctx, s.db, rule)
	if err != nil {
		return nil, err
	}
	return rule, nil
}

func (s *AlertService) DeleteRule(ctx context.Context, ruleId int64) error {
	return dao.DeleteRule(ctx, s.db, ruleId)
}

func (s *AlertService) ListRules(ctx context.Context, req *ListRulesReq) (*ListRulesRsp, error) {
	rules, err := dao.ListRulesBySvcId(s.db, req.ServiceId)
	if err != nil {
		return nil, err
	}
	return &ListRulesRsp{Rules: rules}, nil
}

func (s *AlertService) PatchRulePaused(ctx context.Context, ruleId int64, paused bool) error {
	rule, err := dao.GetRule(s.db, ruleId)
	if err != nil {
		return err
	}
	if rule == nil {
		return nilRuleErr
	}
	rule.IsPaused = paused
	return dao.UpdateRule(ctx, s.db, rule)
}
func (s *AlertService) GetRule(ctx context.Context, ruleId int64) (*alerting.AlertRule, error) {
	return dao.GetRule(s.db, ruleId)
}

func (s *AlertService) NameExistsForSvc(ctx context.Context, svcId, name string) (bool, error) {
	cond := []any{"serviceId = ? and name = ?", svcId, name}
	cnt, err := dao.CountRules(s.db, cond)
	if err != nil {
		return false, err
	}
	return cnt > 0, nil
}

type ListRulesReq struct {
	ProjectId string `json:"project_id"`
	ServiceId string `json:"service_id"`
}

type ListRulesRsp struct {
	Rules []*alerting.AlertRule `json:"rules"`
}

var (
	as     *AlertService
	asOnce sync.Once
)

func NewAlertService() *AlertService {
	asOnce.Do(func() {
		as = &AlertService{db: dao.GetDB()}

	})
	return as
}

func (s *AlertService) GetRuleMock(ctx context.Context, ruleId int64) (*alerting.AlertRule, error) {
	return &alerting.AlertRule{
		Id:                  0,
		Name:                "",
		ServiceId:           "57bbde10-7986-4a52-a36c-6c4611c271a9",
		ServiceName:         "testhk-勿动",
		ServiceCreator:      "thinger",
		ProjectId:           "assets",
		Level:               0,
		Interval:            "",
		For:                 "",
		IsPaused:            false,
		Description:         "测试",
		NotificationMessage: "",
		RelatedMetrics:      nil,
		Conditions:          nil,
		Creator:             "",
	}, nil

}
