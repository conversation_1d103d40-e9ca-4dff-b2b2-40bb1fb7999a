package service

import (
	"fmt"

	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/dao"
)

func NewUserService(db *gorm.DB) *UserService {
	us := &UserService{db: db.Session(&gorm.Session{NewDB: true})}
	return us
}

type UserService struct { // TODO 兼容 Guardian 用户系统
	db *gorm.DB
}

func (s *UserService) CreateUser(user *dao.User, passwd string) error {
	return dao.CreateUser(s.db, user, passwd)
}

func (s *UserService) GetUserByID(userID string) (*dao.User, error) {
	user, err := dao.GetUserByID(s.db, userID)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (s *UserService) GetUserByName(name string) (*dao.User, error) {
	user, err := dao.GetUserByName(s.db, name)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (s *UserService) CountUsers() (int, error) {
	return dao.CountUsers(s.db)
}

func (s *UserService) ListUsers(q *dao.User) ([]*dao.User, error) {
	tx := s.db.Session(&gorm.Session{})
	if q != nil {
		if q.Name != "" {
			tx = tx.Where("name LIKE ?", fmt.Sprintf("%%%s%%", q.Name))
		}
		if q.FullName != "" {
			tx = tx.Where("full_name LIKE ?", fmt.Sprintf("%%%s%%", q.FullName))
		}
		if q.PhoneNumber != "" {
			tx = tx.Where("phone_number LIKE ?", fmt.Sprintf("%%%s%%", q.PhoneNumber))
		}
	}
	users, err := dao.ListUsers(tx)
	if err != nil {
		return nil, err
	}
	return users, nil
}

func (s *UserService) ListUsersByRoleName(role string) ([]*dao.User, error) {
	users, err := dao.ListUsersByRoleName(s.db, role)
	if err != nil {
		return nil, err
	}
	return users, nil
}

func (s *UserService) DeleteUserByID(userID string) error {
	return dao.DeleteUserByID(s.db, userID)
}

func (s *UserService) DeleteUserByName(name string) error {
	return dao.DeleteUserByName(s.db, name)
}

func (s *UserService) CreateRole(role *dao.Role) error {
	return dao.CreateRole(s.db, role)
}

func (s *UserService) GetRoleByID(roleID string) (*dao.Role, error) {
	role, err := dao.GetRoleByID(s.db, roleID)
	if err != nil {
		return nil, err
	}
	return role, nil
}

func (s *UserService) GetRoleByName(name string) (*dao.Role, error) {
	role, err := dao.GetRoleByName(s.db, name)
	if err != nil {
		return nil, err
	}
	return role, nil
}

func (s *UserService) ListRoles() ([]*dao.Role, error) {
	roles, err := dao.ListRoles(s.db)
	if err != nil {
		return nil, err
	}
	return roles, nil
}

func (s *UserService) DeleteRoleByID(username string) error {
	return dao.DeleteRoleByID(s.db, username)
}

func (s *UserService) DeleteRoleByName(username string) error {
	return dao.DeleteRoleByName(s.db, username)
}

func (s *UserService) AppendRoleToUser(userID string, roleID string) error {
	return dao.AppendRoleToUser(s.db, userID, roleID)
}

func (s *UserService) RemoveRoleFromUser(userID string, roleID string) error {
	return dao.RemoveRoleFromUser(s.db, userID, roleID)
}
