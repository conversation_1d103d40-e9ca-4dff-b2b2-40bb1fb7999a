package flows

import (
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/examine/flows/callback"
)

const (
	TableDedupFlowName = "table_dedup_flow"
)

var TableDedupsFlow = &models.ExamineFlow{
	Name: TableDedupFlowName,
	Abbr: "TD",
	Nodes: []*models.ExamineNode{
		{
			Index:     1,
			PrevIndex: 0,
			NextIndex: 2,
		},
		{
			Index:     2,
			PrevIndex: 1,
			NextIndex: 0,
			RoleRequest: []*models.RoleRequest{
				{
					Type:     models.RoleRequestProject,
					RoleName: "空间负责人",
					Name:     "所属空间负责人（或签）",
				},
			},
			Callbacks: []models.NodeFunctionCall{
				{
					Result:   models.Reject,
					Callback: callback.TableDedupRejectHttpCall,
				},
			},
		},
	},
	Callbacks: []models.FlowFunctionCall{
		{
			Status:   models.Finished,
			Callback: callback.HttpCallback,
		},
	},
}
