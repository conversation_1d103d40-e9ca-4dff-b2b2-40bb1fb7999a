package callback

import (
	"bytes"
	"crypto/tls"
	"fmt"
	"net/http"
	"strings"
	"time"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func ServingUnderApprovalHttpCall(req models.CallRequest) models.CallResponse {
	if callReq, ok := req.(*models.HttpCallRequest); ok {
		url := strings.Replace(callReq.HttpInfo.Url, "start", "approval-state", 1)
		url = replaceHost(url)
		token := callReq.HttpInfo.Token
		request, err := http.NewRequest(http.MethodPut, url, bytes.NewReader([]byte(`{"state":"UnderApproval"}`)))
		if err != nil {
			return models.NewErrorResponse(err)
		}
		request.Header.Set("Authorization", token)
		request.Header.Set("Content-Type", "application/json")
		request.Header.Set("Cas-Callback", "true")
		client := &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
			Timeout: time.Second * 60,
		}
		response, err := client.Do(request)
		if err != nil {
			return models.NewErrorResponse(err)
		}
		return models.NewHttpResponse(response)
	} else {
		return models.NewErrorResponse(fmt.Errorf("request type should be [models.HttpInfo]"))
	}
}

func ServingApprovalPassedHttpCall(req models.CallRequest) models.CallResponse {
	if callReq, ok := req.(*models.HttpCallRequest); ok {
		url := strings.Replace(callReq.HttpInfo.Url, "start", "approval-state", 1)
		url = replaceHost(url)
		token := callReq.HttpInfo.Token
		request, err := http.NewRequest(http.MethodPut, url, bytes.NewReader([]byte(`{"state":"ApprovalPassed"}`)))
		if err != nil {
			return models.NewErrorResponse(err)
		}
		request.Header.Set("Authorization", token)
		request.Header.Set("Content-Type", "application/json")
		request.Header.Set("Cas-Callback", "true")
		client := &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
			Timeout: time.Second * 60,
		}
		response, err := client.Do(request)
		if err != nil {
			return models.NewErrorResponse(err)
		}
		return models.NewHttpResponse(response)
	} else {
		return models.NewErrorResponse(fmt.Errorf("request type should be [models.HttpInfo]"))
	}
}

func ServingApprovalRejectHttpCall(req models.CallRequest) models.CallResponse {
	if callReq, ok := req.(*models.HttpCallRequest); ok {
		url := strings.Replace(callReq.HttpInfo.Url, "start", "approval-state", 1)
		url = replaceHost(url)
		token := callReq.HttpInfo.Token
		request, err := http.NewRequest(http.MethodPut, url, bytes.NewReader([]byte(`{"state":"ApprovalRejected"}`)))
		if err != nil {
			return models.NewErrorResponse(err)
		}
		request.Header.Set("Authorization", token)
		request.Header.Set("Content-Type", "application/json")
		request.Header.Set("Cas-Callback", "true")
		client := &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
			Timeout: time.Second * 60,
		}
		response, err := client.Do(request)
		if err != nil {
			return models.NewErrorResponse(err)
		}
		return models.NewHttpResponse(response)
	} else {
		return models.NewErrorResponse(fmt.Errorf("request type should be [models.HttpInfo]"))
	}
}
