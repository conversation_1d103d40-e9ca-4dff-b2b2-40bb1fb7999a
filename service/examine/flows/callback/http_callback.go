package callback

import (
	"bytes"
	"crypto/tls"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func HttpCallback(req models.CallRequest) models.CallResponse {
	if callReq, ok := req.(*models.HttpCallRequest); ok {
		httpInfo := callReq.HttpInfo
		token := callReq.Token
		qs, err := strconv.Unquote(string(httpInfo.Body))
		if err != nil {
			return models.NewErrorResponse(err)
		}
		httpInfo.Url = replaceHost(httpInfo.Url)
		request, err := http.NewRequest(httpInfo.Method, httpInfo.Url, bytes.NewReader([]byte(qs)))
		if err != nil {
			return models.NewErrorResponse(err)
		}
		request.Header.Set("Authorization", token)
		request.Header.Set("Content-Type", "application/json")
		request.Header.Set("Cas-Callback", "true")
		request.Header.Set("Cas-Callback-Result", string(models.Approve))
		client := &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
			Timeout: time.Second * 60,
		}
		response, err := client.Do(request)
		if err != nil {
			return models.NewErrorResponse(err)
		}
		return models.NewHttpResponse(response)
	} else {
		return models.NewErrorResponse(fmt.Errorf("request type should be [models.HttpInfo]"))
	}
}

func RejectHttpCallback(req models.CallRequest) models.CallResponse {
	if callReq, ok := req.(*models.HttpCallRequest); ok {
		httpInfo := callReq.HttpInfo
		token := callReq.Token
		qs, err := strconv.Unquote(string(httpInfo.Body))
		if err != nil {
			return models.NewErrorResponse(err)
		}
		httpInfo.Url = replaceHost(httpInfo.Url)
		request, err := http.NewRequest(httpInfo.Method, httpInfo.Url, bytes.NewReader([]byte(qs)))
		if err != nil {
			return models.NewErrorResponse(err)
		}
		request.Header.Set("Authorization", token)
		request.Header.Set("Content-Type", "application/json")
		request.Header.Set("Cas-Callback", "true")
		request.Header.Set("Cas-Callback-Result", string(models.Reject))
		client := &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
			Timeout: time.Second * 60,
		}
		response, err := client.Do(request)
		if err != nil {
			return models.NewErrorResponse(err)
		}
		return models.NewHttpResponse(response)
	} else {
		return models.NewErrorResponse(fmt.Errorf("request type should be [models.HttpInfo]"))
	}
}

// replaceHost 将前端浏览器的地址替换为svc地址
func replaceHost(origin string) string {
	path := strings.TrimPrefix(origin, "https://")
	path = strings.TrimPrefix(path, "http://")
	firstSlash := strings.Index(path, "/")
	path = path[firstSlash+1:]
	return "https://autocv-portal-service:443/" + path
}
