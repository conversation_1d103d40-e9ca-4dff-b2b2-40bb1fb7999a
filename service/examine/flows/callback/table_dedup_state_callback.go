package callback

import (
	"bytes"
	"crypto/tls"
	"fmt"
	"net/http"
	"strconv"
	"time"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func TableDedupRejectHttpCall(req models.CallRequest) models.CallResponse {
	if callReq, ok := req.(*models.HttpCallRequest); ok {
		httpInfo := callReq.HttpInfo
		token := callReq.HttpInfo.Token
		qs, err := strconv.Unquote(string(httpInfo.Body))
		if err != nil {
			return models.NewErrorResponse(err)
		}
		request, err := http.NewRequest(httpInfo.Method, httpInfo.Url, bytes.NewReader([]byte(qs)))
		if err != nil {
			return models.NewErrorResponse(err)
		}
		request.Header.Set("Authorization", token)
		request.Header.Set("Content-Type", "application/json")
		request.Header.Set("Cas-Callback", "true")
		request.Header.Set("Cas-Callback-Result", string(models.Reject))
		client := &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
			Timeout: time.Second * 60,
		}
		response, err := client.Do(request)
		if err != nil {
			return models.NewErrorResponse(err)
		}
		return models.NewHttpResponse(response)
	} else {
		return models.NewErrorResponse(fmt.Errorf("request type should be [models.HttpInfo]"))
	}
}
