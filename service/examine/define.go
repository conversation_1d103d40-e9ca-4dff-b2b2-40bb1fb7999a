package examine

import (
	"context"
	"fmt"
	"sync"

	"gorm.io/gorm"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/dao/examine"
	"transwarp.io/applied-ai/central-auth-service/dao/rbac"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type NodeStatus string

const (
	StartMessage = "流程发起"

	NodeWaiting  NodeStatus = "waiting"  // 待审批
	NodeApproved NodeStatus = "approved" // 已通过
	NodeRejected NodeStatus = "rejected" // 已拒绝
)

var (
	db                     *gorm.DB
	examineFlowInstanceDao examine.ExamineFlowInstanceDao
	examineRecordDao       examine.ExamineRecordDao
)

func Init(d *gorm.DB) {
	db = d.Session(&gorm.Session{NewDB: true})
	examineFlowInstanceDao = examine.NewExamineFlowInstanceDao()
	examineRecordDao = examine.NewExamineRecordDao()
	InitFlowConfig()
}

type Flow interface {
	Instance() *models.ExamineFlowInstance                   // 获取当前flow的db实例
	Start(ctx context.Context) error                         // 开始一个流程，会从草稿状态变为进行中，直接完成第一个发起节点
	Discard(ctx context.Context) error                       // 废弃一个流程，该流程不再使用
	Withdraw(ctx context.Context) error                      // 撤回一个流程，仅能由发起人执行，流程回到草稿状态
	Check(ctx context.Context) (models.ExamineStatus, error) // 检查流程状态，当前节点变更，或者所有节点完成后结束流程, 在节点状态变更后调用此方法更新状态
	Records() []*models.ExamineRecord                        // 返回该流程的审批记录
	Current() Node                                           // 返回当前节点
	ToFlowInfo() *models.FlowInfo                            // 返回前端展示格式
	Config() *models.ExamineFlow
}

type Node interface {
	GetIndex() int32
	Next() Node
	Prev() Node
	Approve(ctx context.Context, user, message string) error // 通过审批
	Reject(ctx context.Context, user, message string) error  // 拒绝审批
	Check(ctx context.Context) error                         // 检查节点状态
	ToNodeInfo() *models.NodeInfo                            // 返回前端展示格式
	Config() *models.ExamineNode
	CheckAuth(ctx context.Context, user string) (bool, error) // 检查节点权限
}

// SerialFlow 串行的流程
// 一个节点结束后流转到下一个节点
// 对应到一个具体的flow instance，用来做各种逻辑处理和流转
// 某个节点拒绝后直接结束流程
type SerialFlow struct {
	sync.Mutex
	Nodes    []Node
	current  Node
	instance *models.ExamineFlowInstance
	records  []*models.ExamineRecord
}

func (s *SerialFlow) Config() *models.ExamineFlow {
	return s.instance.Flow
}

func (s *SerialFlow) ToFlowInfo() *models.FlowInfo {
	nodeDetails := make([]*models.NodeInfo, 0)
	for _, node := range s.Nodes {
		nodeDetails = append(nodeDetails, node.ToNodeInfo())
	}
	return &models.FlowInfo{
		Flow:        s.instance.Flow,
		ID:          s.instance.ID,
		OrderNumber: s.instance.OrderNumber,
		Object:      s.instance.Object,
		Module:      s.instance.Module,
		Type:        s.instance.Binding.Type,
		CurrentIndex: func() int32 {
			if s.current == nil {
				return 0
			} else {
				return s.current.GetIndex()
			}
		}(),
		Status:          s.instance.Status,
		Detail:          s.instance.Detail,
		Creator:         s.instance.Creator,
		CreatedAt:       s.instance.CreatedAt,
		UpdatedAt:       s.instance.UpdatedAt,
		Records:         s.records,
		NodeInfos:       nodeDetails,
		ProjectId:       s.instance.ProjectId,
		ProjectName:     s.instance.ProjectName,
		CallbackResults: s.instance.CallBackResults,
	}
}

func NewSerialFlow(instance *models.ExamineFlowInstance) *SerialFlow {
	serialFlow := &SerialFlow{
		instance: instance,
	}
	nodeMap := make(map[int32]*BaseNode)
	for _, node := range instance.Flow.Nodes {
		baseNode := &BaseNode{
			ExamineNode: node,
			flow:        serialFlow,
		}
		nodeMap[baseNode.Index] = baseNode
	}
	for _, node := range instance.Flow.Nodes {
		current := nodeMap[node.Index]
		if node.PrevIndex > 0 {
			current.prev = nodeMap[node.PrevIndex]
		}
		if node.NextIndex > 0 {
			current.next = nodeMap[node.NextIndex]
		}
	}
	for _, node := range nodeMap {
		serialFlow.Nodes = append(serialFlow.Nodes, node)
	}
	return serialFlow
}

func (s *SerialFlow) Current() Node {
	return s.current
}

func (s *SerialFlow) Records() []*models.ExamineRecord {
	return s.records
}

func (s *SerialFlow) Instance() *models.ExamineFlowInstance {
	return s.instance
}

func (s *SerialFlow) Start(ctx context.Context) error {
	s.Lock()
	defer func() {
		s.Unlock()
	}()
	if s.instance.Status != models.Draft {
		return fmt.Errorf("flow is already started")
	}
	for _, node := range s.Nodes {
		if node.GetIndex() == 1 {
			s.current = node
		}
	}
	// 修改状态为进行中
	s.instance.Status = models.Processing
	// 开始一个事务
	tx := db.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer dao.Recover(tx)
	childCtx := context.WithValue(ctx, "transaction", tx)
	err := examineFlowInstanceDao.UpdateInstance(tx, s.instance)
	if err != nil {
		tx.Rollback()
		return err
	}
	// 当前节点执行approve操作
	err = s.current.Approve(childCtx, s.instance.Creator, StartMessage)
	if err != nil {
		tx.Rollback()
		return err
	}
	err = tx.Commit().Error
	if err != nil {
		return err
	}
	return nil
}

func (s *SerialFlow) Discard(ctx context.Context) error {
	s.Lock()
	defer s.Unlock()
	// 修改状态为discard
	s.instance.Status = models.Discarded
	err := examineFlowInstanceDao.UpdateInstance(db, s.instance)
	if err != nil {
		return err
	}
	return nil
}

func (s *SerialFlow) Withdraw(ctx context.Context) error {
	s.Lock()
	defer func() {
		s.Unlock()
	}()
	// 修改状态为draft, 节点回到1
	s.instance.Status = models.Draft
	s.instance.CurrentIndex = 1
	// 开始一个事务
	tx := db.Begin()
	if tx.Error != nil {
		return stderr.Wrap(tx.Error, "begin transaction")
	}
	defer dao.Recover(tx)
	err := examineFlowInstanceDao.UpdateInstance(tx, s.instance)
	if err != nil {
		tx.Rollback()
		return err
	}
	// 删除所有审批记录
	records, err := examineRecordDao.ListRecordsByInstanceID(tx, s.instance.ID)
	if err != nil {
		tx.Rollback()
		return err
	}
	ids := make([]uint, 0)
	for _, record := range records {
		ids = append(ids, record.ID)
	}
	err = examineRecordDao.DeleteRecords(tx, ids...)
	if err != nil {
		tx.Rollback()
		return err
	}
	err = tx.Commit().Error
	if err != nil {
		return err
	}
	return nil
}

func (s *SerialFlow) Check(ctx context.Context) (models.ExamineStatus, error) {
	s.Lock()
	defer s.Unlock()
	// 以表里的数据作为基准
	dbRecords, err := examineRecordDao.ListRecordsByInstanceID(db, s.instance.ID)
	if err != nil {
		return models.Processing, err
	}
	s.records = dbRecords
	err = s.updateNodeRecord(ctx)
	if err != nil {
		return models.Processing, err
	}
	status, err := s.updateFlow(ctx)
	if err != nil {
		return models.Processing, err
	}
	return status, nil
}

func (s *SerialFlow) updateNodeRecord(ctx context.Context) error {
	for _, node := range s.Nodes {
		err := node.Check(ctx)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *SerialFlow) updateFlow(ctx context.Context) (models.ExamineStatus, error) {
	if len(s.records) == 0 {
		for _, node := range s.Nodes {
			if node.GetIndex() == 1 {
				s.current = node
			}
		}
		return models.Draft, nil
	}
	lastRecord := s.records[len(s.records)-1]
	if lastRecord.Result == models.Approve {
		node, err := s.getNode(lastRecord.Index)
		if err != nil {
			return models.Processing, err
		}
		s.current = node.Next()
		if s.current == nil {
			// 表示流程已经结束
			err := s.finish(ctx)
			if err != nil {
				return models.Processing, err
			}
			return models.Finished, nil
		} else {
			// 更新instance
			s.instance.CurrentIndex = s.current.GetIndex()
			err := examineFlowInstanceDao.UpdateInstance(db, s.instance)
			if err != nil {
				return models.Processing, err
			}
		}
	} else {
		// 串行流程遇到拒绝记录直接结束整个流程
		err := s.reject(ctx)
		if err != nil {
			return models.Processing, err
		}
		return models.Rejected, nil
	}
	return models.Processing, nil
}

func (s *SerialFlow) finish(ctx context.Context) error {
	// 修改状态为finish
	s.instance.Status = models.Finished
	err := examineFlowInstanceDao.UpdateInstance(db, s.instance)
	if err != nil {
		return err
	}
	// 异步执行回调方法
	go func() {
		flowName := s.instance.Flow.Name
		// 获取flow的回调方法
		var token string
		if v := ctx.Value("token"); v != nil {
			if t, ok := v.(string); ok {
				token = t
			}
		}
		flow := flowConfig.registerFlow[flowName]
		for _, callback := range flow.Callbacks {
			if callback.Status == models.Finished {

				req := &models.HttpCallRequest{
					HttpInfo: s.instance.HttpInfo,
					Token:    token,
				}
				res := callback.Callback(req)
				if res.Error() != nil {
					stdlog.WithError(res.Error()).Errorf("flow [%d] http callback failed.", s.instance.ID)
					// 回调失败
					s.instance.CallBackResults = append(s.instance.CallBackResults, &models.CallbackResult{
						Status:  models.Failed,
						Message: res.Error().Error(),
					})
				} else {
					stdlog.Infof("http response message: %s", res.Message())
					// 回调成功
					s.instance.CallBackResults = append(s.instance.CallBackResults, &models.CallbackResult{
						Status:  models.Success,
						Message: res.Message(),
					})
				}
			}
		}
		// 更新instance
		err := examineFlowInstanceDao.UpdateInstance(db, s.instance)
		if err != nil {
			stdlog.WithError(err).Errorf("update instance after finish callback error.")
		}
	}()
	return nil
}

func (s *SerialFlow) reject(ctx context.Context) error {
	// 修改状态为rejected
	s.instance.Status = models.Rejected
	err := examineFlowInstanceDao.UpdateInstance(db, s.instance)
	if err != nil {
		return err
	}
	// 异步执行回调方法
	go func() {
		flowName := s.instance.Flow.Name
		// 获取flow的回调方法
		var token string
		if v := ctx.Value("token"); v != nil {
			if t, ok := v.(string); ok {
				token = t
			}
		}
		flow := flowConfig.registerFlow[flowName]
		for _, callback := range flow.Callbacks {
			if callback.Status == models.Rejected {
				req := &models.HttpCallRequest{
					HttpInfo: s.instance.RejectHttpInfo,
					Token:    token,
				}
				if req.HttpInfo == nil {
					continue
				}
				res := callback.Callback(req)
				if res.Error() != nil {
					stdlog.WithError(res.Error()).Errorf("flow [%d] http callback failed.", s.instance.ID)
					// 回调失败
					s.instance.CallBackResults = append(s.instance.CallBackResults, &models.CallbackResult{
						Status:  models.Failed,
						Message: res.Error().Error(),
					})
				} else {
					stdlog.Infof("http response message: %s", res.Message())
					// 回调成功
					s.instance.CallBackResults = append(s.instance.CallBackResults, &models.CallbackResult{
						Status:  models.Success,
						Message: res.Message(),
					})
				}
			}
		}
		// 更新instance
		err := examineFlowInstanceDao.UpdateInstance(db, s.instance)
		if err != nil {
			stdlog.WithError(err).Errorf("update instance after reject callback error.")
		}
	}()
	return nil
}

func (s *SerialFlow) getNode(index int32) (Node, error) {
	for _, node := range s.Nodes {
		if node.GetIndex() == index {
			return node, nil
		}
	}
	return nil, fmt.Errorf("node with index [%d] not exist", index)
}

// BaseNode 基础节点
type BaseNode struct {
	*models.ExamineNode
	flow    Flow
	prev    Node
	next    Node
	records []*models.ExamineRecord
}

func (n *BaseNode) Config() *models.ExamineNode {
	return n.ExamineNode
}

func (n *BaseNode) ToNodeInfo() *models.NodeInfo {
	return &models.NodeInfo{
		Index: n.Index,
		Node:  n.ExamineNode,
		Result: func() models.ExamineResult {
			if len(n.records) == 0 {
				return models.Waiting
			} else {
				return n.records[len(n.records)-1].Result
			}
		}(),
		Records: n.records,
	}
}

func (n *BaseNode) Check(ctx context.Context) error {
	// 刷新节点的记录，姑且认为flow中的records是最新的
	n.records = make([]*models.ExamineRecord, 0)
	for _, record := range n.flow.Records() {
		if record.Index == n.Index {
			n.records = append(n.records, record)
		}
	}
	return nil
}

func (n *BaseNode) Next() Node {
	return n.next
}

func (n *BaseNode) Prev() Node {
	return n.prev
}

func (n *BaseNode) GetIndex() int32 {
	return n.Index
}

func (n *BaseNode) Approve(ctx context.Context, user, message string) error {
	result, err := n.CheckAuth(ctx, user)
	if err != nil {
		return err
	}
	if !result {
		// 没有权限
		return fmt.Errorf("user cannot aprove this node because of invalid role request")
	}
	// 新增一条当前节点的通过记录
	record := &models.ExamineRecord{
		InstanceID: n.flow.Instance().ID,
		Index:      n.Index,
		Result:     models.Approve,
		Message:    message,
		Creator:    user,
		ProjectId:  n.flow.Instance().ProjectId,
	}
	var tx *gorm.DB
	if v := ctx.Value("transaction"); v == nil {
		tx = db
	} else {
		tx = v.(*gorm.DB)
	}
	err = examineRecordDao.CreateRecord(tx, record)
	if err != nil {
		return err
	}
	n.records = append(n.records, record)
	// 执行回调函数
	// FIXME 姑且先用同步，后续需要修改成更合适的异步调用方式
	stdlog.Infof("flow [%d] begin to call back node function", n.flow.Instance().ID)
	for _, callback := range n.Callbacks {
		if callback.Result == models.Approve {
			req := &models.HttpCallRequest{
				HttpInfo: n.flow.Instance().HttpInfo,
			}
			res := callback.Callback(req)
			if res.Error() != nil {
				return stderr.Internal.Cause(res.Error(), "flow [%d] node index [%d] http callback failed.", n.flow.Instance().ID, n.Index)
			} else {
				stdlog.Infof("http response message: %s", res.Message())
			}
		}
	}
	return nil
}

func (n *BaseNode) Reject(ctx context.Context, user, message string) error {
	result, err := n.CheckAuth(ctx, user)
	if err != nil {
		return err
	}
	if !result {
		// 没有权限
		return fmt.Errorf("user cannot aprove this node because of invalid role request")
	}
	// 新增一条当前节点的拒绝记录
	record := &models.ExamineRecord{
		InstanceID: n.flow.Instance().ID,
		Index:      n.Index,
		Result:     models.Reject,
		Message:    message,
		Creator:    user,
		ProjectId:  n.flow.Instance().ProjectId,
	}
	var tx *gorm.DB
	if v := ctx.Value("transaction"); v == nil {
		tx = db
	} else {
		tx = v.(*gorm.DB)
	}
	err = examineRecordDao.CreateRecord(tx, record)
	if err != nil {
		return err
	}
	n.records = append(n.records, record)
	// 执行回调函数
	// FIXME 姑且先用同步，后续需要修改成更合适的异步调用方式
	stdlog.Infof("flow [%d] begin to call back node function", n.flow.Instance().ID)
	for _, callback := range n.Callbacks {
		if callback.Result == models.Reject {
			req := &models.HttpCallRequest{
				HttpInfo: n.flow.Instance().HttpInfo,
			}
			res := callback.Callback(req)
			if res.Error() != nil {
				return stderr.Internal.Cause(res.Error(), "flow [%d] node index [%d] http callback failed.", n.flow.Instance().ID, n.Index)
			} else {
				stdlog.Infof("http response message: %s", res.Message())
			}
		}
	}
	return nil
}

func (n *BaseNode) CheckAuth(ctx context.Context, user string) (bool, error) {
	userRoles, roleMap, err := checkUserRole(n.flow.Instance().ProjectId, user)
	if err != nil {
		return false, err
	}
	return checkAuth(n.flow.Instance().ProjectId, userRoles, roleMap, n.RoleRequest), nil
}

func checkUserRole(projectID, user string) ([]*models.UserRole, map[uint64]*models.Role, error) {
	// 获取角色信息
	userRoles, err := rbac.GetUserRoles(db, []string{user}, models.UserType, projectID)
	if err != nil {
		return nil, nil, err
	}
	roleIds := make([]uint64, 0)
	for _, userRole := range userRoles {
		roleIds = append(roleIds, userRole.RoleId)
	}
	roles, err := rbac.GetRolesByRoleIds(db, roleIds)
	if err != nil {
		return nil, nil, err
	}
	roleMap := make(map[uint64]*models.Role)
	for _, role := range roles {
		roleMap[role.Id] = role
	}
	return userRoles, roleMap, nil
}

func checkAuth(projectID string, userRoles []*models.UserRole, roleMap map[uint64]*models.Role, roleReqs []*models.RoleRequest) bool {
	for _, role := range roleMap {
		if role.Name == models.SuperAdministrator.String() {
			return true
		}
	}
	// 判断是否满足role request
	if len(roleReqs) == 0 {
		// 不需要权限，所有人都有权限
		return true
	}
	for _, roleReq := range roleReqs {
		if roleReq.Type == models.RoleRequestAssets {
			// 代表是公共空间角色需求，需要有公共空间相应角色
			for _, userRole := range userRoles {
				if userRole.ProjectId == "assets" && roleMap[userRole.RoleId].Name == roleReq.RoleName {
					return true
				}
			}
		} else if roleReq.Type == models.RoleRequestProject {
			// 代表是对应空间的角色需求
			for _, userRole := range userRoles {
				if userRole.ProjectId == projectID && roleMap[userRole.RoleId].Name == roleReq.RoleName {
					return true
				}
			}
		}
	}
	// 全都不满足
	return false
}
