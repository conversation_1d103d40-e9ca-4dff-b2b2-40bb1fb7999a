package examine

import (
	"context"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"sync"
	"transwarp.io/applied-ai/aiot/vision-std/examine"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/dao/project"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type ExamineFlowService interface {
	CreateFlow(projectID string, req *examine.CreateExamineFlowReq) (*models.FlowInfo, error)
	Approve(req *models.NodeReq) (*models.FlowInfo, error)
	Reject(req *models.NodeReq) (*models.FlowInfo, error)
	ListFlows(projectID string, req *models.ListFlowReq) ([]*models.FlowInfo, error)
	GetFlow(id uint, user string) (*models.FlowInfo, error)
	Discard(id uint) error
	Withdraw(id uint, user string) (*models.FlowInfo, error)
	CountFlows(projectID string, req *models.ListFlowReq) (int, error)
}

var (
	efs     ExamineFlowService
	efsOnce sync.Once
)

func GetExamineFlowService(db *gorm.DB) ExamineFlowService {
	efsOnce.Do(func() {
		service := &examineFlowService{
			db:      db.Session(&gorm.Session{NewDB: true}),
			flowMap: make(map[uint]Flow),
		}
		service.initProcessingFlows()
		efs = service
	})
	return efs
}

func (e *examineFlowService) initProcessingFlows() error {
	flows, err := examineFlowInstanceDao.ListInstances(e.db, "")
	if err != nil {
		return err
	}
	for _, flow := range flows {
		if flow.Status == models.Processing {
			serialFlow := NewSerialFlow(flow)
			_, err := serialFlow.Check(context.Background())
			if err != nil {
				stdlog.WithError(err).Errorf("Init flows: check flow instance [%d] error.", flow.ID)
			} else {
				e.flowMap[flow.ID] = serialFlow
			}
		}
	}
	return nil
}

type examineFlowService struct {
	sync.Mutex
	db      *gorm.DB
	flowMap map[uint]Flow
}

func (e *examineFlowService) CreateFlow(projectID string, req *examine.CreateExamineFlowReq) (*models.FlowInfo, error) {
	// 创建一个instance
	flowName := req.Config.FlowName
	flow, ok := flowConfig.GetFlowByName(flowName)
	if !ok {
		return nil, stderr.Internal.Errorf("Flow name [%s] not registered.", flowName)
	}
	// 获取空间名称
	p, err := project.GetProjectById(e.db, projectID)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "Get project name error.")
	}
	instance := &models.ExamineFlowInstance{
		Flow:           flow,
		OrderNumber:    e.generateOrderNumber(flow),
		Object:         req.Object,
		Module:         req.Module,
		Binding:        req.Config,
		CurrentIndex:   1,
		Status:         models.Draft,
		Detail:         req.Detail,
		Creator:        req.User,
		HttpInfo:       req.HttpInfo,
		RejectHttpInfo: req.RejectHttpInfo,
		ProjectId:      projectID,
		ProjectName:    p.Name,
	}
	err = examineFlowInstanceDao.CreateInstance(e.db, instance)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "create flow instance error. detail: %s", req.Detail)
	}
	// 生成Flow
	serialFlow := NewSerialFlow(instance)
	e.flowMap[instance.ID] = serialFlow
	// 直接开始流程
	err = serialFlow.Start(context.Background())
	if err != nil {
		return nil, stderr.Internal.Cause(err, "start flow [%d] error.", serialFlow.instance.ID)
	}
	stdlog.Infof("flow [%d] started.", instance.ID)
	status, err := serialFlow.Check(context.Background())
	if err != nil {
		return nil, stderr.Internal.Cause(err, "check flow [%d] error.", serialFlow.instance.ID)
	}
	stdlog.Infof("flow [%d] status is [%s] after check.", instance.ID, status)
	return serialFlow.ToFlowInfo(), nil
}

func (e *examineFlowService) Approve(req *models.NodeReq) (*models.FlowInfo, error) {
	var (
		flow Flow
		ok   bool
		err  error
	)
	if flow, ok = e.flowMap[req.ID]; !ok {
		flow, err = e.getSerialFlow(req.ID)
		if err != nil {
			return nil, stderr.Internal.Cause(err, "get flow [%d] error.", req.ID)
		}
	}
	if err := e.checkFlow(flow); err != nil {
		return nil, err
	}
	err = flow.Current().Approve(context.Background(), req.User, req.Message)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "approve flow [%d] error.")
	}
	var ctx context.Context
	if flow.Instance().HttpInfo != nil {
		ctx = context.WithValue(context.Background(), "token", flow.Instance().HttpInfo.Token)
	} else {
		ctx = context.Background()
	}
	status, err := flow.Check(ctx)
	stdlog.Infof("flow [%d] status is [%s] after check.", flow.Instance().ID, status)
	return flow.ToFlowInfo(), nil

}

func (e *examineFlowService) Reject(req *models.NodeReq) (*models.FlowInfo, error) {
	var (
		flow Flow
		ok   bool
		err  error
	)
	if flow, ok = e.flowMap[req.ID]; !ok {
		flow, err = e.getSerialFlow(req.ID)
		if err != nil {
			return nil, stderr.Internal.Cause(err, "get flow [%d] error.", req.ID)
		}
	}
	if err := e.checkFlow(flow); err != nil {
		return nil, err
	}
	err = flow.Current().Reject(context.Background(), req.User, req.Message)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "approve flow [%d] error.")
	}
	var ctx context.Context
	if flow.Instance().RejectHttpInfo != nil {
		ctx = context.WithValue(context.Background(), "token", flow.Instance().RejectHttpInfo.Token)
	} else {
		ctx = context.Background()
	}
	status, err := flow.Check(ctx)
	stdlog.Infof("flow [%d] status is [%s] after check.", flow.Instance().ID, status)
	return flow.ToFlowInfo(), nil
}

func (e *examineFlowService) checkFlow(flow Flow) error {
	if flow.Current() == nil {
		return stderr.Internal.Errorf("flow [%d] is not in process", flow.Instance().ID)
	}
	if flow.Instance().Status != models.Processing {
		return stderr.Internal.Errorf("flow [%d] is not in process", flow.Instance().ID)
	}
	return nil
}

func (e *examineFlowService) ListFlows(projectID string, req *models.ListFlowReq) ([]*models.FlowInfo, error) {
	instances, err := examineFlowInstanceDao.ListInstances(e.db, projectID)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "List project instances error.")
	}
	if len(req.Types) == 0 {
		// 代表返回全部
		infos := make([]*models.FlowInfo, 0)
		for _, instance := range instances {
			infos = append(infos, instance.ToDetail())
		}
		return infos, nil
	} else {
		typeSet := make(map[models.ListType]struct{})
		for _, t := range req.Types {
			typeSet[t] = struct{}{}
		}
		infoMap := make(map[uint]*models.FlowInfo)
		instanceMap := make(map[uint]*models.ExamineFlowInstance)
		for _, instance := range instances {
			instanceMap[instance.ID] = instance
			if _, ok := typeSet[models.ListTypeCreated]; ok {
				if instance.Creator == req.User {
					infoMap[instance.ID] = instance.ToDetail()
				}
			}
			if _, ok := typeSet[models.ListTypeWaiting]; ok {
				if instance.Status == models.Processing {
					current := instance.CurrentIndex
					var currentNode *models.ExamineNode
					for _, node := range instance.Flow.Nodes {
						if node.Index == current {
							currentNode = node
						}
					}
					userRoles, roleMap, err := checkUserRole(instance.ProjectId, req.User)
					if err != nil {
						return nil, stderr.Internal.Cause(err, "check user role error.")
					}
					if checkAuth(instance.ProjectId, userRoles, roleMap, currentNode.RoleRequest) {
						infoMap[instance.ID] = instance.ToDetail()
					}
				}
			}
		}
		// 已处理的实例从record获取
		if _, ok := typeSet[models.ListTypeProceeded]; ok {
			records, err := examineRecordDao.ListRecordsByUser(e.db, req.User)
			if err != nil {
				return nil, stderr.Internal.Cause(err, "list user records error.")
			}
			for _, record := range records {
				if record.Index != 1 { // 忽略发起节点的记录
					if instance, ok := instanceMap[record.InstanceID]; ok {
						infoMap[instance.ID] = instance.ToDetail()
					}
				}
			}
		}
		result := make([]*models.FlowInfo, 0)
		for _, info := range infoMap {
			result = append(result, info)
		}
		return result, nil
	}
}

func (e *examineFlowService) GetFlow(id uint, user string) (*models.FlowInfo, error) {
	flow, ok := e.flowMap[id]
	var flowInfo *models.FlowInfo
	var err error
	if ok {
		flowInfo = flow.ToFlowInfo()
	} else {
		flow, err = e.getSerialFlow(id)
		if err != nil {
			return nil, err
		}
		flowInfo = flow.ToFlowInfo()
	}
	if flow.Current() != nil && flow.Instance().Status == models.Processing {
		permission, err := flow.Current().CheckAuth(context.Background(), user)
		if err != nil {
			return nil, stderr.Internal.Cause(err, "Check flow [%d] auth error.", id)
		}
		flowInfo.Permission = permission
	}
	return flowInfo, nil
}

func (e *examineFlowService) getSerialFlow(id uint) (*SerialFlow, error) {
	instance, err := examineFlowInstanceDao.GetInstance(e.db, id)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "get instance by id [%d] error", id)
	}
	flow := NewSerialFlow(instance)
	e.flowMap[id] = flow
	_, err = flow.Check(context.Background())
	if err != nil {
		return nil, stderr.Internal.Cause(err, "check flow [%d] error.", id)
	}
	return flow, nil
}

func (e *examineFlowService) Discard(id uint) error {
	//TODO implement me
	panic("implement me")
}

func (e *examineFlowService) CountFlows(projectID string, req *models.ListFlowReq) (int, error) {
	// 姑且先返回list的数量
	flows, err := e.ListFlows(projectID, req)
	if err != nil {
		return 0, err
	}
	return len(flows), nil
}

func (e *examineFlowService) Withdraw(id uint, user string) (*models.FlowInfo, error) {
	//TODO implement me
	panic("implement me")
}

func (e *examineFlowService) generateOrderNumber(flow *models.ExamineFlow) string {
	//abbr := flow.Abbr
	//date := time.Now().Format("20060502")
	// FIXME 姑且先用uuid
	return uuid.NewString()
}
