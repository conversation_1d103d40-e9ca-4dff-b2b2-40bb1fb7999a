package examine

import (
	"fmt"
	"strings"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/examine/flows"
)

var (
	flowConfig *FlowConfig
	// 所有的flow配置
	flowList = []*models.ExamineFlow{
		flows.BaseProjectFlow,
		flows.ProjectAssetsFlow,
		flows.AdminProjectAssetsFlow,
		flows.ArticleAssetsFlow,
		flows.ServingDeployFlow,
		flows.CorpusAssetsFlow,
		flows.FileAssetsFlow,
		flows.TableDedupsFlow,
	}
)

func InitFlowConfig() {
	flowConfig = &FlowConfig{
		registerFlow: make(map[string]*models.ExamineFlow),
	}
	err := registerFlows()
	if err != nil {
		panic(err)
	}
}

func registerFlows() error {
	for _, flow := range flowList {
		err := flowConfig.Register(flow)
		if err != nil {
			return err
		}
	}
	return nil
}

// FlowConfig 流程配置
type FlowConfig struct {
	registerFlow map[string]*models.ExamineFlow
}

func (f *FlowConfig) GetFlowByName(flowName string) (*models.ExamineFlow, bool) {
	flow, ok := f.registerFlow[flowName]
	return flow, ok
}

func (f *FlowConfig) Register(flow *models.ExamineFlow) error {
	err := f.check(flow)
	if err != nil {
		return err
	}
	f.registerFlow[flow.Name] = flow
	return nil
}

func (f *FlowConfig) check(flow *models.ExamineFlow) error {
	// 是否存在同名flow
	if _, ok := f.registerFlow[flow.Name]; ok {
		return fmt.Errorf("flow [%s] is duplicated", flow.Name)
	}
	// 缩写格式
	if err := f.checkAbbr(flow); err != nil {
		return err
	}
	// 检查node规范
	if err := f.checkNode(flow); err != nil {
		return err
	}
	return nil
}

func (f *FlowConfig) checkAbbr(flow *models.ExamineFlow) error {
	if len(flow.Abbr) == 2 && strings.ToUpper(flow.Abbr) == flow.Abbr {
		// 校验重复
		exist := false
		for _, registered := range f.registerFlow {
			if registered.Abbr == flow.Abbr {
				exist = true
			}
		}
		if exist {
			return fmt.Errorf("flow abbr [%s] is duplicated", flow.Abbr)
		} else {
			return nil
		}
	} else {
		return fmt.Errorf("flow abbr [%s] is not compliant(tow capital letters)", flow.Abbr)
	}
}

func (f *FlowConfig) checkNode(flow *models.ExamineFlow) error {
	nodes := flow.Nodes
	_, err := f.createCheckingNodes(nodes)
	if err != nil {
		return err
	}
	return nil
}

func (f *FlowConfig) createCheckingNodes(nodes []*models.ExamineNode) (*checkingNode, error) {
	nodeMap := make(map[int32]*checkingNode)
	for _, node := range nodes {
		if _, ok := nodeMap[node.Index]; ok {
			return nil, fmt.Errorf("node index [%d] is duplicated", node.Index)
		}
		nodeMap[node.Index] = &checkingNode{
			index: node.Index,
		}
	}
	for _, node := range nodes {
		current := nodeMap[node.Index]
		if node.PrevIndex > 0 {
			current.prev = nodeMap[node.PrevIndex]
			if current.prev == nil {
				return nil, fmt.Errorf("node index [%d] with not exist prev index [%d]", node.Index, node.PrevIndex)
			}
		}
		if node.NextIndex > 0 {
			current.next = nodeMap[node.NextIndex]
			if current.next == nil {
				return nil, fmt.Errorf("node index [%d] with not exist next index [%d]", node.Index, node.NextIndex)
			}
		}
	}
	// 检查是否有多个开始节点
	count := 0
	var start *checkingNode
	for _, node := range nodeMap {
		if node.prev == nil {
			count++
			start = node
		}
	}
	if count > 1 {
		return nil, fmt.Errorf("flow has more than 1 start node ([%d] start node found)", count)
	}
	// 检查开始节点index是否为1
	if start.index != 1 {
		return nil, fmt.Errorf("start node index should be 1")
	}
	// 检查是否有环
	var fast, slow *checkingNode
	fast = start
	slow = start
	cyclic := false
	for {
		fast = fast.next
		if fast == nil {
			break
		} else {
			fast = fast.next
		}
		if fast == nil {
			break
		}
		slow = slow.next
		if fast.index == slow.index {
			cyclic = true
			break
		}
	}
	if cyclic {
		return nil, fmt.Errorf("flow is cyclic")
	}
	return start, nil
}

type checkingNode struct {
	index int32
	next  *checkingNode
	prev  *checkingNode
}
