package monitor

import (
	"context"
	"fmt"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/central-auth-service/clients"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models/monitor"

	"transwarp.io/applied-ai/grafana-openapi-client-go/client/ds"
	gfmodels "transwarp.io/applied-ai/grafana-openapi-client-go/models"
)

const (
	SvcLabelSelector = "{ref_id='{{.ServiceId}}', ref_version_id=~'{{.VersionIds}}', ref_type='service'}"
)

type GrafanaDatasource struct {
	Type string `json:"type"`
	Uid  string `json:"uid"`
}

func queryMetrics(ctx context.Context, mr *gfmodels.MetricRequest) (ret gfmodels.Responses, err error) {
	err = mr.Validate(nil)
	if err != nil {
		return nil, stderr.Wrap(err, "validate MetricRequest[%v]", mr)
	}
	ok, _, err := clients.GrafanaCli.Ds.QueryMetricsWithExpressionsWithParams(&ds.QueryMetricsWithExpressionsParams{Body: mr, Context: ctx}, nil)
	if err != nil {
		return nil, stderr.Wrap(err, "GrafanaCli QueryMetricsWithExpressions")
	}
	ret = ok.Payload.Results
	return ret, err
}

func (ms *MonitorService) makePanelMetricRequest(r *PanelQueryRequest, data helper.QueryContext) (*gfmodels.MetricRequest, error) {
	from, to, panel := r.From, r.To, r.Panel
	queries := make([]gfmodels.JSON, 0, len(panel.Metrics))
	for _, ref := range panel.Metrics {
		m := ms.metricsMp[ref.Id]
		if m == nil {
			// return nil, fmt.Errorf("makePanelMetricRequest:metric [%s] not found", ref.Id)
			// 如果指标不在配置里，可能是自定义指标，直接使用指标名称和默认label筛选器的形式查询
			m = &monitor.Metric{
				Id:   ref.Id,
				Name: ref.Name,
				Query: map[string]any{
					"legendFormat": "{{ref_version_id}}",
					"expr":         fmt.Sprintf("%s%s", ref.Id, SvcLabelSelector),
				},
			}
		}
		q := utils.DeepCopy(m.Query)
		// render expr
		expr, err := m.RenderExpr(data)
		if err != nil {
			return nil, err
		}
		q["expr"] = expr

		// set interval
		q["interval"] = ""
		q["intervalMs"] = r.Interval
		q["intervalFactor"] = 1
		// set datasource
		q["datasource"] = ms.defaultDatasource
		q["maxDataPoints"] = 1000
		q["refId"] = m.Id
		queries = append(queries, q)
	}
	req := &gfmodels.MetricRequest{
		Queries: queries,
		From:    &from,
		To:      &to,
	}
	return req, nil
}

func (ms *MonitorService) makeQueryJSON(expr, refId string) gfmodels.JSON {
	q := make(map[string]any)
	q["expr"] = expr
	// set interval
	q["interval"] = ""
	q["intervalMs"] = 5000
	q["intervalFactor"] = 1
	// set datasource
	q["datasource"] = ms.defaultDatasource
	q["maxDataPoints"] = 100
	q["refId"] = refId
	return q
}

func (ms *MonitorService) listCustomMetricsNames(ctx context.Context, q *SvcQueryRequest) (names []string, err error) {
	expr := fmt.Sprintf("{ref_metrics_origin='custom', ref_id='%s', ref_version_id=~'%s'}", q.ServiceId, q.VersionIds)
	req := &gfmodels.MetricRequest{
		Queries: []gfmodels.JSON{ms.makeQueryJSON(expr, "A")},
		From:    &q.From,
		To:      &q.To,
	}

	rsp, err := queryMetrics(ctx, req)
	if err != nil {
		return nil, stderr.Wrap(err, "listCustomMetricsNames: queryMetrics", rsp)
	}

	if _, ok := rsp["A"]; !ok {
		return nil, fmt.Errorf("listCustomMetricsNames: queryMetrics: no data found")
	}

	frames := rsp["A"].Frames
	if len(frames) == 1 && len(frames[0].Data.Values) == 0 { // no data
		return
	}

	nameSet := make(map[string]struct{}) // 指标名称去重
	for _, frame := range frames {
		if len(frame.Schema.Fields) != 2 {
			return nil, fmt.Errorf("listCustomMetricsNames: queryMetrics: invalid frame schema")
		}
		metricName := frame.Schema.Fields[1].Name
		nameSet[metricName] = struct{}{}
	}
	for name := range nameSet {
		names = append(names, name)
	}
	return
}
