package monitor

import (
	"strconv"
)

type GlobalPanelType int32

const (
	// GlobalPanelTypeGpuOverview @gotags: description:"GPU资源概览"
	GlobalPanelTypeGpuOverview GlobalPanelType = 0

	// GlobalPanelTypeGpuTRENDS @gotags: description:"GPU资源使用趋势"
	GlobalPanelTypeGpuTRENDS GlobalPanelType = 1

	// GlobalPanelTypeBaseOverview @gotags: description:"基础资源使用总揽"
	GlobalPanelTypeBaseOverview GlobalPanelType = 2

	// GlobalPanelTypeBaseTrend  @gotags: description:"基础资源使用趋势"
	GlobalPanelTypeBaseTrend GlobalPanelType = 3

	// GlobalPanelTypeBaseRanking  @gotags: description:"基础资源使用排行"
	GlobalPanelTypeBaseRanking GlobalPanelType = 4

	// GlobalPanelTypeServiceRanking  @gotags: description:"服务资源使用排行"
	GlobalPanelTypeServiceRanking GlobalPanelType = 5

	// GlobalPanelTypeResourceUsage  @gotags: description:"单个显卡资源使用情况"
	GlobalPanelTypeResourceUsage GlobalPanelType = 6

	// GlobalPanelTypeSvcOverview  @gotags: description:"单个显卡资源使用情况"
	GlobalPanelTypeSvcOverview GlobalPanelType = 7

	// GlobalPanelTypeSvcList  @gotags: description:"服务列表按照设备id查询"
	GlobalPanelTypeSvcList GlobalPanelType = 8

	// GlobalGpuUsed  @gotags: description:"gpu使用总和"
	GlobalGpuUsed GlobalPanelType = 9

	// GlobalNpuInfo  @gotags: description:"npu信息"
	GlobalNpuInfo GlobalPanelType = 10
)

var defaultQuery = QueryRequest{
	From:     "now-5s",
	To:       "now",
	Interval: 10000,
	Step:     1,
}

var (
	defaultStep = 21600000
)

func (t GlobalPanelType) GetGlobalReq(from, to, step string) *GlobalBaseQueryRequest {
	req := &GlobalBaseQueryRequest{
		Provider:        ".*",
		NamespaceName:   ".*",
		Node:            ".*",
		DeviceType:      ".*",
		GlobalPanelType: t,
		RefName:         ".*",
		DeviceUUID:      ".*",
		TopK:            5,
		Service:         ".*",
		Namespace:       ".*",
	}

	switch t {
	case GlobalPanelTypeGpuOverview, GlobalPanelTypeBaseOverview, GlobalPanelTypeResourceUsage,
		GlobalPanelTypeSvcOverview, GlobalPanelTypeSvcList, GlobalGpuUsed, GlobalNpuInfo:
		req.QueryRequest = defaultQuery
	default:
		stepInt, err := strconv.Atoi(step)
		if err != nil {
			req.QueryRequest = defaultQuery
			return req
		}
		req.QueryRequest = QueryRequest{
			From: from,
			To:   to,
			Step: stepInt,
		}
	}

	return req
}

// GetDefaultStep set 6 hours for guage
func (t GlobalPanelType) GetDefaultStep() int {
	return defaultStep
}
