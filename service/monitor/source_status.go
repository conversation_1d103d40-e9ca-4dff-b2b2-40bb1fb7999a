package monitor

type SourceType string

const (
	SourceTypeSOURCE_TYPE_UNKNOW SourceType = "SOURCE_TYPE_UNKNOW"

	SourceTypeSOURCE_TYPE_MODEL_CUBE SourceType = "SOURCE_TYPE_MODEL_CUBE"

	SourceTypeSOURCE_TYPE_APP_CUBE SourceType = "SOURCE_TYPE_APP_CUBE"

	SourceTypeSOURCE_TYPE_VLAB SourceType = "SOURCE_TYPE_VLAB"

	SourceTypeSOURCE_TYPE_CUSTOM SourceType = "SOURCE_TYPE_CUSTOM"

	SourceTypeSOURCE_TYPE_REMOTE SourceType = "SOURCE_TYPE_REMOTE"

	SourceTypeSOURCE_TYPE_KNOWLEDGE SourceType = "SOURCE_TYPE_KNOWLEDGE"
)

// Enum value maps for SourceType.
var (
	SourceTypeEnum = map[int32]string{
		0:  "SOURCE_TYPE_UNKNOW",
		1:  "SOURCE_TYPE_MODEL_CUBE",
		2:  "SOURCE_TYPE_APP_CUBE",
		3:  "SOURCE_TYPE_VLAB",
		11: "SOURCE_TYPE_CUSTOM",
	}
	SourceTypeZh = map[string]string{
		"SOURCE_TYPE_UNKNOW":     "未知",
		"SOURCE_TYPE_MODEL_CUBE": "模型服务",
		"SOURCE_TYPE_APP_CUBE":   "应用服务",
		"SOURCE_TYPE_VLAB":       "vlab",
		"SOURCE_TYPE_CUSTOM":     "自定义镜像",
		"SOURCE_TYPE_REMOTE":     "远程服务",
		"SOURCE_TYPE_KNOWLEDGE":  "知识库",
	}
)
