package monitor

import (
	"encoding/json"
	"fmt"
	"time"
	"transwarp.io/aip/llmops-common/pkg/crd"

	"transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/applied-ai/central-auth-service/models/monitor"
)

const (
	DefaultDatasourceType = "prometheus"
)

type ListSvcMetricsReq struct {
	ServiceId         string             `json:"service_id"`
	ServiceSourceType serving.SourceType `json:"service_source_type"`
}

type ListMetricsRsp struct {
	Metrics []*monitor.Metric `json:"metrics"`
}

type SvcQueryRequest struct {
	QueryRequest
	ServiceId         string                   `json:"service_id"`
	ServiceSourceType serving.SourceType       `json:"service_source_type"`
	VersionIds        string                   `json:"version_ids" description:"版本id列表, |分隔, 如v1|v2|v3, 空字符串表示所有版本"`
	DashboardTab      serving.DashboardTabType `json:"dashboard_tab"`
}

type GlobalBaseQueryRequest struct {
	QueryRequest
	NamespaceName   string          `json:"namespace_name"`
	Namespace       string          `json:"namespace"`
	Node            string          `json:"node"`
	Provider        string          `json:"provider"`
	DeviceType      string          `json:"devicetype"`
	TopK            int             `json:"topK"`
	DeviceUUID      string          `json:"deviceuuid"`
	RefName         string          `json:"ref_name"`
	GlobalPanelType GlobalPanelType `json:"global_panel_type"`
	Service         string          `json:"service"`
}

type QueryRequest struct {
	From     string `json:"from" description:"开始时间,毫秒时间戳 "`
	To       string `json:"to" description:"结束时间,毫秒时间戳"`
	Step     int    `json:"step" description:"聚合时间粒度(时间段长度), 毫秒"`
	Interval int    `json:"-" description:"数据点的查询时间间隔, 毫秒, 现在默认保持和step一致"`
}

func (r *QueryRequest) StepDuration() string {
	return fmt.Sprintf("%dms", r.Step)
}

func (r *QueryRequest) IntervalDuration() string {
	return fmt.Sprintf("%dms", r.Interval)
}

type SvcDashboardData struct {
	ServiceId string           `json:"service_id"`
	Panels    []*PanelWithData `json:"panels"`
}

type PanelWithData struct {
	monitor.Panel
	Queries map[string]*QueryData `json:"queries" description:"key为指标id, value为查询结果"`
}

// QueryData 对应一个指标的查询结果
type QueryData struct {
	Status   int64          `json:"status" description:"状态码, 200表示成功, 其他表示失败"`
	Times    []*json.Number `json:"times" description:"x轴数据, 毫秒时间戳"`
	Frames   []*Frame       `json:"frames"`
	ErrorMsg string         `json:"error_msg" description:"错误信息"`
}

type Frame struct {
	Values []*json.Number    `json:"values" description:"y轴数据, 指标值"`
	Legend string            `json:"legend" description:"图例名称"`
	Labels map[string]string `json:"labels"`
}

type PanelQueryRequest struct {
	QueryRequest
	Panel *monitor.Panel
}

// --- 全局算力大盘相关

type ProjectItem struct {
	ProjectId string `json:"project_id" description:"项目id"`
	Name      string `json:"name" description:"项目名称"`
}

type DeviceTypeItem struct {
	Provider   string `json:"provider" description:"GPU厂商类型"`
	DeviceType string `json:"device_type" description:"GPU卡类型"`
}

type GlobalGPUOptions struct {
	Projects  ProjectItems        `json:"projects" description:"项目选项列表"`
	Nodes     []string            `json:"nodes" description:"节点选项列表"`
	Providers map[string][]string `json:"providers" description:"GPU厂商类型选项列表"`
	Status    []string            `json:"status" description:"GPU状态选项列表"`
}
type ProjectItems struct {
	Internal []ProjectItem `json:"internal" description:"内部项目"`
	External []ProjectItem `json:"external" description:"外部项目"`
}

type GlobalGPUFilter struct {
	ProjectIds map[string][]string `json:"projects" description:"项目Id筛选, 为空时查询全部, 下同"`
	Nodes      string              `json:"nodes" description:"节点筛选"`
	Providers  map[string][]string `json:"providers" description:"GPU厂商类型筛选"`
	DeviceUUID string              `json:"device_uuid" description:"GPU卡类型筛选"`
	Status     string              `json:"status" description:"GPU状态筛选"`
	From       int64               `json:"from" description:"开始时间"`
	To         int64               `json:"to" description:"结束时间"`
	Step       int64               `json:"step" description:"时间粒度"`
	Topk       int64               `json:"topk" description:"topk"`
	FilterType string              `json:"type" description:"排名的过滤类型"`
}

type RequestFilter struct {
	ProjectIds  []string `json:"projects" description:"项目Id筛选, 为空时查询全部, 下同"`
	Nodes       []string `json:"nodes" description:"节点筛选"`
	Providers   []string `json:"providers" description:"GPU厂商类型筛选"`
	DeviceTypes []string `json:"device_types" description:"GPU卡类型筛选"`
	Status      []string `json:"status" description:"GPU状态筛选"`
	From        string   `json:"from" description:"开始时间"`
	To          string   `json:"to" description:"结束时间"`
	Step        string   `json:"step" description:"时间粒度"`
	Topk        int      `json:"topk" description:"排名"`
	DeviceUUID  string   `json:"deviceuuid" description:"卡型号"`
	RefName     string   `json:"ref_name" description:"服务名字,模糊查询"`
	Service     string   `json:"service"`
}

type Item struct {
	Name  string  `json:"name"`
	Value float64 `json:"value"`
}
type GlobalGPUOverview struct {
	GPUTotal               int     `json:"gpu_total" description:"总GPU数量"`
	GPUUsed                int     `json:"gpu_used" description:"已使用GPU数量"`
	FullyLoadedGPUCount    int     `json:"fully_loaded_gpu_count" description:"满载GPU数量"`
	BottleneckGPUCount     int     `json:"bottleneck_gpu_count" description:"瓶颈GPU数量"`
	LoadedGPUCount         int     `json:"loaded_gpu_count" description:"负载GPU数量"`
	IdleGPUCount           int     `json:"idle_gpu_count" description:"空闲GPU数量"`
	GPUMemoryTotal         int64   `json:"gpu_memory_total" description:"GPU显存总量"`
	GPUMemoryAllocated     int64   `json:"gpu_memory_allocated" description:"GPUx显存已分配量"`
	GPUMemoryUsed          int64   `json:"gpu_memory_used" description:"GPU显存已使用"`
	GPUCoresTotal          int64   `json:"gpu_cores_total" description:"GPU算力(vcore)总量"`
	GPUCoresUsed           int64   `json:"gpu_cores_used" description:"GPU算力(vcore)已使用"`
	GPUVCoreAllocated      int64   `json:"gpu_vcore_allocated" description:"GPU算力已分配量"`
	ProjectDistribution    []*Item `json:"project_distribution" description:"GPU按项目分布"`
	NodeDistribution       []*Item `json:"node_distribution" description:"GPU按节点分布"`
	DeviceTypeDistribution []*Item `json:"device_type_distribution" description:"GPU按类型分布"`
}

type GlobalNodeGPUCardBase struct {
	Nodes map[string][]*GPUCard `json:"nodes"`
}

type GPUCard struct {
	GPUName        string        `json:"gpu_name" description:"gpu名称"`
	GPUMemoryUsage float64       `json:"gpu_memory_usage" description:"算力使用率（百分比）"`
	GPUCoresUsage  float64       `json:"gpu_cores_usage" description:"显存使用率（百分比）"`
	Status         crd.GPUStatus `json:"status" description:"状态（空闲、部分负载、资源瓶颈、满载）"`
	UUID           string        `json:"uuid" description:"GPU卡uuid"`
	GPUType        string        `json:"gpu_type" description:"GPU卡类型"`
	Node           string        `json:"node" description:"gpu卡节点"`
}

type GPUInfo struct {
	Detail GPUDetail `json:"gpu_detail"`
	Usage  GPUUsage  `json:"gpu_usage"`
}

type GPUUsage struct {
	GPUCoresAllocated  float64   `json:"gpu_cores_allocated" description:"算力分配"`
	GPUCoresUsed       float64   `json:"gpu_cores_used" description:"算力使用"`
	GPUMemoryAllocated float64   `json:"gpu_memory_allocated" description:"显存分配"`
	GPUMemoryUsed      float64   `json:"gpu_memory_used" description:"显存使用"`
	UpdateTime         time.Time `json:"update_time" description:"更新时间"`
}

type GPUDetail struct {
	GPUMemoryTotal int64           `json:"gpu_memory_total" description:"GPU显存总量"`
	GPUCoresTotal  int64           `json:"gpu_cores_total" description:"GPU算力(vcore)总量"`
	DeviceTypeItem *DeviceTypeItem `json:"device_type_item" description:"GPU卡类型"`
	Index          string          `json:"index" description:"index序号"`
	UUID           string          `json:"uuid" description:"GPU卡uuid"`
	Node           string          `json:"node" description:"所属节点"`
	Status         crd.GPUStatus   `json:"status" description:"状态（空闲、部分负载、资源瓶颈、满载）"`
	GPUName        string          `json:"gpu_name" description:"显卡名字"`
	GPUType        string          `json:"gpu_type" description:"GPU卡类型"`
}

type GlobalBaseOverview struct {
	CPUTotal     float64 `json:"cpu_total" description:"CPU总量"`
	CPUUsed      float64 `json:"cpu_used" description:"CPU已使用量"`
	MemoryTotal  float64 `json:"memory_total" description:"内存总量"`
	MemoryUsed   float64 `json:"memory_used" description:"内存已使用量"`
	StorageTotal float64 `json:"storage_total" description:"存储总量"`
	StorageUsed  float64 `json:"storage_used" description:"存储已使用量"`
}

type UsageRanking []*Item

type GlobalUsageRankings struct {
	GPUMem   UsageRanking `json:"gpu_mem" description:"GPU显存使用排名"`
	GPUCores UsageRanking `json:"gpu_cores" description:"GPU算力使用排名"`
	CPU      UsageRanking `json:"cpu" description:"CPU使用排名"`
	Memory   UsageRanking `json:"memory" description:"服务内存使用排名"`
	Storage  UsageRanking `json:"storage" description:"存储使用排名"`
}

// GlobalSvcOverview 全局服务监控大屏相关
type GlobalSvcOverview struct {
	TenantDistribution      []*Item `json:"tenant_distribution" description:"服务按项目分布"`
	ComputeTypeDistribution []*Item `json:"compute_type_distribution" description:"服务按算力类型分布"`
	ServiceTypeDistribution []*Item `json:"service_type_distribution" description:"服务按服务类型分布"`
	StatusDistribution      []*Item `json:"status_distribution" description:"服务按运行状态分布"`
}

type ServiceItem struct {
	Id          string       `json:"id" description:"服务id"`
	Name        string       `json:"name" description:"服务名称"`
	ProjectItem *ProjectItem `json:"projects" description:"项目选项列表"`
	Type        string       `json:"type" description:"服务类型"`
	Status      string       `json:"status" description:"服务状态"`
	Creator     string       `json:"creator" description:"创建者"`
	CreateTime  int32        `json:"create_time" description:"创建时间"`
	Pod         string       `json:"pod" description:"pod"`
	IsExternal  bool         `json:"is_external" description:"是否为外部服务"`
}

type ServiceList struct {
	Size     int64          `json:"size" description:"服务数量"`
	Services []*ServiceItem `json:"services" description:"服务列表"`
}

type GPUsed struct {
	GPUMemoryUsed int64 `json:"gpu_memory_used" description:"GPU显存总量"`
	GPUCoresUsed  int64 `json:"gpu_cores_used" description:"GPU算力(vcore)总量"`
}

type NPUInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name" description:"npu名字"`
	Namespace   string `json:"namespace" description:"namespace"`
	Node        string `json:"node" description:"所属节点"`
	NodeName    string `json:"node_name" description:"节点名字"`
	PCieBusInfo string `json:"pcie_bus_info" description:"pcie_bus_info"`
	VDieId      string `json:"vdie_id" description:"vdie_id"`
}
