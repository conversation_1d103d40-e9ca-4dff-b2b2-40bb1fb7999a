package monitor

import (
	"encoding/json"
	"testing"
)

func GenerateMockGlobalGPUOptions() *GlobalGPUOptions {
	return &GlobalGPUOptions{
		Projects: ProjectItems{
			Internal: []ProjectItem{
				{
					ProjectId: "assets",
					Name:      "公共空间",
				},
				{
					ProjectId: "default",
					Name:      "快速开始_演示demo",
				},
			},
		},
		Nodes: []string{
			"llm207",
			"llm206",
			"llm209",
		},
		Providers: map[string][]string{
			"NVIDIA": {
				"NVIDIA-NVIDIA A100-SXM",
				"NVIDIA-NVIDIA A100-SXM4-80GB",
			},
			"Ascend": {
				"Ascend310P",
			},
		},

		Status: []string{
			"Idle",
			"PartiallyLoaded",
			"ResourceBottleneck",
			"FullyLoaded",
		},
	}
}

func TestParseStatus(t *testing.T) {
	mockData := GenerateMockGlobalGPUOptions()

	// 将 mockData 转换为 JSON
	jsonBytes, _ := json.MarshalIndent(mockData, "", "    ")
	println(string(jsonBytes))
}
