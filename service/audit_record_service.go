package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"text/template"
	"time"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/auth"
	stdcli "transwarp.io/applied-ai/aiot/vision-std/clients"
	"transwarp.io/applied-ai/aiot/vision-std/database/influxdb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/clients"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/helper"
)

const (
	MeasurementName         = "AUDIT_RECORDS" // influxdb的表名
	RedisKeyNgxLogs         = "nginx_logs"
	MaxSizeListAuditRecords = 1000

	ReqBody      = "req_body"
	RespBody     = "resp_body"
	Headers      = "headers"
	QueryParams  = "query_params"
	MatchedPaths = "matched_paths"
	ProjectId    = "project_id"
	Module       = "module"
	SubModule    = "sub_module"
	OpType       = "op_type"
	User         = "user"
	Event        = "event"
	MatchedAPI   = "matched_api"
	ReqPath      = "req_path"
	Details      = "details"
	Time         = "time"
)

// example:
//
//	{
//	    "headers": {
//	        "x-forwarded-for": "************",
//	        "x-forwarded-port": "443",
//	        "connection": "upgrade",
//	        "user-agent": "Go-http-client\/1.1",
//	        "x-forwarded-proto": "https",
//	        "host": "**************:30745"
//	    },
//	    "query_args": {
//	        "timeout": "300s",
//	        "is_stream_infer": "true",
//	        "project_id": "default"
//	    },
//	    "resp_body": "",
//	    "timestamp": "1717063606.364",
//	    "remote_addr": "************",
//	    "status": "405",
//	    "method": "HEAD",
//	    "uri": "\/api\/v1\/mwh\/svcmgr\/services\/MWH-DEPLOYMENT-co0h0d3tls2rvgclket0\/infer"
//	}
type NgxLogEntry struct {
	Headers    map[string]string `json:"headers"`
	QueryArgs  map[string]any    `json:"query_args"`
	ReqBody    string            `json:"req_body"`
	RespBody   string            `json:"resp_body"`
	Timestamp  string            `json:"timestamp"`
	RemoteAddr string            `json:"remote_addr"`
	Status     string            `json:"status"`
	Method     string            `json:"method"`
	URI        string            `json:"uri"`
	APIModule  string            `json:"api_module"`
}

type NgxLogProcessor struct {
	rd         stdcli.RedisClient
	ifx        influxdb.InfluxWriter
	apiConfigs []*conf.AuditRecordAPIConfig
}

func (n *NgxLogProcessor) Init() {
	for _, a := range n.apiConfigs {
		if a.Regexp == nil {
			a.Regexp = regexp.MustCompile(a.APIPath)
		}
	}
}

func (n *NgxLogProcessor) StartLoop() {
	if conf.IsDevMode() {
		stdlog.Warnf("!!!! skipping deps (ngx log loop) initiation in development mode")
		return
	}
	go func() {
		for {
			if err := n.Handle(); err != nil {
				stdlog.Error(err)
				// time.Sleep(time.Minute)
			}
		}
	}()
}

func (n *NgxLogProcessor) Handle() error {
	// 阻塞直到读取到日志
	v, err := n.rd.BLPop(RedisKeyNgxLogs, time.Hour)
	if err != nil {
		return stderr.Wrap(err, "BLPop [key:%s]", RedisKeyNgxLogs)
	}

	entry := new(NgxLogEntry)
	if err := json.Unmarshal([]byte(v), entry); err != nil {
		return stderr.Wrap(err, "Unmarshal NgxLogEntry [value:%s]", v)
	}

	headers := entry.Headers
	authorization, ok := headers["authorization"]
	if !ok {
		return nil
	}
	jwt, err := auth.ParseToken(authorization)
	if err != nil {
		return nil
	}

	data := map[string]any{
		ReqBody:     entry.ReqBody,
		RespBody:    entry.RespBody,
		Headers:     headers,
		QueryParams: entry.QueryArgs,
	}

	api := matchEntryToAPI(entry, n.apiConfigs, data)
	if api == nil {
		return nil
	}

	details := map[string]string{
		ReqBody:      entry.ReqBody,
		RespBody:     entry.RespBody,
		Headers:      mustMarshal(headers),
		QueryParams:  mustMarshal(entry.QueryArgs),
		MatchedPaths: mustMarshal(data[MatchedPaths]),
	}

	// 默认API都通过query参数指定空间id
	var projectId string
	if v, ok := entry.QueryArgs[ProjectId]; ok {
		if vv, ok := v.(string); ok {
			projectId = vv
		}
	}

	if projectId == "" && api.SubModule == "SPACE_MEMBER_MANAGEMENT" { // 成员管理的projectId参数在请求路径中
		if paths, ok := data[MatchedPaths].([]string); ok && len(paths) > 1 {
			projectId = paths[1]
		}
	}

	var timeMills int64
	// nginx中的timestamp格式为1717064096.366
	timeMills, err = strconv.ParseInt(strings.Replace(entry.Timestamp, ".", "", 1), 10, 64)
	if err != nil {
		stdlog.Warnf("parse unix mill of ngx log, err:%v", err)
		timeMills = time.Now().UnixMilli()
	}

	event, err := render(api.GoTemplate, data, true)
	if err != nil {
		return err
	}

	r := &pb.AuditRecord{
		Module:     helper.StringToEnum[pb.AuditRecordModule]("AuditRecordModule_"+api.Module, pb.AuditRecordModule_value),
		SubModule:  helper.StringToEnum[pb.AuditRecordSubModule]("AuditRecordSubModule_"+api.SubModule, pb.AuditRecordSubModule_value),
		OpType:     helper.StringToEnum[pb.AuditRecordOperateType](api.OpType, pb.AuditRecordOperateType_value),
		Event:      event,
		User:       jwt.GetUsername(),
		TimeMills:  timeMills,
		MatchedApi: api.ToPb(),
		ReqPath:    entry.URI,
		Details:    details,
		ProjectId:  projectId,
	}

	n.ifx.Write(*record2Point(r))
	return nil

}

// 审计事件
type AuditRecordService struct {
	ifx *influxdb.InfluxClient
}

func (s *AuditRecordService) ListAuditRecords(ctx context.Context, req *pb.ListAuditRecordsReq) (rsp *pb.ListAuditRecordsRsp, err error) {

	q := influxdb.NewQueryInfluxReq(MeasurementName)
	if req.Limit < 0 {
		req.Limit = 0
	}
	q.Filter = &influxdb.InfluxFilter{Size: req.Limit}
	projId := req.UserContext.ProjectId
	if projId != "" && projId != "all" {
		q.AddTagFilter(ProjectId, req.UserContext.ProjectId)
	}
	if req.Since != 0 {
		ts, ok := influxdb.ParseUnixTimestamp(req.Since)
		if !ok {
			return nil, stderr.InvalidParam.Errorf("parse unix timestamp %d", req.Since)
		}
		q.Filter.BeginTimeMills = ts.UnixMilli()
	}
	if req.UserId != "" {
		q.AddTagFilter(User, req.UserId)
	}
	for _, x := range req.Modules {
		q.AddTagFilter(Module, x.String())
	}
	for _, x := range req.SubModules {
		q.AddTagFilter(SubModule, x.String())
	}
	for _, x := range req.OpTypes {
		q.AddTagFilter(OpType, x.String())
	}

	qr, err := s.ifx.Query(q)
	if err != nil {
		return nil, stderr.Wrap(err, "influxdbCleint.Query")
	}
	ret, err := result2Records(qr)
	if err != nil {
		return nil, stderr.Wrap(err, "result2Records")
	}
	return &pb.ListAuditRecordsRsp{Records: ret}, nil
}

// matchEntryToAPI returns matched api config and path params
func matchEntryToAPI(entry *NgxLogEntry, apis []*conf.AuditRecordAPIConfig, data map[string]any) *conf.AuditRecordAPIConfig {
	for _, api := range apis {
		// api_module, api_method, uri
		if api.APIModule != entry.APIModule || api.APIMethod != entry.Method {
			continue
		}
		matches := api.Regexp.FindStringSubmatch(entry.URI)
		if len(matches) == 0 || len(matches[0]) != len(entry.URI) {
			continue
		}

		// filter by condition
		if api.Condition != "" {
			ret, err := render(api.Condition, data, false)
			if err != nil {
				stdlog.Warnf("matchEntryToAPI: failed to render condition, [condition:%s, data:%v]", api.Condition, data)
				continue
			}
			if ret != "true" {
				continue
			}
		}

		// matched
		// set matched paths
		data[MatchedPaths] = matches
		return api
	}
	return nil
}

func render(tplStr string, data map[string]any, doFormat bool) (string, error) {
	funcMap := template.FuncMap{
		"sub": func(a, b int) int {
			return a - b
		},
	}

	tpl, err := template.New("audit records").Funcs(funcMap).Parse(tplStr)
	if err != nil {
		return "", err
	}

	if strings.Contains(tplStr, fmt.Sprintf(".%s", ReqBody)) {
		if err = unmarshalDataJson(data, ReqBody); err != nil {
			return "", err
		}
	}
	if strings.Contains(tplStr, fmt.Sprintf(".%s", RespBody)) {
		if err = unmarshalDataJson(data, RespBody); err != nil {
			return "", err
		}
	}
	if doFormat {
		formatRenderData(data)
	}
	buf := new(bytes.Buffer)
	if err = tpl.Execute(buf, data); err != nil {
		return "", stderr.Wrap(err, "Execute template [tpl:%s], [data:%v]", tplStr, data)
	}
	return buf.String(), nil
}

// unmarshalDataJson 假定map中key的值(当前为string)为json字符串，并将其反序列化
func unmarshalDataJson(data map[string]any, key string) error {
	str, ok := data[key].(string)
	if !ok || str == "" {
		return nil
	}
	var rb any
	err := json.Unmarshal([]byte(str), &rb)
	if err != nil {
		return err
	}
	data[key] = rb
	return nil
}

func mustMarshal(v any) string {
	bs, err := json.Marshal(v)
	if err != nil {
		stdlog.Errorf("mustMarshal [%v], err:%v", v, err)
		return ""
	}
	return string(bs)
}

func record2Point(r *pb.AuditRecord) *influxdb.Point {
	if r == nil {
		return nil
	}
	return &influxdb.Point{
		Measurement: MeasurementName,
		Tags: map[string]string{
			Module:    r.Module.String(),
			SubModule: r.SubModule.String(),
			OpType:    r.OpType.String(),
			User:      r.User,
			ProjectId: r.ProjectId,
		},
		Fields: map[string]any{
			Event:      r.Event,
			MatchedAPI: mustMarshal(r.MatchedApi),
			ReqPath:    r.ReqPath,
			Details:    mustMarshal(r.Details),
		},
		Time: time.UnixMilli(r.TimeMills),
	}
}

func result2Records(qr *influxdb.QueryResult) (ret []*pb.AuditRecord, err error) {
	if qr == nil {
		return []*pb.AuditRecord{}, nil
	}
	for _, val := range qr.Values {
		r := new(pb.AuditRecord)
		for i, v := range val {
			col := qr.Columns[i]
			switch col {
			case Module:
				r.Module = helper.StringToEnum[pb.AuditRecordModule](v.(string), pb.AuditRecordModule_value)
			case SubModule:
				r.SubModule = helper.StringToEnum[pb.AuditRecordSubModule](v.(string), pb.AuditRecordSubModule_value)
			case OpType:
				r.OpType = helper.StringToEnum[pb.AuditRecordOperateType](v.(string), pb.AuditRecordOperateType_value)
			case User:
				r.User = v.(string)
			case Event:
				r.Event = v.(string)
			case MatchedAPI:
				r.MatchedApi = new(pb.AuditRecordAPIConfig)
				if err := helper.StringToInterface(v.(string), r.MatchedApi); err != nil {
					return nil, err
				}
			case ReqPath:
				r.ReqPath = v.(string)
			case Details:
				if err := helper.StringToInterface(v.(string), &r.Details); err != nil {
					return nil, err
				}
			case Time:
				// influxdb 返回的时间精度是纳秒, 截去后6位转为毫秒
				vs := v.(json.Number).String()
				ms, found := strings.CutSuffix(vs, "000000")
				if !found {
					stdlog.Warnf("unexpected influx time format: %s", vs)
					ms = vs[:len(vs)-6]
				}
				mills, err := strconv.ParseInt(ms, 10, 64)
				if err != nil {
					return nil, err
				}
				r.TimeMills = mills
			case ProjectId:
				if vv, ok := v.(string); ok {
					r.ProjectId = vv
				}
			}
		}
		ret = append(ret, r)
	}
	return
}

var (
	nlp     *NgxLogProcessor
	ars     *AuditRecordService
	arsOnce sync.Once
)

func NewAuditRecordService() *AuditRecordService {
	arsOnce.Do(func() {
		nlp = &NgxLogProcessor{
			rd:         clients.RedisCli,
			ifx:        clients.InfluxWriter,
			apiConfigs: conf.C.AuditRecord.APIs,
		}
		nlp.Init()
		nlp.StartLoop()
		ars = &AuditRecordService{
			ifx: clients.InfluxdbCli,
		}
	})
	return ars
}

// formatRenderData recursively adds HTML tags configured in config.yaml to all values in the data
func formatRenderData(data interface{}) interface{} {
	switch v := reflect.ValueOf(data); v.Kind() {
	case reflect.Map:
		for _, key := range v.MapKeys() {
			v.SetMapIndex(key, reflect.ValueOf(formatRenderData(v.MapIndex(key).Interface())))
		}
		return data
	case reflect.Slice:
		for i := 0; i < v.Len(); i++ {
			if v.Index(i).IsZero() {
				continue
			}
			v.Index(i).Set(reflect.ValueOf(formatRenderData(v.Index(i).Interface())))
		}
		return data
	case reflect.String:
		return conf.C.AuditRecord.RenderValuePrefix + v.Interface().(string) + conf.C.AuditRecord.RenderValueSuffix
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return conf.C.AuditRecord.RenderValuePrefix + strconv.FormatInt(v.Int(), 10) + conf.C.AuditRecord.RenderValueSuffix
	case reflect.Float32, reflect.Float64:
		return conf.C.AuditRecord.RenderValuePrefix + strconv.FormatFloat(v.Float(), 'f', -1, 64) + conf.C.AuditRecord.RenderValueSuffix
	case reflect.Bool:
		return conf.C.AuditRecord.RenderValuePrefix + strconv.FormatBool(v.Bool()) + conf.C.AuditRecord.RenderValueSuffix
	default:
		return data
	}
}
