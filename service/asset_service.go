package service

import (
	"gorm.io/gorm"
	"os"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao/cache"
	"transwarp.io/applied-ai/central-auth-service/utils/asset/client"
)

func NewAssetService(db *gorm.DB, cache cache.DataCache) *AssetService {
	us := &AssetService{db: db, ic: cache}
	return us
}

type AssetService struct {
	db *gorm.DB
	ic cache.DataCache
}

func (a *AssetService) GetModuleData(req *pb.AssetReq, rscTypes string) (*pb.AssetResp, error) {

	groups := make([]*pb.Group, 0)

	types := strings.Split(rscTypes, ",")

	var modules = make(map[string]int32)
	for _, subType := range types {
		if val, ok := pb.Module_value[subType]; ok {
			modules[subType] = val
		}
	}

	// 如果未填入，那么默认获取所有子模块的数据
	if len(modules) == 0 {
		modules = pb.Module_value
	}

	for key := range modules {

		ret, err := a.GetSubModuleData(key, req.ProjectId, req.Key)

		if err != nil || ret == nil {
			stdlog.Warnf("Cannot get %s data, error: %s ", key, err.Error())
			continue
		}

		groups = append(groups, ret)

	}

	return &pb.AssetResp{Key: req.Key, Groups: groups}, nil
}

func (a *AssetService) GetSubModuleData(module string, projectId string, key string) (*pb.Group, error) {

	query := &pb.AssetReq{
		ProjectId: projectId,
		Key:       key,
	}

	switch module {
	case pb.Module_mw.String():
		return a.getSubModuleData(module, conf.C.Client.MwhSuffix, query)
	case pb.Module_sample.String():
		return a.getSubModuleData(module, conf.C.Client.SampleSuffix, query)
	case pb.Module_mlops.String():
		return a.getSubModuleData(module, conf.C.Client.MlopsSuffix, query)
	}
	return nil, stderr.NotAllowed.Error("data type")
}

func (a *AssetService) getSubModuleData(module string, suffix string, query *pb.AssetReq) (*pb.Group, error) {
	group, _ := a.ic.Search(module, query.ProjectId, query.Key)
	if group == nil || len(group.Items) == 0 {
		//TODO 如何在本地测试连接auto-gateway:80端口
		url := resolveUrl(suffix)
		/*
			url = "http://localhost:8620/api/samplemgr/datasets/-/search-data"
			url = "http://localhost:30080/api/v1/mwh//models/-/search-data"
			url = "http://localhost:30081/api/v1/applet//models/-/search-data"
		*/
		ret, err := client.GetModuleData(url, query, nil)
		if err != nil {
			return nil, err
		}

		a.ic.Set(module, query.ProjectId, ret)
		group, _ = a.ic.Search(module, query.ProjectId, query.Key)

	}
	return group, nil

}

func resolveUrl(suffix string) string {
	return conf.C.Client.Prefix + string(os.PathSeparator) + suffix

}
