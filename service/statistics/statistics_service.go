package service

import (
	"context"
	"sort"
	"strings"
	"sync"
	"time"

	pb "transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service"
	"transwarp.io/applied-ai/central-auth-service/service/project"
	"transwarp.io/applied-ai/central-auth-service/service/statistics/client"
)

type StaticAssetsDetailSlice []*models.StaticAssetsDetail

func (s StaticAssetsDetailSlice) Len() int { return len(s) }
func (s StaticAssetsDetailSlice) Less(i, j int) bool {
	return s[i].UpdateTime > s[j].UpdateTime
}
func (s StaticAssetsDetailSlice) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

func NewStatisticsService(us *service.UserService, ps *project.ProjectService) *StatisticsService {
	s := &StatisticsService{
		us:              us,
		ps:              ps,
		refreshInterval: 3 * time.Minute,
	}

	go s.periodicRefresh()
	return s
}

type StatisticsService struct {
	us    *service.UserService
	ps    *project.ProjectService
	cache struct {
		data     []*models.StaticAssetsDetail
		lastTime time.Time
		sync.RWMutex
	}
	refreshInterval time.Duration
}

func (s *StatisticsService) periodicRefresh() {
	ticker := time.NewTicker(s.refreshInterval)
	defer ticker.Stop()

	ctx := context.Background()
	helper.SetToken(ctx, conf.C.Auth.Token)
	for range ticker.C {
		if _, err := s.refreshCache(ctx); err != nil {
			stdlog.Errorf("refresh statistics cache failed: %v", err)
		}
	}
}

func (s *StatisticsService) refreshCache(ctx context.Context) ([]*models.StaticAssetsDetail, error) {
	type result struct {
		details []*models.StaticAssetsDetail
		err     error
	}

	type projectResult struct {
		projects []*models.ProjectResp
		err      error
	}

	mosChan := make(chan result, 1)
	appsChan := make(chan result, 1)
	corupsChan := make(chan result, 1)
	projectsChan := make(chan projectResult, 1)

	go func() {
		details, err := client.ListModelsAndRemoteSerivces()
		if err != nil {
			stdlog.Errorf("list models and remote services failed: %v", err)
		}
		mosChan <- result{details, err}
	}()

	go func() {
		details, err := client.ListApplicationsAndKbs()
		if err != nil {
			stdlog.Errorf("list applications and kbs failed: %v", err)
		}
		appsChan <- result{details, err}
	}()

	go func() {
		details, err := client.ListDatasetsAndFileAssets()
		if err != nil {
			stdlog.Errorf("list datasets and file assets failed: %v", err)
		}
		corupsChan <- result{details, err}
	}()

	go func() {
		username := helper.GetUsername(ctx)
		projects, err := s.ps.ListProjects(username, "")
		if err != nil {
			projectsChan <- projectResult{nil, err}
			return
		}

		projectsChan <- projectResult{projects, nil}
	}()

	mosResult := <-mosChan
	appsResult := <-appsChan
	corupsResult := <-corupsChan
	projectsResult := <-projectsChan

	if projectsResult.err != nil {
		return nil, projectsResult.err
	}

	totalCap := len(mosResult.details) + len(appsResult.details) + len(corupsResult.details)
	details := make([]*models.StaticAssetsDetail, 0, totalCap)

	if mosResult.details != nil {
		details = append(details, mosResult.details...)
	}
	if appsResult.details != nil {
		details = append(details, appsResult.details...)
	}
	if corupsResult.details != nil {
		details = append(details, corupsResult.details...)
	}

	pjs := projectsResult.projects
	IDNameMap := make(map[string]string, len(pjs))
	for _, v := range pjs {
		IDNameMap[v.ProjectId] = v.Name
	}

	for _, v := range details {
		if name, ok := IDNameMap[v.ProjectID]; ok {
			v.ProjectName = name
		}
	}

	sort.Sort(StaticAssetsDetailSlice(details))

	s.cache.Lock()
	s.cache.data = details
	s.cache.lastTime = time.Now()
	s.cache.Unlock()

	return details, nil
}

func (s *StatisticsService) GetProjectSummary(ctx context.Context) (*models.ProjectSummary, error) {
	userTotalCount, _ := s.us.CountUsers()
	projectTotalCount, _ := s.ps.CountProjects()
	// service not need
	serviceTotalCount := 0
	// serviceTotalCount := client.GetServiceTotalCount()
	summary := &models.ProjectSummary{
		ProjectTotalCount: projectTotalCount,
		UserTotalCount:    userTotalCount,
		ServiceTotalCount: serviceTotalCount,
	}
	return summary, nil
}

// deprecated
func (s *StatisticsService) GetAssetsOverview(ctx context.Context) (*models.AssetsOverview, error) {
	return &models.AssetsOverview{
		ModelOverview:         nil,
		ApplicationOverview:   nil,
		KnowledgeBaseOverview: nil,
		CorupsOverview:        nil,
	}, nil
}

func (s *StatisticsService) GetStaticAssetsDetail(ctx context.Context, query *models.StaticAssetsQueryReq) (*models.StaticAssetsResp, error) {
	s.cache.RLock()
	var details []*models.StaticAssetsDetail
	if time.Since(s.cache.lastTime) < s.refreshInterval && len(s.cache.data) > 0 {
		details = s.cache.data
		s.cache.RUnlock()
	} else {
		s.cache.RUnlock()
		var err error
		details, err = s.refreshCache(ctx)
		if err != nil {
			return nil, err
		}
	}

	filtered := s.filterDetails(details, query)

	total := len(filtered)

	start, end := s.getPageBounds(query.Page, query.PageSize, total)
	pagedDetails := filtered[start:end]

	return &models.StaticAssetsResp{
		Total: total,
		Data:  pagedDetails,
	}, nil
}

func (s *StatisticsService) filterDetails(details []*models.StaticAssetsDetail, query *models.StaticAssetsQueryReq) []*models.StaticAssetsDetail {
	if query == nil {
		return details
	}

	filtered := make([]*models.StaticAssetsDetail, 0, len(details))
	for _, detail := range details {
		if !s.matchesFilter(detail, query) {
			continue
		}
		filtered = append(filtered, detail)
	}

	return filtered
}

func (s *StatisticsService) matchesFilter(detail *models.StaticAssetsDetail, query *models.StaticAssetsQueryReq) bool {
	if len(query.AssetsTypes) > 0 {
		matched := false
		for _, assetsType := range query.AssetsTypes {
			if detail.AssetsType == assetsType {
				matched = true
				break
			}
		}
		if !matched {
			return false
		}
	}

	if query.ProjectName != "" && !strings.Contains(
		strings.ToLower(detail.ProjectName),
		strings.ToLower(query.ProjectName)) {
		return false
	}

	if query.Creator != "" && !strings.Contains(
		strings.ToLower(detail.Creator),
		strings.ToLower(query.Creator)) {
		return false
	}

	return true
}

func (s *StatisticsService) getPageBounds(page, pageSize, total int) (start, end int) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	start = (page - 1) * pageSize
	if start >= total {
		start = total
	}

	end = start + pageSize
	if end > total {
		end = total
	}

	return start, end
}

// deprecated
func (s *StatisticsService) GetDynamicAssetsDetails(ctx context.Context, orderBy string, page, pageSize int) (*pb.ServiceBaseInfoList, error) {
	return client.ListService(orderBy, page, pageSize)
}
