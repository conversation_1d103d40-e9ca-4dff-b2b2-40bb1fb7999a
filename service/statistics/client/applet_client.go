package client

import (
	"encoding/json"
	"sync"

	"google.golang.org/protobuf/encoding/protojson"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/models"
)

var AppletClient *HTTPClient

func init() {
	AppletClient = NewHTTPClient(
		// WithBaseURL("http://**************:32744"),
		WithBaseURL(conf.C.Client.Prefix),
		WithDefaultHeader("Authorization", "Bearer "+conf.C.Auth.Token),
	)
}

func ListApplicationsAndKbs() ([]*models.StaticAssetsDetail, error) {
	var wg sync.WaitGroup
	wg.Add(2)

	var (
		apps, kbs       []*models.StaticAssetsDetail
		appsErr, kbsErr error
	)

	go func() {
		defer wg.Done()
		apps, appsErr = listApplications()
		if appsErr != nil {
			stdlog.Errorf("list applications failed: %s", appsErr.Error())
		}
	}()

	go func() {
		defer wg.Done()
		kbs, kbsErr = listKbs()
		if kbsErr != nil {
			stdlog.Errorf("list knowledge base failed: %s", kbsErr.Error())
		}
	}()

	wg.Wait()

	totalLen := len(apps) + len(kbs)
	details := make([]*models.StaticAssetsDetail, 0, totalLen)

	if appsErr == nil {
		details = append(details, apps...)
	}
	if kbsErr == nil {
		details = append(details, kbs...)
	}

	return details, nil
}

func listApplications() ([]*models.StaticAssetsDetail, error) {
	path := "/applet/api/v1/app/applications"
	resp, err := AppletClient.Get(path, nil)
	if err != nil {
		return nil, err
	}
	apps := make([]*AppletChainBaseDO, 0)
	err = json.Unmarshal(resp.Body, &apps)
	if err != nil {
		return nil, err
	}
	details := make([]*models.StaticAssetsDetail, 0)
	for _, app := range apps {
		details = append(details, &models.StaticAssetsDetail{
			Name:          app.Name,
			ProjectName:   "",
			ProjectID:     app.ProjectID,
			AssetsType:    models.Application,
			AssetsSubtype: models.AssetsSubtype(app.CreatedType),
			UpdateTime:    app.UpdateTime,
			Creator:       app.Creator,
			Description:   app.Desc,
			RefID:         app.ID,
		})
	}
	return details, nil
}

var KnowledgeAssetsSubtypeMap = map[pb.KnowledgeBaseContentType]models.AssetsSubtype{
	pb.KnowledgeBaseContentType_TEXT:  models.KbsTypeText,
	pb.KnowledgeBaseContentType_TABLE: models.KbsTypeTable,
}

func listKbs() ([]*models.StaticAssetsDetail, error) {
	// TODO project id 必须传
	path := "/applet/api/v1/knowlhub/kbs"
	resp, err := AppletClient.Get(path, nil)
	if err != nil {
		return nil, err
	}
	kbsResp := pb.ListKnowledgeBasesRsp{}
	err = protojson.Unmarshal(resp.Body, &kbsResp)
	if err != nil {
		return nil, err
	}
	details := make([]*models.StaticAssetsDetail, 0)
	for _, kbs := range kbsResp.Result {
		details = append(details, &models.StaticAssetsDetail{
			Name:          kbs.KnowledgeBase.Name,
			ProjectName:   "",
			ProjectID:     kbs.KnowledgeBase.ProjectId,
			AssetsType:    models.Knowledge,
			AssetsSubtype: KnowledgeAssetsSubtypeMap[kbs.KnowledgeBase.ContentType],
			UpdateTime:    kbs.KnowledgeBase.UpdateTimeMills,
			Creator:       kbs.KnowledgeBase.CreateUser,
			Description:   kbs.KnowledgeBase.Description,
			RefID:         kbs.KnowledgeBase.Id,
		})
	}
	return details, nil
}

// AppletChainBaseDO 基础属性，列表页查询的时候用到
type AppletChainBaseDO struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	Creator    string `json:"creator"`
	Desc       string `json:"desc"`
	ProjectID  string `json:"projectID"`
	UpdateTime int64  `json:"update_time"`
	CreateTime int64  `json:"create_time"`
	AssetType  string `json:"asset_type"`

	CreatedType AppletType `json:"created_type"`
}

type AppletType = string // 应用的类型
const (
	AppletTypeChain            = "Applet-Chain"     // 应用链
	AppletTypeAssistant        = "Applet-Assistant" // 智能助手
	AppletTypeExternalRegister = "Applet-External"  // 用户通过 web url 注册的外部应用
	AppletTypeCustomDeployed   = "Applet-Custom"    // 用户通过服务管理自行部署的
)
