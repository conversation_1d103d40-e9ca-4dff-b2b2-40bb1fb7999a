package client

import (
	"strconv"

	"google.golang.org/protobuf/encoding/protojson"
	pb "transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
)

var ServiceClient *HTTPClient

func init() {
	ServiceClient = NewHTTPClient(
		WithBaseURL(conf.C.Client.Prefix),
		WithDefaultHeader("Authorization", "Bearer "+conf.C.Auth.Token),
	)
}

func GetServiceTotalCount() int {
	svcs, err := ListService("", -1, -1)
	if err != nil {
		stdlog.Errorf("list serving service list error: %s", err.Error())
		return 0
	}
	return int(svcs.Size)
}

// order_by=update_time&page=4&page_size=100
func ListService(orderBy string, page, pageSize int) (*pb.ServiceBaseInfoList, error) {
	path := "/serving/api/v2/service/list"
	options := NewRequestOptions()
	if orderBy != "" {
		options.WithQueryParam("order_by", "update_time")
	}
	if page > 0 {
		options.WithQueryParam("page", strconv.Itoa(page))
	}
	if pageSize > 0 {
		options.WithQueryParam("pageSize", strconv.Itoa(pageSize))
	}
	resp, err := ServiceClient.Get(path, options)
	if err != nil {
		return nil, err
	}
	serviceList := &pb.ServiceBaseInfoList{}
	err = protojson.Unmarshal(resp.Body, serviceList)
	if err != nil {
		return nil, err
	}

	return serviceList, nil
}
