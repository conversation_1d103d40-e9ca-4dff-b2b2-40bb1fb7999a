package client

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

type HTTPClient struct {
	client         *http.Client
	baseURL        string
	defaultHeaders map[string]string
}

type Option func(*HTTPClient)

func WithTimeout(timeout time.Duration) Option {
	return func(c *HTTPClient) {
		c.client.Timeout = timeout
	}
}

func WithBaseURL(baseURL string) Option {
	return func(c *HTTPClient) {
		c.baseURL = baseURL
	}
}

func WithDefaultHeader(key, value string) Option {
	return func(c *HTTPClient) {
		if c.defaultHeaders == nil {
			c.defaultHeaders = make(map[string]string)
		}
		c.defaultHeaders[key] = value
	}
}

func NewHTTPClient(options ...Option) *HTTPClient {
	client := &HTTPClient{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
		baseURL:        "",
		defaultHeaders: make(map[string]string),
	}

	for _, option := range options {
		option(client)
	}

	return client
}

// RequestOptions 用于配置单个请求
type RequestOptions struct {
	Headers map[string]string
	Query   url.Values
}

// NewRequestOptions 创建一个新的 RequestOptions 实例
func NewRequestOptions() *RequestOptions {
	return &RequestOptions{
		Headers: make(map[string]string),
		Query:   make(url.Values),
	}
}

// WithHeader 为当前请求添加请求头
func (ro *RequestOptions) WithHeader(key, value string) *RequestOptions {
	ro.Headers[key] = value
	return ro
}

// WithQueryParam 为当前请求添加查询参数
func (ro *RequestOptions) WithQueryParam(key, value string) *RequestOptions {
	ro.Query.Add(key, value)
	return ro
}

// Response 是对 http.Response 的封装
type Response struct {
	*http.Response
	Body []byte
}

// DecodeJSON 将响应体解析为 JSON
func (r *Response) DecodeJSON(v interface{}) error {
	return json.Unmarshal(r.Body, v)
}

func (c *HTTPClient) doRequest(method, path string, body io.Reader, options *RequestOptions) (*Response, error) {
	reqURL := path
	if c.baseURL != "" {
		reqURL = c.baseURL + path
	}

	req, err := http.NewRequest(method, reqURL, body)
	if err != nil {
		return nil, stderr.Wrap(err, "create request failed")
	}

	for key, value := range c.defaultHeaders {
		req.Header.Set(key, value)
	}

	if options != nil {
		for key, value := range options.Headers {
			req.Header.Set(key, value)
		}
		if len(options.Query) > 0 {
			req.URL.RawQuery = options.Query.Encode()
		}
	}

	resp, err := c.client.Do(req)
	if err != nil {
		return nil, stderr.Wrap(err, "do request failed.")
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, stderr.Wrap(err, "read response body failed")
	}
	if resp.StatusCode < 200 || resp.StatusCode > 400 {
		return nil, stderr.Wrap(err, "return code is: %d", resp.StatusCode)
	}

	return &Response{
		Response: resp,
		Body:     respBody,
	}, nil
}

func (c *HTTPClient) Get(path string, options *RequestOptions) (*Response, error) {
	return c.doRequest(http.MethodGet, path, nil, options)
}

func (c *HTTPClient) Post(path string, body interface{}, options *RequestOptions) (*Response, error) {
	return c.sendRequestWithBody(http.MethodPost, path, body, options)
}

func (c *HTTPClient) Put(path string, body interface{}, options *RequestOptions) (*Response, error) {
	return c.sendRequestWithBody(http.MethodPut, path, body, options)
}

func (c *HTTPClient) Delete(path string, options *RequestOptions) (*Response, error) {
	return c.doRequest(http.MethodDelete, path, nil, options)
}

func (c *HTTPClient) sendRequestWithBody(method, path string, body interface{}, options *RequestOptions) (*Response, error) {
	var bodyReader io.Reader
	if body != nil {
		switch v := body.(type) {
		case io.Reader:
			bodyReader = v
		case []byte:
			bodyReader = bytes.NewReader(v)
		default:
			jsonData, err := json.Marshal(v)
			if err != nil {
				return nil, fmt.Errorf("编码请求体为 JSON 失败: %w", err)
			}
			bodyReader = bytes.NewReader(jsonData)
			if options == nil {
				options = NewRequestOptions()
			}
			options.WithHeader("Content-Type", "application/json")
		}
	}
	return c.doRequest(method, path, bodyReader, options)
}
