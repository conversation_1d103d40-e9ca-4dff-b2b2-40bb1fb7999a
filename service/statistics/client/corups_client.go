package client

import (
	"encoding/json"
	"strconv"
	"sync"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/models"
)

var CorupsClient *HTTPClient

func init() {
	CorupsClient = NewHTTPClient(
		// WithBaseURL("http://172.17.124.25:31801"),
		WithBaseURL(conf.C.Client.Prefix),
		WithDefaultHeader("Authorization", "Bearer "+conf.C.Auth.Token),
	)
}

func ListDatasetsAndFileAssets() ([]*models.StaticAssetsDetail, error) {
	var wg sync.WaitGroup
	wg.Add(2)

	var (
		datasets, fileAssets       []*models.StaticAssetsDetail
		datasetsErr, fileAssetsErr error
	)

	go func() {
		defer wg.Done()
		datasets, datasetsErr = listDatasets()
		if datasetsErr != nil {
			stdlog.Errorf("list datasets failed: %s", datasetsErr.Error())
		}
	}()

	go func() {
		defer wg.Done()
		fileAssets, fileAssetsErr = listFileAssets()
		if fileAssetsErr != nil {
			stdlog.Errorf("list file assets failed: %s", fileAssetsErr.Error())
		}
	}()

	wg.Wait()

	totalLen := len(datasets) + len(fileAssets)
	details := make([]*models.StaticAssetsDetail, 0, totalLen)

	if datasetsErr == nil {
		details = append(details, datasets...)
	}
	if fileAssetsErr == nil {
		details = append(details, fileAssets...)
	}

	return details, nil
}

func listDatasets() ([]*models.StaticAssetsDetail, error) {
	path := "/cv/api/samplemgr/datasets"
	resp, err := CorupsClient.Get(path,
		NewRequestOptions().WithQueryParam("from", "0").WithQueryParam("size", "10000"),
	)
	if err != nil {
		return nil, err
	}

	datasetPage := DatatsetPage{}
	if err := json.Unmarshal(resp.Body, &datasetPage); err != nil {
		return nil, err
	}
	details := make([]*models.StaticAssetsDetail, 0)
	for _, dataset := range datasetPage.Records {
		details = append(details, &models.StaticAssetsDetail{
			Name:          dataset.Name,
			ProjectName:   "",
			ProjectID:     dataset.ProjectID,
			AssetsType:    models.Corpus,
			AssetsSubtype: models.CorpusTypeDataset,
			UpdateTime:    dataset.UpdateAt.UnixMilli(),
			Creator:       dataset.Creator,
			Description:   dataset.Desc,
			RefID:         strconv.Itoa(int(dataset.ID)),
		})
	}
	return details, nil
}

func listFileAssets() ([]*models.StaticAssetsDetail, error) {
	path := "/cv/api/assets/file-assets"
	resp, err := CorupsClient.Get(path,
		NewRequestOptions().WithQueryParam("from", "0").WithQueryParam("size", "10000"),
	)
	if err != nil {
		return nil, err
	}

	type AssetsResp struct {
		Records []*FileAsset `json:"records"`
	}
	fileAssetsResp := AssetsResp{}

	if err := json.Unmarshal(resp.Body, &fileAssetsResp); err != nil {
		return nil, err
	}
	details := make([]*models.StaticAssetsDetail, 0)
	for _, fileAssets := range fileAssetsResp.Records {
		details = append(details, &models.StaticAssetsDetail{
			Name:          fileAssets.Name,
			ProjectName:   "",
			ProjectID:     fileAssets.ProjectID,
			AssetsType:    models.Corpus,
			AssetsSubtype: models.CorpusTypeFileAsset,
			UpdateTime:    fileAssets.UpdateAt.UnixMilli(),
			Creator:       fileAssets.Creator,
			Description:   fileAssets.Description,
			RefID:         fileAssets.ID,
		})
	}
	return details, nil
}

type DatatsetPage struct {
	Total   int
	Current int
	Pages   int
	Size    int
	Records []*SampleImageDataset
}

type SampleImageDataset struct {
	ID        int32     `json:"id"`
	Name      string    `json:"name"`
	Desc      string    `json:"desc"`
	Creator   string    `json:"creator"`
	CreateAt  time.Time `json:"createAt"`
	UpdateAt  time.Time `json:"updateAt"`
	ProjectID string    `json:"projectId"`
}

type FileAsset struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	FileName    string    `json:"fileName"`
	Description string    `json:"description"`
	Creator     string    `json:"creator" `
	CreateAt    time.Time `json:"createAt"`
	UpdateAt    time.Time `json:"updateAt"`
	ProjectID   string    `json:"projectId"`
}
