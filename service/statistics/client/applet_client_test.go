package client

import (
	"fmt"
	"testing"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

func TestAppletClient(t *testing.T) {
	details, err := listApplications()
	if err != nil {
		stdlog.Errorln(err.<PERSON><PERSON>r())
	}
	fmt.Println(len(details))
}
func TestCorupsClient(t *testing.T) {
	details, err := listFileAssets()
	if err != nil {
		stdlog.Errorln(err.<PERSON>rror())
	}
	fmt.Println(len(details))
}
