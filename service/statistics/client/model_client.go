package client

import (
	"sync"

	"google.golang.org/protobuf/encoding/protojson"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/models"
)

var ModelClient *HTTPClient

func init() {
	ModelClient = NewHTTPClient(
		// WithBaseURL("http://**************:32744"),
		WithBaseURL(conf.C.Client.Prefix),
		WithDefaultHeader("Authorization", "Bearer "+conf.C.Auth.Token),
	)
}

func ListModelsAndRemoteSerivces() ([]*models.StaticAssetsDetail, error) {
	var wg sync.WaitGroup
	wg.Add(2)

	var (
		modelDetails, remoteServiceDetails []*models.StaticAssetsDetail
		modelErr, remoteServiceErr         error
	)

	go func() {
		defer wg.Done()
		modelDetails, modelErr = listModels()
		if modelErr != nil {
			stdlog.Errorf("list models failed: %s", modelErr.Error())
		}
	}()

	go func() {
		defer wg.Done()
		remoteServiceDetails, remoteServiceErr = listRemoteServices()
		if remoteServiceErr != nil {
			stdlog.Errorf("list remote services failed: %s", remoteServiceErr.Error())
		}
	}()

	wg.Wait()

	totalLen := len(modelDetails) + len(remoteServiceDetails)
	details := make([]*models.StaticAssetsDetail, 0, totalLen)

	if modelDetails != nil {
		details = append(details, modelDetails...)
	}
	if remoteServiceDetails != nil {
		details = append(details, remoteServiceDetails...)
	}

	return details, nil
}

func listModels() ([]*models.StaticAssetsDetail, error) {
	path := "/mw/api/v1/mwh/models"
	resp, err := ModelClient.Get(path, NewRequestOptions())
	if err != nil {
		return nil, err
	}

	modelsResp := pb.ListModelsRsp{}
	if err := protojson.Unmarshal(resp.Body, &modelsResp); err != nil {
		return nil, err
	}

	details := make([]*models.StaticAssetsDetail, 0)
	for _, model := range modelsResp.Models {
		details = append(details, &models.StaticAssetsDetail{
			Name:          model.Name,
			ProjectName:   "",
			ProjectID:     model.ProjectId,
			AssetsType:    models.Model,
			AssetsSubtype: models.MwhTypeModel,
			UpdateTime:    model.Detail.UpdateTimeMs,
			Creator:       model.Detail.UserId,
			Description:   model.Detail.Desc,
			RefID:         model.Id,
		})
	}
	return details, nil
}

func listRemoteServices() ([]*models.StaticAssetsDetail, error) {
	path := "/mw/api/v1/mwh/svcmgr/remote-services"
	resp, err := ModelClient.Get(path, nil)
	if err != nil {
		return nil, err
	}
	remoteServiceResp := pb.ReadRemoteServiceRsp{}
	err = protojson.Unmarshal(resp.Body, &remoteServiceResp)
	if err != nil {
		return nil, err
	}

	details := make([]*models.StaticAssetsDetail, 0)
	for _, rs := range remoteServiceResp.Services {
		details = append(details, &models.StaticAssetsDetail{
			Name:          rs.Name,
			ProjectName:   "",
			ProjectID:     rs.ProjectId,
			AssetsType:    models.Model,
			AssetsSubtype: models.MwhTypeRemoteService,
			UpdateTime:    rs.Detail.UpdateTimeMs,
			Creator:       rs.Detail.UserId,
			Description:   rs.Detail.Desc,
			RefID:         rs.Id,
		})
	}
	return details, nil
}
