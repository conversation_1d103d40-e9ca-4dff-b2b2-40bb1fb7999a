package service

import (
	"context"
	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
)

func NewPortalInfoService(db *gorm.DB) *PortalInfoService {
	ps := &PortalInfoService{db: db.Session(&gorm.Session{NewDB: true})}
	return ps
}

type PortalInfoService struct {
	db *gorm.DB
}

func (p *PortalInfoService) GetPortalInfo(ctx context.Context) (*dao.PortalInfoDO, error) {
	return dao.GetPortalInfo(p.db, helper.GetProjectID(ctx))

}

func (p *PortalInfoService) UpsertPortalInfo(portalInfo *dao.PortalInfoDO) error {
	return dao.UpsertPortalInfo(p.db, portalInfo)
}
