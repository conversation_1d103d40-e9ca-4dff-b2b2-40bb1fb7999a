package tenant

import v1 "k8s.io/api/core/v1"

const (
	ResourceNvidiaGpu              v1.ResourceName = "nvidia.com/gpu"
	ResourceNvidiaGpuMem           v1.ResourceName = "nvidia.com/gpumem"
	ResourceNvidiaGpuMemPercentage v1.ResourceName = "nvidia.com/gpumem-percentage"
	ResourceNvidiaGpucores         v1.ResourceName = "nvidia.com/gpucores"

	ResourceRequestsNvidiaGPU              v1.ResourceName = "requests.nvidia.com/gpu"
	ResourceRequestsNvidiaGPUMemory        v1.ResourceName = "requests.nvidia.com/gpumem"
	ResourceRequestsNvidiaGpuMemPercentage v1.ResourceName = "requests.nvidia.com/gpumem-percentage"
	ResourceRequestsNvidiaGpucores         v1.ResourceName = "requests.nvidia.com/gpucores"

	ResourceVGPUCore         v1.ResourceName = "transwarp.io/vgpu-core"
	ResourceVGPUMem          v1.ResourceName = "transwarp.io/vgpu-memory"
	ResourceRequestsVGPUCore v1.ResourceName = "requests.transwarp.io/vgpu-core"
	ResourceRequestsVGPUMem  v1.ResourceName = "requests.transwarp.io/vgpu-memory"

	// ResourceKnowl to be remove
	// ResourceKnowl                v1.ResourceName = "transwarp.io/knowl"
	ResourceKnowledgeBaseStorage v1.ResourceName = "transwarp.io/knowledge-base-storage"
	ResourceFileStorage          v1.ResourceName = "transwarp.io/file-storage"

	ResourceBandwidth        v1.ResourceName = "transwarp.io/bandwidth"
	ResourceIngressBandwidth v1.ResourceName = "transwarp.io/ingress-bandwidth"
	ResourceEgressBandwidth  v1.ResourceName = "transwarp.io/egress-bandwidth"
)

const (
	ApplicantKey string = "transwarp.io/applicant"
	DiskSpaceKey string = "transwarp.io/diskspace"
	DeltaSizeKey string = "transwarp.io/deltasize"
	TenantUidKey string = "transwarp.io/tenantuid"
	RuleTypeKey  string = "transwarp.io/rule-type"
	NewSizeKey   string = "transwarp.io/file-storage"
)

const (
	NamespaceLableNsType    string = "llmops.transwarp.io/ns-type"    // system | tenant
	NamespaceLableManagedBy string = "llmops.transwarp.io/managed-by" // dev | demo
	NamespaceLableUsedBy    string = "llmops.transwarp.io/used-by"    // proj1, proj2
)

type NsType string

const (
	SystemNs NsType = "system"
	TenantNs NsType = "tenant"
)
