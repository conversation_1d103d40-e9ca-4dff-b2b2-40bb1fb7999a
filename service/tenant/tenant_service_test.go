package tenant

import (
	"os"
	"testing"

	"gopkg.in/yaml.v3"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/resourcequota"
)

// TestFlushDefaultResourceQuotaV2ToYaml 测试将默认资源配额写入YAML文件
func TestFlushDefaultResourceQuotaV2ToYaml(t *testing.T) {
	// 获取默认资源配额
	defaultQuota := resourcequota.GetDefaultResourceQuotaV2()
	if defaultQuota == nil {
		t.Fatal("Failed to get default resource quota")
	}

	tmpl := resourcequota.LlmopsQueueHelmValues{
		Resourcequota: resourcequota.LlmopsQueueResourceQuota{
			Cohort: defaultQuota.Hard,
		},
	}

	// 将结构体转换为YAML并写入文件
	file, err := os.Create("values.yaml")
	if err != nil {
		t.Fatalf("Failed to create values.yaml: %v", err)
	}
	defer file.Close()

	// 写入values.yaml文件
	fileEncoder := yaml.NewEncoder(file)
	fileEncoder.SetIndent(2) // 设置缩进为2个空格
	err = fileEncoder.Encode(tmpl)
	if err != nil {
		t.Fatalf("Failed to encode to YAML: %v", err)
	}
	err = fileEncoder.Close()
	if err != nil {
		t.Fatalf("Failed to close encoder: %v", err)
	}

	t.Logf("Successfully wrote default resource quota to values.yaml")
}
