package util

import (
	customtypes "transwarp.io/applied-ai/central-auth-service/service/tenant/types"
)

const (
	ResourceGroupManagedNamespace = "resources-group/managed-namespace"
)

func MergeLabels(a, b map[string]string) map[string]string {
	res := make(map[string]string)
	for k, v := range a {
		res[k] = v
	}
	for k, v := range b {
		res[k] = v
	}
	return res
}

func GetManagedByLabels() map[string]string {
	ns, _ := GetCurrentNamespace()
	return map[string]string{
		customtypes.NamespaceLableManagedBy: ns,
	}
}

func WithManagedByLabels(labels map[string]string) map[string]string {
	return MergeLabels(labels, GetManagedByLabels())
}

func ContainLabels(target, subset map[string]string) bool {
	if target == nil || subset == nil {
		return false
	}

	for key, value := range subset {
		targetValue, exists := target[key]
		if !exists || targetValue != value {
			return false
		}
	}
	return true
}

func GetResourceGroupManagedNamespaceLabels() map[string]string {
	ns, _ := GetCurrentNamespace()
	return map[string]string{
		ResourceGroupManagedNamespace: ns,
	}
}
