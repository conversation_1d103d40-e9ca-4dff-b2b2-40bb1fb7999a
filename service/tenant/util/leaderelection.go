package util

import (
	"context"
	"fmt"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/leaderelection"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type LeaderElectionConfig struct {
	LeaseName        string
	LeaseNamespace   string
	Identity         string
	Client           *kubernetes.Clientset
	Callback         func(ctx context.Context)
	OnStoppedLeading func()
	OnNewLeader      func(identity string)

	LeaseDuration time.Duration
	RenewDeadline time.Duration
	RetryPeriod   time.Duration
}

func NewDefaultLeaderElectionConfig(leaseName, leaseNamespace, identity string, client *kubernetes.Clientset, callback func(ctx context.Context)) LeaderElectionConfig {
	return LeaderElectionConfig{
		LeaseName:      leaseName,
		LeaseNamespace: leaseNamespace,
		Identity:       identity,
		Client:         client,
		Callback:       callback,
		LeaseDuration:  15 * time.Second,
		RenewDeadline:  10 * time.Second,
		RetryPeriod:    2 * time.Second,
	}
}

func StartLeaderElection(ctx context.Context, config LeaderElectionConfig) error {
	if config.Client == nil {
		return fmt.Errorf("kubernetes client is required for leader election")
	}
	if config.Callback == nil {
		return fmt.Errorf("callback function is required for leader election")
	}
	if config.Identity == "" {
		return fmt.Errorf("identity is required for leader election identity")
	}
	if config.LeaseName == "" {
		return fmt.Errorf("lease name is required")
	}
	if config.LeaseNamespace == "" {
		return fmt.Errorf("lease namespace is required")
	}

	lock := &resourcelock.LeaseLock{
		LeaseMeta: metav1.ObjectMeta{
			Name:      config.LeaseName,
			Namespace: config.LeaseNamespace,
		},
		Client: config.Client.CoordinationV1(),
		LockConfig: resourcelock.ResourceLockConfig{
			Identity: config.Identity,
		},
	}

	leaderElectionConfig := leaderelection.LeaderElectionConfig{
		Lock:          lock,
		LeaseDuration: config.LeaseDuration,
		RenewDeadline: config.RenewDeadline,
		RetryPeriod:   config.RetryPeriod,
		Callbacks: leaderelection.LeaderCallbacks{
			OnStartedLeading: func(ctx context.Context) {
				stdlog.Infof("[%s] I am the leader, starting my operations.", config.Identity)
				config.Callback(ctx)
			},
			OnStoppedLeading: func() {
				stdlog.Infof("[%s] I am no longer the leader, stopping my operations.", config.Identity)
				if config.OnStoppedLeading != nil {
					config.OnStoppedLeading()
				}
			},
			OnNewLeader: func(identity string) {
				if identity == config.Identity {
					return
				}
				stdlog.Infof("[%s] New leader elected: %s", config.Identity, identity)
				if config.OnNewLeader != nil {
					config.OnNewLeader(identity)
				}
			},
		},
	}

	go leaderelection.RunOrDie(ctx, leaderElectionConfig)

	return nil
}
