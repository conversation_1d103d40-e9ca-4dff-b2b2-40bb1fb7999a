package handler

import (
	"context"
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

var IngressDeleteHandlerName = "ingress_delete_handler"

type IngressDeleteHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := IngressDeleteHandler{
		name: IngressDeleteHandlerName,
	}
	RegisterHandler(IngressDeleteHandlerName, &h)
}

func NewIngressDeleteHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := IngressDeleteHandler{
		name:    IngressDeleteHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}

func (h *IngressDeleteHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, IngressDeleteHandlerName)

	// whatever doHandle success or failed. call h.next.handle alwarys
	err := h.doHandle(ctx, namespace, params)
	if err != nil {
		stdlog.Errorf("Delete ingress for %s failed, err: %+v", namespace, err)
	}
	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *IngressDeleteHandler) doHandle(ctx context.Context, namespace string, params HandlerParams) error {
	ns, err := util.GetCurrentNamespace()
	if err != nil {
		stdlog.Errorf("Error reading current namespace: %v", err)
		return err
	}

	err = h.kclient.KubeClient.ExtensionsV1beta1().Ingresses(ns).Delete(context.TODO(), fmt.Sprintf("%s-ingress", namespace), metav1.DeleteOptions{})
	if err != nil {
		return err
	}

	return nil
}

func (h *IngressDeleteHandler) rollback(ctx context.Context, namespace string) {
	stdlog.Infof("Rollback %s for namespace %s", h.name, namespace)
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
