package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	helm "transwarp.io/applied-ai/central-auth-service/service/tenant/helm"
)

var HippoInitHandlerName = "hippo_init_handler"

type HippoInitHandler struct {
	name string
	pre  IHandler
	next IHandler
}

func init() {
	h := HippoInitHandler{
		name: HippoInitHandlerName,
	}
	RegisterHandler(HippoInitHandlerName, &h)
}

func NewHippoInitHandler(next IHandler) IHandler {
	h := HippoInitHandler{
		name: HippoInitHandlerName,
		next: next,
	}
	return &h
}

func (h *HippoInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, HippoInitHandlerName)

	if conf.C.Tenant.Hippo.Enabled {
		installed, err := helm.HippoExists(namespace)
		if err != nil {
			stdlog.Errorf("Check whether hippo installed in %s throw err: %+v", namespace, err)
		} else if installed {
			stdlog.Infof("Hippo in ns %s has installed.", namespace)
		} else {
			stdlog.Infof("Installing hippo in ns %s.", namespace)
			err := helm.InstallHippo(namespace)
			if err != nil {
				stdlog.Errorf("Install hippo in %s failed: %+v", namespace, err)
			} else {
				stdlog.Infof("Installing hippo in ns %s done.", namespace)
			}
		}
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *HippoInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
