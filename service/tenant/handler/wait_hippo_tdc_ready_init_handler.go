package handler

import (
	"context"
	"strings"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	client "transwarp.io/applied-ai/central-auth-service/service/tenant/client"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

var WaitHippoTdcReadyInitHandlerName = "wait_hippo_tdc_ready_init_handler"

type WaitHippoTdcReadyInitHandler struct {
	name      string
	pre       IHandler
	next      IHandler
	kclient   *kubernetes.KClientset
	tdcClient *client.TDCClient
}

func init() {
	h := WaitHippoTdcReadyInitHandler{
		name: WaitHippoTdcReadyInitHandlerName,
	}
	RegisterHandler(WaitHippoTdcReadyInitHandlerName, &h)
}

func NewWaitHippoTdcReadyInitHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := WaitHippoTdcReadyInitHandler{
		name:    WaitHippoTdcReadyInitHandlerName,
		next:    next,
		kclient: kclient,
	}
	h.tdcClient = client.GetClientFactory().GetTDCClient()
	return &h
}

func (h *WaitHippoTdcReadyInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, WaitHippoTdcReadyInitHandlerName)

	maxSleepSeconds := 1800
	sleepSeconds := 0
	for sleepSeconds <= maxSleepSeconds {
		hippo, err := h.tdcClient.GetHippo(ctx, namespace)
		if err != nil {
			stdlog.Errorf("Check hippo tdc %s ready throw err: %+v", namespace, err)
		} else if strings.ToLower(hippo.Status) == "running" {
			stdlog.Infof("Namespace %s's hippo tdc is ready.", namespace)
			break
		}
		time.Sleep(30 * time.Second)
		sleepSeconds = sleepSeconds + 30
	}

	if sleepSeconds > maxSleepSeconds {
		stdlog.Warnf("Namespace %s's hippo tdc not ready after %d seconds.", namespace, maxSleepSeconds)
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *WaitHippoTdcReadyInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
