package handler

import (
	"context"

	corev1 "k8s.io/api/core/v1"
	"transwarp.io/applied-ai/central-auth-service/models"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

type IHandler interface {
	handle(ctx context.Context, namespace string, params HandlerParams) error
	rollback(ctx context.Context, namespace string)
}

type <PERSON>ler<PERSON>hain struct {
	head IHandler
}

var handlers map[string]IHandler

type HandlerParams struct {
	Namespace           string
	NamespaceLables     map[string]string
	ResourceQuota       *corev1.ResourceQuota
	TenantResourceQuota *models.TenantResourceQuota
}

func RegisterHandler(name string, handler IH<PERSON>ler) {
	if handlers == nil {
		handlers = make(map[string]IHandler, 0)
	}
	handlers[name] = handler
}

func (hc *HandlerChain) Handle(ctx context.Context, namespace string, params HandlerParams) error {
	return hc.head.handle(ctx, namespace, params)
}

func NewK8sInitHandlerChain(namespace string, kclient *kubernetes.KClientset) *HandlerChain {
	// file-storage and knowledge-base-storage will init by llmops-basic using default value
	// wait-namesapce-ready -> resource-quota -> limitrange -> ingress -> network-policy -> llmops-basic -> hippo
	deih := NewDocEngineInitHandler(nil)
	hh := NewHippoInitHandler(deih)
	lqih := NewLlmopsQueueInitHandler(hh, kclient)
	lh := NewLlmopsBasicInitHandler(lqih)
	nh := NewNetworkPolicyInitHandler(lh, kclient)
	ih := NewIngressInitHandler(nh, kclient)
	lrh := NewLimitRangeInitHandler(ih, kclient)
	rqh := NewResourceQuotaInitHandler(lrh, kclient)
	wnrh := NewWaitNamespaceReadyInitHandler(rqh, kclient)

	return &HandlerChain{
		head: wnrh,
	}
}

func NewTdcInitHandlerChain(namespace string, kclient *kubernetes.KClientset) *HandlerChain {
	// file-storage will init by llmops-basic
	// wait-namespace-ready -> patch-namesapce-label -> resource-quota -> ingress -> network-policy -> llmops-basic -> hippo-tdc -> waiting-hippo-ready -> knowledge-base-storage
	kbsih := NewKnowledgeBaseStorageTdcInitHandler(nil, kclient)
	rrtih := NewReduceResourceTdcInitHandler(kbsih, kclient)
	whrih := NewWaitHippoTdcReadyInitHandler(rrtih, kclient)
	hih := NewHippoTdcInitHandler(whrih)
	wttrih := NewWaitTdcTenantReadyInitHandler(hih, kclient)
	lqih := NewLlmopsQueueInitHandler(wttrih, kclient)
	lbih := NewLlmopsBasicInitHandler(lqih)
	nptih := NewNetworkPolicyTdcInitHandler(lbih, kclient)
	iih := NewIngressInitHandler(nptih, kclient)
	rquh := NewResourceQuotaUpdateHandler(iih, kclient)
	pnlih := NewPatchNamespaceLabelInitHandler(rquh, kclient)
	wnrih := NewWaitNamespaceReadyInitHandler(pnlih, kclient)

	return &HandlerChain{
		head: wnrih,
	}
}

func NewTdc5InitHandlerChain(namespace string, kclient *kubernetes.KClientset) *HandlerChain {
	// file-storage will init by llmops-basic
	// wait-namespace-ready -> patch-namesapce-label -> resource-quota -> ingress -> network-policy -> llmops-basic -> hippo-tdc -> waiting-hippo-ready -> knowledge-base-storage

	deih := NewDocEngineInitHandler(nil)
	hh := NewHippoTdc5InitHandler(deih)
	lqih := NewLlmopsQueueInitHandler(hh, kclient)
	lbih := NewLlmopsBasicInitHandler(lqih)
	nptih := NewNetworkPolicyTdcInitHandler(lbih, kclient)
	iih := NewIngressInitHandler(nptih, kclient)
	lrh := NewLimitRangeInitHandler(iih, kclient)
	rqih := NewResourceQuotaInitHandler(lrh, kclient)
	pnlih := NewPatchNamespaceLabelInitHandler(rqih, kclient)
	wnrih := NewWaitNamespaceReadyInitHandler(pnlih, kclient)

	return &HandlerChain{
		head: wnrih,
	}
}

func NewK8sUpdateHandlerChain(namespace string, kclient *kubernetes.KClientset) *HandlerChain {
	// file-storage -> knowledge-base-storage -> resource-quota -> ingress
	iuh := NewIngressUpdateHandler(nil, kclient)
	lquh := NewLlmopsQueueUpdateHandler(iuh, kclient)
	rquh := NewResourceQuotaUpdateHandler(lquh, kclient)
	kbsuh := NewKnowledgeBaseStorageUpdateHandler(rquh, kclient)
	fsuh := NewFileStorageUpdateHandler(kbsuh, kclient)

	return &HandlerChain{
		head: fsuh,
	}
}

func NewTdc5UpdateHandlerChain(namespace string, kclient *kubernetes.KClientset) *HandlerChain {
	// file-storage -> knowledge-base-storage -> resource-quota -> ingress
	iuh := NewIngressUpdateHandler(nil, kclient)
	lquh := NewLlmopsQueueUpdateHandler(iuh, kclient)
	rquh := NewResourceQuotaUpdateHandler(lquh, kclient)
	kbsuh := NewKnowledgeBaseStorageUpdateHandler(rquh, kclient)
	fsuh := NewFileStorageUpdateHandler(kbsuh, kclient)

	return &HandlerChain{
		head: fsuh,
	}
}

func NewTdcUpdateHandlerChain(namespace string, kclient *kubernetes.KClientset) *HandlerChain {
	// file-storage -> knowledge-base-storage -> resource-quota -> ingress
	iuh := NewIngressUpdateHandler(nil, kclient)
	lquh := NewLlmopsQueueUpdateHandler(iuh, kclient)
	rquh := NewResourceQuotaUpdateHandler(lquh, kclient)
	kbsuh := NewKnowledgeBaseStorageUpdateHandler(rquh, kclient)
	fsuh := NewFileStorageUpdateHandler(kbsuh, kclient)

	return &HandlerChain{
		head: fsuh,
	}
}

func NewTdcHippoInitHandlerChain(namespace string, kclient *kubernetes.KClientset) *HandlerChain {
	// wait-tdc-tenant-ready hippo-tdc -> waiting-hippo-ready -> knowledge-base-storage
	kbsih := NewKnowledgeBaseStorageTdcInitHandler(nil, kclient)
	whrih := NewWaitHippoTdcReadyInitHandler(kbsih, kclient)
	hih := NewHippoTdcInitHandler(whrih)
	wttrih := NewWaitTdcTenantReadyInitHandler(hih, kclient)

	return &HandlerChain{
		head: wttrih,
	}
}

func NewTdc5HippoInitHandlerChain(namespace string, kclient *kubernetes.KClientset) *HandlerChain {
	// wait-tdc-tenant-ready hippo-tdc -> waiting-hippo-ready -> knowledge-base-storage
	// kbsih := NewKnowledgeBaseStorageTdcInitHandler(nil, kclient)
	// whrih := NewWaitHippoTdcReadyInitHandler(kbsih, kclient)
	hih := NewHippoTdc5InitHandler(nil)
	wttrih := NewWaitTdc5TenantReadyInitHandler(hih, kclient)

	return &HandlerChain{
		head: wttrih,
	}
}

func NewIngressInitHandlerChain(namespace string, kclient *kubernetes.KClientset) *HandlerChain {
	iih := NewIngressInitHandler(nil, kclient)
	wnrih := NewWaitNamespaceReadyInitHandler(iih, kclient)

	return &HandlerChain{
		head: wnrih,
	}
}

func NewK8sDeleteHandlerChain(namespace string, kclient *kubernetes.KClientset) *HandlerChain {
	deuh := NewDocEngineUninstallHandler(nil)
	huh := NewHippoUninstallHandler(deuh)
	lbuh := NewLlmopsBasicUninstallHandler(huh)
	idh := NewIngressDeleteHandler(lbuh, kclient)

	return &HandlerChain{
		head: idh,
	}
}

func NewTdcDeleteHandlerChain(namespace string, kclient *kubernetes.KClientset) *HandlerChain {
	lbuh := NewLlmopsBasicUninstallHandler(nil)
	idh := NewIngressDeleteHandler(lbuh, kclient)

	return &HandlerChain{
		head: idh,
	}
}

func NewTdc5DeleteHandlerChain(namespace string, kclient *kubernetes.KClientset) *HandlerChain {
	deuh := NewDocEngineUninstallHandler(nil)
	lbuh := NewLlmopsBasicUninstallHandler(deuh)
	idh := NewIngressDeleteHandler(lbuh, kclient)

	return &HandlerChain{
		head: idh,
	}
}

func NewLlmopsBaiscHandlerChain(namespace string, kclient *kubernetes.KClientset) *HandlerChain {
	lbh := NewLlmopsBasicInitHandler(nil)

	return &HandlerChain{
		head: lbh,
	}
}
