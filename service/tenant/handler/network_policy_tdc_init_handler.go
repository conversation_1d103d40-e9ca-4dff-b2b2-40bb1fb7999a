package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

var NetworkPolicyTdcInitHandlerName = "network_policy_tdc_init_handler"

type NetworkPolicyTdcInitHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := NetworkPolicyInitHandler{
		name: NetworkPolicyTdcInitHandlerName,
	}
	RegisterHandler(NetworkPolicyTdcInitHandlerName, &h)
}

func NewNetworkPolicyTdcInitHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := NetworkPolicyInitHandler{
		name:    NetworkPolicyTdcInitHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}

func (h *NetworkPolicyTdcInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, NetworkPolicyTdcInitHandlerName)

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *NetworkPolicyTdcInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
