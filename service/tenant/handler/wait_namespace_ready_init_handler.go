package handler

import (
	"context"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

var WaitNamespaceReadyInitHandlerName = "wait_namespace_ready_init_handler"

type WaitNamespaceReadyInitHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := WaitNamespaceReadyInitHandler{
		name: NamespaceInitHandlerName,
	}
	RegisterHandler(NamespaceInitHandlerName, &h)
}

func NewWaitNamespaceReadyInitHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := WaitNamespaceReadyInitHandler{
		name:    WaitNamespaceReadyInitHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}

func (h *WaitNamespaceReadyInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, WaitNamespaceReadyInitHandlerName)

	maxSleepSeconds := 128
	sleepSeconds := 1
	for sleepSeconds <= maxSleepSeconds {
		ready, err := h.kclient.IsNamespaceReady(namespace)
		if err != nil {
			stdlog.Errorf("Check namespace %s ready throw err: %+v", namespace, err)
		} else if ready {
			stdlog.Infof("Namespace %s is ready.", namespace)
			break
		}
		time.Sleep(time.Duration(sleepSeconds) * time.Second)
		sleepSeconds = sleepSeconds << 2
	}

	if sleepSeconds > maxSleepSeconds {
		stdlog.Errorf("Namespace %s not ready after %d seconds.", namespace, maxSleepSeconds*2)
		h.rollback(ctx, namespace)
		return stderr.Errorf("Waiting namespace %s ready timeout: %d seconds.", namespace, maxSleepSeconds*2)
	} else {
		if h.next != nil {
			return h.next.handle(ctx, namespace, params)
		}
		return nil
	}
}

func (h *WaitNamespaceReadyInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
