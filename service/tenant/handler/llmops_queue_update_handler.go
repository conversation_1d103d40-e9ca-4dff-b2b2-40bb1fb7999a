package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	resourcequota "transwarp.io/applied-ai/central-auth-service/service/tenant/resourcequota"
)

var LlmopsQueueUpdateHandlerName = "llmops_queue_update_handler"

type LlmopsQueueUpdateHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := LlmopsQueueUpdateHandler{
		name: LlmopsQueueUpdateHandlerName,
	}
	RegisterHandler(LlmopsQueueUpdateHandlerName, &h)
}

func NewLlmopsQueueUpdateHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := LlmopsQueueUpdateHandler{
		name:    LlmopsQueueUpdateHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}

func (h *LlmopsQueueUpdateHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, LlmopsQueueUpdateHandlerName)

	err := h.doHandle(ctx, namespace, params)
	if err != nil {
		stdlog.Errorf("Update queue for %s failed, err %+v", namespace, err)
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *LlmopsQueueUpdateHandler) doHandle(ctx context.Context, namespace string, params HandlerParams) error {
	if conf.C.Tenant.LlmopsQueue.Enabled {
		stdlog.Infof("Updating llmops-queue in ns %s.", namespace)
		manager, err := resourcequota.GetResourceQuotaManager()
		if err != nil {
			stdlog.Errorf("Failed to get resource quota manager: %+v", err)
			return err
		}
		err = manager.CreateOrUpgradeResourceQuota(namespace, &params.TenantResourceQuota.Hard)
		if err != nil {
			return err
		}
	}

	return nil
}

func (h *LlmopsQueueUpdateHandler) rollback(ctx context.Context, namespace string) {
	stdlog.Infof("Rollback %s for namespace %s", h.name, namespace)
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
