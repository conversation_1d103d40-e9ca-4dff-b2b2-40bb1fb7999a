package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

var ResourceQuotaUpdateHandlerName = "resource_quota_update_handler"

type ResourceQuotaUpdateHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := ResourceQuotaUpdateHandler{
		name: ResourceQuotaUpdateHandlerName,
	}
	RegisterHandler(ResourceQuotaUpdateHandlerName, &h)
}

func NewResourceQuotaUpdateHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := ResourceQuotaUpdateHandler{
		name:    ResourceQuotaUpdateHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}

func (h *ResourceQuotaUpdateHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, ResourceQuotaUpdateHandlerName)

	err := h.kclient.UpdateResourceQuota(namespace, params.ResourceQuota)
	if err != nil {
		stdlog.Errorf("Update namespace %s's resource quota failed: %+v", namespace, err)
		h.rollback(ctx, namespace)
		return stderr.Errorf("Update namespace %s's resource quota failed: %+v", namespace, err)
	} else {
		if h.next != nil {
			return h.next.handle(ctx, namespace, params)
		}
		return nil
	}
}

func (h *ResourceQuotaUpdateHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
