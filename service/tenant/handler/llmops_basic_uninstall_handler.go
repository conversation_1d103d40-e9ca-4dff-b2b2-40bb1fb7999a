package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	helm "transwarp.io/applied-ai/central-auth-service/service/tenant/helm"
)

var LlmopsBasicUninstallHandlerName = "llmops_basic_uninstall_handler"

type LlmopsBasicUninstallHandler struct {
	name string
	pre  IHandler
	next IHandler
}

func init() {
	h := LlmopsBasicInitHandler{
		name: LlmopsBasicUninstallHandlerName,
	}
	RegisterHandler(LlmopsBasicUninstallHandlerName, &h)
}

func NewLlmopsBasicUninstallHandler(next IHandler) IHandler {
	h := LlmopsBasicUninstallHandler{
		name: LlmopsBasicUninstallHandlerName,
		next: next,
	}
	return &h
}

func (h *LlmopsBasicUninstallHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, LlmopsBasicUninstallHandlerName)

	err := helm.UninstallLlmopsBasic(namespace)
	if err != nil {
		stdlog.Errorf("Helm uninstall llmops-basic failed, err: %+v", err)
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *LlmopsBasicUninstallHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
