package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

var NetworkPolicyInitHandlerName = "network_policy_init_handler"

type NetworkPolicyInitHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := NetworkPolicyInitHandler{
		name: NetworkPolicyInitHandlerName,
	}
	RegisterHandler(NetworkPolicyInitHandlerName, &h)
}

func NewNetworkPolicyInitHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := NetworkPolicyInitHandler{
		name:    NetworkPolicyInitHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}

func (h *NetworkPolicyInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, NetworkPolicyInitHandlerName)

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *NetworkPolicyInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
