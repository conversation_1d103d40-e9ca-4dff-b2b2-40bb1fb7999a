package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	helm "transwarp.io/applied-ai/central-auth-service/service/tenant/helm"
)

var DocEngineUninstallHandlerName = "doc_engine_uninstall_handler"

type DocEngineUninstallHandler struct {
	name string
	pre  IHandler
	next IHandler
}

func init() {
	h := DocEngineUninstallHandler{
		name: DocEngineUninstallHandlerName,
	}
	RegisterHandler(DocEngineUninstallHandlerName, &h)
}

func NewDocEngineUninstallHandler(next IHandler) IHandler {
	h := DocEngineUninstallHandler{
		name: DocEngineUninstallHandlerName,
		next: next,
	}
	return &h
}

func (h *DocEngineUninstallHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, DocEngineUninstallHandlerName)

	if conf.C.Tenant.DocEngine.Enabled {
		err := helm.UninstallDocEngine(namespace)
		if err != nil {
			stdlog.Errorf("Helm uninstall doc engine in %s throw err: %+v", namespace, err)
		}
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *DocEngineUninstallHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
