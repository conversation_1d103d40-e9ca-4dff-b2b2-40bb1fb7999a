package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

var ReduceResourceTdcInitHandlerName = "reduce_resource_tdc_init_handler"

type ReduceResourceTdcInitHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := ReduceResourceTdcInitHandler{
		name: ReduceResourceTdcInitHandlerName,
	}
	RegisterHandler(ReduceResourceTdcInitHandlerName, &h)
}

func NewReduceResourceTdcInitHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := ReduceResourceTdcInitHandler{
		name:    ReduceResourceTdcInitHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}

func (h *ReduceResourceTdcInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, ReduceResourceTdcInitHandlerName)

	// scale cas-srv to 1
	h.kclient.ScaleDeploymentReplicas(namespace, "transwarp.product=guardian,transwarp.name=cas-srv", 1)
	// scale guardian-server to 1
	h.kclient.ScaleWstatefulsetReplicas(namespace, "transwarp.product=guardian,transwarp.name=guardian-server", 1)

	// deploy heimdal 200m	1Gi
	h.kclient.PatchDeploymentCpuAndMemoryResource(namespace, "transwarp.product=heimdal,transwarp.name=heimdal", "200m", "1Gi", "200m", "1Gi")
	// deploy tdh-exporter 400m	500Mi
	h.kclient.PatchDeploymentCpuAndMemoryResource(namespace, "transwarp.product=tdh-exporter,transwarp.name=tdh-exporter", "400m", "500Mi", "400m", "500Mi")
	// wsts apacheds-master 300m	2.5Gi
	h.kclient.PatchWstatefulsetCpuAndMemoryResource(namespace, "transwarp.product=guardian,transwarp.name=apacheds-master", "300m", "2.5Gi", "300m", "2.5Gi")
	// wsts apacheds-slave-1 200m	1.5Gi
	h.kclient.PatchWstatefulsetCpuAndMemoryResource(namespace, "transwarp.product=guardian,transwarp.name=apacheds-slave-1", "200m", "1.5Gi", "200m", "1.5Gi")
	// wsts guardian-server  500m	2.5Gi
	h.kclient.PatchWstatefulsetCpuAndMemoryResource(namespace, "transwarp.product=guardian,transwarp.name=guardian-server", "500m", "2.5Gi", "500m", "2.5Gi")
	// deploy cas-srv 500m	1.5Gi
	h.kclient.PatchDeploymentCpuAndMemoryResource(namespace, "transwarp.product=guardian,transwarp.name=cas-srv", "500m", "1.5Gi", "500m", "1.5Gi")
	// deploy tcc 1	1.5Gi
	h.kclient.PatchDeploymentCpuAndMemoryResource(namespace, "transwarp.product=tcc,transwarp.name=tcc", "1000m", "1.5Gi", "1000m", "1.5Gi")

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *ReduceResourceTdcInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
