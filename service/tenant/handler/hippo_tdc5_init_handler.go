package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	client "transwarp.io/applied-ai/central-auth-service/service/tenant/client"
)

var HippoTdc5InitHandlerName = "hippo_tdc5_init_handler"

type HippoTdc5InitHandler struct {
	name       string
	pre        IHandler
	next       IHandler
	tdc5Client *client.TDC5Client
}

func init() {
	h := HippoTdc5InitHandler{
		name: HippoTdc5InitHandlerName,
	}
	RegisterHandler(HippoTdc5InitHandlerName, &h)
}

func NewHippoTdc5InitHandler(next IHandler) IHandler {
	h := HippoTdc5InitHandler{
		name: HippoTdc5InitHandlerName,
		next: next,
	}
	h.tdc5Client = client.GetClientFactory().GetTDC5Client()
	return &h
}

func (h *HippoTdc5InitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, HippoTdc5InitHandlerName)

	if conf.C.Tenant.Hippo.Enabled {
		stdlog.Infof("Check whether hippo is installed in namespace %s.", namespace)
		installed, err := h.tdc5Client.IsHippoInstalled(ctx, namespace)
		// hippo instance already exists, return
		if err != nil {
			stdlog.Errorf("Try get hippo before install hippo failed: %+v", err)
		} else if installed {
			stdlog.Infof("Hippo has installed in namespace %s.", namespace)
		} else {
			err := h.tdc5Client.InstallHippo(ctx, namespace)
			if err != nil {
				stdlog.Errorf("Installing hippo failed: %+v", err)
			} else {
				stdlog.Infof("Install hippo in namespace %s completed.", namespace)
			}
		}
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *HippoTdc5InitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
