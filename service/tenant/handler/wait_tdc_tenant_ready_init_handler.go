package handler

import (
	"context"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	client "transwarp.io/applied-ai/central-auth-service/service/tenant/client"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

var WaitTdcTenantReadyInitHandlerName = "wait_tdc_tenant_ready_init_handler"

type WaitTdcTenantReadyInitHandler struct {
	name      string
	pre       IHandler
	next      IHandler
	kclient   *kubernetes.KClientset
	tdcClient *client.TDCClient
}

func init() {
	h := WaitTdcTenantReadyInitHandler{
		name: WaitTdcTenantReadyInitHandlerName,
	}
	RegisterHandler(WaitTdcTenantReadyInitHandlerName, &h)
}

func NewWaitTdcTenantReadyInitHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := WaitTdcTenantReadyInitHandler{
		name:    WaitTdcTenantReadyInitHandlerName,
		next:    next,
		kclient: kclient,
	}
	h.tdcClient = client.GetClientFactory().GetTDCClient()
	return &h
}

func (h *WaitTdcTenantReadyInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, WaitTdcTenantReadyInitHandlerName)

	maxSleepSeconds := 3601
	sleepSeconds := 0
	sleepInterval := 30
	for sleepSeconds <= maxSleepSeconds {
		tenant, _ := h.tdcClient.GetTenant(ctx, namespace)
		stdlog.Infof("Tenant %s status is %s ", namespace, tenant.TenantStatus)
		if tenant.TenantStatus == "ACTIVATING" {
			break
		} else if tenant.TenantStatus == "CREATING" {
			time.Sleep(time.Duration(sleepInterval) * time.Second)
			sleepSeconds = sleepSeconds + sleepInterval
		} else {
			stdlog.Errorf("Create tdc tenant %s failed with status: %s", namespace, tenant.TenantStatus)
			return stderr.Errorf("Create tdc tenant %s failed with status: %s", namespace, tenant.TenantStatus)
		}
	}

	if sleepSeconds > maxSleepSeconds {
		stdlog.Errorf("Tdc tenant %s not ready after %d seconds.", namespace, maxSleepSeconds*2)
		return stderr.Errorf("Waiting tdc tenant %s ready timeout: %d seconds.", namespace, maxSleepSeconds*2)
	} else {
		if h.next != nil {
			return h.next.handle(ctx, namespace, params)
		}
		return nil
	}
}

func (h *WaitTdcTenantReadyInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
