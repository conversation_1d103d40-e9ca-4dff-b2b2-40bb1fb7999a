package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

var PatchNamespaceLabelInitHandlerName = "patch_namespace_label_init_handler"

type PatchNamespaceLabelInitHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := PatchNamespaceLabelInitHandler{
		name: PatchNamespaceLabelInitHandlerName,
	}
	RegisterHandler(PatchNamespaceLabelInitHandlerName, &h)
}

func NewPatchNamespaceLabelInitHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := PatchNamespaceLabelInitHandler{
		name:    PatchNamespaceLabelInitHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}

func (h *PatchNamespaceLabelInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, PatchNamespaceLabelInitHandlerName)

	var lables map[string]string = make(map[string]string)
	if params.NamespaceLables != nil {
		lables = params.NamespaceLables
	}
	err := h.kclient.PatchNamespaceLable(namespace, lables)
	if err != nil {
		stdlog.Errorf("Patch namespace %s labels failed, err: %+v", namespace, err)
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *PatchNamespaceLabelInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
