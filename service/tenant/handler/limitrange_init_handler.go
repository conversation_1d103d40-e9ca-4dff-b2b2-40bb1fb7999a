package handler

import (
	"context"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

var DefaultLimitRangeName = "limitrange-default"
var LimitRangeInitHandlerName = "limit_range_init_handler"

type LimitRangeInitHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := LimitRangeInitHandler{
		name: LimitRangeInitHandlerName,
	}
	RegisterHandler(LimitRangeInitHandlerName, &h)
}

func NewLimitRangeInitHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := LimitRangeInitHandler{
		name:    LimitRangeInitHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}

func (h *LimitRangeInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, LimitRangeInitHandlerName)

	limitRange := &v1.LimitRange{
		ObjectMeta: metav1.ObjectMeta{
			Name:      DefaultLimitRangeName,
			Namespace: namespace,
		},
		Spec: v1.LimitRangeSpec{
			Limits: []v1.LimitRangeItem{
				{
					Type: v1.LimitTypeContainer,
					Default: v1.ResourceList{
						v1.ResourceCPU:    resource.MustParse("500m"),
						v1.ResourceMemory: resource.MustParse("1024Mi"),
					},
					DefaultRequest: v1.ResourceList{
						v1.ResourceCPU:    resource.MustParse("200m"),
						v1.ResourceMemory: resource.MustParse("256Mi"),
					},
				},
			},
		},
	}
	_, err := h.kclient.KubeClient.CoreV1().LimitRanges(namespace).Create(context.TODO(), limitRange, metav1.CreateOptions{})
	if err != nil {
		stdlog.Errorf("Set limit range failed in ns %s", namespace)
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *LimitRangeInitHandler) rollback(ctx context.Context, namespace string) {
	err := h.kclient.KubeClient.CoreV1().LimitRanges(namespace).Delete(context.TODO(), DefaultLimitRangeName, metav1.DeleteOptions{})
	if err != nil {
		stdlog.Errorf("Delete %s's limit range failed when exec rollback: %v", namespace, err)
	}
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
