package handler

import (
	"context"
	"fmt"

	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	consts "transwarp.io/applied-ai/central-auth-service/service/tenant/consts"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	customtypes "transwarp.io/applied-ai/central-auth-service/service/tenant/types"
)

var KnowledgeBaseStorageTdcInitHandlerName = "knowledge_base_storage_tdc_init_handler"

type KnowledgeBaseStorageTdcInitHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := KnowledgeBaseStorageTdcInitHandler{
		name: KnowledgeBaseStorageTdcInitHandlerName,
	}
	RegisterHandler(KnowledgeBaseStorageTdcInitHandlerName, &h)
}

func NewKnowledgeBaseStorageTdcInitHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := KnowledgeBaseStorageTdcInitHandler{
		name:    KnowledgeBaseStorageTdcInitHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}

func (h *KnowledgeBaseStorageTdcInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, KnowledgeBaseStorageTdcInitHandlerName)

	err := h.doHandle(ctx, namespace, params)
	if err != nil {
		stdlog.Errorf("Init tdc knowledge base storage pvc to resource quota limit failed, err: %+v", err)
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *KnowledgeBaseStorageTdcInitHandler) doHandle(ctx context.Context, namespace string, params HandlerParams) error {
	var quantity resource.Quantity
	if v, ok := params.ResourceQuota.Spec.Hard[customtypes.ResourceKnowledgeBaseStorage]; ok {
		quantity = v
	} else {
		q, err := resource.ParseQuantity(conf.C.Tenant.DefaultQuota.KnowledgeBaseStorage)
		if err != nil {
			stdlog.Errorf("Error Parse knowledge base storage quota to quantity failed, err: %+v", err)
			return err
		}
		quantity = q
	}
	knowledgeBaseStorage := quantity.Value() / (1 << 30)

	pvcs, err := h.kclient.KubeClient.CoreV1().PersistentVolumeClaims(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: consts.TdcHippoPvcLabelSelector,
	})
	if err != nil {
		stdlog.Errorf("Find hippo pvc in %s err: %+v.", namespace, err)
		return err
	}
	if len(pvcs.Items) <= 0 {
		return stderr.Error("Not found pvcs in namespace %s with label %s.", namespace, consts.TdcHippoPvcLabelSelector)
	}

	numOfPvc := len(pvcs.Items)
	eachPvcSize := make([]int64, numOfPvc)
	avg := knowledgeBaseStorage / int64(numOfPvc)
	remainder := knowledgeBaseStorage % int64(numOfPvc)
	for i := 0; i < numOfPvc; i++ {
		eachPvcSize[i] = avg
	}
	for i := 0; i < int(remainder); i++ {
		eachPvcSize[i]++
	}

	for i := 0; i < numOfPvc; i++ {
		if eachPvcSize[i] <= 0 {
			stdlog.Warnf("Init hippo pvc size is <=%d, continue directly", eachPvcSize[i])
			continue
		}
		pvc := pvcs.Items[i]
		newSize := fmt.Sprintf("%dGi", eachPvcSize[i])
		stdlog.Infof("Do Patch PVC %s storage to %s", pvc.Name, newSize)

		err := h.kclient.PatchPvcCapacity(pvc.Namespace, pvc.Name, newSize)
		if err != nil {
			stdlog.Errorf("Patch pvc %s in %s errror.", pvc.Name, namespace)
			return err
		}
	}

	return nil
}

func (h *KnowledgeBaseStorageTdcInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
