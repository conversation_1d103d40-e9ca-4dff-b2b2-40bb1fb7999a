package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

var NamespaceInitHandlerName = "namespace_init_handler"

type NamespaceInitHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := NamespaceInitHandler{
		name: NamespaceInitHandlerName,
	}
	RegisterHandler(NamespaceInitHandlerName, &h)
}

func NewNamespaceInitHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := NamespaceInitHandler{
		name:    NamespaceInitHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}

func (h *NamespaceInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, NamespaceInitHandlerName)

	var lables map[string]string = make(map[string]string)
	if params.NamespaceLables != nil {
		lables = params.NamespaceLables
	}
	err := h.kclient.CreateNamespace(namespace, lables)
	if err != nil {
		stdlog.Errorf("Create namespace %s failed, err: %+v", namespace, err)
		h.rollback(ctx, namespace)
	} else {
		if h.next != nil {
			return h.next.handle(ctx, namespace, params)
		}
		return nil
	}
	return err
}

func (h *NamespaceInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
