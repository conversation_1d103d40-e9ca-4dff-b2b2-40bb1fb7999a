package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

var FileStorageInitHandlerName = "file_storage_init_handler"

type FileStorageInitHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := FileStorageInitHandler{
		name: FileStorageInitHandlerName,
	}
	RegisterHandler(FileStorageInitHandlerName, &h)
}

func NewFileStorageInitHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := FileStorageInitHandler{
		name:    FileStorageInitHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}

func (h *FileStorageInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, FileStorageInitHandlerName)

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *FileStorageInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
