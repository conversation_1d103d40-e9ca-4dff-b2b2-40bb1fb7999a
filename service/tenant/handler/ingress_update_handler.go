package handler

import (
	"context"
	"encoding/json"
	"fmt"

	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	customtypes "transwarp.io/applied-ai/central-auth-service/service/tenant/types"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

var IngressUpdateHandlerName = "ingress_update_handler"

type IngressUpdateHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := IngressUpdateHandler{
		name: IngressUpdateHandlerName,
	}
	RegisterHandler(IngressUpdateHandlerName, &h)
}

func NewIngressUpdateHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := IngressUpdateHandler{
		name:    IngressUpdateHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}

func (h *IngressUpdateHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, IngressUpdateHandlerName)

	// whatever doHandle success or failed. call h.next.handle alwarys
	err := h.doHandle(ctx, namespace, params)
	if err != nil {
		stdlog.Errorf("Update ingress bandwidth for %s failed, err %+v", namespace, err)
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *IngressUpdateHandler) doHandle(ctx context.Context, namespace string, params HandlerParams) error {
	ns, err := util.GetCurrentNamespace()
	if err != nil {
		stdlog.Errorf("Error reading current namespace: %v", err)
		return err
	}

	var quatity resource.Quantity
	if v, ok := params.ResourceQuota.Spec.Hard[customtypes.ResourceBandwidth]; ok {
		quatity = v
	} else {
		q, err := resource.ParseQuantity(conf.C.Tenant.DefaultQuota.Bandwidth)
		if err != nil {
			stdlog.Errorf("Error Parse default quota bandwidth to quntity: %v", err)
			return err
		}
		quatity = q
	}
	limitRateKb := quatity.Value() / (1 << 10)

	ingressName := fmt.Sprintf("%s-ingress", namespace)

	patchData := map[string]interface{}{
		"metadata": map[string]interface{}{
			"annotations": map[string]string{
				"nginx.ingress.kubernetes.io/limit-rate": fmt.Sprintf("%d", limitRateKb),
			},
		},
	}

	patchBytes, err := json.Marshal(patchData)
	if err != nil {
		stdlog.Errorf("Error marshalling patch data: %v", err)
		return err
	}

	result, err := h.kclient.KubeClient.NetworkingV1().Ingresses(ns).Patch(context.TODO(), ingressName, types.MergePatchType, patchBytes, metav1.PatchOptions{})
	if err != nil {
		stdlog.Errorf("Error patching Ingress: %v\n", err)
		return err
	}

	stdlog.Infof("Patch Ingress %q for %s done.\n", result.GetObjectMeta().GetName(), namespace)

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *IngressUpdateHandler) rollback(ctx context.Context, namespace string) {
	stdlog.Infof("Rollback %s for namespace %s", h.name, namespace)
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
