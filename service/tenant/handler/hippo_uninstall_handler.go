package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	helm "transwarp.io/applied-ai/central-auth-service/service/tenant/helm"
)

var HippoUninstallHandlerName = "hippo_uninstall_handler"

type HippoUninstallHandler struct {
	name string
	pre  IHandler
	next IHandler
}

func init() {
	h := HippoUninstallHandler{
		name: HippoUninstallHandlerName,
	}
	RegisterHandler(HippoUninstallHandlerName, &h)
}

func NewHippoUninstallHandler(next IHandler) IHandler {
	h := HippoUninstallHandler{
		name: HippoUninstallHandlerName,
		next: next,
	}
	return &h
}

func (h *HippoUninstallHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, HippoUninstallHandlerName)

	if conf.C.Tenant.Hippo.Enabled {
		err := helm.UninstallHippo(namespace)
		if err != nil {
			stdlog.Errorf("Helm uninstall hippo in %s throw err: %+v", namespace, err)
		}
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *HippoUninstallHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
