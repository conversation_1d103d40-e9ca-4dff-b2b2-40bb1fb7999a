package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

var ResourceQuotaInitHandlerName = "resource_quota_init_handler"

type ResourceQuotaInitHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := ResourceQuotaInitHandler{
		name: ResourceQuotaInitHandlerName,
	}
	RegisterHandler(ResourceQuotaInitHandlerName, &h)
}

func NewResourceQuotaInitHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := ResourceQuotaInitHandler{
		name:    ResourceQuotaInitHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}

func (h *ResourceQuotaInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, ResourceQuotaInitHandlerName)

	err := h.doHandle(ctx, namespace, params)
	if err != nil {
		h.rollback(ctx, namespace)
	} else {
		if h.next != nil {
			return h.next.handle(ctx, namespace, params)
		}
		return nil
	}
	return err
}

func (h *ResourceQuotaInitHandler) doHandle(ctx context.Context, namespace string, params HandlerParams) error {
	quota := params.ResourceQuota
	if quota == nil {
		stdlog.Errorf("Illegal param ResourceQuota is nil")
		return stderr.Error("Illegal param ResourceQuota is nil")
	}

	err := h.kclient.CreateResourceQuota(namespace, quota)
	if err != nil {
		stdlog.Errorf("Create resource quota for %s failed, err: %+v", namespace, err)
		return err
	}
	return nil
}

func (h *ResourceQuotaInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
