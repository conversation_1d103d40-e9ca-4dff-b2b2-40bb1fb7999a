package handler

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	resourcequota "transwarp.io/applied-ai/central-auth-service/service/tenant/resourcequota"
)

var LlmopsQueueInitHandlerName = "llmops_queue_init_handler"

type LlmopsQueueInitHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := LlmopsQueueInitHandler{
		name: LlmopsQueueInitHandlerName,
	}
	RegisterHandler(LlmopsQueueInitHandlerName, &h)
}

func NewLlmopsQueueInitHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := LlmopsQueueInitHandler{
		name:    LlmopsQueueInitHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}

func (h *LlmopsQueueInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, LlmopsQueueInitHandlerName)

	err := h.doHandle(ctx, namespace, params)
	if err != nil {
		stdlog.Errorf("Init queue for %s failed, err %+v", namespace, err)
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *LlmopsQueueInitHandler) doHandle(ctx context.Context, namespace string, params HandlerParams) error {
	if conf.C.Tenant.LlmopsQueue.Enabled {
		stdlog.Infof("Installing llmops-queue in ns %s.", namespace)

		manager, err := resourcequota.GetResourceQuotaManager()
		if err != nil {
			stdlog.Errorf("Failed to get resource quota manager: %+v", err)
			return err
		}
		if params.TenantResourceQuota == nil {
			stdlog.Errorf("TenantResourceQuota is nil")
			return nil
		}
		err = manager.CreateOrUpgradeResourceQuota(namespace, &params.TenantResourceQuota.Hard)
		if err != nil {
			return err
		}
	} else {
		stdlog.Infof("Skip Installing llmops-queue in ns %s, because llmops-queue not enabled.", namespace)
	}

	return nil
}

func (h *LlmopsQueueInitHandler) rollback(ctx context.Context, namespace string) {
	stdlog.Infof("Rollback %s for namespace %s", h.name, namespace)
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
