package handler

import (
	"context"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	client "transwarp.io/applied-ai/central-auth-service/service/tenant/client"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

var WaitTdc5TenantReadyInitHandlerName = "wait_tdc5_tenant_ready_init_handler"

type WaitTdc5TenantReadyInitHandler struct {
	name       string
	pre        IHandler
	next       IHandler
	kclient    *kubernetes.KClientset
	tdc5Client *client.TDC5Client
}

func init() {
	h := WaitTdc5TenantReadyInitHandler{
		name: WaitTdc5TenantReadyInitHandlerName,
	}
	RegisterHandler(WaitTdc5TenantReadyInitHandlerName, &h)
}

func NewWaitTdc5TenantReadyInitHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := WaitTdc5TenantReadyInitHandler{
		name:    WaitTdc5TenantReadyInitHandlerName,
		next:    next,
		kclient: kclient,
	}
	h.tdc5Client = client.GetClientFactory().GetTDC5Client()
	return &h
}

func (h *WaitTdc5TenantReadyInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, WaitTdc5TenantReadyInitHandlerName)

	maxSleepSeconds := 3601
	sleepSeconds := 0
	sleepInterval := 30
	for sleepSeconds <= maxSleepSeconds {
		tenant, _ := h.tdc5Client.GetTenant(ctx, namespace)
		stdlog.Infof("Tdc5 tenant %s status is %s ", namespace, tenant.TenantStatus)
		if tenant.TenantStatus == "Active" {
			break
		} else if tenant.TenantStatus == "CREATING" {
			time.Sleep(time.Duration(sleepInterval) * time.Second)
			sleepSeconds = sleepSeconds + sleepInterval
		} else {
			stdlog.Errorf("Create tdc5 tenant %s failed with status: %s", namespace, tenant.TenantStatus)
			return stderr.Errorf("Create tdc5 tenant %s failed with status: %s", namespace, tenant.TenantStatus)
		}
	}

	if sleepSeconds > maxSleepSeconds {
		stdlog.Errorf("Tdc5 tenant %s not ready after %d seconds.", namespace, maxSleepSeconds*2)
		return stderr.Errorf("Waiting tdc5 tenant %s ready timeout: %d seconds.", namespace, maxSleepSeconds*2)
	} else {
		if h.next != nil {
			return h.next.handle(ctx, namespace, params)
		}
		return nil
	}
}

func (h *WaitTdc5TenantReadyInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
