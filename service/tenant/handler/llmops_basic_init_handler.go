package handler

import (
	"context"
	"fmt"
	"os"

	"k8s.io/apimachinery/pkg/api/resource"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	helm "transwarp.io/applied-ai/central-auth-service/service/tenant/helm"
	customtypes "transwarp.io/applied-ai/central-auth-service/service/tenant/types"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

var LlmopsBasicInitHandlerName = "llmops_basic_init_handler"

type LlmopsBasicInitHandler struct {
	name string
	pre  IHandler
	next IHandler
}

func init() {
	h := LlmopsBasicInitHandler{
		name: LlmopsBasicInitHandlerName,
	}
	RegisterHandler(LlmopsBasicInitHandlerName, &h)
}

func NewLlmopsBasicInitHandler(next IHandler) IHandler {
	h := LlmopsBasicInitHandler{
		name: LlmopsBasicInitHandlerName,
		next: next,
	}
	return &h
}

func (h *LlmopsBasicInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, LlmopsBasicInitHandlerName)

	sfsPath := fmt.Sprintf("/sfs/tenants/%s", namespace)
	err := os.MkdirAll(sfsPath, os.ModePerm)
	if err != nil {
		stdlog.Errorf("Mkdir %s for tenant %s failed, err: %+v", sfsPath, namespace, err)
	}

	// add llmops prefix, linux system has dir sys
	sysPath := fmt.Sprintf("/llmops/sys/tenants/%s", namespace)
	err = os.MkdirAll(sysPath, os.ModePerm)
	if err != nil {
		stdlog.Errorf("Mkdir %s for tenant %s failed, err: %+v", sysPath, namespace, err)
	}

	// --set global.storage.request=100Gi
	// --set global.storage.sfs.request=100Gi
	var quantity resource.Quantity
	if v, ok := params.ResourceQuota.Spec.Hard[customtypes.ResourceKnowledgeBaseStorage]; ok {
		quantity = v
	} else {
		q, err := resource.ParseQuantity(conf.C.Tenant.DefaultQuota.KnowledgeBaseStorage)
		if err != nil {
			stdlog.Errorf("Error Parse knowledge base storage quota to quantity failed, err: %+v", err)
			return err
		}
		quantity = q
	}
	knowledgeBaseStorage := util.ToGiInt(quantity.Value())
	sysStorageRequest := fmt.Sprintf("global.storage.request=%s", knowledgeBaseStorage)

	if v, ok := params.ResourceQuota.Spec.Hard[customtypes.ResourceFileStorage]; ok {
		quantity = v
	} else {
		q, err := resource.ParseQuantity(conf.C.Tenant.DefaultQuota.FileStorage)
		if err != nil {
			stdlog.Errorf("Error Parse file storage quota to quantity failed, err: %+v", err)
			return err
		}
		quantity = q
	}
	fileStorage := util.ToGiInt(quantity.Value())
	sfsStorageRequest := fmt.Sprintf("global.storage.sfs.request=%s", fileStorage)

	err = helm.InstallLlmopsBasic(namespace, []string{sfsStorageRequest, sysStorageRequest})
	if err != nil {
		stdlog.Errorf("Helm install llmops-basic failed, err: %+v", err)
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *LlmopsBasicInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
