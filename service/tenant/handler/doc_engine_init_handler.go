package handler

import (
	"context"
	"fmt"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	httpclient "transwarp.io/applied-ai/central-auth-service/service/tenant/client"
	helm "transwarp.io/applied-ai/central-auth-service/service/tenant/helm"
)

var DocEngineInitHandlerName = "doc_engine_init_handler"

type DocEngineInitHandler struct {
	name string
	pre  IHandler
	next IHandler
}

func init() {
	h := DocEngineInitHandler{
		name: DocEngineInitHandlerName,
	}
	RegisterHandler(DocEngineInitHandlerName, &h)
}

func NewDocEngineInitHandler(next IHandler) IHandler {
	h := DocEngineInitHandler{
		name: DocEngineInitHandlerName,
		next: next,
	}
	return &h
}

func (h *DocEngineInitHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, DocEngineInitHandlerName)

	if conf.C.Tenant.DocEngine.Enabled {
		installed, err := helm.DocEngineExists(namespace)
		if err != nil {
			stdlog.Errorf("Check whether doc engine installed in %s throw err: %+v", namespace, err)
		} else if installed {
			stdlog.Infof("Doc engine in ns %s has installed.", namespace)
		} else {
			stdlog.Infof("Installing doc engine in ns %s.", namespace)

			setParams := make([]string, 0)
			host, port, err := httpclient.GetHippoServiceHostPort(namespace)
			stdlog.Errorf("Hippo service host: %s port: %s.", host, port)
			if err != nil {
				stdlog.Errorf("Get hippo service host port failed: %v", err)
			}
			setParams = append(setParams, fmt.Sprintf("global.hippo.host=%s", host))
			setParams = append(setParams, fmt.Sprintf("global.hippo.port=%s", port))

			masterHost, masterPort, err := httpclient.GetHippoMasterServiceHostPort(namespace)
			stdlog.Errorf("Hippo master service host: %s port: %s.", masterHost, masterPort)
			if err != nil {
				stdlog.Errorf("Get hippo master service host port failed: %v", err)
			}
			setParams = append(setParams, fmt.Sprintf("global.hippo.masterHost=%s", masterHost))
			setParams = append(setParams, fmt.Sprintf("global.hippo.masterPort=%s", masterPort))

			err = helm.InstallDocEngine(namespace, setParams)
			if err != nil {
				stdlog.Errorf("Install doc engine in %s failed: %+v", namespace, err)
			} else {
				stdlog.Infof("Installing doc engine in ns %s done.", namespace)
			}
		}
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *DocEngineInitHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
