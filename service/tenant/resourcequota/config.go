package resourcequota

import (
	"fmt"
	"os"

	"sigs.k8s.io/yaml"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	k8s "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

const (
	defaultHamiNamespace                = "llm-system"
	defaultHamiSchedulerDeviceConfigMap = "llm-system-hami-scheduler-device"
)

// DeviceConfig 包含所有设备类型的配置
type DeviceConfig struct {
	Nvidia    *NvidiaConfig    `yaml:"nvidia"`
	Cambricon *CambriconConfig `yaml:"cambricon"`
	Hygon     *HygonConfig     `yaml:"hygon"`
	Metax     *MetaxConfig     `yaml:"metax"`
	Mthreads  *MthreadsConfig  `yaml:"mthreads"`
	Iluvatar  *IluvatarConfig  `yaml:"iluvatar"`
	VNPUs     []VNPUConfig     `yaml:"vnpus"`
}

// NvidiaConfig 包含NVIDIA设备的配置
type NvidiaConfig struct {
	ResourceCountName            string `yaml:"resourceCountName"`
	ResourceMemoryName           string `yaml:"resourceMemoryName"`
	ResourceMemoryPercentageName string `yaml:"resourceMemoryPercentageName"`
	ResourceCoreName             string `yaml:"resourceCoreName"`
	ResourcePriorityName         string `yaml:"resourcePriorityName"`
	// 其他字段省略
}

// CambriconConfig 包含寒武纪设备的配置
type CambriconConfig struct {
	ResourceCountName  string `yaml:"resourceCountName"`
	ResourceMemoryName string `yaml:"resourceMemoryName"`
	ResourceCoreName   string `yaml:"resourceCoreName"`
}

// HygonConfig 包含海光设备的配置
type HygonConfig struct {
	ResourceCountName  string `yaml:"resourceCountName"`
	ResourceMemoryName string `yaml:"resourceMemoryName"`
	ResourceCoreName   string `yaml:"resourceCoreName"`
}

// MetaxConfig 包含元脑设备的配置
type MetaxConfig struct {
	ResourceCountName string `yaml:"resourceCountName"`
}

// MthreadsConfig 包含摩尔线程设备的配置
type MthreadsConfig struct {
	ResourceCountName  string `yaml:"resourceCountName"`
	ResourceMemoryName string `yaml:"resourceMemoryName"`
	ResourceCoreName   string `yaml:"resourceCoreName"`
}

// IluvatarConfig 包含壁仞设备的配置
type IluvatarConfig struct {
	ResourceCountName  string `yaml:"resourceCountName"`
	ResourceMemoryName string `yaml:"resourceMemoryName"`
	ResourceCoreName   string `yaml:"resourceCoreName"`
}

// VNPUConfig 包含华为昇腾设备的配置
type VNPUConfig struct {
	ChipName           string `yaml:"chipName"`
	CommonWord         string `yaml:"commonWord"`
	ResourceName       string `yaml:"resourceName"`
	ResourceMemoryName string `yaml:"resourceMemoryName"`
	// 其他字段省略
}

// ResourceNames 包含设备的资源名称
type ResourceNames struct {
	ChipName   string
	CommonWord string
	CountName  string
	MemoryName string
	CoreName   string
}

// LoadDeviceConfig 从k8s configmap加载设备配置
func LoadDeviceConfig(namespace, configMapName string) (*DeviceConfig, error) {
	client, err := k8s.NewKClientset()
	if err != nil {
		return nil, err
	}

	// 获取ConfigMap
	configMap, err := client.K8sInformerClient.GetConfigMap(namespace, configMapName)
	if err != nil {
		return nil, fmt.Errorf("failed to get ConfigMap %s in namespace %s: %w", configMapName, namespace, err)
	}

	// 获取device-config.yaml内容
	configData, ok := configMap.Data["device-config.yaml"]
	if !ok {
		return nil, fmt.Errorf("ConfigMap %s does not contain key 'device-config.yaml'", configMapName)
	}

	// 解析YAML
	var deviceConfig DeviceConfig
	if err := yaml.Unmarshal([]byte(configData), &deviceConfig); err != nil {
		return nil, fmt.Errorf("failed to unmarshal device config: %w", err)
	}

	return &deviceConfig, nil
}

// LoadDeviceConfigFromFile 从文件加载设备配置
func LoadDeviceConfigFromFile(filePath string) (*DeviceConfig, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read device config file: %w", err)
	}

	var deviceConfig DeviceConfig
	if err := yaml.Unmarshal(data, &deviceConfig); err != nil {
		return nil, fmt.Errorf("failed to unmarshal device config: %w", err)
	}

	return &deviceConfig, nil
}

// GetDeviceResourceNamesFromFile 从文件获取设备资源名称
func GetDeviceResourceNamesFromFile(filePath string) (map[string]ResourceNames, error) {
	config, err := LoadDeviceConfigFromFile(filePath)
	if err != nil {
		return nil, err
	}

	return GetAllResourceNames(config), nil
}

// GetAllResourceNames 获取所有设备类型的资源名称
func GetAllResourceNames(config *DeviceConfig) map[string]ResourceNames {
	result := make(map[string]ResourceNames)

	// NVIDIA
	if config.Nvidia != nil {
		result["nvidia"] = ResourceNames{
			ChipName:   "nvidia",
			CommonWord: "nvidia",
			CountName:  config.Nvidia.ResourceCountName,
			MemoryName: config.Nvidia.ResourceMemoryName,
			CoreName:   config.Nvidia.ResourceCoreName,
		}
	}

	// Cambricon
	if config.Cambricon != nil {
		result["cambricon"] = ResourceNames{
			ChipName:   "carbricon",
			CommonWord: "carbricon",
			CountName:  config.Cambricon.ResourceCountName,
			MemoryName: config.Cambricon.ResourceMemoryName,
			CoreName:   config.Cambricon.ResourceCoreName,
		}
	}

	// Hygon
	if config.Hygon != nil {
		result["hygon"] = ResourceNames{
			ChipName:   "hygon",
			CommonWord: "hygon",
			CountName:  config.Hygon.ResourceCountName,
			MemoryName: config.Hygon.ResourceMemoryName,
			CoreName:   config.Hygon.ResourceCoreName,
		}
	}

	// Metax
	if config.Metax != nil {
		result["metax"] = ResourceNames{
			ChipName:   "metax",
			CommonWord: "metax",
			CountName:  config.Metax.ResourceCountName,
		}
	}

	// Mthreads
	if config.Mthreads != nil {
		result["mthreads"] = ResourceNames{
			ChipName:   "mthreads",
			CommonWord: "mthreads",
			CountName:  config.Mthreads.ResourceCountName,
			MemoryName: config.Mthreads.ResourceMemoryName,
			CoreName:   config.Mthreads.ResourceCoreName,
		}
	}

	// Iluvatar
	if config.Iluvatar != nil {
		result["iluvatar"] = ResourceNames{
			ChipName:   "iluvatar",
			CommonWord: "iluvatar",
			CountName:  config.Iluvatar.ResourceCountName,
			MemoryName: config.Iluvatar.ResourceMemoryName,
			CoreName:   config.Iluvatar.ResourceCoreName,
		}
	}

	// VNPUs (Huawei Ascend)
	for _, vnpu := range config.VNPUs {
		result[vnpu.ChipName] = ResourceNames{
			ChipName:   vnpu.ChipName,
			CommonWord: vnpu.CommonWord,
			CountName:  vnpu.ResourceName,
			MemoryName: vnpu.ResourceMemoryName,
		}
	}

	return result
}

// GetDeviceResourceNames 从k8s configmap获取设备资源名称
func GetDeviceResourceNames() (map[string]ResourceNames, error) {
	config, err := LoadDeviceConfig(
		func() string {
			if hamiNs := os.Getenv("HAMI_NAMESPACE"); hamiNs != "" {
				return hamiNs
			} else {
				return defaultHamiNamespace
			}
		}(),
		func() string {
			if cmn := os.Getenv("HAMI_SCHEDULER_DEVICE_CONFIG_MAP"); cmn != "" {
				return cmn
			} else {
				return defaultHamiSchedulerDeviceConfigMap
			}
		}())
	if err != nil {
		stdlog.Warnf("Load device config from k8s failed, try load from file.")
		config, err = LoadDeviceConfigFromFile("./etc/device-config.yaml")
		if err != nil {
			return nil, err
		}
	}

	return GetAllResourceNames(config), nil
}
