package resourcequota

import (
	"testing"
	"time"

	"transwarp.io/aip/llmops-common/pkg/crd"
)

// TestFlushDefaultResourceQuotaV2ToYaml 测试将默认资源配额写入YAML文件
func TestFlushDefaultResourceQuotaV2ToYaml(t *testing.T) {
	manager, err := GetResourceQuotaManager()
	if err != nil {
		t.Fatalf("Failed to get resource quota manager: %v", err)
	}

	crd.Init()

	defaultQuota := manager.GetDefaultQuota()
	if defaultQuota == nil {
		t.Fatal("Failed to get default resource quota")
	}

	// err = manager.CreateOrUpgradeResourceQuota("dev-ls", &defaultQuota.Hard)
	// if err != nil {
	// 	t.Fatalf("Failed to create or upgrade resource quota: %v", err)
	// }
	spec1 := manager.getDefaultLimitsResourceQuotaSpec()
	if spec1 == nil {
		t.Fatal("Failed to get default limits resource quota spec")
	}
	limits, _ := manager.GetClusterResourceLimits()
	if limits == nil {
		t.Fatal("Failed to get cluster resource limits")
	}

	spec, err := manager.getResourcequota("dev-ls")
	if err != nil {
		t.Fatalf("Failed to get resource quota: %v", err)
	}
	if spec == nil {
		t.Fatal("Failed to get resource quota")
	}
	time.Sleep(10 * time.Minute)
}

func TestNxgToResourceQuotaSpec(t *testing.T) {
	manager, err := GetResourceQuotaManager()
	if err != nil {
		t.Fatalf("Failed to get resource quota manager: %v", err)
	}

	crd.Init()

	quota, err := manager.NxgToResourceQuotaSpec([]string{"d9bc4a88-e4f8-4803-a90b-49a47fd2eaca"})
	if err != nil {
		t.Fatalf("Failed to convert nxg to resource quota: %v", err)
	}
	if quota == nil {
		t.Fatal("Failed to convert nxg to resource quota")
	}
}
