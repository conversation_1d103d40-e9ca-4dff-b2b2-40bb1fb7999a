package resourcequota

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	xpu "transwarp.io/aip/llmops-common/pkg/crd"
	knm_v1 "transwarp.io/applied-ai/kube-nodexpu-manager/apis/resources/v1alpha1"

	"gopkg.in/yaml.v3"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/models"
	helm "transwarp.io/applied-ai/central-auth-service/service/tenant/helm"
	k8s "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

const (
	annotationKeyResourceQuotaSpec = "transwarp.io/resource-quota-spec"

	labelKeyResourceQuotaSpec   = "transwarp.io/resource-quota-spec"
	labelValueResourceQuotaSpec = "true"

	resourceQuotaConfigKey = "resourcequota-spec.yaml"
)

var (
	instance *ResourceQuotaManager
	once     sync.Once
)

// tmpl for render helm values.yaml
type LlmopsQueueHelmValues struct {
	Resourcequota LlmopsQueueResourceQuota `json:"resourcequota" yaml:"resourcequota"`
}

type LlmopsQueueResourceQuota struct {
	Cohort models.ResourceQuotaSpec `json:"cohort" yaml:"cohort"`
}

type ResourceQuotaManager struct {
	defaultQuota *models.TenantResourceQuota

	kclient     *k8s.KClientset
	configWatch chan *corev1.ConfigMap
	stopCh      chan struct{}

	// xpugroup
	nxgWatch chan *knm_v1.NodeXpuGroup
}

func GetResourceQuotaManager() (*ResourceQuotaManager, error) {
	var initErr error
	once.Do(func() {
		var manager *ResourceQuotaManager
		manager, initErr = initResourceQuotaManager()
		if initErr == nil {
			instance = manager
		}
	})
	if initErr != nil {
		return nil, initErr
	}
	return instance, nil
}

func initResourceQuotaManager() (*ResourceQuotaManager, error) {
	kclient, err := k8s.NewKClientset()
	if err != nil {
		return nil, err
	}

	manager := &ResourceQuotaManager{
		kclient:     kclient,
		configWatch: make(chan *corev1.ConfigMap),
		stopCh:      make(chan struct{}),
	}

	if err := manager.WatchResourceQuotaCreateOrUpdate(); err != nil {
		return nil, err
	}

	return manager, nil
}

func (qm *ResourceQuotaManager) getConfigMapName(namespace string) string {
	return fmt.Sprintf("%s-quota", namespace)
}

func (qm *ResourceQuotaManager) getConfigMapNameLabels() map[string]string {
	return util.WithManagedByLabels(map[string]string{
		labelKeyResourceQuotaSpec: labelValueResourceQuotaSpec,
	})
}

func (qm *ResourceQuotaManager) CreateOrUpgradeResourceQuota(namespace string, spec *models.ResourceQuotaSpec) error {
	specYaml, err := yaml.Marshal(spec)
	if err != nil {
		return err
	}

	cmClient := qm.kclient.KubeClient.CoreV1().ConfigMaps(namespace)

	name := qm.getConfigMapName(namespace)
	cm, err := cmClient.Get(context.Background(), name, metav1.GetOptions{})

	if k8serrors.IsNotFound(err) {
		// Create new ConfigMap if not found
		cm = &corev1.ConfigMap{
			ObjectMeta: metav1.ObjectMeta{
				Name:      name,
				Namespace: namespace,
				Labels:    qm.getConfigMapNameLabels(),
			},
			Data: map[string]string{
				resourceQuotaConfigKey: string(specYaml),
			},
		}

		_, err = cmClient.Create(context.Background(), cm, metav1.CreateOptions{})
		if err != nil {
			return err
		}
		stdlog.Infof("Created resource quota ConfigMap for namespace %s", namespace)
		return nil
	}

	if err != nil {
		return err
	}

	// Update existing ConfigMap
	if cm.Data == nil {
		cm.Data = make(map[string]string)
	}
	cm.Data[resourceQuotaConfigKey] = string(specYaml)

	_, err = cmClient.Update(context.Background(), cm, metav1.UpdateOptions{})
	if err != nil {
		return err
	}

	stdlog.Infof("Updated resource quota ConfigMap for namespace %s", namespace)
	return nil
}

func (qm *ResourceQuotaManager) WatchResourceQuotaCreateOrUpdate() error {
	informer := qm.kclient.K8sInformerClient.ConfigMapInformer
	// Add event handler with label filtering
	informer.AddEventHandler(cache.FilteringResourceEventHandler{
		FilterFunc: func(obj interface{}) bool {
			cm, ok := obj.(*corev1.ConfigMap)
			if !ok {
				return false
			}
			return util.ContainLabels(cm.Labels, qm.getConfigMapNameLabels())
		},
		Handler: cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				cm := obj.(*corev1.ConfigMap)
				qm.configWatch <- cm
			},
			UpdateFunc: func(oldObj, newObj interface{}) {
				cm := newObj.(*corev1.ConfigMap)
				qm.configWatch <- cm
			},
		},
	})

	if !cache.WaitForCacheSync(qm.stopCh, informer.HasSynced) {
		stdlog.Errorf("Failed to sync resource quota ConfigMap informer.")
	}

	go func() {
		for {
			select {
			case cm := <-qm.configWatch:
				if err := qm.installOrUpgradeHelmRelease(cm); err != nil {
					stdlog.Errorf("Failed to install or upgrade resource quota using helm: %v", err)
				}
			}
		}
	}()

	return nil
}

func (qm *ResourceQuotaManager) Cleanup() {
	if qm.stopCh != nil {
		close(qm.stopCh)
	}
}

func (qm *ResourceQuotaManager) installOrUpgradeHelmRelease(cm *corev1.ConfigMap) error {
	specYaml, ok := cm.Data[resourceQuotaConfigKey]
	if !ok {
		return stderr.Errorf("Resource quota spec not found in ConfigMap")
	}

	namespace := cm.Namespace

	spec := &models.ResourceQuotaSpec{}
	if err := yaml.Unmarshal([]byte(specYaml), spec); err != nil {
		return err
	}

	valuesPath, err := qm.renderValues(namespace, spec)
	if err != nil {
		stdlog.Errorf("Failed to render values for llmops-queue in %s: %+v", namespace, err)
		return err
	}

	return helm.InstallLlmopsQueue(namespace, nil, []string{valuesPath}...)
}

func (qm *ResourceQuotaManager) renderValues(namespace string, spec *models.ResourceQuotaSpec) (string, error) {
	values := LlmopsQueueHelmValues{
		Resourcequota: LlmopsQueueResourceQuota{
			Cohort: *spec,
		},
	}

	valuesPath := fmt.Sprintf("/tmp/%s-quota-values-%s.yaml", namespace,
		func() string {
			now := time.Now()
			year, month, day := now.Date()

			return fmt.Sprintf("%d-%02d-%02d", year, month, day)
		}())

	file, err := os.Create(valuesPath)
	if err != nil {
		stdlog.Errorf("Failed to create values file for namespace %s: %v", namespace, err)
		return "", err
	}
	defer file.Close()

	fileEncoder := yaml.NewEncoder(file)
	fileEncoder.SetIndent(2)
	if err = fileEncoder.Encode(values); err != nil {
		stdlog.Errorf("Failed to encode values to YAML for namespace %s: %v", namespace, err)
		return "", err
	}

	if err = fileEncoder.Close(); err != nil {
		stdlog.Errorf("Failed to close encoder for namespace %s: %v", namespace, err)
		return "", err
	}

	stdlog.Infof("Successfully rendered values file for namespace %s at %s", namespace, valuesPath)
	return valuesPath, nil
}

func (qm *ResourceQuotaManager) getResourcequota(namespace string) (*models.ResourceQuotaSpec, error) {
	lister := qm.kclient.K8sInformerClient.ConfigMapLister

	name := qm.getConfigMapName(namespace)

	cm, err := lister.ConfigMaps(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	if cm == nil {
		return nil, stderr.Errorf("Resource quota configmap not found")
	}

	specYaml, ok := cm.Data[resourceQuotaConfigKey]
	if !ok {
		return nil, stderr.Errorf("Resource quota spec not found in ConfigMap")
	}

	spec := &models.ResourceQuotaSpec{}
	if err := yaml.Unmarshal([]byte(specYaml), spec); err != nil {
		return nil, err
	}
	return spec, nil
}

func (qm *ResourceQuotaManager) GetTenantResourcequota(namespace string) (*models.TenantResourceQuota, error) {
	spec, err := qm.getResourcequota(namespace)
	if err != nil {
		return nil, err
	}
	return &models.TenantResourceQuota{
		QuotaType: spec.QuotaType,
		Hard:      *spec,
	}, nil
}

func (qm *ResourceQuotaManager) GetDefaultQuota() *models.TenantResourceQuota {
	if qm.defaultQuota == nil {
		defaultQuota := &models.TenantResourceQuota{
			QuotaName: "default",
			Hard: models.ResourceQuotaSpec{
				Pods: conf.C.Tenant.DefaultQuota.Pods,

				Bandwidth: conf.C.Tenant.DefaultQuota.Bandwidth,

				Knowl:                conf.C.Tenant.DefaultQuota.KnowledgeBaseStorage,
				KnowledgeBaseStorage: conf.C.Tenant.DefaultQuota.KnowledgeBaseStorage,
				FileStorage:          conf.C.Tenant.DefaultQuota.FileStorage,

				// compatiable v1
				LimitsCpu:    conf.C.Tenant.DefaultQuota.LimitsCpu,
				LimitsMemory: conf.C.Tenant.DefaultQuota.LimitsMemory,
				Gpu:          conf.C.Tenant.DefaultQuota.Gpu,
				GpuMemory:    conf.C.Tenant.DefaultQuota.GpuMemory,

				CPU: models.ResourceQuotaUnit{
					Name:         corev1.ResourceCPU,
					NominalQuota: conf.C.Tenant.DefaultQuota.LimitsCpu,
				},
				Memory: models.ResourceQuotaUnit{
					Name:         corev1.ResourceMemory,
					NominalQuota: conf.C.Tenant.DefaultQuota.LimitsMemory,
				},
				AcceleratedComputing: make(map[string][]*models.ResourceQuotaUnit),
				Queue:                &models.QueueResourceQuota{},
			},
		}
		defaultQuota.Hard.Queue.Task.CPU = defaultQuota.Hard.CPU
		defaultQuota.Hard.Queue.Task.CPU.NominalQuota = util.Zero
		defaultQuota.Hard.Queue.Infer.CPU = defaultQuota.Hard.CPU
		defaultQuota.Hard.Queue.Infer.CPU.NominalQuota = util.Zero
		defaultQuota.Hard.Queue.Default.CPU = defaultQuota.Hard.CPU
		defaultQuota.Hard.Queue.Default.CPU.NominalQuota = util.Zero
		defaultQuota.Hard.Queue.Task.Memory = defaultQuota.Hard.Memory
		defaultQuota.Hard.Queue.Task.Memory.NominalQuota = util.ZeroGi
		defaultQuota.Hard.Queue.Infer.Memory = defaultQuota.Hard.Memory
		defaultQuota.Hard.Queue.Infer.Memory.NominalQuota = util.ZeroGi
		defaultQuota.Hard.Queue.Default.Memory = defaultQuota.Hard.Memory
		defaultQuota.Hard.Queue.Default.Memory.NominalQuota = util.ZeroGi

		rns, err := GetDeviceResourceNames()
		if err != nil {
			stdlog.Errorf("Get device resource names failed, error: %s.", err)
		} else {
			spec, err := qm.getClusterTotalAcceleratedComputingResourceQuotaSpec()
			if err != nil {
				stdlog.Errorf("Get cluster total accelerated computing resource quota spec failed, error: %s.", err)
				for k, rn := range rns {
					defaultQuota.Hard.AcceleratedComputing[k] = []*models.ResourceQuotaUnit{
						{
							Name:         corev1.ResourceName(rn.CountName),
							NominalQuota: util.Zero,
						},
					}

					if rn.MemoryName != "" {
						defaultQuota.Hard.AcceleratedComputing[k] = append(
							defaultQuota.Hard.AcceleratedComputing[k],
							&models.ResourceQuotaUnit{
								Name:         corev1.ResourceName(rn.MemoryName),
								NominalQuota: util.ZeroGi,
							},
						)
					}

					if rn.CoreName != "" {
						defaultQuota.Hard.AcceleratedComputing[k] = append(
							defaultQuota.Hard.AcceleratedComputing[k],
							&models.ResourceQuotaUnit{
								Name:         corev1.ResourceName(rn.CoreName),
								NominalQuota: util.Zero,
							},
						)
					}
				}
			} else {
				for k, v := range spec.AcceleratedComputing {
					defaultQuota.Hard.AcceleratedComputing[k] = v
					for _, u := range v {
						u.BorrowingLimit = ""
						u.LendingLimit = ""
					}
				}
			}
		}
		defaultQuota.Hard.Queue.Task.AcceleratedComputing = defaultQuota.Hard.AcceleratedComputing
		for _, v := range defaultQuota.Hard.Queue.Task.AcceleratedComputing {
			for _, u := range v {
				u.NominalQuota = util.ZeroWithUnit(u.NominalQuota)
				u.BorrowingLimit = ""
				u.LendingLimit = ""
			}
		}
		defaultQuota.Hard.Queue.Infer.AcceleratedComputing = defaultQuota.Hard.AcceleratedComputing
		for _, v := range defaultQuota.Hard.Queue.Infer.AcceleratedComputing {
			for _, u := range v {
				u.NominalQuota = util.ZeroWithUnit(u.NominalQuota)
				u.BorrowingLimit = ""
				u.LendingLimit = ""
			}
		}
		defaultQuota.Hard.Queue.Default.AcceleratedComputing = defaultQuota.Hard.AcceleratedComputing
		for _, v := range defaultQuota.Hard.Queue.Default.AcceleratedComputing {
			for _, u := range v {
				u.NominalQuota = util.ZeroWithUnit(u.NominalQuota)
				u.BorrowingLimit = ""
				u.LendingLimit = ""
			}
		}

		qm.defaultQuota = defaultQuota
	}

	limits, err := qm.GetClusterResourceLimits()
	if err != nil {
		stdlog.Errorf("Get cluster resource limits failed, error: %s.", err)
		qm.defaultQuota.Limits = *qm.getDefaultLimitsResourceQuotaSpec()
	} else {
		qm.defaultQuota.Limits = *limits
	}

	return qm.defaultQuota
}

func (qm *ResourceQuotaManager) getDefaultLimitsResourceQuotaSpec() *models.ResourceQuotaSpec {
	limits := &models.ResourceQuotaSpec{
		LimitsCpu:            "100000",
		LimitsMemory:         "100000Gi",
		Pods:                 "100000",
		RequestsCpu:          "100000",
		RequestsMemory:       "100000Gi",
		RequestsStorage:      "10000000Gi",
		Bandwidth:            "1000Gi",
		EgressBandwidth:      "1000Gi",
		IngressBandwidth:     "1000Gi",
		Gpu:                  "100000",
		GpuMemory:            "100000Gi",
		Knowl:                "100000Gi",
		KnowledgeBaseStorage: "100000Gi",
		FileStorage:          "100000Gi",

		CPU: models.ResourceQuotaUnit{
			Name:         corev1.ResourceCPU,
			NominalQuota: "100000",
		},
		Memory: models.ResourceQuotaUnit{
			Name:         corev1.ResourceMemory,
			NominalQuota: "100000Gi",
		},
		AcceleratedComputing: make(map[string][]*models.ResourceQuotaUnit),
		Queue:                &models.QueueResourceQuota{},
	}
	limits.Queue.Task.CPU = limits.CPU
	limits.Queue.Infer.CPU = limits.CPU
	limits.Queue.Default.CPU = limits.CPU
	limits.Queue.Task.Memory = limits.Memory
	limits.Queue.Infer.Memory = limits.Memory
	limits.Queue.Default.Memory = limits.Memory
	limits.Queue.Task.AcceleratedComputing = limits.AcceleratedComputing
	limits.Queue.Infer.AcceleratedComputing = limits.AcceleratedComputing
	limits.Queue.Default.AcceleratedComputing = limits.AcceleratedComputing

	return limits
}

func (qm *ResourceQuotaManager) GetClusterResourceLimits() (*models.ResourceQuotaSpec, error) {
	limits := qm.getDefaultLimitsResourceQuotaSpec()
	// todo get total cpu and memory
	nodes, err := qm.kclient.K8sInformerClient.ListNodes("")
	if err != nil {
		return nil, err
	}
	var totalCpu, totalMemory, totalPods resource.Quantity
	for _, node := range nodes {
		cpu := node.Status.Capacity[corev1.ResourceCPU]
		totalCpu.Add(cpu)
		memory := node.Status.Capacity[corev1.ResourceMemory]
		totalMemory.Add(memory)
		pods := node.Status.Capacity[corev1.ResourcePods]
		totalPods.Add(pods)
	}
	limits.LimitsCpu = strconv.FormatInt(totalCpu.Value(), 10)
	limits.LimitsMemory = util.ToGiInt(totalMemory.Value())
	limits.Pods = strconv.FormatInt(totalPods.Value(), 10)
	limits.CPU = models.ResourceQuotaUnit{
		Name:         corev1.ResourceCPU,
		NominalQuota: strconv.FormatInt(totalCpu.Value(), 10),
	}
	limits.Memory = models.ResourceQuotaUnit{
		Name:         corev1.ResourceMemory,
		NominalQuota: util.ToGiInt(totalMemory.Value()),
	}

	limits.Queue.Task.CPU = limits.CPU
	limits.Queue.Infer.CPU = limits.CPU
	limits.Queue.Default.CPU = limits.CPU
	limits.Queue.Task.Memory = limits.Memory
	limits.Queue.Infer.Memory = limits.Memory
	limits.Queue.Default.Memory = limits.Memory

	// set accelerated computing
	spec, err := qm.getClusterTotalAcceleratedComputingResourceQuotaSpec()
	if err != nil {
		if limits.AcceleratedComputing == nil {
			limits.AcceleratedComputing = make(map[string][]*models.ResourceQuotaUnit, 0)
		}
	} else {
		limits.AcceleratedComputing = spec.AcceleratedComputing
	}
	limits.Queue.Task.AcceleratedComputing = limits.AcceleratedComputing
	limits.Queue.Infer.AcceleratedComputing = limits.AcceleratedComputing
	limits.Queue.Default.AcceleratedComputing = limits.AcceleratedComputing

	return limits, nil
}

func (qm *ResourceQuotaManager) getClusterTotalAcceleratedComputingResourceQuotaSpec() (*models.ResourceQuotaSpec, error) {
	spec := &models.ResourceQuotaSpec{
		AcceleratedComputing: make(map[string][]*models.ResourceQuotaUnit),
	}

	// using crd package listXpu and scan all device
	xpus, err := xpu.Rg.ListXpus(context.Background())
	if err != nil {
		stdlog.Errorf("Failed to list xpu: %v", err)
		return spec, nil
	}
	ac, err := qm.xpusToAcceleratedComputingResourceQuotaSpec(&xpus)
	if err != nil {
		return spec, nil
	}
	spec.AcceleratedComputing = *ac

	return spec, nil
}

func (qm *ResourceQuotaManager) xpusToAcceleratedComputingResourceQuotaSpec(xpus *[]*knm_v1.NodeXpu) (*map[string][]*models.ResourceQuotaUnit, error) {
	ac := make(map[string][]*models.ResourceQuotaUnit)

	type Device struct {
		Name   string
		Count  resource.Quantity
		Memory resource.Quantity
	}
	dc := make(map[string]*Device, 0)
	for _, xpu := range *xpus {
		v := xpu.Spec.Vendor
		t := xpu.Spec.Type // type == commonWord
		m := xpu.Spec.Devmem
		key := v
		if v == "Ascend" {
			key = t
		}
		key = strings.ToLower(key)
		if _, ok := dc[key]; !ok {
			dc[key] = &Device{
				Name: key,
			}
		}
		dc[key].Count.Add(*resource.NewQuantity(1, resource.DecimalSI))
		dc[key].Memory.Add(m)
	}
	rns, err := GetDeviceResourceNames()
	if err != nil {
		stdlog.Errorf("Failed to get device resource names: %v", err)
		return &ac, nil
	}
	for k, v := range rns {
		dck := strings.ToLower(v.CommonWord)
		if _, ok := dc[dck]; !ok {
			continue
		}
		ac[k] = []*models.ResourceQuotaUnit{
			{
				Name:         corev1.ResourceName(v.CountName),
				NominalQuota: strconv.FormatInt(dc[dck].Count.Value(), 10),
			},
			{
				Name:         corev1.ResourceName(v.MemoryName),
				NominalQuota: util.ToGiInt(dc[dck].Memory.Value()),
			},
		}
	}
	return &ac, nil
}

func GetDefaultResourceQuotaV2() *models.TenantResourceQuota {
	manager, err := GetResourceQuotaManager()
	if err != nil {
		stdlog.Errorf("Failed to get resource quota manager: %v", err)
		return &models.TenantResourceQuota{}
	}
	return manager.GetDefaultQuota()
}

func (qm *ResourceQuotaManager) WatchXpuGroup() {
	// informer := qm.kclient.K8sInformerClient.ConfigMapInformer
	informer := xpu.Rg.GroupInformer.Informer()

	// Add event handler with label filtering
	informer.AddEventHandler(cache.FilteringResourceEventHandler{
		FilterFunc: func(obj interface{}) bool {
			nxg, ok := obj.(*knm_v1.NodeXpuGroup)
			if !ok {
				return false
			}
			return util.ContainLabels(nxg.Labels, util.GetResourceGroupManagedNamespaceLabels())
		},
		Handler: cache.ResourceEventHandlerFuncs{
			UpdateFunc: func(oldObj, newObj interface{}) {
				nxg := newObj.(*knm_v1.NodeXpuGroup)
				qm.nxgWatch <- nxg
			},
		},
	})

	if !cache.WaitForCacheSync(qm.stopCh, informer.HasSynced) {
		stdlog.Errorf("Failed to sync resource quota ConfigMap informer.")
	}

	go func() {
		for {
			select {
			case nxg := <-qm.nxgWatch:
				if err := qm.flushResourceQuotaWhenXpuGroupUpdate(nxg); err != nil {
					stdlog.Errorf("Failed to flush resource quota when watch nxg update: %v", err)
				}
			}
		}
	}()
}

func (qm *ResourceQuotaManager) flushResourceQuotaWhenXpuGroupUpdate(nxg *knm_v1.NodeXpuGroup) error {
	nsXpuMap, err := xpu.Rg.ListNamespaceBindingGroupName(context.Background(), nxg.GetName())
	if err != nil {
		return err
	}

	xpus, err := xpu.Rg.ListXpus(context.Background())
	if err != nil {
		return err
	}

	for ns, items := range nsXpuMap {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.XpuIds...)
		}

		ids = util.RemoveDuplicatesString(ids)

		nsXpus := make([]*knm_v1.NodeXpu, 0)
		for _, id := range ids {
			for _, xpu := range xpus {
				if xpu.Spec.ID == id {
					nsXpus = append(nsXpus, xpu)
					break
				}
			}
		}

		ac, err := qm.xpusToAcceleratedComputingResourceQuotaSpec(&nsXpus)
		if err != nil {
			stdlog.Errorf("Failed to convert accelerated computing resource quota spec: %v", err)
			continue
		}
		quotaSpec, err := qm.getResourcequota(ns)
		if err != nil {
			stdlog.Errorf("Failed to get resource quota: %v", err)
			continue
		}
		switch quotaSpec.QuotaType {
		case models.QuotaTypeDynamic:
			quotaSpec.AcceleratedComputing = *ac
			if err := qm.CreateOrUpgradeResourceQuota(ns, quotaSpec); err != nil {
				stdlog.Errorf("Failed to create or upgrade resource quota: %v", err)
				continue
			}
		default:
		}
	}
	return nil
}

func (qm *ResourceQuotaManager) NxgToResourceQuotaSpec(nxgIDs []string) (*models.TenantResourceQuota, error) {
	tenantQuota := &models.TenantResourceQuota{
		Hard: models.ResourceQuotaSpec{
			AcceleratedComputing: make(map[string][]*models.ResourceQuotaUnit),
		},
	}

	groups, err := xpu.Rg.GroupLister.List(labels.Everything())
	if err != nil {
		return tenantQuota, err
	}

	nsXpus := make([]*knm_v1.NodeXpu, 0)

	xpuIds := make([]string, 0)
	for _, group := range groups {
		if !util.Contains(nxgIDs, group.GetName()) {
			continue
		}
		for _, node := range group.Spec.Nodes {
			xpuIds = append(xpuIds, node.XpuIds...)
		}
	}

	xpuIds = util.RemoveDuplicatesString(xpuIds)

	xpus, err := xpu.Rg.ListXpus(context.Background())
	if err != nil {
		return tenantQuota, err
	}

	for _, id := range xpuIds {
		for _, xpu := range xpus {
			if xpu.Spec.ID == id {
				nsXpus = append(nsXpus, xpu)
				break
			}
		}
	}

	ac, err := qm.xpusToAcceleratedComputingResourceQuotaSpec(&nsXpus)
	if err != nil {
		return tenantQuota, err
	}

	tenantQuota.Hard.AcceleratedComputing = *ac

	return tenantQuota, nil
}
