package client

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCreateTenant(t *testing.T) {
	tenantUid := "dev-sophon"
	body := CreateTdc5TenantReqBody{
		TenantName: tenantUid,
		TenantUid:  tenantUid,
		LabelSelector: map[string]string{
			"llmops.transwarp.io/managed-by": "dev",
			"llmops.transwarp.io/ns-type":    "tenant",
		},
		ClusterId: "7d8ada5f-e32a-4fdc-ae32-b573df7cfe7d",
	}
	err := GetClientFactory().GetTDC5Client().CreateTenant(context.TODO(), &body)
	assert.Nil(t, err)
}

func TestGetTenant(t *testing.T) {
	tenantUid := "tenantvtucrstt"
	tenant, err := GetClientFactory().GetTDC5Client().GetTenant(context.TODO(), tenantUid)
	if err != nil {
		fmt.Println(err.Error())
	} else {
		fmt.Println(*tenant)
	}
}
func TestGetTenants(t *testing.T) {
	tenants, err := GetClientFactory().GetTDC5Client().ListTenants(context.TODO())
	assert.Nil(t, err)
	for tenant := range tenants {
		fmt.Println(tenant)
		// assert.NotEmpty(t, tenant.TenantUid)
	}
}

func TestGetHippo(t *testing.T) {
	is, err := GetClientFactory().GetTDC5Client().IsHippoInstalled(context.TODO(), "tenantvtucrstt")
	fmt.Println(is)
	if err != nil {
		fmt.Println(err.Error())
	}
}

func TestIsInstallHippoAll(t *testing.T) {
	//tid := "tenantvtucrstt"
	tid := "tenanyxyifjj"
	_, err := GetClientFactory().GetTDC5Client().IsHippoInstalled(context.TODO(), tid)
	if err != nil {
		fmt.Println(err.Error())
	}
}

func TestInstallHippoAll(t *testing.T) {
	err := GetClientFactory().GetTDC5Client().InstallHippo(context.TODO(), "tenantvtucrstt")
	// err := InstallTdc5Hippo("tenantvtucrstt")
	if err != nil {
		fmt.Println(err.Error())
	}
}

// create cloud product succeeded: ebeddab7-87fa-403b-b09e-1f7ad367700d
// create product instance succeeded: 9d39a566-371e-45d4-aa19-7091fd39018d
func TestInstallHippo(t *testing.T) {
	ci := "ebeddab7-87fa-403b-b09e-1f7ad367700d"
	// ci := "9d39a566-371e-45d4-aa19-7091fd39018d"
	// ci := "81db6e5c-9f4c-4436-95fc-6ca66f66dfbd"
	err := GetClientFactory().GetTDC5Client().installComponent(context.TODO(), ci)
	if err != nil {
		fmt.Println(err.Error())
	}
}
