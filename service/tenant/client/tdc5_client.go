package client

import (
	"context"
	"fmt"
	"strings"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/models"
	customtypes "transwarp.io/applied-ai/central-auth-service/service/tenant/types"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

type Tdc5TenantStatus string

const (
	TDC5_CREATING      Tdc5TenantStatus = "CREATING"
	TDC5_CREATE_FAILED Tdc5TenantStatus = "CREATE_FAILED"
	TDC5_READY         Tdc5TenantStatus = "READY"
	TDC5_CANCELING     Tdc5TenantStatus = "CANCELING"
	TDC5_CANCEL_FAILED Tdc5TenantStatus = "CANCEL_FAILED"
	TDC5_CANCELED      Tdc5TenantStatus = "CANCELED"
)

// TDC5Client TDC5服务客户端
type TDC5Client struct {
	*HTTPClient
}

// NewTDC5Client 创建新的TDC5客户端
func NewTDC5Client(baseURL string) *TDC5Client {
	if baseURL == "" {
		var err error
		baseURL, err = GetTdc5TenantServiceBaseUrl()
		if err != nil {
			stdlog.Errorf("Failed to get TDC5 tenant service base URL: %v", err)
		}
	}

	// 获取访问令牌
	token, err := GetTdc5XCloudAccessToken()
	if err != nil {
		stdlog.Errorf("Failed to get TDC5 X-Cloud-Access-Token: %v", err)
	}

	return &TDC5Client{
		HTTPClient: NewHTTPClient(
			WithBaseURL(baseURL),
			WithHeader("X-Cloud-Access-Token", token),
			WithHeader("Content-Type", "application/json"),
			WithHeader("accept", "application/json"),
			WithTimeout(60*time.Second),
		),
	}
}

func tdc5TenantToTenant(tdc5Tenant *Tdc5Tenant) *models.Tenant {
	if tdc5Tenant == nil {
		return nil
	}
	tenant := models.Tenant{
		TenantName:   tdc5Tenant.TenantName,
		TenantUid:    tdc5Tenant.TenantUid,
		TenantStatus: tdc5Tenant.TenantStatus,
		Creator:      tdc5Tenant.Creator,
	}
	// llmops tenantUid == tdc5 specify clusterid namespace
	if len(tdc5Tenant.Clusters) > 0 {
		for _, cluster := range tdc5Tenant.Clusters {
			if cluster.ClusterId == conf.C.Tenant.TDC5.TenantService.ClusterID {
				tenant.TenantUid = cluster.ClusterNamespace
				tenant.ClusterNamespace = cluster.ClusterNamespace
			}
		}
	} else {
		stdlog.Warnf("no clusters found of tdc5 tenant: %+v", tdc5Tenant)
		tenant.ClusterNamespace = tdc5Tenant.TenantUid
	}
	if strings.EqualFold(tenant.TenantStatus, string(TDC5_READY)) {
		tenant.TenantStatus = "Active"
	}
	return &tenant
}

// ListTenants 获取所有租户
func (c *TDC5Client) ListTenants(ctx context.Context) ([]*models.Tenant, error) {
	tdc5resp, err := c.listTdc5Tenants(ctx)
	if err != nil {
		return nil, err
	}
	tenants := make([]*models.Tenant, 0)
	for _, v := range tdc5resp {
		if !strings.EqualFold(v.TenantStatus, string(TDC5_CANCELED)) { // 过滤已删除的租户
			tenants = append(tenants, tdc5TenantToTenant(v))
		}
	}

	return tenants, nil
}

func (c *TDC5Client) listTdc5Tenants(ctx context.Context) ([]*Tdc5Tenant, error) {
	ns, err := util.GetCurrentNamespace()
	if err != nil {
		return nil, err
	}

	reqBody := ListTenantsReqBody{
		LabelSelector: map[string]string{
			customtypes.NamespaceLableManagedBy: ns,
		},
	}

	var response []*Tdc5Tenant
	err = c.DoRequest(ctx, "POST", conf.C.Tenant.TDC5.TenantService.GetAllTenantsPath, reqBody, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (c *TDC5Client) GetTenant(ctx context.Context, tenantID string) (*models.Tenant, error) {
	tenants, err := c.ListTenants(ctx)
	if err != nil {
		return nil, err
	}
	for _, t := range tenants {
		if t.TenantUid == tenantID {
			return t, nil
		}
	}
	return nil, stderr.Errorf("Not found tenant on tdc5 by ns: %s", tenantID)
}

func (c *TDC5Client) CreateTenant(ctx context.Context, tenant *models.Tenant, labelSelector map[string]string) error {
	tdc5TenantReqBody := CreateTdc5TenantReqBody{
		TenantName:    tenant.TenantName,
		TenantUid:     tenant.TenantUid,
		LabelSelector: labelSelector,
		ClusterId:     conf.C.Tenant.TDC5.TenantService.ClusterID,
	}
	return c.DoRequest(ctx, "POST", conf.C.Tenant.TDC5.TenantService.CreateTenantPath, tdc5TenantReqBody, nil)
}

// GetHippo 获取Hippo实例
func (c *TDC5Client) GetHippo(ctx context.Context, tenantUid string) (*Tdc5ComponentInstance, error) {
	queryProdInstPath := "/api/v1/broker/productInstance"
	hippoInstanceName := conf.C.Tenant.TDC5.HippoConfig.ProdInstanceName
	queryParams := fmt.Sprintf("?tenantUid=%s&prodInstName=%s", tenantUid, hippoInstanceName)

	var response Tdc5ComponentInstance
	err := c.DoRequest(ctx, "GET", queryProdInstPath+queryParams, nil, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// IsHippoInstalled 检查Hippo是否已安装
func (c *TDC5Client) IsHippoInstalled(ctx context.Context, tenantUid string) (bool, error) {
	queryProdInstPath := "/api/v1/broker/productInstance"
	hippoInstanceName := conf.C.Tenant.TDC5.HippoConfig.ProdInstanceName
	queryParams := fmt.Sprintf("?tenantUid=%s&prodInstName=%s", tenantUid, hippoInstanceName)

	var productInstances []Tdc5ComponentInstance
	err := c.DoRequest(ctx, "GET", queryProdInstPath+queryParams, nil, &productInstances)
	if err != nil {
		stdlog.Warnf("No Hippo instance found for tdc5 tenant %s", tenantUid)
		return false, nil
	}

	for _, instance := range productInstances {
		if instance.ProductInstanceName == hippoInstanceName {
			return true, nil
		}
	}

	return false, nil
}

// CreateComponent 创建组件
func (c *TDC5Client) createComponent(ctx context.Context, tenantUid string) (string, error) {
	createComponentPath := "/api/v1/broker/componentInstance"

	requestBody := CreateComponentRequestBody{
		TenantUid:        tenantUid,
		ProdMetaType:     conf.C.Tenant.TDC5.HippoConfig.ProdMetaType,
		ProdMetaVersion:  conf.C.Tenant.TDC5.HippoConfig.ProdMetaVersion,
		ProdInstanceName: conf.C.Tenant.TDC5.HippoConfig.ProdInstanceName,
		Component: ComponentConfig{
			Type:           conf.C.Tenant.TDC5.HippoConfig.ComponentType,
			Version:        conf.C.Tenant.TDC5.HippoConfig.ComponentVersion,
			EnableKerberos: conf.C.Tenant.TDC5.HippoConfig.ComponentEnableKerberos == "true",
			NetworkType:    conf.C.Tenant.TDC5.HippoConfig.ComponentNetworkKype,
		},
	}

	resp, err := c.DoRequestWihoutUnmarshal(ctx, "POST", createComponentPath, requestBody)
	if err != nil {
		return "", err
	}
	componentInstanceId := string(*resp)

	return componentInstanceId, nil
}

// InstallComponent 安装组件
func (c *TDC5Client) installComponent(ctx context.Context, componentInstanceId string) error {
	installComponentPath := fmt.Sprintf("/api/v1/broker/componentInstance/%s/install", componentInstanceId)
	err := c.DoRequest(ctx, "POST", installComponentPath, nil, nil)
	if err != nil {
		stdlog.Errorf("Failed triggered installation for component instance: %s", componentInstanceId)
		return nil
	}
	stdlog.Infof("Successfully triggered installation for component instance: %s", componentInstanceId)
	return nil
}

// GetComponentStatus 获取组件状态
func (c *TDC5Client) getComponentStatus(ctx context.Context, componentInstanceId string) (string, error) {
	getComponentStatusPath := fmt.Sprintf("/api/v1/broker/componentInstance/%s", componentInstanceId)

	var response Tdc5ComponentInstance
	err := c.DoRequest(ctx, "GET", getComponentStatusPath, nil, &response)
	if err != nil {
		return "", err
	}

	return response.Status, nil
}

// InstallHippo 安装Hippo
func (c *TDC5Client) InstallHippo(ctx context.Context, tenantUid string) error {
	// 检查是否已安装
	installed, err := c.IsHippoInstalled(ctx, tenantUid)
	if err != nil {
		return err
	}
	if installed {
		stdlog.Infof("Hippo is already installed for tenant: %s", tenantUid)
		return nil
	}

	// 创建组件
	componentInstanceId, err := c.createComponent(ctx, tenantUid)
	if err != nil {
		return stderr.Errorf("Failed to create Hippo component: %v", err)
	}

	// 安装组件
	err = c.installComponent(ctx, componentInstanceId)
	if err != nil {
		return stderr.Errorf("Failed to install Hippo component: %v", err)
	}

	// 等待安装完成
	maxRetries := 30
	for i := 0; i < maxRetries; i++ {
		status, err := c.getComponentStatus(ctx, componentInstanceId)
		if err != nil {
			stdlog.Warnf("Failed to get component status: %v, retrying...", err)
		} else {
			stdlog.Infof("Component %s installation status: %s", componentInstanceId, status)
			if status == "RUNNING" {
				stdlog.Infof("Hippo installation completed successfully for tenant: %s", tenantUid)
				return nil
			} else if status == "FAILED" {
				return stderr.Errorf("Hippo installation failed for tenant: %s", tenantUid)
			}
		}
		time.Sleep(10 * time.Second)
	}

	return stderr.Errorf("Timed out waiting for Hippo installation to complete for tenant: %s", tenantUid)
}

// for tdc5
type Tdc5TenantList struct {
	Data []Tdc5Tenant `json:"data"`
}

type Tdc5Tenant struct {
	TenantUid    string        `json:"tenantUid"`
	TenantName   string        `json:"tenantName"`
	TenantStatus string        `json:"tenantStatus"`
	Creator      string        `json:"creator"`
	Clusters     []Tdc5Cluster `json:"clusters"`
}

type Tdc5Cluster struct {
	ClusterId        string `json:"clusterId"`
	ClusterNamespace string `json:"clusterNamespace"`
}

type Tdc5ProductInstance struct {
	ID                      string      `json:"id"`
	OriginID                int         `json:"originId"`
	SID                     string      `json:"sid"`
	ComponentInstanceName   string      `json:"componentInstanceName"`
	CompMetaID              string      `json:"compMetaId"`
	ComponentType           string      `json:"componentType"`
	ComponentVersion        string      `json:"componentVersion"`
	ProductType             string      `json:"productType"`
	ProductVersion          string      `json:"productVersion"`
	TenantUid               string      `json:"tenantUid"`
	EnableKerberos          bool        `json:"enableKerberos"`
	IsShared                bool        `json:"isShared"`
	CrossTenant             bool        `json:"crossTenant"`
	SharedDescription       string      `json:"sharedDescription"`
	Creator                 string      `json:"creator"`
	ProductInstanceID       string      `json:"productInstanceId"`
	ProductInstanceName     string      `json:"productInstanceName"`
	Icon                    string      `json:"icon"`
	Status                  string      `json:"status"`
	CreateTime              int64       `json:"createTime"`
	LastModifiedTime        int64       `json:"lastModifiedTime"`
	SharedStatus            string      `json:"sharedStatus"`
	SecurityOn              bool        `json:"securityOn"`
	SecuritySupported       bool        `json:"securitySupported"`
	TenantName              string      `json:"tenantName"`
	PrivateCloudEnvironment bool        `json:"privateCloudEnvironment"`
	MacvlanSupported        bool        `json:"macvlanSupported"`
	CatalogSupported        bool        `json:"catalogSupported"`
	Catalog                 interface{} `json:"catalog"`
	Product                 string      `json:"product"`
	ElasticSupported        bool        `json:"elasticSupported"`
	ElasticEnabled          bool        `json:"elasticEnabled"`
	PluginSupported         bool        `json:"pluginSupported"`
	SupportRollingRestart   bool        `json:"supportRollingRestart"`
	CanCrossCluster         bool        `json:"canCrossCluster"`
	Active                  bool        `json:"active"`
	CompDepStaleness        interface{} `json:"compDepStaleness"`
	NetworkType             string      `json:"networkType"`
	NeedScaleOut            bool        `json:"needScaleOut"`
	Installed               bool        `json:"installed"`
}
