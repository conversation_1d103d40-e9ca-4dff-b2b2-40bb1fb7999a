package client

import (
	"context"
	"encoding/base64"
	"fmt"
	"strconv"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
)

var kclient, _ = kubernetes.NewKClientset()

const (
	TdcHippoServiceLabelSelector = "transwarp.name=hippohttpserver"
	K8sHippoServicelabelSelector = "io.transwarp.aip.service=autocv-hippo"
)

type HippoClient struct {
	*HTTPClient
	namespace string
}

func NewHippoClient(namespace string) (*HippoClient, error) {
	url, err := GetHippoServiceUrl(namespace)
	if err != nil {
		return nil, err
	}

	auth := "Basic " + base64.StdEncoding.EncodeToString([]byte("shiva:shiva"))

	return &HippoClient{
		HTTPClient: NewHTTPClient(
			WithBaseURL(url),
			WithHeader("Authorization", auth),
			WithTimeout(1*time.Second),
		),
		namespace: namespace,
	}, nil
}

func (c *HippoClient) GetOverview(ctx context.Context) (*Overview, error) {
	var overview Overview
	err := c.DoRequest(ctx, "GET", "/api/shiva/overview", nil, &overview)
	if err != nil {
		return nil, err
	}
	return &overview, nil
}

func (c *HippoClient) IsRunning(ctx context.Context) bool {
	overview, err := c.GetOverview(ctx)
	if err != nil {
		return false
	}

	if overview != nil &&
		strings.ToLower(overview.TServerHealthy) == "green" &&
		strings.ToLower(overview.MasterHealthy) == "green" {
		return true
	}

	if overview != nil {
		stdlog.Warnf("Check hippo status: tServerHealthy: %s and masterHealthy: %s",
			overview.TServerHealthy, overview.MasterHealthy)
	} else {
		stdlog.Warnf("Check hippo status: overview is nil")
	}

	return false
}

func (c *HippoClient) GetUsedStorage(ctx context.Context) (int, error) {
	overview, err := c.GetOverview(ctx)
	if err != nil {
		return 0, err
	}
	return overview.DiskStats.ShivaUsedGB, nil
}

func GetHippoServiceUrl(namespace string) (string, error) {
	service, err := getHippoService(namespace)
	if err != nil {
		return "", err
	}

	if service == nil {
		stdlog.Errorf("Hippo service not found in namespace %s", namespace)
		return "", stderr.Errorf("Hippo service not found")
	}

	formatUrl := "http://%s.%s.svc:%s"
	for _, v := range service.Spec.Ports {
		if strings.ToLower(v.Name) == "hippo" {
			return fmt.Sprintf(formatUrl, service.Name, service.Namespace, strconv.Itoa(int(v.Port))), nil
		}
	}

	stdlog.Warnf("Not found named hippo port, just try using first")
	return fmt.Sprintf(formatUrl, service.Name, service.Namespace, strconv.Itoa(int(service.Spec.Ports[0].Port))), nil
}

func GetHippoServiceName(namespace string) (string, error) {
	service, err := getHippoService(namespace)
	if err != nil {
		return "", err
	}

	if service == nil {
		stdlog.Errorf("Hippo service not found in namespace %s", namespace)
		return "", stderr.Errorf("Hippo service not found")
	}

	return service.Name, nil
}

func getHippoService(namespace string) (*corev1.Service, error) {
	labelSelector := TdcHippoServiceLabelSelector
	if conf.C.Tenant.Strategy == "k8s" {
		labelSelector = K8sHippoServicelabelSelector
	} else if conf.C.Tenant.Strategy == "tdc5" {
		labelSelector = conf.C.Tenant.TDC5.HippoSvcLabels
	}

	services, err := kclient.K8sInformerClient.ListServices(namespace, labelSelector)
	if err != nil {
		stdlog.Errorf("Get hippo services in namespace %s with label %s failed %+v",
			namespace, labelSelector, err)
		return nil, err
	}

	if len(services) > 0 {
		svc := &services[0]
		return svc, nil
	}

	stdlog.Errorf("Hippo service not found in namespace %s", namespace)
	return nil, nil
}
