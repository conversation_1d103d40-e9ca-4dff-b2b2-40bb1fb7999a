package client

import (
	"context"
	"sync"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
)

var (
	clientFactory *ClientFactory
	once          sync.Once
)

type ClientFactory struct {
	tdc5Client   *TDC5Client
	tdcClient    *TDCClient
	hippoClients map[string]*HippoClient
	mu           sync.RWMutex
}

func GetClientFactory() *ClientFactory {
	once.Do(func() {
		clientFactory = &ClientFactory{
			hippoClients: make(map[string]*HippoClient),
		}
	})
	return clientFactory
}

// GetTDC5Client 获取TDC5客户端
func (f *ClientFactory) GetTDC5Client() *TDC5Client {
	f.mu.RLock()
	if f.tdc5Client != nil {
		defer f.mu.RUnlock()
		return f.tdc5Client
	}
	f.mu.RUnlock()

	f.mu.Lock()
	defer f.mu.Unlock()

	baseURL, err := GetTdc5TenantServiceBaseUrl()
	if err != nil {
		stdlog.Errorf("Failed to get TDC5 tenant service base URL: %v", err)
		return nil
	}

	f.tdc5Client = NewTDC5Client(baseURL)
	return f.tdc5Client
}

// GetTDCClient 获取TDC客户端
func (f *ClientFactory) GetTDCClient() *TDCClient {
	f.mu.RLock()
	if f.tdcClient != nil {
		defer f.mu.RUnlock()
		return f.tdcClient
	}
	f.mu.RUnlock()

	f.mu.Lock()
	defer f.mu.Unlock()

	baseURL, err := GetTdcTenantServiceBaseUrl()
	if err != nil {
		stdlog.Errorf("Failed to get TDC tenant service base URL: %v", err)
		return nil
	}

	f.tdcClient = NewTDCClient(baseURL)
	return f.tdcClient
}

// GetHippoClient 获取Hippo客户端
func (f *ClientFactory) GetHippoClient(namespace string) (*HippoClient, error) {
	f.mu.RLock()
	client, exists := f.hippoClients[namespace]
	if exists {
		defer f.mu.RUnlock()
		return client, nil
	}
	f.mu.RUnlock()

	f.mu.Lock()
	defer f.mu.Unlock()

	client, exists = f.hippoClients[namespace]
	if exists {
		return client, nil
	}

	var err error
	client, err = NewHippoClient(namespace)
	if err != nil {
		return nil, err
	}

	f.hippoClients[namespace] = client
	return client, nil
}

// IsHippoRunning 检查Hippo是否运行中
func IsHippoRunning(namespace string) bool {
	client, err := GetClientFactory().GetHippoClient(namespace)
	if err != nil {
		stdlog.Errorf("Failed to get Hippo client: %v", err)
		return false
	}

	return client.IsRunning(context.Background())
}

func GetHippoUsedStorage(namespace string) (int, error) {
	client, err := GetClientFactory().GetHippoClient(namespace)
	if err != nil {
		return 0, err
	}

	return client.GetUsedStorage(context.Background())
}

func GetTdc5TenantServiceBaseUrl() (string, error) {
	if conf.C.Tenant.TDC5.TenantService.BaseURL == "" {
		return "", stderr.Errorf("TDC5 tenant service base url is empty")
	}

	return conf.C.Tenant.TDC5.TenantService.BaseURL, nil
}

func GetTdcTenantServiceBaseUrl() (string, error) {
	if conf.C.Tenant.TDC.TenantService.BaseURL == "" {
		return "", stderr.Errorf("TDC tenant service base url is empty")
	}

	return conf.C.Tenant.TDC.TenantService.BaseURL, nil
}

func GetTdcBrokerServiceBaseUrl() (string, error) {
	if conf.C.Tenant.TDC.BrokerService.BaseURL == "" {
		return "", stderr.Errorf("TDC broker service base url is empty")
	}

	return conf.C.Tenant.TDC.BrokerService.BaseURL, nil
}

func GetTdc5XCloudAccessToken() (string, error) {
	return conf.C.Tenant.TDC5.XCloudAccessToken, nil
}

func GetGuardianAccessToken() string {
	return conf.C.Tenant.TDC.GuardianAccessToken
}

// 获取TDC Hippo服务ID
func GetTdcHippoServiceId() (string, error) {
	return conf.C.Tenant.TDC.BrokerService.HippoServiceId, nil
}
func GetTdcHippoInstanceName() (string, error) {
	return conf.C.Tenant.TDC.BrokerService.HippoInstanceName, nil
}
