package client

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	stdauth "transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

// HTTPClient 通用HTTP客户端
type HTTPClient struct {
	*http.Client
	baseURL string
	headers map[string]string
}

type HTTPClientOption func(*HTTPClient)

func WithTimeout(timeout time.Duration) HTTPClientOption {
	return func(c *HTTPClient) {
		c.Timeout = timeout
	}
}

func WithBaseURL(baseURL string) HTTPClientOption {
	return func(c *HTTPClient) {
		c.baseURL = baseURL
	}
}

func WithHeader(key, value string) HTTPClientOption {
	return func(c *HTTPClient) {
		if c.headers == nil {
			c.headers = make(map[string]string)
		}
		c.headers[key] = value
	}
}

func WithInsecureSkipVerify(skip bool) HTTPClientOption {
	return func(c *HTTPClient) {
		transport := c.Transport.(*http.Transport)
		transport.TLSClientConfig.InsecureSkipVerify = skip
	}
}

func NewHTTPClient(options ...HTTPClientOption) *HTTPClient {
	client := &HTTPClient{
		Client: &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
			Timeout: 10 * time.Second,
		},
		headers: make(map[string]string),
	}

	for _, option := range options {
		option(client)
	}

	return client
}

func (c *HTTPClient) DoRequest(ctx context.Context, method, path string, requestBody interface{}, result interface{}) error {
	respBody, err := c.DoRequestWihoutUnmarshal(ctx, method, path, requestBody)
	if err != nil {
		return err
	}
	if result != nil && len(*respBody) > 0 {
		if err := json.Unmarshal(*respBody, result); err != nil {
			stdlog.Debug(fmt.Sprintf("Failed to unmarshal response: %s", string(*respBody)))
			return err
		}
	}
	return nil
}

func (c *HTTPClient) DoRequestWihoutUnmarshal(ctx context.Context, method, path string, requestBody interface{}) (*[]byte, error) {
	url := path
	if c.baseURL != "" && !isAbsoluteURL(path) {
		url = c.baseURL + path
	}

	var reqBody io.Reader
	if requestBody != nil {
		jsonBody, err := json.Marshal(requestBody)
		if err != nil {
			return nil, err
		}
		reqBody = bytes.NewBuffer(jsonBody)
	}

	req, err := http.NewRequestWithContext(ctx, method, url, reqBody)
	if err != nil {
		return nil, err
	}

	for key, value := range c.headers {
		req.Header.Set(key, value)
	}

	if ctx != nil {
		if tokenVal := ctx.Value("token"); tokenVal != nil {
			if token, ok := tokenVal.(string); ok {
				req.Header.Set(stdauth.TokenHeader, token)
			}
		}
		if tokenVal := ctx.Value("Ctx-Token"); tokenVal != nil {
			if token, ok := tokenVal.(string); ok {
				req.Header.Set(stdauth.TokenHeader, token)
			}
		}
	}

	resp, err := c.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	stdlog.Info(fmt.Sprintf("HTTP %s %s -> status: %d", method, url, resp.StatusCode))

	res, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return nil, fmt.Errorf("status code: %d, response: %s", resp.StatusCode, string(res))
	}

	return &res, nil
}

func isAbsoluteURL(url string) bool {
	return len(url) > 7 && (url[:7] == "http://" || url[:8] == "https://")
}
