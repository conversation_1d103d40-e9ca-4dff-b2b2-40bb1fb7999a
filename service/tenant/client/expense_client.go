package client

import (
	"context"
	"time"
)

type ExpenseClient struct {
	*HTTPClient
}

func NewExpenseClient(url string) *ExpenseClient {
	if url == "" {
		url = "http://llmops-expense:9527"
	}
	return &ExpenseClient{
		HTTPClient: NewHTTPClient(
			WithBaseURL(url),
			<PERSON><PERSON><PERSON><PERSON>("Content-Type", "application/json"),
			<PERSON><PERSON><PERSON><PERSON>("accept", "application/json"),
			WithTimeout(30*time.Second),
		),
	}
}

type ExpenseDiskRecord struct {
	ExpenseRuleId uint   `json:"expenseRule_id"`
	Applicant     string `json:"applicant"`
	DiskSpace     int64  `json:"disk_space"`
	TenantUid     string `json:"tenant_uid"`
	// ExpenseRuleType
	RuleType ExpenseRuleType `json:"rule_type" description:"SAMPLED_DISK or SAMPLED_KNOWLEDGE"`
}

type ExpenseRuleType string

const (
	ExpenseRuleTypeSampleDisk      ExpenseRuleType = "SAMPLED_DISK"
	ExpenseRuleTypeSampleKnowledge ExpenseRuleType = "SAMPLED_KNOWLEDGE"
)

type Empty struct {
}

func (m *ExpenseClient) RecordDiskConsumption(ctx context.Context, record *ExpenseDiskRecord) error {
	requestUrl := "/api/v1/expense/disk"
	var result *Empty
	method := "POST"
	// err := m.doRequest(ctx, method, requestUrl, record, &result)
	err := m.DoRequest(ctx, method, requestUrl, record, &result)
	if err != nil {
		return err
	}
	return nil
}

type Node struct {
	Name string    `json:"name,omitempty"`
	Gpus []NodeGpu `json:"gpus,omitempty"`
}

type NodeGpu struct {
	Number             string  `json:"number"`
	ID                 string  `json:"id"`
	Name               string  `json:"name"`
	TotalVCore         int     `json:"totalVCore"`
	TotalMemory        int64   `json:"totalMemory"`
	TotalMemoryGIB     float64 `json:"totalMemoryGiB"`
	Node               string  `json:"node"`
	Model              string  `json:"model"`
	Vendor             string  `json:"vendor"`
	AllocatedVCore     int     `json:"allocatedVCore"`
	AllocatedMemory    int64   `json:"allocatedMemory"`
	AllocatedMemoryGIB float64 `json:"allocatedMemoryGiB"`
}

func (m *ExpenseClient) GetNodeGpuInfo(ctx context.Context) ([]*Node, error) {
	requestUrl := "/api/v1/resource/node-gpus"
	var result []*Node
	method := "GET"
	err := m.DoRequest(ctx, method, requestUrl, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

type AscendNPUIDInfos struct {
	Infos []*AscendNPUIDInfo
}

type AscendNPUIDInfo struct {
	HamiID string // hami 的 uuid id
	Node   string
	VDIEID string // npu exporter 的uuid
	NPUIdx string
}

func (m *ExpenseClient) GetNpuInfos(ctx context.Context) (*AscendNPUIDInfos, error) {
	requestUrl := "/api/v1/resource/npu-id-info"
	result := &AscendNPUIDInfos{
		Infos: []*AscendNPUIDInfo{},
	}
	method := "GET"
	err := m.DoRequest(ctx, method, requestUrl, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

type ClusterInfo struct {
	Id        string `json:"id"`
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
}
type NodeResourceInfo struct {
	Name           string            `json:"name"`
	Cpu            ResourceInfo      `json:"cpu"`
	Memory         ResourceInfo      `json:"memory"`
	GpuCard        ResourceInfo      `json:"gpu_card"`    // 卡数量
	VGpuCore       ResourceInfo      `json:"vgpu_core"`   // gpu 核心
	VGpuMemory     ResourceInfo      `json:"vgpu_memory"` // gpu 显存
	Pod            ResourceInfo      `json:"pod"`
	Gpus           []NodeGpu         `json:"gpus"`
	Status         []NodeStatus      `json:"status"` // 节点状态 Ready, NotReady, Unknown, SchedulingDisabled
	Arch           string            `json:"arch"`
	CreateTimeUnix int64             `json:"create_time_unix"`
	Labels         map[string]string `json:"labels"` // node 的 labels
	NodeSystemInfo NodeSystemInfo    `json:"node_system_info"`
}

type ResourceInfo struct {
	Allocatable float64 `json:"allocatable"` // 可分配的
	Available   float64 `json:"available"`   // 可用的
	Used        float64 `json:"used"`        // 已分配的
	Unit        string  `json:"unit"`
}
type NodeStatus string

type NodeSystemInfo struct {
	Architecture            string `json:"architecture"`              // "architecture": "amd64",
	BootID                  string `json:"boot_id"`                   // "bootID": "9d3d4eac-7fa7-4d90-8822-9fe4904bc39c",
	ContainerRuntimeVersion string `json:"container_runtime_version"` // "containerRuntimeVersion": "docker://20.10.14",
	KernelVersion           string `json:"kernel_version"`            // "kernelVersion": "3.10.0-1160.el7.x86_64",
	KubeProxyVersion        string `json:"kube_proxy_version"`        // "kubeProxyVersion": "v1.19.15",
	KubeletVersion          string `json:"kubelet_version"`           // "kubeletVersion": "v1.19.15",
	MachineID               string `json:"machine_id"`                // "machineID": "3a0a47a9c867433bab1e93984313c1c1",
	OperatingSystem         string `json:"operating_system"`          // "operatingSystem": "linux",
	OSImage                 string `json:"os_image"`                  // "osImage": "CentOS Linux 7 (Core)",
	SystemUUID              string `json:"system_uuid"`               // "systemUUID": "10049600-C258-11ED-8000-3CECEF36CE9C"
}

type ClusterResourceInfo struct {
	Clusters []ClusterInfo      `json:"clusters"`
	Nodes    []NodeResourceInfo `json:"nodes"`
}

func (m *ExpenseClient) GetCluster(ctx context.Context) (*ClusterResourceInfo, error) {
	requestUrl := "/api/v1/resource/cluster"
	result := &ClusterResourceInfo{
		Clusters: make([]ClusterInfo, 0),
		Nodes:    make([]NodeResourceInfo, 0),
	}
	method := "GET"
	err := m.DoRequest(ctx, method, requestUrl, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
