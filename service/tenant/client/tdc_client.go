package client

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type TdcTenantStatus string

const (
	CANCELED   TdcTenantStatus = "CANCELED"
	ACTIVATING TdcTenantStatus = "ACTIVATING"
)

type TDCClient struct {
	*HTTPClient
}

func NewTDCClient(baseURL string) *TDCClient {
	if baseURL == "" {
		var err error
		baseURL, err = GetTdcTenantServiceBaseUrl()
		if err != nil {
			stdlog.Errorf("Failed to get TDC tenant service base URL: %v", err)
		}
	}

	return &TDCClient{
		HTTPClient: NewHTTPClient(
			WithBaseURL(baseURL),
			<PERSON><PERSON><PERSON><PERSON>("Guardian-Access-Token", GetGuardianAccessToken()),
			<PERSON><PERSON><PERSON><PERSON>("Content-Type", "application/json"),
			<PERSON><PERSON><PERSON><PERSON>("accept", "application/json"),
			WithTimeout(30*time.Second),
		),
	}
}

func (c *TDCClient) ListTenants(ctx context.Context) ([]*models.Tenant, error) {
	var resp TdcTenantList
	err := c.DoRequest(ctx, "GET", fmt.Sprintf("/api/v1/tenants/list?pageNum=1&pageSize=%d", 1000), nil, &resp)
	if err != nil {
		return nil, err
	}

	// 过滤已删除的租户
	tenants := make([]*models.Tenant, 0)
	for _, v := range resp.Data {
		if !strings.EqualFold(v.TenantStatus, string(CANCELED)) {
			tenants = append(tenants, tdcTenantToTenant(&v))
		}
	}
	return tenants, nil
}

func (c *TDCClient) GetTenant(ctx context.Context, tenantID string) (*models.Tenant, error) {
	tenants, err := c.ListTenants(ctx)
	if err != nil {
		return nil, err
	}

	for _, t := range tenants {
		if t.TenantUid == tenantID {
			return t, nil
		}
	}

	return nil, stderr.Errorf("Tenant not found: %s", tenantID)
}

func (c *TDCClient) CreateTenant(ctx context.Context, tenant *models.Tenant) error {
	body := TdcTenant{
		TenantName:        tenant.TenantName,
		TenantUid:         tenant.TenantUid,
		TenantDescription: tenant.TenantDescription,
		Creator:           conf.C.Tenant.TDC.TenantService.Creator,
		Company:           conf.C.Tenant.TDC.TenantService.Company,
		Department:        conf.C.Tenant.TDC.TenantService.Department,
		Password:          conf.C.Tenant.TDC.TenantService.Password,
		UserFullName:      conf.C.Tenant.TDC.TenantService.UserFullName,
		UserEmail:         conf.C.Tenant.TDC.TenantService.UserEmail,
	}
	return c.DoRequest(ctx, "POST", "/api/v1/tenants/uid", body, nil)
}

func (c *TDCClient) SearchHippoInstances(ctx context.Context, tenantID string) ([]*TdcInstance, error) {
	brokerURL, err := GetTdcBrokerServiceBaseUrl()
	if err != nil {
		return nil, err
	}

	brokerClient := NewHTTPClient(
		WithBaseURL(brokerURL),
		WithHeader("Guardian-Access-Token", GetGuardianAccessToken()),
		WithHeader("Content-Type", "application/json"),
	)

	req := TdcSearchInstanceReq{
		TenantId:      tenantID,
		GuardianToken: GetGuardianAccessToken(),
	}

	var instances []*TdcInstance
	err = brokerClient.DoRequest(ctx, "POST", "/v2/service-instances/search-instances", req, &instances)
	if err != nil {
		return nil, err
	}

	return instances, nil
}

func (c *TDCClient) IsHippoInstalled(ctx context.Context, tenantID string) (bool, error) {
	instances, err := c.SearchHippoInstances(ctx, tenantID)
	if err != nil {
		return false, err
	}

	hippoInstanceName, err := GetTdcHippoInstanceName()
	if err != nil {
		return false, err
	}

	for _, instance := range instances {
		if instance.Name == hippoInstanceName {
			return true, nil
		}
	}

	return false, nil
}

func (c *TDCClient) GetHippo(ctx context.Context, tenantID string) (*models.Instance, error) {
	instances, err := c.SearchHippoInstances(ctx, tenantID)
	if err != nil {
		return nil, err
	}

	hippoInstanceName, err := GetTdcHippoInstanceName()
	if err != nil {
		return nil, err
	}

	for _, v := range instances {
		if v.Name == hippoInstanceName {
			result := tdcInstanceToInstance(v)
			if IsHippoRunning(tenantID) {
				result.Status = "RUNNING"
			}
			return result, nil
		}
	}

	return nil, stderr.Errorf("Hippo instance not found for tenant: %s", tenantID)
}

func tdcTenantToTenant(tdcTenant *TdcTenant) *models.Tenant {
	tenant := models.Tenant{
		TenantName:   tdcTenant.TenantName,
		TenantUid:    tdcTenant.TenantUid,
		TenantStatus: tdcTenant.TenantStatus,
		TccUrl:       tdcTenant.TccUrl,
		Creator:      tdcTenant.Creator,
		CreateTime:   tdcTenant.CreateTime,
	}
	return &tenant
}

func tdcInstanceToInstance(tdcInstance *TdcInstance) *models.Instance {
	instance := models.Instance{
		Id:     strconv.Itoa(tdcInstance.ID),
		Name:   tdcInstance.Name,
		Status: tdcInstance.Status,
	}
	return &instance
}

// for tdc
type TdcTenantList struct {
	Data []TdcTenant `json:"data"`
}

type TdcTenant struct {
	TenantUid         string `json:"tenantUid"`
	TenantName        string `json:"tenantName"`
	TenantStatus      string `json:"tenantStatus"`
	TenantDescription string `json:"tenantDescription"`
	Creator           string `json:"creator"`
	Password          string `json:"password"`
	Company           string `json:"company"`
	Department        string `json:"department"`
	UserFullName      string `json:"userFullName"`
	UserEmail         string `json:"userEmail"`
	UserPhone         string `json:"userPhone"`
	Quota             string `json:"quota"`
	TccUrl            string `json:"tccUrl"`
	CreateTime        uint64 `json:"createTime"`
}

type TdcSearchInstanceReq struct {
	TenantId      string
	GuardianToken string
}

type TdcInstance struct {
	CategoryID           int    `json:"categoryId"`
	ExternalInstanceCode string `json:"externalInstanceCode"`
	ExternalOrderID      string `json:"externalOrderId"`
	ExternalProjectCode  string `json:"externalProjectCode"`
	ID                   int    `json:"id"`
	Name                 string `json:"name"`
	UUID                 string `json:"uuid"`
	Nodes                int    `json:"nodes"`
	ProductUUID          string `json:"productUuid"`
	Status               string `json:"status"`
	Visibility           string `json:"visibility"`
}
