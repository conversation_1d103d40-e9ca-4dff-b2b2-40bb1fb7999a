package client

// Overview Hippo概览信息
type Overview struct {
	DatabaseNum         int              `json:"databaseNum"`
	TabletServerNum     int              `json:"tabletServerNum"`
	DiskStats           DiskStats        `json:"diskStats"`
	TabletNum           int              `json:"tabletNum"`
	VersionDetails      []VersionDetails `json:"versionDetails"`
	TServerHealthy      string           `json:"tServerHealthy"`
	MasterGroup         []MasterGroup    `json:"masterGroup"`
	Version             string           `json:"version"`
	ClusterStateFatals  []interface{}    `json:"clusterStateFatals"`
	TableInTrashNum     int              `json:"tableInTrashNum"`
	ActiveMasterAddress string           `json:"activeMasterAddress"`
	TableNum            int              `json:"tableNum"`
	MasterHealthy       string           `json:"masterHealthy"`
	TabletServerList    []TabletServer   `json:"tabletServerList"`
}

// DiskStats 磁盘统计信息
type DiskStats struct {
	IOErrCount             int `json:"iOErrCount"`
	PhysicalFreeCapacityGB int `json:"physicalFreeCapacityGB"`
	PhysicalCapacityGB     int `json:"physicalCapacityGB"`
	ShivaUsedGB            int `json:"shivaUsedGB"`
}

// VersionDetails 版本详情
type VersionDetails struct {
	Name   string `json:"name"`
	Branch string `json:"branch"`
	SHA    string `json:"sha"`
}

// MasterGroup 主节点组
type MasterGroup struct {
	Leader    bool   `json:"leader"`
	Address   string `json:"address"`
	Healthy   string `json:"healthy"`
	StartTime string `json:"startTime"`
	Status    string `json:"status"`
}

// TabletServer 平板服务器
type TabletServer struct {
	TotalStoreCapacityUnit int    `json:"totalStoreCapacityUnit"`
	Address                string `json:"address"`
	PhysicalFreeCapacityGB int    `json:"physicalFreeCapacityGB"`
	Topology               string `json:"topology"`
	Healthy                string `json:"healthy"`
	PhysicalCapacityGB     int    `json:"physicalCapacityGB"`
	TabletNum              int    `json:"tabletNum"`
	ID                     string `json:"id"`
	Decommission           string `json:"decommission"`
	ShivaUsedGB            int    `json:"shivaUsedGB"`
	Status                 string `json:"status"`
	IOErrCount             int    `json:"ioErrCount"`
}

// Tdc5ComponentInstance TDC5组件实例
type Tdc5ComponentInstance struct {
	ID                      string      `json:"id"`
	OriginID                int         `json:"originId"`
	SID                     string      `json:"sid"`
	ComponentInstanceName   string      `json:"componentInstanceName"`
	CompMetaID              string      `json:"compMetaId"`
	ComponentType           string      `json:"componentType"`
	ComponentVersion        string      `json:"componentVersion"`
	ProductType             string      `json:"productType"`
	ProductVersion          string      `json:"productVersion"`
	TenantUid               string      `json:"tenantUid"`
	EnableKerberos          bool        `json:"enableKerberos"`
	IsShared                bool        `json:"isShared"`
	CrossTenant             bool        `json:"crossTenant"`
	SharedDescription       string      `json:"sharedDescription"`
	Creator                 string      `json:"creator"`
	ProductInstanceID       string      `json:"productInstanceId"`
	ProductInstanceName     string      `json:"productInstanceName"`
	Icon                    string      `json:"icon"`
	Status                  string      `json:"status"`
	CreateTime              int64       `json:"createTime"`
	LastModifiedTime        int64       `json:"lastModifiedTime"`
	SharedStatus            string      `json:"sharedStatus"`
	SecurityOn              bool        `json:"securityOn"`
	SecuritySupported       bool        `json:"securitySupported"`
	TenantName              string      `json:"tenantName"`
	PrivateCloudEnvironment bool        `json:"privateCloudEnvironment"`
	MacvlanSupported        bool        `json:"macvlanSupported"`
	CatalogSupported        bool        `json:"catalogSupported"`
	Catalog                 interface{} `json:"catalog"`
	Product                 string      `json:"product"`
	ElasticSupported        bool        `json:"elasticSupported"`
	ElasticEnabled          bool        `json:"elasticEnabled"`
	PluginSupported         bool        `json:"pluginSupported"`
	SupportRollingRestart   bool        `json:"supportRollingRestart"`
	CanCrossCluster         bool        `json:"canCrossCluster"`
	Active                  bool        `json:"active"`
	CompDepStaleness        interface{} `json:"compDepStaleness"`
	NetworkType             string      `json:"networkType"`
	NeedScaleOut            bool        `json:"needScaleOut"`
	Installed               bool        `json:"installed"`
}

// ComponentConfig 组件配置
type ComponentConfig struct {
	Type           string `json:"type"`
	Version        string `json:"version"`
	EnableKerberos bool   `json:"enableKerberos"`
	NetworkType    string `json:"networkType"`
}

// CreateComponentRequestBody 创建组件请求体
type CreateComponentRequestBody struct {
	TenantUid        string          `json:"tenantUid"`
	ProdMetaType     string          `json:"prodMetaType"`
	ProdMetaVersion  string          `json:"prodMetaVersion"`
	ProdInstanceName string          `json:"prodInstanceName"`
	Component        ComponentConfig `json:"component"`
}

// ListTenantsReqBody 列出租户请求体
type ListTenantsReqBody struct {
	LabelSelector map[string]string `json:"labelSelector"`
}

// CreateTdc5TenantReqBody 创建TDC5租户请求体
type CreateTdc5TenantReqBody struct {
	TenantUid     string            `json:"tenantUid"`
	TenantName    string            `json:"tenantName"`
	Creator       string            `json:"creator"`
	LabelSelector map[string]string `json:"labelSelector"`
	ClusterId     string            `json:"clusterId"`
}
