package consts

const (
	TdcHippoPvcLabelSelector = "transwarp.name=hippotabletserver,transwarp.product=hippo,pvc.prefix=hippo-data"

	K8sHippoPvcLabelSelectorFormat       = "llmops.transwarp.io/storage-type=sys,llmops.transwarp.io/managed-by=%s"
	FileSystemStorageLabelSelectorFormat = "llmops.transwarp.io/storage-type=sfs,llmops.transwarp.io/managed-by=%s"

	IngressTemplatePathForTdc = "./etc/ingress_template_tdc.yaml"
	IngressTemplatePath       = "./etc/ingress_template.yaml"

	HamiGpuAnnotationKey = "hami.io/node-nvidia-register"

	INIT_TENANT_KEY = "INIT_TENANT"
)
