package tenant

import (
	"context"
	"errors"
	"fmt"

	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"transwarp.io/aip/llmops-common/pkg/expense"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	dao "transwarp.io/applied-ai/central-auth-service/dao/tenant"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/client"
	httpclient "transwarp.io/applied-ai/central-auth-service/service/tenant/client"
	handler "transwarp.io/applied-ai/central-auth-service/service/tenant/handler"
	helm "transwarp.io/applied-ai/central-auth-service/service/tenant/helm"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	customtypes "transwarp.io/applied-ai/central-auth-service/service/tenant/types"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

type KubernetesTenantService struct {
	kclient *kubernetes.KClientset
	db      *gorm.DB
}

func (k *KubernetesTenantService) ListTenants(ctx context.Context) ([]*models.Tenant, error) {
	eg, gctx := errgroup.WithContext(ctx)
	groupsCh := make(chan []expense.ResourceGroup, 1)
	eg.Go(func() error {
		// 注入注入资源组信息
		resourceGroups, err := expense.ListNodeXPUGroup(gctx, &expense.ListXPUGroupParam{Tenants: []string{}})
		if err != nil {
			return fmt.Errorf("list node xpu group: %w", err)
		}
		groupsCh <- resourceGroups
		return nil
	})

	ts, err := dao.ListTenants(k.db)
	if err != nil {
		return nil, err
	}
	nss, err := k.kclient.K8sInformerClient.NamespaceLister.List(labels.Set(getDefaultLabels()).AsSelector())
	if err != nil {
		return nil, err
	}
	m := make(map[string]*corev1.Namespace, 0)
	for _, v := range nss {
		m[v.Name] = v
	}
	tenants := make([]*models.Tenant, 0)
	for _, v := range ts {
		if value, ok := m[v.TenantUid]; ok {
			v.TenantStatus = string(value.Status.Phase)
			tenants = append(tenants, v)
		}
	}
	err = eg.Wait()
	if err != nil {
		return nil, err
	}
	tenantRsMap := expense.GetTenantResourceGroupMap(<-groupsCh)
	for _, t := range tenants {
		if v, ok := tenantRsMap[t.TenantUid]; ok {
			t.ResourceGroupDetails = expense.BatchCvtToResourceGroupTenant(v, t.TenantUid)
		}
	}
	// 注入能否删除
	for _, t := range tenants {
		t.CanDelete = t.DeleteSafe(false)
	}
	return tenants, nil
}

func getTenantCanDelete() (map[string]bool, error) {
	res := make(map[string]bool)
	return res, nil
}

func (k *KubernetesTenantService) GetTenant(ctx context.Context, tenantId string, ignoreXpuErr bool) (*models.Tenant, error) {
	eg, gctx := errgroup.WithContext(ctx)
	groupsCh := make(chan []expense.ResourceGroup, 1)
	eg.Go(func() error {
		// 注入注入资源组信息
		resourceGroups, err := expense.ListNodeXPUGroup(gctx, &expense.ListXPUGroupParam{Tenants: []string{tenantId}})
		if err != nil {
			stdlog.Errorf("list node xpu group: %v", err)
			if !ignoreXpuErr {
				return fmt.Errorf("list node xpu group: %w", err)
			}
		}
		groupsCh <- resourceGroups
		return nil
	})
	tenant, err := dao.GetTenant(k.db, tenantId)
	if err != nil {
		return nil, err
	}
	if tenant.TenantUid == "" {
		return nil, errors.New("tenant not found")
	}
	hippoSvc, err := httpclient.GetHippoServiceName(tenantId)
	if err != nil {
		stdlog.Errorf("Get hippo svc name failed: %+v", err)
	}
	tenant.HippoServiceName = hippoSvc
	err = eg.Wait()
	if err != nil {
		return nil, err
	}
	tenantRsMap := expense.GetTenantResourceGroupMap(<-groupsCh)
	if v, ok := tenantRsMap[tenantId]; ok {
		tenant.ResourceGroupDetails = expense.BatchCvtToResourceGroupTenant(v, tenantId)
	}
	// 注入能否删除
	tenant.CanDelete = tenant.DeleteSafe(false)
	rq, err := k.GetResourceQuota(tenantId)
	if err != nil {
		return nil, err
	}
	tenant.TenantQuotas = *rq
	return k.fillTenantStatus(tenant)
}

func (k *KubernetesTenantService) fillTenantStatus(tenant *models.Tenant) (*models.Tenant, error) {
	ns, err := k.kclient.K8sInformerClient.GetNamespace(tenant.TenantUid)
	if err != nil {
		return nil, err
	}
	tenant.TenantStatus = string(ns.Status.Phase)
	return tenant, nil
}

func (k *KubernetesTenantService) UpdateTenant(ctx context.Context, tenant *models.Tenant, labels map[string]string) (*models.Tenant, error) {
	if tenant.TenantUid == "" {
		return nil, stderr.Internal.Errorf("update tenant err,no tenant id")
	}
	var resourceQuota *corev1.ResourceQuota
	err := k.db.Session(&gorm.Session{NewDB: true}).Transaction(func(tx *gorm.DB) error {
		err := tx.Save(tenant).Error
		if err != nil {
			return err
		}
		rq := &tenant.TenantQuotas
		if rq.Hard.LimitsCpu == "" && rq.Hard.LimitsMemory == "" {
			rq = getDefaultResourceQuota()
			tenant.TenantQuotas = *rq
		}
		// create resource quota
		resourceQuota, err = toKubernetesResourceQuota(tenant.TenantUid, &rq.Hard)
		if err != nil {
			return err
		}
		if err := expense.BindingXPUGroupToTenant(ctx, &expense.BindingXPUGroupParam{
			XPUGroup: tenant.ResourceGroupIDs,
			TenantID: tenant.TenantUid,
		}); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	annotations := make(map[string]string, 0)
	annotations[customtypes.TenantUidKey] = tenant.TenantUid
	annotations[customtypes.NewSizeKey] = tenant.TenantQuotas.Hard.FileStorage

	resourceQuota.Annotations = annotations

	params := handler.HandlerParams{
		Namespace:           tenant.TenantUid,
		NamespaceLables:     getNsLables(labels),
		ResourceQuota:       resourceQuota,
		TenantResourceQuota: &tenant.TenantQuotas,
	}
	updateHandlerChain := handler.NewK8sUpdateHandlerChain(tenant.TenantUid, k.kclient)
	//  async exec init tenant
	go updateHandlerChain.Handle(context.Background(), tenant.TenantUid, params)

	return tenant, nil
}

// TODO add label to ns, mark ns as llmops tenant
func (k *KubernetesTenantService) CreateTenant(ctx context.Context, tenant *models.Tenant, lables map[string]string, ignoreXpuErr bool) (*models.Tenant, error) {
	if !conf.C.InitData.IsCustom() {
		tenant.TenantUid, _ = util.GenNewNamespace(tenant.TenantUid, false)
	}
	var resourceQuota *corev1.ResourceQuota
	err := k.db.Session(&gorm.Session{NewDB: true}).Transaction(func(tx *gorm.DB) error {
		err := tx.Create(tenant).Error
		if err != nil {
			return err
		}
		if !conf.C.InitData.IsCustom() || conf.C.InitData.CreateNamespace {
			err = k.kclient.CreateNamespace(tenant.TenantUid, getNsLables(lables))
			if err != nil {
				return err
			}
		}
		rq := &tenant.TenantQuotas
		if rq.Hard.LimitsCpu == "" && rq.Hard.LimitsMemory == "" {
			rq = getDefaultResourceQuota()
		}
		// create resource quota
		resourceQuota, err = toKubernetesResourceQuota(tenant.TenantUid, &rq.Hard)
		if err != nil {
			return err
		}
		if len(tenant.ResourceGroupIDs) > 0 {
			if err := expense.BindingXPUGroupToTenant(ctx, &expense.BindingXPUGroupParam{
				XPUGroup: tenant.ResourceGroupIDs,
				TenantID: tenant.TenantUid,
			}); err != nil {
				stdlog.Errorf("bind xpu group to tenant: %v", err)
				if !ignoreXpuErr {
					return err
				}
			}
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	annotations := make(map[string]string, 0)
	annotations[customtypes.TenantUidKey] = tenant.TenantUid
	annotations[customtypes.NewSizeKey] = tenant.TenantQuotas.Hard.FileStorage

	resourceQuota.Annotations = annotations

	params := handler.HandlerParams{
		Namespace:           tenant.TenantUid,
		NamespaceLables:     getNsLables(lables),
		ResourceQuota:       resourceQuota,
		TenantResourceQuota: &tenant.TenantQuotas,
	}
	initHandlerChain := handler.NewK8sInitHandlerChain(tenant.TenantUid, k.kclient)
	//  async exec init tenant
	go initHandlerChain.Handle(context.Background(), tenant.TenantUid, params)

	return tenant, nil
}

func (k *KubernetesTenantService) DeleteTenant(ctx context.Context, tenantId string) error {
	t, err := dao.GetTenant(k.db, tenantId)
	if err != nil {
		return err
	}
	if t == nil || t.TenantUid == "" {
		stdlog.Warnf("tenant %s not found from db", tenantId)
		return nil
	}

	err = dao.DeleteTenant(k.db, tenantId)
	if err != nil {
		return err
	}

	handlerChain := handler.NewK8sDeleteHandlerChain(tenantId, k.kclient)
	err = handlerChain.Handle(context.Background(), tenantId, handler.HandlerParams{})
	if err != nil {
		stdlog.Errorf("Exec uninstall handler chain failed: %+v", err)
		return err
	}

	err = k.kclient.KubeClient.CoreV1().Namespaces().Delete(context.Background(), tenantId, *metav1.NewDeleteOptions(5 * 60))
	if err != nil {
		return err
	}
	if err := expense.BindingXPUGroupToTenant(ctx, &expense.BindingXPUGroupParam{
		XPUGroup: []string{},
		TenantID: tenantId,
	}); err != nil {
		return err
	}
	return nil
}

func (k *KubernetesTenantService) BatchDeleteTenant(ctx context.Context, tenantIds []string) error {
	for _, tenant := range tenantIds {
		if err := k.DeleteTenant(ctx, tenant); err != nil {
			return err
		}
	}
	return nil
}

func (k *KubernetesTenantService) EnsureLlmopsBasic(tenantUid string) (*models.Instance, error) {
	return ensureLlmopsBasic(tenantUid)
}

func (k *KubernetesTenantService) EnsureHippo(tenantUid string) (*models.Instance, error) {
	exists, err := helm.HippoExists(tenantUid)
	if err != nil {
		stdlog.Errorf("Error checking hippo release %s status: %v\n", tenantUid, err)
		return nil, err
	}
	if exists {
		return k.GetHippo(tenantUid)
	}
	err = helm.InstallHippo(tenantUid)
	if err != nil {
		stdlog.Errorf("Install hippo failed, %s", err.Error())
		return nil, err
	}

	return helm.GetHippoRelease(tenantUid)
}

func (k *KubernetesTenantService) GetHippo(tenantUid string) (*models.Instance, error) {
	return helm.GetHippoRelease(tenantUid)
}

func (k *KubernetesTenantService) GetResourceQuota(tenantUid string) (*models.TenantResourceQuota, error) {
	ch := make(chan string)
	go func() {
		// 异步调用 http
		hippoUsedStorage, err := k.GetHippoUsedStorage(tenantUid)
		if err != nil {
			stdlog.Errorf("Get hippo used storage failed, error: %s.", err.Error())
		}
		ch <- hippoUsedStorage
	}()
	quota, err := getResourceQuota(tenantUid)
	if err != nil {
		return nil, err
	}
	hs := <-ch
	quota.Used.Knowl = hs
	quota.Used.KnowledgeBaseStorage = hs

	return quota, nil
}

func (k *KubernetesTenantService) GetDefaultResourceQuota() *models.TenantResourceQuota {
	return getDefaultResourceQuota()
}

func (k *KubernetesTenantService) UpdateResourceQuota(ctx context.Context, ns string, quota *models.ResourceQuotaSpec) (*models.TenantResourceQuota, error) {
	resourceQuota, err := toKubernetesResourceQuota(ns, quota)
	if err != nil {
		return nil, err
	}
	annotations := make(map[string]string, 0)
	annotations[customtypes.TenantUidKey] = ns
	annotations[customtypes.NewSizeKey] = quota.FileStorage

	resourceQuota.Annotations = annotations

	params := handler.HandlerParams{
		Namespace:     ns,
		ResourceQuota: resourceQuota,
	}
	updateHandlerChain := handler.NewK8sUpdateHandlerChain(ns, k.kclient)
	err = updateHandlerChain.Handle(ctx, ns, params)
	if err != nil {
		return nil, err
	}

	return k.GetResourceQuota(ns)
}

func (t *KubernetesTenantService) ValidateTenantName(ctx context.Context, name string) error {
	tenants, err := t.ListTenants(ctx)
	if err != nil {
		return err
	}
	for _, tenant := range tenants {
		if tenant.TenantName == name {
			return stderr.Error(fmt.Sprintf("tenant name %s exists", name))
		}
	}
	return nil
}

func (t *KubernetesTenantService) ValidateTenantUid(ctx context.Context, uid string) error {
	newNsName, valid := util.GenNewNamespace(uid, false)
	if !valid {
		return stderr.Error(fmt.Sprintf(`Invalid tenant id %s, 1. can only contain lowercase alphanumeric characters (a-z, 0-9) and hyphens (-);
		2. must start and end with an alphanumeric character; 3. must be between 1 and 63 characters.`, newNsName))
	}

	tenants, err := t.ListTenants(ctx)
	if err != nil {
		return err
	}
	for _, tenant := range tenants {
		if tenant.TenantUid == newNsName {
			return stderr.Error(fmt.Sprintf("tenant uid %s exsits", newNsName))
		}
	}
	return nil
}

func (t *KubernetesTenantService) GetHippoUsedStorage(tenantUid string) (string, error) {
	usedstorage, err := client.GetHippoUsedStorage(tenantUid)
	return fmt.Sprintf("%dGi", usedstorage), err
}

func (t *KubernetesTenantService) CreateTenantIngress(uid string) error {
	hc := handler.NewIngressInitHandlerChain(uid, t.kclient)
	return hc.Handle(context.TODO(), uid, handler.HandlerParams{
		Namespace: uid,
	})
}

func (t *KubernetesTenantService) NxgToResourceQuota(nxgIDs []string) (*models.TenantResourceQuota, error) {
	return nxgToResourceQuota(nxgIDs)
}
