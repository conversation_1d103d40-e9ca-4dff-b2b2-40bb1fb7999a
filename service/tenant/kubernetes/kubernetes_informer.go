package kubernetes

import (
	"time"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	applisterv1 "k8s.io/client-go/listers/apps/v1"
	corelisterv1 "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/cache"
)

type K8sInformerClient struct {
	DeployLister        applisterv1.DeploymentLister
	ServiceInformer     cache.SharedInformer
	NodeLister          corelisterv1.NodeLister
	ServiceLister       corelisterv1.ServiceLister
	NamespaceLister     corelisterv1.NamespaceLister
	ResourceQuotaLister corelisterv1.ResourceQuotaLister
	PodLister           corelisterv1.PodLister
	ConfigMapInformer   cache.SharedInformer
	ConfigMapLister     corelisterv1.ConfigMapLister
}

func (k *K8sInformerClient) Init(kubeClient *kubernetes.Clientset) error {
	sharedInformerFactory := informers.NewSharedInformerFactory(kubeClient, time.Minute*10)

	k.NodeLister = sharedInformerFactory.Core().V1().Nodes().Lister()
	k.ServiceLister = sharedInformerFactory.Core().V1().Services().Lister()
	k.NamespaceLister = sharedInformerFactory.Core().V1().Namespaces().Lister()
	k.ResourceQuotaLister = sharedInformerFactory.Core().V1().ResourceQuotas().Lister()
	k.PodLister = sharedInformerFactory.Core().V1().Pods().Lister()
	k.ConfigMapInformer = sharedInformerFactory.Core().V1().ConfigMaps().Informer()
	k.ConfigMapLister = sharedInformerFactory.Core().V1().ConfigMaps().Lister()

	stopCh := make(chan struct{})
	sharedInformerFactory.Start(stopCh)

	return nil
}

func (k *K8sInformerClient) ListNodes(labelSelector string) ([]v1.Node, error) {
	var nodes []*v1.Node
	var err error
	if labelSelector == "" {
		nodes, err = k.NodeLister.List(labels.Everything())
		if err != nil {
			return nil, err
		}
	} else {
		selector, err := k.toSelector(labelSelector)
		if err != nil {
			return nil, err
		}
		nodes, err = k.NodeLister.List(selector)
		if err != nil {
			return nil, err
		}
	}
	res := make([]v1.Node, 0)
	for _, v := range nodes {
		res = append(res, *v)
	}
	return res, nil
}

func (k *K8sInformerClient) GetNamespace(namespace string) (*v1.Namespace, error) {
	ns, err := k.NamespaceLister.Get(namespace)
	if err != nil {
		return nil, err
	}
	return ns, nil
}

func (k *K8sInformerClient) GetResourceQuota(namespace, quotaName string) (*v1.ResourceQuota, error) {
	quota, err := k.ResourceQuotaLister.ResourceQuotas(namespace).Get(quotaName)
	if err != nil {
		return nil, err
	}
	return quota, nil
}

func (k *K8sInformerClient) ListServices(namespace string, labelSelector string) ([]v1.Service, error) {
	selector, err := k.toSelector(labelSelector)
	if err != nil {
		return nil, err
	}
	svcs, err := k.ServiceLister.Services(namespace).List(selector)
	if err != nil {
		return nil, err
	}
	res := make([]v1.Service, 0)
	for _, v := range svcs {
		res = append(res, *v)
	}
	return res, nil
}

func (k *K8sInformerClient) toSelector(labelSelector string) (labels.Selector, error) {
	return labels.Parse(labelSelector)
}

func (k *K8sInformerClient) GetConfigMap(namespace, name string) (*v1.ConfigMap, error) {
	return k.ConfigMapLister.ConfigMaps(namespace).Get(name)
}
