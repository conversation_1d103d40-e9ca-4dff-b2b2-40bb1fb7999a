package kubernetes

import (
	"container/heap"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/samber/lo"

	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/retry"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

// tcos crd wsts
var WstsCrd = schema.GroupVersionResource{Group: "apps.transwarp.io", Version: "v1alpha1", Resource: "statefulsets"}

type KClientset struct {
	KubeClient        *kubernetes.Clientset
	DynamicClient     *dynamic.DynamicClient
	K8sInformerClient *K8sInformerClient
}

var kclientset *KClientset = nil

func GetK8sRestConfig() (*rest.Config, error) {
	var config *rest.Config
	var err error
	// 本地调试模式
	if strings.ToUpper(os.Getenv("LOCAL_DEBUG_MODEL")) == "TRUE" {
		kubeConfigPath := filepath.Join(os.Getenv("MLOPS_CONF_DIR"), "kubeconfig")
		config, err = clientcmd.BuildConfigFromFlags("", kubeConfigPath)
		if err != nil {
			return nil, stderr.Wrap(err, "build config from :%v err", kubeConfigPath)
		}
	} else {
		// 集群模式
		stdlog.Debug("fail to read kubeConfig file, use serviceaccount")
		config, err = rest.InClusterConfig()
		if err != nil {
			return nil, stderr.Wrap(err, "init cluster config err")
		}
	}
	return config, nil
}

func NewKClientset() (*KClientset, error) {
	if kclientset != nil {
		return kclientset, nil
	}

	config, err := GetK8sRestConfig()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to load Kubernetes config: %v\n", err)
		return nil, err
	}

	// for test
	// 226
	// token := "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	// 207
	// token := "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	// config := &rest.Config{
	// 	// 226
	// 	// Host:        "https://**************:16443",
	// 	// 207
	//	Host:        "https://**************:6443",
	//	BearerToken: token,
	//	TLSClientConfig: rest.TLSClientConfig{
	//		Insecure: true,
	//	},
	// }

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		stdlog.Errorf("Failed to create Kubernetes clientset: %v", err)
		return nil, err
	}
	dc, err := dynamic.NewForConfig(config)
	if err != nil {
		stdlog.Errorf("Failed to create dynamic client: %v", err)
		return nil, err
	}
	informerCli := &K8sInformerClient{}
	if err := informerCli.Init(clientset); err != nil {
		stdlog.Errorf("Init k8s informer client failed %+v.", err)
	}

	kclientset = &KClientset{
		KubeClient:        clientset,
		DynamicClient:     dc,
		K8sInformerClient: informerCli,
	}
	return kclientset, nil
}

func (c *KClientset) ListNodes(labelSelector string) ([]corev1.Node, error) {
	resp, err := c.KubeClient.CoreV1().Nodes().List(context.Background(), metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		return nil, err
	}
	nodes := make([]corev1.Node, 0)
	nodes = append(nodes, resp.Items...)
	return nodes, nil
}

// func (c *KClientset) GetClusterHamiGpu(labelSelector string) ([]*models.HamiGpu, error) {
// 	nodes, err := c.ListNodes(labelSelector)
// 	if err != nil {
// 		return nil, err
// 	}
//
// 	// TODO
// 	for _, node := range nodes {
// 		if v, ok := node.Annotations[consts.HamiGpuAnnotationKey]; ok {
// 			stdlog.Infof("Node %s gpu info: %s.", node.Name, v)
// 		}
// 	}
// 	return nil, nil
// }

func (c *KClientset) ListNamespaces(label map[string]string) ([]corev1.Namespace, error) {
	var labelSelector []string
	for key, value := range label {
		labelSelector = append(labelSelector, fmt.Sprintf("%s=%s", key, value))
	}
	labs, err := labels.Parse(strings.Join(labelSelector, ","))
	if err != nil {
		return nil, fmt.Errorf("parse labels: %w", err)
	}
	resp, err := c.K8sInformerClient.NamespaceLister.List(labs)
	if err != nil {
		return nil, stderr.Error(fmt.Sprintf("List ns by lables %s failed.", strings.Join(labelSelector, ",")), err)
	}
	return lo.FromSlicePtr(resp), nil
}

func (c *KClientset) CreateNamespace(namespace string, labels map[string]string) error {
	ns := &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name:   namespace,
			Labels: labels,
		},
	}
	_, err := c.KubeClient.CoreV1().Namespaces().Create(context.Background(), ns, metav1.CreateOptions{})
	if err != nil {
		return err
	}
	return nil
}

func (c *KClientset) IsNamespaceReady(namespace string) (bool, error) {
	ns, err := c.KubeClient.CoreV1().Namespaces().Get(context.TODO(), namespace, metav1.GetOptions{})
	if err != nil {
		return false, err
	}

	if ns.Status.Phase == corev1.NamespaceActive {
		return true, nil
	}
	return false, nil
}

func (c *KClientset) CreateResourceQuota(namespace string, quota *corev1.ResourceQuota) error {
	_, err := c.KubeClient.CoreV1().ResourceQuotas(namespace).Create(context.TODO(), quota, metav1.CreateOptions{})
	if err != nil {
		stdlog.Errorf("Create %s resource quota %s failed. Error msg: %s", namespace, quota.Name, err.Error())
		return err
	}
	return nil
}

func (c *KClientset) PatchResourceQuota(namespace, quotaName, resourceName, resourceValue, updateby string) error {
	rv, err := resource.ParseQuantity(resourceValue)
	if err != nil {
		stdlog.Errorf("Failed to parse resource value %s, error: %v", resourceValue, err)
		return err
	}
	patch := &v1.ResourceQuota{
		Spec: v1.ResourceQuotaSpec{
			Hard: v1.ResourceList{v1.ResourceName(resourceName): rv},
		},
		ObjectMeta: metav1.ObjectMeta{
			Annotations: map[string]string{"transwarp.io/update-by": updateby},
		},
	}

	patchBytes, err := json.Marshal(patch)
	if err != nil {
		fmt.Println("Error marshaling patch:", err)
		return err
	}

	_, err = c.KubeClient.CoreV1().ResourceQuotas(namespace).Patch(context.TODO(), quotaName, types.MergePatchType, patchBytes, metav1.PatchOptions{})
	if err != nil {
		stdlog.Errorf("Patch %s resource quota %s's %s:%s failed. Error msg: %+v", namespace, resourceName, resourceName, resourceValue, err)
		return err
	}
	return nil
}

func (c *KClientset) ListServices(namespace, labelSelector string) ([]corev1.Service, error) {
	services, err := c.KubeClient.CoreV1().Services(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		return nil, err
	}
	return services.Items, nil
}

func (c *KClientset) GetResourceQuota(namespace, quotaName string) (*corev1.ResourceQuota, error) {
	quota, err := c.KubeClient.CoreV1().ResourceQuotas(namespace).Get(context.TODO(), quotaName, metav1.GetOptions{})
	if err != nil {
		stdlog.Errorf("Failed to %s get quota %s, error: %s", namespace, quotaName, err.Error())
		return nil, err
	}
	return quota, nil
}

func (c *KClientset) UpdateResourceQuota(namespace string, quota *corev1.ResourceQuota) error {
	_, err := c.KubeClient.CoreV1().ResourceQuotas(namespace).Update(context.TODO(), quota, metav1.UpdateOptions{})
	if err != nil {
		stdlog.Errorf("Update %s resource quota %s failed. Error msg: %s", namespace, quota.Name, err.Error())
		return err
	}
	return nil
}

func (c *KClientset) SetDefaultLimitRange(namespace string) error {
	limitRange := &v1.LimitRange{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "default-limitrange",
			Namespace: namespace,
		},
		Spec: v1.LimitRangeSpec{
			Limits: []v1.LimitRangeItem{
				{
					Type: v1.LimitTypeContainer,
					Default: v1.ResourceList{
						v1.ResourceCPU:    resource.MustParse("500m"),
						v1.ResourceMemory: resource.MustParse("1024Mi"),
					},
					DefaultRequest: v1.ResourceList{
						v1.ResourceCPU:    resource.MustParse("200m"),
						v1.ResourceMemory: resource.MustParse("256Mi"),
					},
				},
			},
		},
	}
	_, err := c.KubeClient.CoreV1().LimitRanges(namespace).Create(context.TODO(), limitRange, metav1.CreateOptions{})
	if err != nil {
		stdlog.Errorf("Set limit range failed in ns %s", namespace)
		return err
	}
	return nil
}

func (c *KClientset) UpdatePVCCapacityWithLableSelector(namespace, newCapacity, labelSelector string) error {
	// 获取符合标签的 PVC 列表
	pvcs, err := c.KubeClient.CoreV1().PersistentVolumeClaims(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		stdlog.Errorf("Error listing PVCs: %v", err)
		return err
	}

	// 遍历 PVC 列表并更新容量
	for _, pvc := range pvcs.Items {
		err := c.PatchPvcCapacity(namespace, pvc.Name, newCapacity)
		if err != nil {
			stdlog.Errorf("Error patching %s PVC %s capacity to %s: %v", namespace, pvc.Name, newCapacity, err)
			return err
		} else {
			stdlog.Infof("Patching %s PVC %s capacity to %s successfully\n", namespace, pvc.Name, newCapacity)
		}
	}

	return nil
}

func (c *KClientset) PatchPvcCapacityWithLableSelector(namespace, labelSelector string, deltaCapacityGi int64) error {
	stdlog.Infof("Total delta pvc storage (label: %s): %dGi\n", labelSelector, deltaCapacityGi)

	if deltaCapacityGi <= 0 {
		return nil
	}
	pvcs, err := c.KubeClient.CoreV1().PersistentVolumeClaims(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		stdlog.Errorf("Find all pvc of with label %s in namespace %s throw err: %+v", labelSelector, namespace, err)
		return err
	}

	if len(pvcs.Items) <= 0 {
		stdlog.Errorf("Not found pvc in namespace %s with label %s.", namespace, labelSelector)
		return stderr.Error("Not found pvc in namespace %s with label %s.", namespace, labelSelector)
	}

	numOfPvc := len(pvcs.Items)
	deltaArray := make([]int64, numOfPvc)
	for count := 0; count < int(deltaCapacityGi); {
		for i := 0; i < numOfPvc && count < int(deltaCapacityGi); i = i + 1 {
			deltaArray[i] = deltaArray[i] + 1
			count = count + 1
		}
	}

	pq := make(util.PriorityQueue, 0)
	for i := 0; i < numOfPvc; i = i + 1 {
		pvc := pvcs.Items[i]
		quantity := pvc.Spec.Resources.Requests[corev1.ResourceStorage]
		heap.Push(&pq, &util.Item{
			Value:       i,
			CurrentSize: quantity.Value() / (1 << 30),
			Delta:       0,
		})
	}
	for count := 0; count < int(deltaCapacityGi); {
		item := heap.Pop(&pq).(*util.Item)
		item.CurrentSize = item.CurrentSize + 1
		item.Delta = item.Delta + 1
		heap.Push(&pq, item)
		count = count + 1
	}

	for pq.Len() > 0 {
		item := heap.Pop(&pq).(*util.Item)
		pvc := pvcs.Items[item.Value]

		stdlog.Infof("Pvc: %s, origin storage: %dGi, delta storage: %dGi.", pvc.Name, item.CurrentSize-item.Delta, item.Delta)

		if item.Delta > 0 {
			currentSize := pvc.Spec.Resources.Requests[corev1.ResourceStorage]
			newSize := currentSize.DeepCopy()
			delta, err := resource.ParseQuantity(fmt.Sprintf("%dGi", item.Delta))
			if err != nil {
				return err
			}
			newSize.Add(delta)
			pvc.Spec.Resources.Requests[corev1.ResourceStorage] = newSize
			stdlog.Infof("Do update pvc %s storage to %dGi", pvc.Name, newSize.Value()/(1<<30))

			err = c.PatchPvcCapacity(pvc.Namespace, pvc.Name, util.ToGiInt(newSize.Value()))
			if err != nil {
				stdlog.Errorf("Patch pvc %s in %s errror: %+v.", pvc.Name, namespace, err)
				return err
			}
		}
	}

	return nil
}

func (c *KClientset) PatchPvcCapacity(namespace, pvcName, newCapacity string) error {
	patchData := map[string]interface{}{
		"spec": map[string]interface{}{
			"resources": map[string]interface{}{
				"requests": map[string]interface{}{
					"storage": newCapacity,
				},
			},
		},
	}

	patchBytes, err := json.Marshal(patchData)
	if err != nil {
		stdlog.Errorf("Error marshaling pvc patch data: %v", err)
	}

	_, err = c.KubeClient.CoreV1().PersistentVolumeClaims(namespace).Patch(context.TODO(), pvcName, types.MergePatchType, patchBytes, metav1.PatchOptions{})
	if err != nil {
		stdlog.Errorf("Error patch pvc capacity: %v", err)
		return err
	}
	return nil
}

func (c *KClientset) PatchNamespaceLable(namespace string, labels map[string]string) error {
	// 创建 JSON 补丁数据
	patchData, err := json.Marshal(map[string]interface{}{
		"metadata": map[string]interface{}{
			"labels": labels,
		},
	})
	if err != nil {
		stdlog.Errorf("Error creating namespace %s patch data: %+v", namespace, err)
		return err
	}

	// 执行 PATCH 操作
	_, err = c.KubeClient.CoreV1().Namespaces().Patch(context.TODO(), namespace, types.StrategicMergePatchType, patchData, metav1.PatchOptions{})
	if err != nil {
		stdlog.Errorf("Error patching namespace %s: %s", namespace, err.Error())
		return err
	}

	return nil
}

func (c *KClientset) ScaleDeploymentReplicas(namespace, labelSelector string, replicas int) error {
	patch := map[string]interface{}{
		"spec": map[string]interface{}{
			"replicas": replicas,
		},
	}
	patchBytes, err := json.Marshal(patch)
	if err != nil {
		stdlog.Errorf("Error creating %s' deployment with label %s replicas patch data: %+v", namespace, labelSelector, err)
		return err
	}

	deploymentList, err := c.KubeClient.AppsV1().Deployments(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		stdlog.Errorf("List namespace %s's deployment with label %s err: %+v", namespace, labelSelector, err)
		return err
	}

	for _, d := range deploymentList.Items {
		_, err := c.KubeClient.AppsV1().Deployments(namespace).Patch(context.TODO(), d.Name, types.StrategicMergePatchType, patchBytes, metav1.PatchOptions{})
		if err != nil {
			stdlog.Errorf("Error patch namespace %s's deployment with label %s err: %+v", namespace, labelSelector, err)
			return err
		}
	}
	return nil
}

func (c *KClientset) ScaleWstatefulsetReplicas(namespace, labelSelector string, replicas int) error {
	patch := map[string]interface{}{
		"spec": map[string]interface{}{
			"replicas": replicas,
		},
	}
	patchBytes, err := json.Marshal(patch)
	if err != nil {
		stdlog.Errorf("Error creating %s's wsts with label %s replicas patch data: %+v", namespace, labelSelector, err)
		return err
	}

	wstatefulsetList, err := c.DynamicClient.Resource(WstsCrd).Namespace(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		stdlog.Errorf("List namespace %s's wsts with label %s err: %+v", namespace, labelSelector, err)
		return err
	}

	for _, wsts := range wstatefulsetList.Items {
		err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
			_, err := c.DynamicClient.Resource(WstsCrd).Namespace(namespace).Patch(context.TODO(), wsts.GetName(), types.MergePatchType, patchBytes, metav1.PatchOptions{})
			return err
		})
		if err != nil {
			stdlog.Errorf("Error patch namespace %s's wsts %s with label %s err: %+v", namespace, wsts.GetName(), labelSelector, err)
			return err
		}
	}
	return nil
}

func (c *KClientset) PatchDeploymentCpuAndMemoryResource(namespace, labelSelector, limitsCpu, limitsMemory, requestsCpu, requestsMemory string) error {
	patch := []map[string]interface{}{
		{
			"op":    "replace",
			"path":  "/spec/template/spec/containers/0/resources/limits/cpu",
			"value": limitsCpu,
		},
		{
			"op":    "replace",
			"path":  "/spec/template/spec/containers/0/resources/limits/memory",
			"value": limitsMemory,
		},
		{
			"op":    "replace",
			"path":  "/spec/template/spec/containers/0/resources/requests/cpu",
			"value": requestsCpu,
		},
		{
			"op":    "replace",
			"path":  "/spec/template/spec/containers/0/resources/requests/memory",
			"value": requestsMemory,
		},
	}

	patchBytes, err := json.Marshal(patch)
	if err != nil {
		stdlog.Errorf("Error creating %s's deployment with label %s resource patch data: %+v", namespace, labelSelector, err)
		return err
	}

	deploymentList, err := c.KubeClient.AppsV1().Deployments(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		stdlog.Errorf("List namespace %s's deployment with label %s err: %+v", namespace, labelSelector, err)
		return err
	}

	for _, deployment := range deploymentList.Items {
		_, err = c.KubeClient.AppsV1().Deployments(namespace).Patch(context.TODO(), deployment.Name, types.JSONPatchType, patchBytes, metav1.PatchOptions{})
		if err != nil {
			stdlog.Errorf("Error patch namespace %s's deployment %s with label %s resource err: %+v", namespace, deployment.Name, labelSelector, err)
			return err
		}
	}
	return nil
}

// tcos crd
func (c *KClientset) PatchWstatefulsetCpuAndMemoryResource(namespace, labelSelector, limitsCpu, limitsMemory, requestsCpu, requestsMemory string) error {
	patch := []map[string]interface{}{
		{
			"op":    "replace",
			"path":  "/spec/template/spec/containers/0/resources/limits/cpu",
			"value": limitsCpu,
		},
		{
			"op":    "replace",
			"path":  "/spec/template/spec/containers/0/resources/limits/memory",
			"value": limitsMemory,
		},
		{
			"op":    "replace",
			"path":  "/spec/template/spec/containers/0/resources/requests/cpu",
			"value": requestsCpu,
		},
		{
			"op":    "replace",
			"path":  "/spec/template/spec/containers/0/resources/requests/memory",
			"value": requestsMemory,
		},
	}

	patchBytes, err := json.Marshal(patch)
	if err != nil {
		stdlog.Errorf("Error creating %s's wsts with label %s resource patch data: %+v", namespace, labelSelector, err)
		return err
	}

	wstatefulsetList, err := c.DynamicClient.Resource(WstsCrd).Namespace(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		stdlog.Errorf("List namespace %s's wsts with label %s err: %+v", namespace, labelSelector, err)
		return err
	}

	for _, wsts := range wstatefulsetList.Items {
		err := retry.RetryOnConflict(retry.DefaultRetry, func() error {
			_, err := c.DynamicClient.Resource(WstsCrd).Namespace(namespace).Patch(context.TODO(), wsts.GetName(), types.JSONPatchType, patchBytes, metav1.PatchOptions{})
			return err
		})
		if err != nil {
			stdlog.Errorf("Error patch namespace %s's wsts %s with label %s err: %+v", namespace, wsts.GetName(), labelSelector, err)
			return err
		}
	}
	return nil
}

func (c *KClientset) AllowVolumeExpansion(namespace, labelSelector string) bool {
	labs, err := labels.Parse(labelSelector)
	if err != nil {
		stdlog.Errorf("parse labelSelector %s err: %+v", labelSelector, err)
		return false
	}
	pvcs, err := c.K8sInformerClient.PVCLister.PersistentVolumeClaims(namespace).List(labs)
	if err != nil {
		stdlog.Errorf("Find pvc of with label %s in namespace %s throw err: %+v", labelSelector, namespace, err)
		return false
	}
	if len(pvcs) == 0 {
		stdlog.Errorf("Not found pvc of with label %s in namespace %s.", labelSelector, namespace)
		return false
	}
	pvc := pvcs[0]
	if pvc.Status.Phase != v1.ClaimBound {
		stdlog.Errorf("Pvc %s in namespace %s is not bound to a pv.", pvc.Name, namespace)
		return false
	}

	pv, err := c.K8sInformerClient.PVLister.Get(pvc.Spec.VolumeName)
	if err != nil {
		stdlog.Errorf("Get pv %s bound by pvc %s in namespace %s throw err: %+v", pvc.Spec.VolumeName, pvc.Name, namespace, err)
		return false
	}

	storageClassName := pv.Spec.StorageClassName
	if storageClassName == "" {
		stdlog.Infof("Pv %s no storageclass", pv.Name)
		return false
	}
	sc, err := c.K8sInformerClient.SCLister.Get(storageClassName)
	if err != nil {
		stdlog.Errorf("Not found storageclass with sc name %s corresponding to pvc %s.", storageClassName, pvc.Name)
		return false
	}
	if sc.AllowVolumeExpansion == nil || !*sc.AllowVolumeExpansion {
		stdlog.Infof("Storageclass %s no parameter AllowVolumeExpansion or is false.", storageClassName)
		return false
	}

	// pv.kubernetes.io/provisioned-by: csi.juicefs.com
	provisioner := sc.Provisioner
	if provisionedBy, ok := pv.Annotations["pv.kubernetes.io/provisioned-by"]; ok {
		if strings.EqualFold(strings.Trim(provisioner, " "), strings.Trim(provisionedBy, " ")) {
			return true
		}
	}
	return false
}
