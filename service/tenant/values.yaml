resourcequota:
  cohort:
    pods: 1k
    limits_cpu: "32"
    limits_memory: 128Gi
    requests_cpu: ""
    requests_memory: ""
    requests_storage: ""
    bandwidth: 2Gi
    egress_bandwidth: ""
    ingress_bandwidth: ""
    gpu: "200"
    gpu_memory: 80Gi
    knowl: 500Gi
    knowledege_base_storage: 500Gi
    file_storage: 1000Gi
    cpu:
      name: cpu
      nominal_quota: "32"
      borrowing_limit: "0"
      lending_limit: "32"
    memory:
      name: memory
      nominal_quota: 128Gi
      borrowing_limit: 0Gi
      lending_limit: 128Gi
    accelerated_computing:
      310P3:
        - name: huawei.com/Ascend310P
          nominal_quota: "0"
          borrowing_limit: "0"
          lending_limit: "0"
        - name: huawei.com/Ascend310P-memory
          nominal_quota: "0"
          borrowing_limit: 0Gi
          lending_limit: 0Gi
      910B:
        - name: huawei.com/Ascend910A
          nominal_quota: "0"
          borrowing_limit: "0"
          lending_limit: "0"
        - name: huawei.com/Ascend910A-memory
          nominal_quota: "0"
          borrowing_limit: 0Gi
          lending_limit: 0Gi
      910B4:
        - name: huawei.com/Ascend910B
          nominal_quota: "0"
          borrowing_limit: "0"
          lending_limit: "0"
        - name: huawei.com/Ascend910B-memory
          nominal_quota: "0"
          borrowing_limit: 0Gi
          lending_limit: 0Gi
      cambricon:
        - name: cambricon.com/vmlu
          nominal_quota: "0"
          borrowing_limit: "0"
          lending_limit: "0"
        - name: cambricon.com/mlu.smlu.vmemory
          nominal_quota: "0"
          borrowing_limit: 0Gi
          lending_limit: 0Gi
        - name: cambricon.com/mlu.smlu.vcore
          nominal_quota: "0"
          borrowing_limit: "0"
          lending_limit: "0"
      hygon:
        - name: hygon.com/dcunum
          nominal_quota: "0"
          borrowing_limit: "0"
          lending_limit: "0"
        - name: hygon.com/dcumem
          nominal_quota: "0"
          borrowing_limit: 0Gi
          lending_limit: 0Gi
        - name: hygon.com/dcucores
          nominal_quota: "0"
          borrowing_limit: "0"
          lending_limit: "0"
      iluvatar:
        - name: iluvatar.ai/vgpu
          nominal_quota: "0"
          borrowing_limit: "0"
          lending_limit: "0"
        - name: iluvatar.ai/vcuda-memory
          nominal_quota: "0"
          borrowing_limit: 0Gi
          lending_limit: 0Gi
        - name: iluvatar.ai/vcuda-core
          nominal_quota: "0"
          borrowing_limit: "0"
          lending_limit: "0"
      metax:
        - name: metax-tech.com/gpu
          nominal_quota: "0"
          borrowing_limit: "0"
          lending_limit: "0"
      mthreads:
        - name: mthreads.com/vgpu
          nominal_quota: "0"
          borrowing_limit: "0"
          lending_limit: "0"
        - name: mthreads.com/sgpu-memory
          nominal_quota: "0"
          borrowing_limit: 0Gi
          lending_limit: 0Gi
        - name: mthreads.com/sgpu-core
          nominal_quota: "0"
          borrowing_limit: "0"
          lending_limit: "0"
      nvidia:
        - name: nvidia.com/gpu
          nominal_quota: "0"
          borrowing_limit: "0"
          lending_limit: "0"
        - name: nvidia.com/gpumem
          nominal_quota: "0"
          borrowing_limit: 0Gi
          lending_limit: 0Gi
        - name: nvidia.com/gpucores
          nominal_quota: "0"
          borrowing_limit: "0"
          lending_limit: "0"
    queue:
      default:
        cpu:
          name: cpu
          nominal_quota: "32"
          borrowing_limit: "0"
          lending_limit: "32"
        memory:
          name: ""
          nominal_quota: ""
        accelerated_computing:
          310P3:
            - name: huawei.com/Ascend310P
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: huawei.com/Ascend310P-memory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
          910B:
            - name: huawei.com/Ascend910A
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: huawei.com/Ascend910A-memory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
          910B4:
            - name: huawei.com/Ascend910B
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: huawei.com/Ascend910B-memory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
          cambricon:
            - name: cambricon.com/vmlu
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: cambricon.com/mlu.smlu.vmemory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
            - name: cambricon.com/mlu.smlu.vcore
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
          hygon:
            - name: hygon.com/dcunum
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: hygon.com/dcumem
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
            - name: hygon.com/dcucores
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
          iluvatar:
            - name: iluvatar.ai/vgpu
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: iluvatar.ai/vcuda-memory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
            - name: iluvatar.ai/vcuda-core
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
          metax:
            - name: metax-tech.com/gpu
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
          mthreads:
            - name: mthreads.com/vgpu
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: mthreads.com/sgpu-memory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
            - name: mthreads.com/sgpu-core
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
          nvidia:
            - name: nvidia.com/gpu
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: nvidia.com/gpumem
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
            - name: nvidia.com/gpucores
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
      task:
        cpu:
          name: cpu
          nominal_quota: "32"
          borrowing_limit: "0"
          lending_limit: "32"
        memory:
          name: memory
          nominal_quota: 128Gi
          borrowing_limit: 0Gi
          lending_limit: 128Gi
        accelerated_computing:
          310P3:
            - name: huawei.com/Ascend310P
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: huawei.com/Ascend310P-memory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
          910B:
            - name: huawei.com/Ascend910A
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: huawei.com/Ascend910A-memory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
          910B4:
            - name: huawei.com/Ascend910B
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: huawei.com/Ascend910B-memory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
          cambricon:
            - name: cambricon.com/vmlu
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: cambricon.com/mlu.smlu.vmemory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
            - name: cambricon.com/mlu.smlu.vcore
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
          hygon:
            - name: hygon.com/dcunum
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: hygon.com/dcumem
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
            - name: hygon.com/dcucores
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
          iluvatar:
            - name: iluvatar.ai/vgpu
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: iluvatar.ai/vcuda-memory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
            - name: iluvatar.ai/vcuda-core
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
          metax:
            - name: metax-tech.com/gpu
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
          mthreads:
            - name: mthreads.com/vgpu
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: mthreads.com/sgpu-memory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
            - name: mthreads.com/sgpu-core
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
          nvidia:
            - name: nvidia.com/gpu
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: nvidia.com/gpumem
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
            - name: nvidia.com/gpucores
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
      infer:
        cpu:
          name: cpu
          nominal_quota: "32"
          borrowing_limit: "0"
          lending_limit: "32"
        memory:
          name: memory
          nominal_quota: 128Gi
          borrowing_limit: 0Gi
          lending_limit: 128Gi
        accelerated_computing:
          310P3:
            - name: huawei.com/Ascend310P
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: huawei.com/Ascend310P-memory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
          910B:
            - name: huawei.com/Ascend910A
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: huawei.com/Ascend910A-memory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
          910B4:
            - name: huawei.com/Ascend910B
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: huawei.com/Ascend910B-memory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
          cambricon:
            - name: cambricon.com/vmlu
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: cambricon.com/mlu.smlu.vmemory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
            - name: cambricon.com/mlu.smlu.vcore
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
          hygon:
            - name: hygon.com/dcunum
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: hygon.com/dcumem
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
            - name: hygon.com/dcucores
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
          iluvatar:
            - name: iluvatar.ai/vgpu
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: iluvatar.ai/vcuda-memory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
            - name: iluvatar.ai/vcuda-core
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
          metax:
            - name: metax-tech.com/gpu
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
          mthreads:
            - name: mthreads.com/vgpu
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: mthreads.com/sgpu-memory
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
            - name: mthreads.com/sgpu-core
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
          nvidia:
            - name: nvidia.com/gpu
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
            - name: nvidia.com/gpumem
              nominal_quota: "0"
              borrowing_limit: 0Gi
              lending_limit: 0Gi
            - name: nvidia.com/gpucores
              nominal_quota: "0"
              borrowing_limit: "0"
              lending_limit: "0"
