package service

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/dao"
)

func NewArticleService(db *gorm.DB) *ArticleService {
	as := &ArticleService{db: db.Session(&gorm.Session{NewDB: true})}
	return as
}

type ArticleService struct {
	db *gorm.DB
}

func (a *ArticleService) CreateArticle(article *dao.Article) (string, error) {
	if article.Id == "" {
		article.Id = uuid.New().String()
	}
	return article.Id, dao.CreateArticle(a.db, article)
}

func (a *ArticleService) ListArticles(projectId string, status string) ([]*dao.Article, error) {
	if status == "" {
		return dao.ListArticles(a.db, projectId)
	} else {
		return dao.ListArticlesByStatus(a.db, projectId, status)
	}
}

func (a *ArticleService) GetArticle(id string) (*dao.Article, error) {
	article, err := dao.GetArticle(a.db, id)
	if err != nil {
		return nil, err
	}
	return article, nil
}
func (a *ArticleService) UpdateArticle(id string, article *dao.Article) error {
	return dao.UpdateArticle(a.db, id, article)
}
