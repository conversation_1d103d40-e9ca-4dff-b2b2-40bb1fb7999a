package project_test

import (
	"testing"

	"gorm.io/gorm"
	conf2 "transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/project"
)

var (
	err error
	db  *gorm.DB
)

func init() {
	db, err = dao.ConnectDB(&conf.DBConfig{
		Type:   "mysql",
		SQLite: nil,
		MySQL: &conf2.MysqlConfig{
			Username:       "root",
			Password:       "236512",
			Host:           "localhost",
			Port:           "3306",
			DBName:         "central_auth_service",
			MaxIdle:        10,
			MaxConn:        50,
			NotPrintSql:    false,
			NotCreateTable: false,
		},
		Debug: true,
	})
	if err != nil {
		panic(err)
	}
}
func TestProjectMemberService_UpdateProjectMembers(t *testing.T) {
	type args struct {
		projectMemberReqs []*models.ProjectMemberReq
		projectID         string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "初始化数据",
			args: args{
				projectMemberReqs: []*models.ProjectMemberReq{
					{
						Name:     "1",
						RoleId:   1,
						UserType: "1",
					},
					{
						Name:     "2",
						RoleId:   1,
						UserType: "1",
					},
					{
						Name:     "3",
						RoleId:   1,
						UserType: "1",
					},
				},
				projectID: "1",
			},
			wantErr: false,
		},
		{
			name: "删除部分,增加部分,修改部分",
			args: args{
				projectMemberReqs: []*models.ProjectMemberReq{
					{
						Name:     "1",
						RoleId:   1,
						UserType: "1",
					},
					{
						Name:     "2",
						RoleId:   2,
						UserType: "2",
					},
					{
						Name:     "4",
						RoleId:   4,
						UserType: "4",
					},
				},
				projectID: "1",
			},
			wantErr: false,
		},
	}
	s := project.NewProjectMemberService(db)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := s.UpdateProjectMembers(tt.args.projectMemberReqs, tt.args.projectID, "cwz"); (err != nil) != tt.wantErr {
				t.Errorf("ProjectMemberService.UpdateProjectMembers() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestProjectMemberService_CreateProjectMembers(t *testing.T) {
	type args struct {
		projectMemberReqs []*models.ProjectMemberReq
		projectID         string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "1",
			args: args{
				projectMemberReqs: []*models.ProjectMemberReq{
					{
						Name:     "11",
						RoleId:   11,
						UserType: "11",
					},
					{
						Name:     "12",
						RoleId:   12,
						UserType: "12",
					},
					{
						Name:     "13",
						RoleId:   13,
						UserType: "13",
					},
				},
				projectID: "1",
			},
			wantErr: false,
		},
	}
	s := project.NewProjectMemberService(db)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := s.CreateProjectMembers(tt.args.projectMemberReqs, tt.args.projectID, "cwz"); (err != nil) != tt.wantErr {
				t.Errorf("ProjectMemberService.CreateProjectMembers() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
