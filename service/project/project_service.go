package project

import (
	"fmt"
	"strconv"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/helper"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdfs"
	"transwarp.io/applied-ai/central-auth-service/service/tenant"

	"gorm.io/gorm"

	"transwarp.io/applied-ai/central-auth-service/dao/project"
	"transwarp.io/applied-ai/central-auth-service/dao/rbac"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/utils"
)

type ProjectService struct {
	db            *gorm.DB
	tenantService tenant.TenantService
}

func NewProjectService(db *gorm.DB) *ProjectService {
	ps := &ProjectService{db: db.Session(&gorm.Session{NewDB: true})}
	ps.tenantService = tenant.NewTenantService(ps.db)
	if err := ps.InitAllProjectsLocation(); err != nil {
		panic(fmt.Sprintf("init all projests' location: %s", err.Error()))
	}
	return ps
}

func (s *ProjectService) CreateProject(proj *models.Project) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		if err := project.CreateProject(s.db, proj); err != nil {
			return err
		}

		// 将创建用户加入到空间成员
		username := proj.CreateUser
		projectMember := new(models.ProjectMember)
		projectMember.ProjectId = proj.ProjectId
		projectMember.Name = username
		projectMember.UserType = models.UserType
		projectMember.CreateUser = username
		projectMember.CreateTime = time.Now()
		projectMember.UpdateTime = time.Now()
		if err := project.CreateProjectMember(s.db, projectMember); err != nil {
			return err
		}

		// 将创建用户的空间角色设置为空间负责人
		userRole := new(models.UserRole)
		userRole.Name = username
		userRole.RoleId = uint64(helper.ProjectOwnerID)
		userRole.BindType = models.UserType
		userRole.ProjectId = proj.ProjectId
		userRole.CreateTime = time.Now()
		userRole.UpdateTime = time.Now()
		if err := rbac.CreateUserRole(s.db, userRole); err != nil {
			return err
		}
		return nil
	})
}

func (s *ProjectService) SaveProject(proj *models.Project) error {
	return project.SaveProject(s.db, proj)
}

func (s *ProjectService) QueryList(params map[string]interface{}) ([]*models.ProjectResp, error) {
	projects, err := project.QueryProjects(s.db, params)
	if err != nil {
		return nil, err
	}
	var rs []*models.ProjectResp
	for _, pro := range projects {
		rsp := &models.ProjectResp{}
		rsp.ProjectId = pro.ProjectId
		rsp.Name = pro.Name
		rsp.TenantUid = pro.TenantUid
		rs = append(rs, rsp)
	}
	return rs, nil
}

func (s *ProjectService) QueryTenantIDbyProjects(params []string) ([]string, error) {
	tenantID, err := project.QueryTenantIDbyProjects(s.db, params)
	if err != nil {
		return nil, err
	}
	return tenantID, nil
}

func (s *ProjectService) GetProjectByTenant(params string) (*models.Project, error) {
	tenantID, err := project.GetProjectByTenant(s.db, params)
	if err != nil {
		return nil, err
	}
	return tenantID, nil
}

func (s *ProjectService) CountProjects() (int, error) {
	return project.CountProjects(s.db)
}

func (s *ProjectService) ListProjects(username string, permissionCode string) ([]*models.ProjectResp, error) {
	// 权限点编码不为空，获取当前用户
	if permissionCode != "" {
		permissions, err := rbac.GetPermissionsByPermissionCode(s.db, permissionCode)
		if err != nil {
			return nil, err
		}
		permissionIds := make([]uint64, 0)
		// 筛选出有编辑权限的权限id
		editOperationPermissionIds := make([]uint64, 0)
		for _, permission := range permissions {
			if permission.Action == "*" {
				editOperationPermissionIds = append(editOperationPermissionIds, permission.ID)
			}
			permissionIds = append(permissionIds, permission.ID)
		}

		// 筛选出有该权限点的角色id
		rolePermissions, err := rbac.GetRolePermissionsByPermissionIds(s.db, permissionIds)
		if err != nil {
			return nil, err
		}
		allOperationRoleIds := make([]uint64, 0)
		editOperationRoleIds := make([]uint64, 0)
		permissionIdRoleIdMap := make(map[uint64]uint64)
		for _, rolePermission := range rolePermissions {
			allOperationRoleIds = append(allOperationRoleIds, rolePermission.RoleId)
			permissionIdRoleIdMap[rolePermission.PermissionId] = rolePermission.RoleId
		}
		for _, editOperationPermissionId := range editOperationPermissionIds {
			editOperationRoleId := permissionIdRoleIdMap[editOperationPermissionId]
			editOperationRoleIds = append(editOperationRoleIds, editOperationRoleId)
		}

		currUserGroups, err := rbac.GetUserGroupsByUsername(s.db, username)
		if err != nil {
			return nil, err
		}
		// 获取当前用户所属的用户组
		currGroupNames := make([]string, 0)
		for _, currUserGroup := range currUserGroups {
			currGroupNames = append(currGroupNames, currUserGroup.GroupName)
		}
		permissionCodeUserRoles, err := rbac.GetUserRolesByRoleIds(s.db, allOperationRoleIds)
		if err != nil {
			return nil, err
		}
		permissionCodeProjectIds := make([]string, 0)
		nameRoleIdProjectIdMap := make(map[string]string)
		for _, permissionCodeUserRole := range permissionCodeUserRoles {
			nameRoleIdProjectIdMap[permissionCodeUserRole.Name+strconv.FormatUint(permissionCodeUserRole.RoleId, 10)+
					permissionCodeUserRole.BindType.String()] = permissionCodeUserRole.ProjectId
			permissionCodeProjectIds = append(permissionCodeProjectIds, permissionCodeUserRole.ProjectId)
		}

		editOperationProjectIds := make([]string, 0)
		editOperationProjectIdMap := make(map[string]bool)
		for _, editOperationRoleId := range editOperationRoleIds {
			nameRoleIdTypeStr := username + strconv.FormatUint(editOperationRoleId, 10) + models.UserType.String()
			val, ok := nameRoleIdProjectIdMap[nameRoleIdTypeStr]
			if ok {
				editOperationProjectIds = append(editOperationProjectIds, val)
				editOperationProjectIdMap[val] = true
			}
			for _, groupName := range currGroupNames {
				groupNameRoleIdTypeStr := groupName + strconv.FormatUint(editOperationRoleId, 10) + models.UserGroupType.String()
				val, ok := nameRoleIdProjectIdMap[groupNameRoleIdTypeStr]
				if ok {
					editOperationProjectIds = append(editOperationProjectIds, val)
					editOperationProjectIdMap[val] = true
				}
			}
		}

		allOperationProjectIds := make([]string, 0)
		for _, allOperationRoleId := range allOperationRoleIds {
			nameRoleIdTypeStr := username + strconv.FormatUint(allOperationRoleId, 10) + models.UserType.String()
			val, ok := nameRoleIdProjectIdMap[nameRoleIdTypeStr]
			if ok {
				allOperationProjectIds = append(allOperationProjectIds, val)
			}
			for _, groupName := range currGroupNames {
				groupNameRoleIdTypeStr := groupName + strconv.FormatUint(allOperationRoleId, 10) + models.UserGroupType.String()
				val, ok := nameRoleIdProjectIdMap[groupNameRoleIdTypeStr]
				if ok {
					allOperationProjectIds = append(allOperationProjectIds, val)
				}
			}
		}

		permissionCodeProjects, err := project.GetProjectsByProjectIds(s.db, allOperationProjectIds)
		if err != nil {
			return nil, err
		}
		projectRespArr, err := convertProjects(permissionCodeProjects, s.db)
		if err != nil {
			return nil, err
		}
		for _, projectResp := range projectRespArr {
			if _, ok := editOperationProjectIdMap[projectResp.ProjectId]; ok {
				projectResp.Disabled = false
			} else {
				projectResp.Disabled = true
			}
		}
		return projectRespArr, nil
	} else {
		userRoles, err := rbac.GetUserRoles(s.db, []string{username}, models.UserType, "")
		if err != nil {
			return nil, err
		}
		userGroups, err := rbac.BatchGetUserGroupsByUserNames(s.db, []string{username})
		if err != nil {
			return nil, err
		}
		groupNames := make([]string, 0)
		for _, userGroup := range userGroups {
			groupNames = append(groupNames, userGroup.GroupName)
		}
		groupRoles, err := rbac.GetUserRoles(s.db, groupNames, models.UserGroupType, "")
		roleIds := make([]uint64, 0)
		for _, userRole := range userRoles {
			roleIds = append(roleIds, userRole.RoleId)
		}
		for _, groupRole := range groupRoles {
			roleIds = append(roleIds, groupRole.RoleId)
		}
		roles, err := rbac.GetRolesByRoleIds(s.db, roleIds)
		if err != nil {
			return nil, err
		}
		userPlatformRole := models.GeneralUser
		for _, role := range roles {
			if role.Name == models.SuperAdministrator.String() {
				userPlatformRole = models.SuperAdministrator
				break
			} else if role.Name == models.Administrator.String() {
				userPlatformRole = models.Administrator
				continue
			} else {
				userPlatformRole = models.GeneralUser
			}
		}

		projects := make([]*models.Project, 0)
		if userPlatformRole == models.SuperAdministrator {
			projects, err = project.ListProjects(s.db, "")
		}

		userCollabProjectIds, err := project.GetProjectIdsByNameAndUserType(s.db, []string{username}, models.UserType)
		if err != nil {
			return nil, err
		}
		groupCollabProjectIds, err := project.GetProjectIdsByNameAndUserType(s.db, groupNames, models.UserGroupType)
		if err != nil {
			return nil, err
		}
		collabProjectIds := append(userCollabProjectIds, groupCollabProjectIds...)
		if userPlatformRole == models.GeneralUser || userPlatformRole == models.Administrator {
			// 普通 管理员, 也能看到 assets
			// collabProjectIds = utils.RemoveValueAndDuplicates(collabProjectIds, helper.ProjectIdAssets)
		} else {
			collabProjectIds = utils.RemoveDupElements(collabProjectIds)
		}
		collabProjects, err := project.GetProjectsByProjectIds(s.db, collabProjectIds)
		if err != nil {
			return nil, err
		}
		if userPlatformRole == models.Administrator {
			projects, err = project.ListProjects(s.db, username)
			projects = append(projects, collabProjects...)
			projects = utils.RemoveDupProjects(projects)
		}
		if userPlatformRole == models.GeneralUser {
			projects = collabProjects
		}
		projectRespArr, err := convertProjects(projects, s.db)
		if err != nil {
			return nil, err
		}
		return projectRespArr, nil
	}
}

func (s *ProjectService) GetProjectRespById(projectId string) (*models.ProjectResp, error) {
	proj, err := project.GetProjectById(s.db, projectId)
	if err != nil {
		return nil, err
	}
	projectResp := convertProject(proj)
	projectMemberStatisticsArr, err := project.StatisticsProjectMember(s.db, []string{projectId})
	// projectId -> memberCount
	projectMemberStatisticsMap := make(map[string]uint64)
	for _, projectMemberStatistics := range projectMemberStatisticsArr {
		projectMemberStatisticsMap[projectMemberStatistics.ProjectId] = projectMemberStatistics.MemberCount
	}
	// 设置空间成员数量
	projectResp.MemberCount = projectMemberStatisticsMap[projectResp.ProjectId]
	return projectResp, nil
}

func (s *ProjectService) GetProjectById(projectId string) (*models.Project, error) {
	return project.GetProjectById(s.db, projectId)
}

func (s *ProjectService) ExistsProjectId(projectId string) (bool, error) {
	return project.ExistsProjectId(s.db, projectId)
}

func (s *ProjectService) GetProjectByName(name string) (*models.Project, error) {
	proj, err := project.GetProjectByName(s.db, name)
	if err != nil {
		return nil, err
	}
	return proj, nil
}

func (s *ProjectService) DeleteProjectById(projectId string) error {
	// 根据空间id删除空间记录
	if err := project.DeleteProjectById(s.db, projectId); err != nil {
		return err
	}
	// 根据空间id删除空间成员信息
	if err := project.DeleteProjectMembersByProjectId(s.db, projectId); err != nil {
		return err
	}
	// 根据空间id删除空间成员绑定的空间角色信息
	if err := rbac.DeleteUserRolesByProjectId(s.db, projectId); err != nil {
		return err
	}
	return nil
}

func convertProjects(projects []*models.Project, db *gorm.DB) ([]*models.ProjectResp, error) {
	projectRespArr := make([]*models.ProjectResp, 0)
	projectIds := make([]string, 0)
	for _, proj := range projects {
		projectRespArr = append(projectRespArr, convertProject(proj))
		projectIds = append(projectIds, proj.ProjectId)
	}
	projectMemberStatisticsArr, err := project.StatisticsProjectMember(db, projectIds)
	if err != nil {
		return nil, err
	}
	// projectId -> memberCount
	projectMemberStatisticsMap := make(map[string]uint64)
	for _, projectMemberStatistics := range projectMemberStatisticsArr {
		projectMemberStatisticsMap[projectMemberStatistics.ProjectId] = projectMemberStatistics.MemberCount
	}
	// 设置空间成员数量
	for _, projectResp := range projectRespArr {
		projectResp.MemberCount = projectMemberStatisticsMap[projectResp.ProjectId]
	}
	return projectRespArr, nil
}

func convertProject(project *models.Project) *models.ProjectResp {
	projectResp := new(models.ProjectResp)
	// projectResp.Id = project.Id
	projectResp.ProjectId = project.ProjectId
	projectResp.Name = project.Name
	projectResp.Description = project.Description
	if project.Labels != "" {
		projectResp.Labels = utils.JsonStrToMap(project.Labels)
	}
	projectResp.Industry = project.Industry
	projectResp.Logo = project.Logo
	projectResp.CreateUser = project.CreateUser
	projectResp.CreateTime = project.CreateTime
	projectResp.TenantUid = project.TenantUid
	projectResp.Examine = project.Examine
	return projectResp
}

// InitAllProjectsLocation 初始化所有对应空间的文件存储根目录（容器内）
func (s *ProjectService) InitAllProjectsLocation() error {
	if conf.IsDevMode() {
		stdlog.Warnf("!!!! skipping deps (location) initiation in development mode")
		return nil
	}
	projects := make([]*models.Project, 0)
	err := s.db.Session(&gorm.Session{NewDB: true}).Find(&projects).Error
	if err != nil {
		return stderr.Wrap(err, "list all projects")
	}
	for _, proj := range projects {
		if proj.TenantUid == "" {
			return stderr.Internal.Error("project %s has no tenant uid", proj.Name)
		}
		_, err = stdfs.NewProjectLocation(proj.TenantUid, proj.ProjectId, true)
		if err != nil {
			return stderr.Wrap(err, "check the location of project %s", proj.Name)
		}
	}
	return nil
}
