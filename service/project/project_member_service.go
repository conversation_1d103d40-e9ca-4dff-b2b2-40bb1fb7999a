package project

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/dao/project"
	"transwarp.io/applied-ai/central-auth-service/dao/rbac"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/utils"
)

type ProjectMemberService struct {
	db *gorm.DB
}

func NewProjectMemberService(db *gorm.DB) *ProjectMemberService {
	pms := &ProjectMemberService{db: db.Session(&gorm.Session{NewDB: true})}
	return pms
}

func (s *ProjectMemberService) ListProjectMembers(projectId string) ([]*models.ProjectMemberResp, error) {
	projectMembers, err := project.ListProjectMembers(s.db, projectId)
	if err != nil {
		return nil, err
	}
	userRoles, err := rbac.GetUserRolesByProjectId(s.db, projectId)
	if err != nil {
		return nil, err
	}
	userRoleIdMap := make(map[string]uint64)
	roleIds := make([]uint64, 0)
	for _, userRole := range userRoles {
		userRoleIdMap[userRole.Name+models.BindType.String(userRole.BindType)+userRole.ProjectId] = userRole.RoleId
		roleIds = append(roleIds, userRole.RoleId)
	}
	roles, err := rbac.GetRolesByRoleIds(s.db, roleIds)
	if err != nil {
		return nil, err
	}
	roleIdNameMap := make(map[uint64]string)
	for _, role := range roles {
		roleIdNameMap[role.Id] = role.Name
	}
	// name + bindType + projectId -> roleName
	userRoleNameMap := make(map[string]string)
	for k, v := range userRoleIdMap {
		userRoleNameMap[k] = roleIdNameMap[v]
	}

	platformUserRoles, err := rbac.GetPlatformUserRoles(s.db)
	if err != nil {
		return nil, err
	}
	nameTypeRoleIdMap := make(map[string]uint64)
	platformRoleIds := make([]uint64, 0)
	for _, platformUserRole := range platformUserRoles {
		nameTypeRoleIdMap[platformUserRole.Name+models.BindType.String(platformUserRole.BindType)] = platformUserRole.RoleId
		platformRoleIds = append(platformRoleIds, platformUserRole.RoleId)
	}
	platformRoles, err := rbac.GetRolesByRoleIds(s.db, platformRoleIds)
	if err != nil {
		return nil, err
	}
	idNameMap := make(map[uint64]string)
	for _, platformRole := range platformRoles {
		idNameMap[platformRole.Id] = platformRole.Name
	}
	// name + bindType -> roleName
	nameTypeRoleNameMap := make(map[string]string)
	for k, v := range nameTypeRoleIdMap {
		nameTypeRoleNameMap[k] = idNameMap[v]
	}
	projectMemberRespArr := make([]*models.ProjectMemberResp, 0)
	for _, projectMember := range projectMembers {
		projectMemberResp := new(models.ProjectMemberResp)
		projectMemberResp.ProjectId = projectMember.ProjectId
		projectMemberResp.Name = projectMember.Name
		projectMemberResp.UserType = projectMember.UserType
		projectMemberResp.CreateUser = projectMember.CreateUser
		projectMemberResp.CreateTime = projectMember.CreateTime
		projectMemberResp.ProjectRoleId = userRoleIdMap[projectMember.Name+
			models.BindType.String(projectMember.UserType)+projectMember.ProjectId]
		projectMemberResp.ProjectRoleName = userRoleNameMap[projectMember.Name+
			models.BindType.String(projectMember.UserType)+projectMember.ProjectId]
		projectMemberResp.PlatformRoleName = nameTypeRoleNameMap[projectMember.Name+
			models.BindType.String(projectMember.UserType)]
		projectMemberRespArr = append(projectMemberRespArr, projectMemberResp)
	}
	return projectMemberRespArr, nil
}

func (s *ProjectMemberService) CreateProjectMembers(projectMemberReqs []*models.ProjectMemberReq, projectID string, username string) error {
	_, err := project.GetProjectById(s.db, projectID)
	if err != nil {
		return err
	}
	var (
		now            = time.Now()
		projectMembers = make([]*models.ProjectMember, 0, len(projectMemberReqs))
		userRoles      = make([]*models.UserRole, 0, len(projectMemberReqs))
	)
	// todo 升级gorm版本，改为批量添加的方式，提高效率 // 已修改
	for _, memReq := range projectMemberReqs {
		projectMembers = append(projectMembers, &models.ProjectMember{
			ProjectId:  projectID,
			Name:       memReq.Name,
			UserType:   memReq.UserType,
			CreateUser: username,
			CreateTime: now,
			UpdateTime: now,
		})
		userRoles = append(userRoles, &models.UserRole{
			UserRoleReq: models.UserRoleReq{
				Name:      memReq.Name,
				RoleId:    memReq.RoleId,
				BindType:  memReq.UserType,
				ProjectId: projectID,
			},
			CreateTime: now,
			UpdateTime: now,
		})
	}
	if len(projectMembers) == 0 || len(userRoles) == 0 {
		return errors.New("新添加的空间成员为空")
	}
	return s.db.Transaction(func(tx *gorm.DB) error {
		err = project.CreateProjectMembers(tx, projectMembers)
		if err != nil {
			return err
		}
		err = rbac.CreateUserRoles(tx, userRoles)
		if err != nil {
			return err
		}
		return nil
	})
}

// UpdateProjectMembers 修改空间成员
// 将删除 memsbers & roles 后重建数据的逻辑, 改为增量式修改: 只删除或添加有改动的数据
func (s *ProjectMemberService) UpdateProjectMembers(projectMemberReqs []*models.ProjectMemberReq, projectID string, username string) error {
	now := time.Now()
	return s.db.Transaction(func(tx *gorm.DB) error {
		_, err := project.GetProjectById(tx, projectID)
		if err != nil {
			return err
		}
		// 获取空间内已有的成员, 转为 map 后与入参做差异比对,
		// 将需要新增与修改的数据分别存储, map 中剩下的是需要删除的
		oldMembers, err := project.ListProjectMembers(tx, projectID)
		if err != nil {
			return err
		}
		oldMemMap := make(map[string]*models.ProjectMember, len(oldMembers))
		for _, m := range oldMembers {
			oldMemMap[m.Name] = m
		}
		oldUserRoles, err := rbac.GetUserRolesByProjectId(tx, projectID)
		if err != nil {
			return err
		}
		oldUserRolesMap := make(map[string]*models.UserRole, len(oldUserRoles))
		for _, u := range oldUserRoles {
			oldUserRolesMap[u.Name] = u
		}

		var (
			createMems      = make([]*models.ProjectMember, 0) // 新增的成员
			updateMems      = make([]*models.ProjectMember, 0) // 更新的成员
			createUserRoles = make([]*models.UserRole, 0)      // 新增的 role
			updateUserRoles = make([]*models.UserRole, 0)      // 更新的 role
		)
		// 获取成员差异
		for _, proMemReq := range projectMemberReqs {
			oldMem, found := oldMemMap[proMemReq.Name]
			if !found { // 未找到则新增
				createMems = append(createMems, &models.ProjectMember{
					ProjectId:  projectID,
					Name:       proMemReq.Name,
					UserType:   proMemReq.UserType,
					CreateUser: username,
					CreateTime: now,
					UpdateTime: now,
				})
			} else { // 找到则判断是否需要更新
				delete(oldMemMap, proMemReq.Name)
				if proMemReq.UserType != oldMem.UserType {
					updateMems = append(updateMems, &models.ProjectMember{
						Id:         oldMem.Id,
						ProjectId:  projectID,
						Name:       proMemReq.Name,
						UserType:   proMemReq.UserType,
						CreateUser: username,
						UpdateTime: now,
					})
				}
			}

			oldUserrole, found := oldUserRolesMap[proMemReq.Name]
			if !found { // 未找到则新增
				createUserRoles = append(createUserRoles, &models.UserRole{
					UserRoleReq: models.UserRoleReq{
						Name:      proMemReq.Name,
						RoleId:    proMemReq.RoleId,
						BindType:  proMemReq.UserType,
						ProjectId: projectID,
					},
					CreateTime: now,
					UpdateTime: now,
				})
			} else { // 找到则判断是否需要更新
				delete(oldUserRolesMap, proMemReq.Name)
				if proMemReq.UserType != oldUserrole.BindType || proMemReq.RoleId != oldUserrole.RoleId {
					updateUserRoles = append(updateUserRoles, &models.UserRole{
						Id: oldUserrole.Id,
						UserRoleReq: models.UserRoleReq{
							Name:      proMemReq.Name,
							RoleId:    proMemReq.RoleId,
							BindType:  proMemReq.UserType,
							ProjectId: projectID,
						},
						UpdateTime: now,
					})
				}
			}
		}
		// 删除
		// 绑定了此空间为默认空间的用户, 不能被删除
		names := make([]string, 0, 10)
		oldMembers = oldMembers[:0]
		for _, m := range oldMemMap {
			user, err := dao.GetUserByName(tx, m.Name)
			if err != nil {
				return fmt.Errorf("获取用户信息: %w", err)
			}
			if user.DefaultProject == projectID {
				names = append(names, m.Name)
			}
			oldMembers = append(oldMembers, m)
		}
		if len(names) > 0 {
			return fmt.Errorf("用户[%s]已绑定当前空间为默认空间，请前往管理中心，修改用户配置后重试", strings.Join(names, ","))
		}
		if len(oldMembers) > 0 {
			err = tx.Delete(&oldMembers).Error
			if err != nil {
				return fmt.Errorf("删除空间成员: %w", err)
			}
		}
		oldUserRoles = oldUserRoles[:0]
		for _, u := range oldUserRolesMap {
			oldUserRoles = append(oldUserRoles, u)
		}
		if len(oldUserRoles) > 0 {
			err = tx.Delete(&oldUserRoles).Error
			if err != nil {
				return fmt.Errorf("删除空间角色: %w", err)
			}
		}
		// 新增
		if len(createMems) > 0 {
			err = project.CreateProjectMembers(tx, createMems)
			if err != nil {
				return fmt.Errorf("创建空间成员: %w", err)
			}
		}
		if len(createUserRoles) > 0 {
			err = rbac.CreateUserRoles(tx, createUserRoles)
			if err != nil {
				return fmt.Errorf("创建空间角色: %w", err)
			}
		}
		// 修改
		for _, u := range updateMems {
			err = tx.Model(u).Updates(u).Error
			if err != nil {
				return fmt.Errorf("更新空间成员: %w", err)
			}
		}
		for _, u := range updateUserRoles {
			err = tx.Model(u).Updates(u).Error
			if err != nil {
				return fmt.Errorf("更新空间角色: %w", err)
			}
		}
		return nil
	})
}

func (s *ProjectMemberService) DeleteProjectMemberById(projectMemberReq *models.ProjectMemberReq, projectID string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		_, err := project.GetProjectById(tx, projectID)
		if err != nil {
			return err
		}
		user, err := dao.GetUserByName(tx, projectMemberReq.Name)
		if err != nil {
			return err
		}
		if user.DefaultProject == projectID {
			return fmt.Errorf("该用户已绑定当前空间为默认空间，请前往管理中心，修改用户配置后重试")
		}
		userRole := new(models.UserRole)
		userRole.Name = projectMemberReq.Name
		userRole.ProjectId = projectID
		userRole.BindType = projectMemberReq.UserType
		if err := rbac.DeleteUserRole(tx, userRole); err != nil {
			return err
		}
		return project.DeleteProjectMemberById(tx, projectMemberReq, projectID)
	})
}

func (s *ProjectMemberService) BatchDeleteProjectMembers(projectMemberReqs []*models.ProjectMemberReq, projectID string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		_, err := project.GetProjectById(tx, projectID)
		if err != nil {
			return err
		}
		names := []string{}
		for _, projectMemberReq := range projectMemberReqs {
			user, err := dao.GetUserByName(tx, projectMemberReq.Name)
			if err != nil {
				return err
			}
			if user.DefaultProject == projectID {
				names = append(names, projectMemberReq.Name)
				continue
			}
			if len(names) > 0 {
				continue
			}
			if err := project.DeleteProjectMemberById(tx, projectMemberReq, projectID); err != nil {
				return err
			}
			userRole := new(models.UserRole)
			userRole.Name = projectMemberReq.Name
			userRole.BindType = projectMemberReq.UserType
			userRole.ProjectId = projectID

			if err := rbac.DeleteUserRole(tx, userRole); err != nil {
				return err
			}
		}
		if len(names) > 0 {
			return fmt.Errorf("用户[%s]已绑定当前空间为默认空间，请前往管理中心，修改用户配置后重试", strings.Join(names, ","))
		}
		return nil
	})
}

func (s *ProjectMemberService) ListProjectUsers(projectID string, permissionCode string,
	permissionAction string,
) ([]string, error) {
	var userRoles []*models.UserRole
	var err error
	if permissionCode == "" {
		userRoles, err = rbac.GetUserRolesByProjectId(s.db, projectID)
	} else {
		userRoles, err = s.ListUserRolesByProjectPermission(projectID, permissionCode, permissionAction)
	}
	if err != nil {
		return nil, stderr.Wrap(err, "ProjectMemberService: ListProjectUsers method error, project id [%s]", projectID)
	}
	superAdminUserRoles, err := s.ListSuperAdminUserRoles()
	if err != nil {
		return nil, stderr.Wrap(err, "ProjectMemberService: ListProjectUsers method error")
	}
	userRoles = append(userRoles, superAdminUserRoles...)
	var usernames []string
	var groupNames []string
	for _, userRole := range userRoles {
		switch userRole.BindType {
		case models.UserGroupType:
			groupNames = append(groupNames, userRole.Name)
		default:
			usernames = append(usernames, userRole.Name)
		}
	}
	userGroups, err := rbac.BatchGetUserGroupsByGroupNames(s.db, groupNames)
	if err != nil {
		return nil, stderr.Wrap(err, "BatchGetUserGroupsByGroupNames failed by group names [%v]", groupNames)
	}
	for _, group := range userGroups {
		usernames = append(usernames, group.Username)
	}
	// 当前空间的创建者默认拥有空间内所有权限
	curProject, err := project.GetProjectById(s.db, projectID)
	if err != nil {
		return nil, stderr.Wrap(err, "ProjectMemberService: GetProjectById failed, project id [%s]", projectID)
	}
	usernames = append(usernames, curProject.CreateUser)
	return utils.RemoveDupElements(usernames), nil
}

func (s *ProjectMemberService) ListProjectGroups(projectID string, permCode string, permAction string) ([]*models.GroupResp, error) {
	var (
		err       error
		userRoles []*models.UserRole
	)
	if permCode == "" {
		userRoles, err = rbac.GetUserRolesByProjectId(s.db, projectID)
	} else {
		userRoles, err = s.ListUserRolesByProjectPermission(projectID, permCode, permAction)
	}
	if err != nil {
		return nil, stderr.Wrap(err, "ProjectMemberService: ListProjectUsers method error, project id [%s]", projectID)
	}
	superAdminUserRoles, err := s.ListSuperAdminUserRoles()
	if err != nil {
		return nil, stderr.Wrap(err, "ProjectMemberService: ListProjectUsers method error")
	}
	userRoles = append(userRoles, superAdminUserRoles...)
	groupNames := lo.FilterMap(userRoles, func(e *models.UserRole, _ int) (string, bool) {
		return e.Name, e.BindType == models.UserGroupType
	})
	groups, err := rbac.ListGroups(s.db.Where("name in ?", groupNames))
	if err != nil {
		return nil, stderr.Wrap(err, "ListGroups failed by group names [%v]", groupNames)
	}
	plGroupRoles, err := rbac.GetPlatformUserRoles(s.db.Where("name in ?", groupNames).Where("bind_type = ?", models.UserGroupType))
	if err != nil {
		return nil, stderr.Wrap(err, "ListPlatformUserRoles failed")
	}
	plGroupRoleMap := lo.SliceToMap(plGroupRoles, func(e *models.UserRole) (string, uint64) {
		return e.Name, e.RoleId
	})
	roles, err := rbac.GetRolesByRoleIds(s.db, lo.Values(plGroupRoleMap))
	if err != nil {
		return nil, stderr.Wrap(err, "GetRolesByRoleIds failed")
	}
	roleMap := lo.SliceToMap(roles, func(e *models.Role) (uint64, string) {
		return e.Id, e.Name
	})
	userGroups, err := rbac.BatchGetUserGroupsByGroupNames(s.db, groupNames)
	if err != nil {
		return nil, stderr.Wrap(err, "BatchGetUserGroupsByGroupNames failed by group names [%v]", groupNames)
	}
	userGroupMap := make(map[string][]string)
	for _, ug := range userGroups {
		userGroupMap[ug.GroupName] = append(userGroupMap[ug.GroupName], ug.Username)
	}

	return lo.Map(groups, func(e *models.Group, _ int) *models.GroupResp {
		return &models.GroupResp{
			Gid:              e.Id,
			Name:             e.Name,
			PlatformRoleName: roleMap[plGroupRoleMap[e.Name]],
			PlatformRoleId:   plGroupRoleMap[e.Name],
			Description:      e.Description,
			UserNames:        userGroupMap[e.Name],
			CreateUser:       e.CreateUser,
			CreateTime:       e.CreateTime,
		}
	}), nil
}

func (s *ProjectMemberService) ListUserRolesByProjectPermission(projectID string, permissionCode string,
	permissionAction string,
) ([]*models.UserRole, error) {
	if permissionAction == "" {
		permissionAction = string(models.All)
	}
	permission, err := rbac.GetPermissionsByPermissionCodeAction(s.db, permissionCode, permissionAction)
	if err != nil {
		return nil, stderr.Wrap(err, "ProjectMemberService:ListProjectUsers, get permission failed"+
			" by code [%s] and action [%s]", permissionCode, permissionAction)
	}
	rolePermissions, err := rbac.GetRolePermissionsByPermissionIds(s.db, []uint64{permission.ID})
	var roleIds []uint64
	for _, rolePermission := range rolePermissions {
		roleIds = append(roleIds, rolePermission.RoleId)
	}
	userRoles, err := rbac.GetUserRolesByProjectAndRoleIds(s.db, projectID, roleIds)
	if err != nil {
		return nil, stderr.Wrap(err, "GetUserRolesByProjectAndRoleIds failed by project id [%s] and"+
			" role id [%v]", projectID, roleIds)
	}
	return userRoles, nil
}

func (s *ProjectMemberService) ListSuperAdminUserRoles() ([]*models.UserRole, error) {
	superAdminRole, err := rbac.GetRoleByNameAndType(s.db, string(models.SuperAdministrator), models.PlatformRoleType)
	if err != nil {
		return nil, stderr.Wrap(err, "ProjectMemberService: ListSuperAdminUsers failed")
	}
	userRoles, err := rbac.GetUserRolesByRoleIds(s.db, []uint64{superAdminRole.Id})
	if err != nil {
		return nil, stderr.Wrap(err, "ProjectMemberService: GetUserRolesByRoleIds failed by role id [%d] ", superAdminRole.Id)
	}
	return userRoles, nil
}

func (s ProjectMemberService) CreateProjectMember(req models.ProjectMemberReq, projectId string, currentUser string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		projectMember := new(models.ProjectMember)
		projectMember.ProjectId = projectId
		projectMember.Name = req.Name
		projectMember.UserType = req.UserType
		projectMember.CreateUser = currentUser
		projectMember.CreateTime = time.Now()
		projectMember.UpdateTime = time.Now()
		if err := project.CreateProjectMember(tx, projectMember); err != nil {
			return err
		}

		userRole := new(models.UserRole)
		userRole.Name = req.Name
		userRole.RoleId = req.RoleId
		userRole.BindType = req.UserType
		userRole.ProjectId = projectId
		userRole.UpdateTime = time.Now()
		userRole.CreateTime = time.Now()
		if err := rbac.CreateUserRole(tx, userRole); err != nil {
			return err
		}
		return nil
	})
}
