package project

import (
	"fmt"

	"gorm.io/gorm"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/dao/project"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type ProjectCategoryService struct {
	db *gorm.DB
}

func NewProjectCategoryService(db *gorm.DB) *ProjectCategoryService {
	return &ProjectCategoryService{db: db}
}

func (t *ProjectCategoryService) ListProjectCategories() ([]*models.ProjectCategory, error) {
	return project.ListProjectCategories(t.db)
}

func (t *ProjectCategoryService) GetProjectCategory(id uint64) (*models.ProjectCategory, error) {
	return project.GetProjectCategory(t.db, id)
}

func (t *ProjectCategoryService) GetProjectCategoryByName(name string) (*models.ProjectCategory, error) {
	return project.GetProjectCategoryByName(t.db, name)
}

func (t *ProjectCategoryService) RebuildProjectCategory(categories []*models.ProjectCategory) ([]*models.ProjectCategory, error) {
	set := make(map[string]*models.ProjectCategory, len(categories))
	j := 0
	for _, v := range categories {
		_, ok := set[v.Name]
		if ok {
			continue
		}
		set[v.Name] = v
		categories[j] = v
		j++
	}
	categories = categories[:j]
	err := project.TruncateProjectCategory(t.db)
	if err != nil {
		return nil, err
	}
	for _, v := range categories {
		project.CreateProjectCategory(t.db, v)
	}
	return t.ListProjectCategories()
}

func (t *ProjectCategoryService) CreateProjectCategory(category *models.ProjectCategory) (*models.ProjectCategory, error) {
	c, err := t.GetProjectCategoryByName(category.Name)
	if err != nil {
		return nil, err
	}
	if c != nil && c.Name != "" {
		return nil, stderr.Error(fmt.Sprintf("category %s exsits", category.Name))
	}
	err = project.CreateProjectCategory(t.db, category)
	if err != nil {
		return nil, err
	}
	return t.GetProjectCategory(category.Id)
}

func (t *ProjectCategoryService) DeleteProjectCategory(id uint64) error {
	return project.DeleteProjectCategory(t.db, id)
}

func (t *ProjectCategoryService) UpdateProjectCategory(category *models.ProjectCategory, id uint64) error {
	return project.UpdateProjectCategory(t.db, category, id)
}
