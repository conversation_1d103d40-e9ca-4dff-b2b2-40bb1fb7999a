package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
)

var (
	TotalApplicationCount = NewGaugeVec(prometheus.GaugeOpts{
		Name: "llmops_application_total_count",
		Help: "Total count of application",
	}, []string{"type"})

	TotalApplicationPluginCount = NewGaugeVec(prometheus.GaugeOpts{
		Name: "llmops_application_plugin_total_count",
		Help: "Total count of application plugin",
	}, []string{"type"})

	TotalKnowledgebaseCount = NewGaugeVec(prometheus.GaugeOpts{
		Name: "llmops_knowledge_base_total_count",
		Help: "Total count of knowledge base",
	}, []string{"type"})

	TotalKnowledgebaseChunkCount = NewGaugeVec(prometheus.GaugeOpts{
		Name: "llmops_knowledge_base_chunk_total_count",
		Help: "Total count of knowledge base chunk",
	}, []string{"type"})
)
