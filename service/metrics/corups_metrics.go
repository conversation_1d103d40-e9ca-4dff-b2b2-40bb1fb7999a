package metrics

import (
	"context"
	"fmt"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/prometheus/common/model"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/central-auth-service/clients"
)

var (
	TotalFileAssetsCount = NewGaugeVec(prometheus.GaugeOpts{
		Name: "llmops_file_assets_total_count",
		Help: "Total count of files",
	}, []string{"type"})
	TotalDirAssetsCount = NewGaugeVec(prometheus.GaugeOpts{
		Name: "llmops_dir_assets_total_count",
		Help: "Total count of dir",
	}, []string{"type"})

	TotalDatatsetCount = NewGaugeVec(prometheus.GaugeOpts{
		Name: "llmops_dataset_total_count",
		Help: "Total count of dataset",
	}, []string{"type"})

	TotalDatasetStorageSize = NewGaugeVec(prometheus.GaugeOpts{
		Name: "llmops_dataset_storage_total_size_bytes",
		Help: "Total size of datasets in storage in bytes",
	}, []string{"type"})
)

type GaugeVecHelper struct {
	*prometheus.GaugeVec
	Name string
}

func NewGaugeVec(opts prometheus.GaugeOpts, labelNames []string) *GaugeVecHelper {
	return &GaugeVecHelper{
		GaugeVec: promauto.NewGaugeVec(opts, labelNames),
		Name:     opts.Name,
	}
}

func getMetricsName(metrics *GaugeVecHelper) string {
	return metrics.Name
}

var cache = stdsrv.NewStdCache[int64](30*time.Minute, 15*time.Minute)

func getMetricsIntValue(ctx context.Context, enableCache bool, metrics *GaugeVecHelper, endTime time.Time) (int64, error) {
	key := getCacheKey(metrics, endTime)
	if value, ok := cache.Get(key); ok && enableCache {
		return value, nil
	}
	
	ret := int64(0)
	query := fmt.Sprintf("last_over_time({__name__=\"%s\"}[24h] @ %d)", getMetricsName(metrics), endTime.Unix())
	result, warnings, err := clients.PromQueryClient.Query(ctx, query, time.Now())
	if err != nil {
		return 0, err
	}
	if len(warnings) > 0 {
		stdlog.Warnf("Prometheus query warnings: %v", warnings)
	}
	if vector, ok := result.(model.Vector); ok && len(vector) > 0 {
		ret = int64(vector[0].Value)
	}

	if enableCache {
		cache.Set(key, ret)
	}

	return ret, nil
}

func getCacheKey(metrics *GaugeVecHelper, endTime time.Time) string {
	return fmt.Sprintf("%s-%s", getMetricsName(metrics), getDayInfo(endTime))
}

func getDayInfo(time time.Time) string {
	return time.Format("2006-01-02")
}
