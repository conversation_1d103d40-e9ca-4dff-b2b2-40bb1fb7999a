package client

import (
	"fmt"
	"testing"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

func TestCorupsClientDataset(t *testing.T) {
	stats, err := GetDatasetStats()
	if err != nil {
		stdlog.<PERSON><PERSON>rln(err.<PERSON><PERSON>r())
	}
	fmt.Println(stats)
}

func TestCorupsClientFileAssets(t *testing.T) {
	stats, err := GetFileAssetsStats()
	if err != nil {
		stdlog.Errorln(err.<PERSON>rror())
	}
	fmt.Println(stats)
}
