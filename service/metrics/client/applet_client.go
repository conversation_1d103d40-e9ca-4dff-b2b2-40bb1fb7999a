package client

import (
	"encoding/json"

	"google.golang.org/protobuf/encoding/protojson"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/central-auth-service/conf"
)

var AppletClient *HTTPClient

func init() {
	AppletClient = NewHTTPClient(
		// WithBaseURL("http://**************:32747"),
		WithBaseURL(conf.C.Client.Prefix),
		WithDefaultHeader("Authorization", "Bearer "+conf.C.Auth.Token),
	)
}

type ApplicationStats struct {
	AppNums    int64 `json:"app_nums" description:"应用数量"`
	ExperiNums int64 `json:"experi_nums" description:"应用体验数量"`
	PluginNums int64 `json:"plugin_nums" description:"应用插件数量"`
}

func GetApplicationStats() (*ApplicationStats, error) {
	path := "/applet/api/v1/portal-info/app/stats"
	resp, err := AppletClient.Get(path, NewRequestOptions())
	if err != nil {
		return nil, err
	}
	stats := &ApplicationStats{}
	err = json.Unmarshal(resp.Body, stats)
	if err != nil {
		return nil, err
	}
	return stats, nil
}

func GetKnowledgeBaseStats() (*pb.CollectKnowledgeBaseStatsRsp, error) {
	path := "/applet/api/v1/knowlhub/stats"
	resp, err := AppletClient.Get(path, NewRequestOptions())
	if err != nil {
		return nil, err
	}
	stats := &pb.CollectKnowledgeBaseStatsRsp{}
	err = protojson.Unmarshal(resp.Body, stats)
	if err != nil {
		return nil, err
	}
	return stats, nil
}
