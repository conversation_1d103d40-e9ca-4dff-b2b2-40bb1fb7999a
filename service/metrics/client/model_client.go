package client

import (
	"encoding/json"
	"transwarp.io/aip/llmops-common/pb"

	"transwarp.io/applied-ai/central-auth-service/conf"
)

var ModelClient *HTTPClient

func init() {
	ModelClient = NewHTTPClient(
		// WithBaseURL("http://**************:32744"),
		WithBaseURL(conf.C.Client.Prefix),
		WithDefaultHeader("Authorization", "Bearer "+conf.C.Auth.Token),
	)
}

type ModelStats struct {
	ModelsCount   int64 `json:"models_count,omitempty"`
	ReleasesCount int64 `json:"releases_count,omitempty"`
}

type StatsInfoResp struct {
	TimeSeconds       int64                           `json:"time_seconds" description:"排序标志"`
	TimeDayInfo       string                          `json:"time_day_info" description:"日期"`
	ModelInfo         ModelStats                      `json:"model_info"`
	AppInfo           ApplicationStats                `json:"app_info"`
	KnowledgeBaseInfo pb.CollectKnowledgeBaseStatsRsp `json:"knowledge_base_info"`
	DatasetInfo       DatasetStats                    `json:"dataset_info"`
	FileInfo          FileAssetsStats                 `json:"file_info"`
}

func GetModelStats() (*ModelStats, error) {
	path := "/mw/api/v1/mwh/models/stats"
	resp, err := ModelClient.Get(path, NewRequestOptions())
	if err != nil {
		return nil, err
	}
	stats := &ModelStats{}
	err = json.Unmarshal(resp.Body, stats)
	if err != nil {
		return nil, err
	}
	return stats, nil
}
