package client

import (
	"encoding/json"

	"transwarp.io/applied-ai/central-auth-service/conf"
)

var CorupsClient *HTTPClient

func init() {
	CorupsClient = NewHTTPClient(
		// WithBaseURL("http://172.17.120.207:32747"),
		WithBaseURL(conf.C.Client.Prefix),
		WithDefaultHeader("Authorization", "Bearer "+conf.C.Auth.Token),
	)
}

type DatasetStats struct {
	Count int64 `json:"count"`
	Size  int64 `json:"size"`
}

type FileAssetsStats struct {
	FileCount int64 `json:"file_count"`
	DirCount  int64 `json:"dir_count"`
}

func GetDatasetStats() (*DatasetStats, error) {
	path := "/cv/api/samplemgr/datasets/stats"
	resp, err := CorupsClient.Get(path, NewRequestOptions())
	if err != nil {
		return nil, err
	}
	stats := &DatasetStats{}
	err = json.Unmarshal(resp.Body, stats)
	if err != nil {
		return nil, err
	}
	return stats, nil
}

func GetFileAssetsStats() (*FileAssetsStats, error) {
	path := "/cv/api/assets/stats"
	resp, err := CorupsClient.Get(path, NewRequestOptions())
	if err != nil {
		return nil, err
	}
	stats := &FileAssetsStats{}
	err = json.Unmarshal(resp.Body, stats)
	if err != nil {
		return nil, err
	}
	return stats, nil
}
