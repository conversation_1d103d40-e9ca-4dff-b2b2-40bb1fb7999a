package metrics

import (
	"context"
	"os"
	"sort"
	"sync"
	"time"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/service/metrics/client"
)

const (
	UpdateInterval = 1 * time.Minute
)

type StatsType string
type StatsSubType string

const (
	Corups        StatsType    = "corups"
	Data          StatsSubType = "data"
	Application   StatsSubType = "application"
	KnowledgeBase StatsSubType = "knowledge_base"
	Model         StatsType    = "model"
)

func init() {
	if os.Getenv("DEV_SKIP_INIT_DEPS") == "true" {
		stdlog.Warnf("!!!! skipping deps (corups stats) initiation in development mode")
		return
	}

	NewMetricsStatsService().Start()
}

type MetricsStatsService struct {
	stopChan chan struct{}
}

func NewMetricsStatsService() *MetricsStatsService {
	return &MetricsStatsService{
		stopChan: make(chan struct{}),
	}
}

func (s *MetricsStatsService) Start() {
	go s.runMetricsUpdater()
}

func (s *MetricsStatsService) Stop() {
	close(s.stopChan)
}

func (s *MetricsStatsService) runMetricsUpdater() {
	ticker := time.NewTicker(UpdateInterval)
	defer ticker.Stop()

	s.updateCurrentStats()

	for {
		select {
		case <-ticker.C:
			s.updateCurrentStats()
		case <-s.stopChan:
			return
		}
	}
}

func (s *MetricsStatsService) updateCurrentStats() {
	var wg sync.WaitGroup
	wg.Add(5) // 5个更新任务

	go func() {
		defer wg.Done()
		s.updateFileAssetsCurrentStats()
	}()

	go func() {
		defer wg.Done()
		s.updateDatasetCurrentStats()
	}()

	go func() {
		defer wg.Done()
		s.updateModelCurrentStats()
	}()

	go func() {
		defer wg.Done()
		s.updateApplicationCurrentStats()
	}()

	go func() {
		defer wg.Done()
		s.updateKnowledgeBaseCurrentStats()
	}()

	wg.Wait()
}

func (s *MetricsStatsService) updateFileAssetsCurrentStats() {
	stats, err := client.GetFileAssetsStats()
	if err != nil {
		stdlog.Errorf("Failed to get file assets current stats: %+v", err)
		return
	}

	TotalFileAssetsCount.WithLabelValues(string(Data)).Set(float64(stats.FileCount))
	TotalDirAssetsCount.WithLabelValues(string(Data)).Set(float64(stats.DirCount))
}
func (s *MetricsStatsService) updateDatasetCurrentStats() {
	stats, err := client.GetDatasetStats()
	if err != nil {
		stdlog.Errorf("Failed to get corups current stats: %+v", err)
		return
	}

	TotalDatatsetCount.WithLabelValues(string(Corups)).Set(float64(stats.Count))
	TotalDatasetStorageSize.WithLabelValues(string(Corups)).Set(float64(stats.Size))
}

func (s *MetricsStatsService) updateModelCurrentStats() {
	stats, err := client.GetModelStats()
	if err != nil {
		stdlog.Errorf("Failed to get model current stats: %+v", err)
		return
	}

	TotalModelCount.WithLabelValues(string(Model)).Set(float64(stats.ModelsCount))
	TotalModelVersionCount.WithLabelValues(string(Model)).Set(float64(stats.ReleasesCount))
}

func (s *MetricsStatsService) updateApplicationCurrentStats() {
	stats, err := client.GetApplicationStats()
	if err != nil {
		stdlog.Errorf("Failed to get application current stats: %+v", err)
		return
	}

	TotalApplicationCount.WithLabelValues(string(Application)).Set(float64(stats.AppNums))
	TotalApplicationPluginCount.WithLabelValues(string(Application)).Set(float64(stats.PluginNums))
}

func (s *MetricsStatsService) updateKnowledgeBaseCurrentStats() {
	stats, err := client.GetKnowledgeBaseStats()
	if err != nil {
		stdlog.Errorf("Failed to get knowledge base current stats: %+v", err)
		return
	}
	TotalKnowledgebaseCount.WithLabelValues(string(KnowledgeBase)).Set(float64(stats.NumKnowledgeBases))
	TotalKnowledgebaseChunkCount.WithLabelValues(string(KnowledgeBase)).Set(float64(stats.NumChunks))
}

func (s *MetricsStatsService) ListAllMetrics(ctx context.Context, startTimeSecond, endTimeSecond int) ([]*client.StatsInfoResp, error) {
	resp := make([]*client.StatsInfoResp, 0)

	endTime := time.Unix(int64(endTimeSecond), 0)
	startTime := time.Unix(int64(startTimeSecond), 0)

	for !endTime.Before(startTime) {
		enableCache := endTime.Unix() != int64(endTimeSecond) // today
		modelCount, err1 := getMetricsIntValue(ctx, enableCache, TotalModelCount, endTime)
		modelVersionCount, err2 := getMetricsIntValue(ctx, enableCache, TotalModelVersionCount, endTime)
		appCount, err3 := getMetricsIntValue(ctx, enableCache, TotalApplicationCount, endTime)
		appPluginCount, err4 := getMetricsIntValue(ctx, enableCache, TotalApplicationPluginCount, endTime)
		kbCount, err5 := getMetricsIntValue(ctx, enableCache, TotalKnowledgebaseCount, endTime)
		kbChunkCount, err6 := getMetricsIntValue(ctx, enableCache, TotalKnowledgebaseChunkCount, endTime)
		datasetCount, err7 := getMetricsIntValue(ctx, enableCache, TotalDatatsetCount, endTime)
		dataSetStorageSize, err8 := getMetricsIntValue(ctx, enableCache, TotalDatasetStorageSize, endTime)
		fileAssetCount, err9 := getMetricsIntValue(ctx, enableCache, TotalFileAssetsCount, endTime)
		dirAssetsCount, err10 := getMetricsIntValue(ctx, enableCache, TotalDirAssetsCount, endTime)

		err := stderr.JoinErrors(err1, err2, err3, err4, err5, err6, err7, err8, err9, err10)
		if err != nil {
			return nil, stderr.Wrap(err, "failed to query from prometheus")
		}

		statsInfoResp := &client.StatsInfoResp{
			TimeSeconds: endTime.Unix(),
			TimeDayInfo: getDayInfo(endTime),
			ModelInfo: client.ModelStats{
				ModelsCount:   modelCount,
				ReleasesCount: modelVersionCount,
			},
			AppInfo: client.ApplicationStats{
				AppNums:    appCount,
				PluginNums: appPluginCount,
			},
			KnowledgeBaseInfo: pb.CollectKnowledgeBaseStatsRsp{
				NumKnowledgeBases: kbCount,
				NumChunks:         kbChunkCount,
			},
			DatasetInfo: client.DatasetStats{
				Count: datasetCount,
				Size:  dataSetStorageSize,
			},
			FileInfo: client.FileAssetsStats{
				FileCount: fileAssetCount,
				DirCount:  dirAssetsCount,
			},
		}

		endTime = endTime.Add(-24 * time.Hour)
		resp = append(resp, statsInfoResp)
	}

	sort.Slice(resp, func(i, j int) bool {
		return resp[i].TimeSeconds < resp[j].TimeSeconds
	})

	return resp, nil
}
