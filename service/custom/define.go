package custom

import (
	"gorm.io/gorm"

	"transwarp.io/applied-ai/central-auth-service/clients"
	"transwarp.io/applied-ai/central-auth-service/dao"
)

var (
	cs dao.CustomConfigService
	ls LogoService
	ns NoticeService
	is IconService
)

func Init(db *gorm.DB) {
	cs = dao.MustCheckAndGetCustomConfigService()

	ls = &logoService{
		db: db.Session(&gorm.Session{NewDB: true}),
	}
	// 检查logo是否有记录，没有就新增一条记录
	if err := ls.Check(); err != nil {
		panic(err)
	}

	// 检查icon是否有记录，没有就新增一条记录
	is = &iconService{
		db: db.Session(&gorm.Session{NewDB: true}),
	}
	if err := is.Check(); err != nil {
		panic(err)
	}

	ns = &noticeService{
		db: db.Session(&gorm.Session{NewDB: true}),
		rc: clients.RedisCli,
	}
}

func GetConfigService() dao.CustomConfigService {
	return cs
}

func GetLogoService() LogoService {
	return ls
}

func GetNoticeService() NoticeService {
	return ns
}

func GetIconService() IconService {
	return is
}
