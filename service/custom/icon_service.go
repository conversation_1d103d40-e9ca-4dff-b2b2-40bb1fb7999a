package custom

import (
	"gorm.io/gorm"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type IconService interface {
	GetIcon() (*models.CustomIcon, error)
	UpdateIcon(icon *models.CustomIcon) error
	Check() error
}

type iconService struct {
	db *gorm.DB
}

func (i *iconService) GetIcon() (*models.CustomIcon, error) {
	var icon models.CustomIcon
	result := i.db.Where(models.CustomIcon{System: models.True}).First(&icon)
	if result.Error != nil {
		return nil, stderr.Internal.Cause(result.Error, "Get custom icon error.")
	}
	return &icon, nil
}

func (i *iconService) UpdateIcon(icon *models.CustomIcon) error {
	result := i.db.Model(&models.CustomIcon{}).Where(&models.CustomIcon{System: models.True}).Select("default", "path", "updated_by").Updates(models.CustomIcon{
		System:    models.True,
		Path:      icon.Path,
		Default:   icon.Default,
		UpdatedBy: icon.UpdatedBy,
	})
	if result.Error != nil {
		return stderr.Internal.Cause(result.Error, "Update custom icon path error.")
	}
	return nil
}

func (i *iconService) Check() error {
	var icons []*models.CustomIcon
	result := i.db.Where(&models.CustomIcon{System: models.True}).Find(&icons)
	if result.Error != nil {
		return stderr.Internal.Cause(result.Error, "Get custom icons error.")
	}
	if len(icons) == 0 {
		// 新增一条数据
		result = i.db.Create(&models.CustomIcon{
			Default:   models.True,
			System:    models.True,
			Path:      "",
			UpdatedBy: "system",
		})
		if result.Error != nil {
			return stderr.Internal.Cause(result.Error, "add custom icon error.")
		}
	}
	return nil
}
