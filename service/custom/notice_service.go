package custom

import (
	"gorm.io/gorm"
	"time"
	stdcli "transwarp.io/applied-ai/aiot/vision-std/clients"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type NoticeService interface {
	CreateNotice(notice *models.CustomNotice) error
	GetNotice(id uint) (*models.CustomNotice, error)
	UpdateNotice(notice *models.CustomNotice) error
	ListNotices() ([]*models.CustomNotice, error)
	DeleteNotice(ids ...uint) error
	OnlineNotice(id uint) error
	OfflineNotice(id uint) error
}

const (
	OnlineLockKey = "cas:custom:notice:online"
	MaxOnline     = 5
)

type noticeService struct {
	db *gorm.DB
	rc stdcli.RedisClient
}

func (n *noticeService) CreateNotice(notice *models.CustomNotice) error {
	result := n.db.Create(notice)
	if result.Error != nil {
		return stderr.Internal.Cause(result.Error, "Create notice error.")
	}
	return nil
}

func (n *noticeService) GetNotice(id uint) (*models.CustomNotice, error) {
	var notice models.CustomNotice
	result := n.db.First(&notice, id)
	if result.Error != nil {
		return nil, stderr.Internal.Cause(result.Error, "Get notice [%d] error.", id)
	}
	return &notice, nil
}

func (n *noticeService) UpdateNotice(notice *models.CustomNotice) error {
	result := n.db.Model(notice).Updates(notice)
	if result.Error != nil {
		return stderr.Internal.Cause(result.Error, "Update notice [%s] error.", notice.ID)
	}
	return nil
}

func (n *noticeService) ListNotices() ([]*models.CustomNotice, error) {
	var notices []*models.CustomNotice
	result := n.db.Find(&notices)
	if result.Error != nil {
		return nil, stderr.Internal.Cause(result.Error, "Get notice list error.")
	}
	return notices, nil
}

func (n *noticeService) DeleteNotice(ids ...uint) error {
	result := n.db.Delete(&models.CustomNotice{}, ids)
	if result.Error != nil {
		return stderr.Internal.Cause(result.Error, "Delete notice %+v error.", ids)
	}
	return nil
}

func (n *noticeService) OnlineNotice(id uint) error {
	// 此操作为了确保在多人同时更新时也保证只有五条上线记录，需要加锁
	lock, err := n.rc.Lock(OnlineLockKey, time.Second*10)
	if err != nil {
		return stderr.Internal.Cause(err, "Get redis online lock error.")
	}
	defer n.rc.Release(lock)
	notices, err := n.ListNotices()
	if err != nil {
		return err
	}
	count := 0
	for _, notice := range notices {
		if notice.Online == models.True {
			count++
		}
		if count >= MaxOnline {
			break
		}
	}
	if count >= MaxOnline {
		return stderr.Internal.Errorf("online notice number more than 5")
	}
	result := n.db.Model(&models.CustomNotice{}).Where("id = ?", id).Update("online", models.True)
	if result.Error != nil {
		return stderr.Internal.Cause(result.Error, "Update notice [%s] online error.", id)
	}
	return nil
}

func (n *noticeService) OfflineNotice(id uint) error {
	result := n.db.Model(&models.CustomNotice{}).Where("id = ?", id).Update("online", models.False)
	if result.Error != nil {
		return stderr.Internal.Cause(result.Error, "Update notice [%s] online error.", id)
	}
	return nil
}
