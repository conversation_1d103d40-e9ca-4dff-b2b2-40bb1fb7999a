package custom

import (
	"gorm.io/gorm"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type LogoService interface {
	GetLogo() (*models.CustomLogo, error)
	UpdateLogo(logo *models.CustomLogo) error
	Check() error
}

type logoService struct {
	db *gorm.DB
}

func (l *logoService) GetLogo() (*models.CustomLogo, error) {
	var logo models.CustomLogo
	result := l.db.Where(models.CustomLogo{System: models.True}).First(&logo)
	if result.Error != nil {
		return nil, stderr.Internal.Cause(result.Error, "Get custom logo error.")
	}
	return &logo, nil
}

func (l *logoService) UpdateLogo(logo *models.CustomLogo) error {
	result := l.db.Model(&models.CustomLogo{}).Where(&models.CustomLogo{System: models.True}).Select("default", "path", "updated_by").Updates(models.CustomLogo{
		System:    models.True,
		Path:      logo.Path,
		Default:   logo.Default,
		UpdatedBy: logo.UpdatedBy,
	})
	if result.Error != nil {
		return stderr.Internal.Cause(result.Error, "Update custom logo path error.")
	}
	return nil
}

func (l *logoService) Check() error {
	var logos []*models.CustomLogo
	result := l.db.Where(&models.CustomLogo{System: models.True}).Find(&logos)
	if result.Error != nil {
		return stderr.Internal.Cause(result.Error, "Get custom logos error.")
	}
	if len(logos) == 0 {
		// 新增一条数据
		result = l.db.Create(&models.CustomLogo{
			Default:   models.True,
			System:    models.True,
			Path:      "",
			UpdatedBy: "system",
		})
		if result.Error != nil {
			return stderr.Internal.Cause(result.Error, "add custom logo error.")
		}
	}
	return nil
}
