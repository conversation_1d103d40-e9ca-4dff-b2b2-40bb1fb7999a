package service

import (
	"sync"

	"gorm.io/gorm"
)

func NewAuthService(db *gorm.DB, allowMultiClientLoginAtSameTime bool) *AuthService {
	us := &AuthService{
		db:                              db.Session(&gorm.Session{NewDB: true}),
		lockoutInfo:                     make(map[string]*LockInfo),
		lockMutex:                       sync.Mutex{},
		allowMultiClientLoginAtSameTime: allowMultiClientLoginAtSameTime,
	}
	return us
}

type AuthService struct {
	db *gorm.DB
	// In-memory store for tracking lockout information
	lockoutInfo map[string]*LockInfo
	lockMutex   sync.Mutex
	// allow account login from multi client at the same time
	allowMultiClientLoginAtSameTime bool
}
