package service

import (
	"gorm.io/gorm"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/central-auth-service/dao"
	session "transwarp.io/applied-ai/central-auth-service/utils/auth"
)

func NewAccessTokenService(db *gorm.DB, ss session.SessionStore) *AccessTokenService {
	ts := &AccessTokenService{db: db, ss: ss}
	return ts
}

type AccessTokenService struct {
	db *gorm.DB
	ss session.SessionStore
}

func (s *AccessTokenService) CreateAccessToken(creator string, token *dao.AccessToken) error {
	token.ID = toolkit.NewUUID()
	token.Creator = creator
	token.CreateTime = time.Now().Format("2006-01-02T15:04:05.999Z")
	token.Status = dao.AccessTokenTypeUsing

	return dao.CreateAccessToken(s.db, token)
}
func (s *AccessTokenService) DeleteAccessTokenByID(tokenID string) error {
	return dao.DeleteAccessTokenByID(s.db, tokenID)
}
func (s *AccessTokenService) DeleteAccessTokenByUsername(username string) error {
	return dao.DeleteAccessTokenByUsername(s.db, username)
}
func (s *AccessTokenService) UpdateAccessToken(token *dao.AccessToken) error {
	return dao.UpdateAccessToken(s.db, token)
}
func (s *AccessTokenService) DisableAccessTokenByUsername(username string) error {
	ats, err := dao.GetAccessTokenByUsername(s.db, username)
	if err != nil {
		return err
	}
	for _, at := range ats {
		if err := s.DisableAccessTokenByID(at.ID); err != nil {
			return err
		}
	}
	return nil
}
func (s *AccessTokenService) DisableAccessTokenByID(tokenID string) error {
	at, err := dao.GetAccessTokenByID(s.db, tokenID)
	if err != nil {
		return err
	}

	jt, err := auth.ParseToken(at.Token)
	if err != nil {
		return err
	}
	if err := s.ss.Delete(toolkit.MD5Bytes([]byte(jt.InternalToken()))); err != nil {
		return err
	}
	at.Status = dao.AccessTokenTypeInvalid
	return dao.UpdateAccessToken(s.db, at)
}
func (s *AccessTokenService) GetAccessTokenByID(tokenID string) (*dao.AccessToken, error) {
	token, err := dao.GetAccessTokenByID(s.db, tokenID)
	if err != nil {
		return nil, err
	}
	return token, nil
}
func (s *AccessTokenService) GetAccessTokenByUsername(username string) ([]*dao.AccessToken, error) {
	tokens, err := dao.GetAccessTokenByUsername(s.db, username)
	if err != nil {
		return nil, err
	}
	return tokens, nil
}
func (s *AccessTokenService) GetAccessTokenByToken(token string) (*dao.AccessToken, error) {
	at, err := dao.GetAccessTokenByToken(s.db, token)
	if err != nil {
		return nil, err
	}
	return at, nil
}
