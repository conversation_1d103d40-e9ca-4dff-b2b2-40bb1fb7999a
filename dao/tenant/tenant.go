package tenant

import (
	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func ListTenants(tx *gorm.DB) ([]*models.Tenant, error) {
	tenants := make([]*models.Tenant, 0)
	if err := tx.Session(&gorm.Session{NewDB: true}).Find(&tenants).Error; err != nil {
		return nil, err
	}
	return tenants, nil
}

func GetTenant(tx *gorm.DB, uid string) (*models.Tenant, error) {
	tenant := new(models.Tenant)
	if err := tx.Session(&gorm.Session{NewDB: true}).Where("uid = ?", uid).Find(&tenant).Error; err != nil {
		return nil, err
	}
	return tenant, nil
}

func CreateTenant(tx *gorm.DB, tenant *models.Tenant) error {
	return tx.Session(&gorm.Session{NewDB: true}).Create(tenant).Error
}

func UpdateTenant(tx *gorm.DB, tenant *models.Tenant, uid string) error {
	return tx.Session(&gorm.Session{NewDB: true}).Model(models.Tenant{}).Where("uid = ?", uid).Updates(tenant).Error
}

func DeleteTenant(tx *gorm.DB, uid string) error {
	if err := tx.Session(&gorm.Session{NewDB: true}).Where("uid = ?", uid).Delete(&models.Tenant{}).Error; err != nil {
		return err
	}
	return nil
}
