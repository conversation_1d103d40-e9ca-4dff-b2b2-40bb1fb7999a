package dao

import (
	"encoding/json"
	"gorm.io/gorm"
	"strings"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
)

type PortalInfo struct {
	ProjectId          string `json:"project_id" gorm:"primary_key;column:project_id;type:varchar(50)"`
	SelectedAppInfos   string `json:"selected_app_infos" gorm:"column:selected_app_infos;type:MEDIUMTEXT"`
	SelectedModelInfos string `json:"selected_model_infos" gorm:"column:selected_model_infos;type:MEDIUMTEXT"`
}

func (p *PortalInfo) Cvt2DO() (*PortalInfoDO, error) {
	portalInfoDO := new(PortalInfoDO)
	portalInfoDO.ProjectId = p.ProjectId

	portalInfoDO.SelectedAppInfos = make(map[string][]*SimpleAppInfo)
	portalInfoDO.AllSelectedAppInfos = make([]*SimpleAppInfo, 0)

	portalInfoDO.SelectedModelInfos = make(map[string][]*pb.ModelService)
	portalInfoDO.AllSelectedModelInfos = make([]*pb.ModelService, 0)

	if strings.TrimSpace(p.SelectedAppInfos) != "" {
		if err := json.Unmarshal([]byte(p.SelectedAppInfos), &portalInfoDO.SelectedAppInfos); err != nil {
			return nil, err
		}
	}

	if strings.TrimSpace(p.SelectedModelInfos) != "" {
		if err := json.Unmarshal([]byte(p.SelectedModelInfos), &portalInfoDO.SelectedModelInfos); err != nil {
			return nil, err
		}
	}
	return portalInfoDO, nil
}

type PortalInfoDO struct {
	ProjectId             string                        `json:"project_id"`
	SelectedAppInfos      map[string][]*SimpleAppInfo   `json:"selected_app_infos"`
	SelectedModelInfos    map[string][]*pb.ModelService `json:"selected_model_infos"`
	AllSelectedAppInfos   []*SimpleAppInfo              `json:"all_selected_app_infos"`
	AllSelectedModelInfos []*pb.ModelService            `json:"all_selected_model_infos"`
}

func (p *PortalInfoDO) Cvt2PO() (*PortalInfo, error) {
	portalInfoPO := new(PortalInfo)
	portalInfoPO.ProjectId = p.ProjectId
	if p.SelectedAppInfos != nil {
		portalInfoPO.SelectedAppInfos = stdsrv.AnyToString(p.SelectedAppInfos)
	}
	if p.SelectedModelInfos != nil {
		portalInfoPO.SelectedModelInfos = stdsrv.AnyToString(p.SelectedModelInfos)
	}
	return portalInfoPO, nil
}

func GetPortalInfo(tx *gorm.DB, projectId string) (*PortalInfoDO, error) {
	portalInfo := new(PortalInfo)
	if err := tx.Where("project_id = ?", projectId).First(portalInfo).Error; err != nil {
		portalInfo.ProjectId = projectId
	}
	return portalInfo.Cvt2DO()
}

func UpsertPortalInfo(tx *gorm.DB, DO *PortalInfoDO) error {
	PO, err := DO.Cvt2PO()
	if err != nil {
		return stderr.Wrap(err, "failed to do cvt2PO")
	}
	projectId := PO.ProjectId

	if projectId == "" {
		return stderr.Error("the project id is empty str")
	}

	err = tx.Where("project_id = ?", projectId).First(new(PortalInfo)).Error
	if err != nil {
		return tx.Create(PO).Error
	} else {
		PO.ProjectId = ""
		return tx.Where("project_id = ?", projectId).Updates(PO).Error
	}
}

// fixme 共同结构,可以定义在common中
type SimpleAppInfo struct {
	Id             string `json:"id"`
	ProjectId      string `json:"project_id"`
	EnableAccess   bool   `json:"enable_access" description:"当前用户是否可以访问"`
	ExperimentInfo struct {
		ImageUrl     string         `json:"image_url"`
		Introduction string         `json:"introduction"`
		Name         string         `json:"name"`
		Examples     []string       `json:"examples"`
		Members      stdsrv.Members `json:"members"  description:"可见成员列表"`
		SrcUrl       string         `json:"src_url"  description:"当为外部应用时, 该字段存放外部应用的完整跳转链接"`
	} `json:"experiment_info"`
	ServiceInfo struct {
		Id         string `json:"id"`
		HealthInfo struct {
			Healthy bool `json:"healthy"`
		} `json:"health_info"`
	} `json:"service_info"`
	CreatedType string `json:"created_type"`
}
