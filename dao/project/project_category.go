package project

import (
	"encoding/json"
	"os"
	"path/filepath"

	"gorm.io/gorm"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func InitProjectCategories(tx *gorm.DB) error {
	dir, err := filepath.Abs("./")
	categoriesJson, err := os.ReadFile(dir + "/etc/project_categories.json")
	if err != nil {
		stdlog.WithError(err).Errorf("failed to read project_categories.json")
		return err
	}
	categories := make([]*models.ProjectCategory, 0)
	if err := json.Unmarshal(categoriesJson, &categories); err != nil {
		stdlog.WithError(err).Errorf("failed to parse project_categories.json")
		return err
	}
	for _, category := range categories {
		if err := CreateOrUpdateProjectCategory(tx, category); err != nil {
			stdlog.WithError(err).<PERSON>rrorf("failed to create project category into db")
			return err
		}
	}
	return nil
}

func ListProjectCategories(tx *gorm.DB) ([]*models.ProjectCategory, error) {
	categories := make([]*models.ProjectCategory, 0)
	if err := tx.Session(&gorm.Session{NewDB: true}).Find(&categories).Error; err != nil {
		return nil, err
	}
	return categories, nil
}

func GetProjectCategory(tx *gorm.DB, id uint64) (*models.ProjectCategory, error) {
	category := new(models.ProjectCategory)
	if err := tx.Session(&gorm.Session{NewDB: true}).Where("id = ?", id).Find(&category).Error; err != nil {
		return nil, err
	}
	return category, nil
}

func GetProjectCategoryByName(tx *gorm.DB, name string) (*models.ProjectCategory, error) {
	category := new(models.ProjectCategory)
	if err := tx.Session(&gorm.Session{NewDB: true}).Where("name = ?", name).Find(&category).Error; err != nil {
		return nil, err
	}
	return category, nil
}

func CreateProjectCategory(tx *gorm.DB, category *models.ProjectCategory) error {
	return tx.Session(&gorm.Session{NewDB: true}).Model(models.ProjectCategory{}).Create(category).Error
}

func CreateOrUpdateProjectCategory(tx *gorm.DB, category *models.ProjectCategory) error {
	var count int64
	if err := tx.Session(&gorm.Session{NewDB: true}).Model(models.ProjectCategory{}).Where("name = ?", category.Name).Count(&count).Error; err != nil {
		return err
	}
	if count == 1 {
		return UpdateProjectCategory(tx, category, category.Id)
	}
	return tx.Session(&gorm.Session{NewDB: true}).Create(category).Error
}

func UpdateProjectCategory(tx *gorm.DB, category *models.ProjectCategory, id uint64) error {
	return tx.Session(&gorm.Session{NewDB: true}).Model(models.ProjectCategory{}).Where("id = ?", id).Updates(category).Error
}

func DeleteProjectCategory(tx *gorm.DB, id uint64) error {
	if err := tx.Session(&gorm.Session{NewDB: true}).Where("id = ?", id).Delete(&models.ProjectCategory{}).Error; err != nil {
		return err
	}
	return nil
}

func TruncateProjectCategory(tx *gorm.DB) error {
	if err := tx.Session(&gorm.Session{NewDB: true}).Where("1 = 1").Delete(&models.ProjectCategory{}).Error; err != nil {
		return err
	}
	return nil
}
