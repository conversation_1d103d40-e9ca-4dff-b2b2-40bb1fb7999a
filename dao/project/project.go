package project

import (
	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func CreateProject(tx *gorm.DB, project *models.Project) error {
	return tx.Create(project).Error
}

func CreateOrUpdateProject(tx *gorm.DB, project *models.Project) error {
	return tx.Where(models.Project{ProjectId: project.ProjectId}).Assign(models.Project{Name: project.Name,
		Description: project.Description, Labels: project.Labels, UpdateTime: project.UpdateTime}).FirstOrCreate(&project).Error
}

func SaveProject(tx *gorm.DB, project *models.Project) error {
	if err := tx.Save(project).Error; err != nil {
		return err
	}
	return nil
}

func QueryProjects(tx *gorm.DB, params map[string]interface{}) ([]*models.Project, error) {
	projects := make([]*models.Project, 0)
	if err := tx.Session(&gorm.Session{NewDB: true}).Where(params).Find(&projects).Error; err != nil {
		return nil, err
	}
	return projects, nil
}

func DeleteProjectById(tx *gorm.DB, projectId string) error {
	if err := tx.Where("project_id = ?", projectId).Delete(&models.Project{}).Error; err != nil {
		return err
	}
	return nil
}

func CountProjects(tx *gorm.DB) (int, error) {
	var count int64
	if err := tx.Model(&models.Project{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return int(count), nil
}

func ListProjects(tx *gorm.DB, username string) ([]*models.Project, error) {
	projects := make([]*models.Project, 0)
	if username == "" {
		if err := tx.Find(&projects).Error; err != nil {
			return nil, err
		}
	} else {
		if err := tx.Where("create_user = ?", username).Find(&projects).Error; err != nil {
			return nil, err
		}
	}
	return projects, nil
}

func GetProjectsByProjectIds(tx *gorm.DB, projectIds []string) ([]*models.Project, error) {
	projects := make([]*models.Project, 0)
	if err := tx.Where("project_id in (?)", projectIds).Find(&projects).Error; err != nil {
		return nil, err
	}
	return projects, nil
}

func GetProjectById(tx *gorm.DB, projectId string) (*models.Project, error) {
	proj := new(models.Project)
	if err := tx.Where("project_id = ?", projectId).First(proj).Error; err != nil {
		return nil, err
	}
	return proj, nil
}

func ExistsProjectId(tx *gorm.DB, projectId string) (bool, error) {
	cnt := int64(0)
	if err := tx.Model(&models.Project{}).Where("project_id = ?", projectId).Count(&cnt).Error; err != nil {
		return false, err
	}
	return cnt > 0, nil
}

func GetProjectByName(tx *gorm.DB, name string) (*models.Project, error) {
	proj := new(models.Project)
	if err := tx.Where("name = ?", name).Find(proj).Error; err != nil {
		return nil, err
	}
	return proj, nil
}
func QueryTenantIDbyProjects(tx *gorm.DB, projects []string) ([]string, error) {
	res := make([]string, 0)
	if err := tx.Model(&models.Project{}).Select("tenant_uid").
		Where("project_id in (?)", projects).Find(&res).Error; err != nil {
		return nil, err
	}
	return res, nil
}

func GetProjectByTenant(tx *gorm.DB, tenantUid string) (*models.Project, error) {
	proj := new(models.Project)
	if err := tx.Model(&models.Project{}).Where("tenant_uid = ?", tenantUid).Find(proj).Error; err != nil {
		return nil, err
	}
	return proj, nil
}
