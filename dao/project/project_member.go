package project

import (
	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func ListProjectMembers(tx *gorm.DB, projectID string) ([]*models.ProjectMember, error) {
	projectMembers := make([]*models.ProjectMember, 0)
	if err := tx.Where("project_id = ?", projectID).Find(&projectMembers).Error; err != nil {
		return nil, err
	}
	return projectMembers, nil
}

func GetProjectIdsByNameAndUserType(tx *gorm.DB, names []string, userType models.BindType) ([]string, error) {
	projectMembers := make([]*models.ProjectMember, 0)
	if err := tx.Where("name in (?) and user_type = ?", names, userType).Find(&projectMembers).Error; err != nil {
		return nil, err
	}
	projectIds := make([]string, 0)
	for _, projectMember := range projectMembers {
		projectIds = append(projectIds, projectMember.ProjectId)
	}
	return projectIds, nil
}

func CreateProjectMember(tx *gorm.DB, projectMember *models.ProjectMember) error {
	return tx.Create(projectMember).Error
}

func CreateOrUpdateProjectMember(tx *gorm.DB, projectMember *models.ProjectMember) error {
	return tx.Where(models.ProjectMember{ProjectId: projectMember.ProjectId, Name: projectMember.Name,
		UserType: projectMember.UserType}).Assign(models.ProjectMember{UpdateTime: projectMember.UpdateTime}).
		FirstOrCreate(&projectMember).Error
}

func CreateProjectMembers(tx *gorm.DB, projectMembers []*models.ProjectMember) error {
	return tx.Create(projectMembers).Error
}

func UpdateProjectMembers(tx *gorm.DB, projectMembers []*models.ProjectMember) error {
	return tx.Updates(projectMembers).Error
}

func DeleteProjectMemberById(tx *gorm.DB, projectMemberReq *models.ProjectMemberReq, projectID string) error {
	if err := tx.Where("name = ? and project_id = ? and user_type = ?", projectMemberReq.Name,
		projectID, projectMemberReq.UserType).Delete(&models.ProjectMember{}).Error; err != nil {
		return err
	}
	return nil
}

func DeleteProjectMembersByProjectId(tx *gorm.DB, projectID string) error {
	if err := tx.Where("project_id = ?", projectID).Delete(&models.ProjectMember{}).Error; err != nil {
		return err
	}
	return nil
}

func BatchDeleteProjectMembers(tx *gorm.DB, projectMemberReqs []*models.ProjectMemberReq, projectID string) error {
	projectMembers := make([]*models.ProjectMember, len(projectMemberReqs))

	for _, projectMemberReq := range projectMemberReqs {
		projectMember := new(models.ProjectMember)
		projectMember.Name = projectMemberReq.Name
		projectMember.UserType = projectMemberReq.UserType
		projectMember.ProjectId = projectID
		projectMembers = append(projectMembers, projectMember)
	}

	if err := tx.Where("project_id = ?", projectID).Delete(&projectMembers).Error; err != nil {
		return err
	}
	return nil
}

func DeleteProjectMembersByNameAndUserType(tx *gorm.DB, name string, userType string) error {
	projectMembers := make([]*models.ProjectMember, 0)
	if err := tx.Where("name = ? and user_type = ?", name, userType).
		Delete(&projectMembers).Error; err != nil {
		return err
	}
	return nil
}

func BatchDeleteProjectMembersByNamesAndUserType(tx *gorm.DB, names []string, userType string) error {
	projectMembers := make([]*models.ProjectMember, 0)
	if err := tx.Where("name in (?) and user_type = ?", names, userType).
		Delete(&projectMembers).Error; err != nil {
		return err
	}
	return nil
}

func StatisticsProjectMember(tx *gorm.DB, projectIDs []string) ([]*models.ProjectMemberStatistics, error) {
	projectMemberStatistics := make([]*models.ProjectMemberStatistics, 0)
	if err := tx.Model(&models.ProjectMember{}).Select("project_id, count(*) as member_count").
		Where("project_id IN (?)", projectIDs).Group("project_id").Find(&projectMemberStatistics).Error; err != nil {
		return nil, err
	}
	return projectMemberStatistics, nil
}
