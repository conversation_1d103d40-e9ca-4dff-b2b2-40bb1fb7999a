package dao

import (
	"context"

	"gorm.io/gorm"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/clients"
	models "transwarp.io/applied-ai/central-auth-service/models/alerting"
	"transwarp.io/applied-ai/grafana-openapi-client-go/client/provisioning"
	gfmodels "transwarp.io/applied-ai/grafana-openapi-client-go/models"
)

func CreateRule(ctx context.Context, db *gorm.DB, rule *models.AlertRule) error {
	tx := db.Begin()
	if tx.Error != nil {
		return stderr.Wrap(tx.Error, "begin transaction")
	}
	defer Recover(tx)
	if err := tx.Create(rule).Error; err != nil {
		tx.Rollback()
		return stderr.Wrap(err, "create rule")
	}
	provisioned, err := rule.Provisioned()
	if err != nil {
		tx.Rollback()
		return stderr.Wrap(err, "provision rule")
	}
	ret, err := clients.GrafanaCli.Provisioning.RoutePostAlertRule(&provisioning.RoutePostAlertRuleParams{
		Body:    provisioned,
		Context: ctx,
	})
	if err != nil {
		tx.Rollback()
		return stderr.Wrap(err, "GrafanaCli post alert rule")
	}
	stdlog.Infof("GrafanaCli post alert rule: %v", ret.Payload)

	if err := alignGroupInterval(rule, provisioned); err != nil {
		tx.Rollback()
		return stderr.Wrap(err, "align group interval")
	}

	if err := tx.Commit().Error; err != nil {
		stdlog.WithError(err).Errorf("Failed to commit transaction")
		return stderr.Wrap(err, "commit transaction")
	}

	return nil
}

func ListRulesBySvcId(db *gorm.DB, svcId string) ([]*models.AlertRule, error) {
	rules := make([]*models.AlertRule, 0)
	if err := db.Where("serviceId = ?", svcId).Find(&rules).Error; err != nil {
		return nil, err
	}
	return rules, nil
}

func GetRule(db *gorm.DB, ruleId int64) (*models.AlertRule, error) {
	rule := new(models.AlertRule)
	if err := db.First(rule, ruleId).Error; err != nil {
		return nil, err
	}
	return rule, nil
}

func UpdateRule(ctx context.Context, db *gorm.DB, rule *models.AlertRule) error {
	tx := db.Begin()
	if tx.Error != nil {
		return stderr.Wrap(tx.Error, "begin transaction")
	}
	defer Recover(tx)
	if err := tx.Save(rule).Error; err != nil {
		tx.Rollback()
		return stderr.Wrap(err, "update rule")
	}
	provisioned, err := rule.Provisioned()
	if err != nil {
		tx.Rollback()
		return stderr.Wrap(err, "provision rule")
	}
	ret, err := clients.GrafanaCli.Provisioning.RoutePutAlertRule(&provisioning.RoutePutAlertRuleParams{
		UID:     provisioned.UID,
		Body:    provisioned,
		Context: ctx,
	})
	if err != nil {
		tx.Rollback()
		return stderr.Wrap(err, "GrafanaCli put alert rule")
	}
	stdlog.Infof("GrafanaCli put alert rule: %v", ret.Payload)

	if err := alignGroupInterval(rule, provisioned); err != nil {
		tx.Rollback()
		return stderr.Wrap(err, "align group interval")
	}

	if err := tx.Commit().Error; err != nil {
		stdlog.WithError(err).Errorf("Failed to commit transaction")
		return stderr.Wrap(err, "commit transaction")
	}
	return nil
}

func DeleteRule(ctx context.Context, db *gorm.DB, ruleId int64) error {
	tx := db.Begin()
	if tx.Error != nil {
		return stderr.Wrap(tx.Error, "begin transaction")
	}
	defer Recover(tx)
	if err := tx.Delete(&models.AlertRule{}, ruleId).Error; err != nil {
		tx.Rollback()
		return stderr.Wrap(err, "delete rule")
	}

	if _, err := clients.GrafanaCli.Provisioning.RouteDeleteAlertRule(&provisioning.RouteDeleteAlertRuleParams{
		UID:     models.RuleUID(ruleId),
		Context: ctx,
	}); err != nil {
		tx.Rollback()
		return stderr.Wrap(err, "GrafanaCli delete alert rule")
	}

	if err := tx.Commit().Error; err != nil {
		stdlog.WithError(err).Errorf("Failed to commit transaction")
		return stderr.Wrap(err, "commit transaction")
	}
	return nil
}

func CountRules(db *gorm.DB, where []any) (cnt int64, err error) {
	ret := db.Model(&models.AlertRule{}).Where(where[0], where[1:]...).Count(&cnt)
	if ret.Error != nil {
		return 0, ret.Error
	}
	return cnt, nil
}

// alignGroupInterval updates the interval of rule group(which will be implictly created by rule group name with 1m interval)
func alignGroupInterval(rule *models.AlertRule, provisioned *gfmodels.ProvisionedAlertRule) error {
	intSec, err := rule.IntervalSeconds()
	if err != nil {
		return stderr.Wrap(err, "parse rule interval")
	}
	putGroupParams := &provisioning.RoutePutAlertRuleGroupParams{
		FolderUID: clients.GrafanaFolder,
		Group:     *provisioned.RuleGroup,
		Body: &gfmodels.AlertRuleGroup{
			FolderUID: clients.GrafanaFolder,
			Interval:  intSec,
			Title:     *provisioned.RuleGroup,
		},
	}
	if _, err := clients.GrafanaCli.Provisioning.RoutePutAlertRuleGroup(putGroupParams); err != nil {
		return stderr.Wrap(err, "GrafanaCli put alert rule group")
	}
	return nil
}
