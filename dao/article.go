package dao

import (
	"gorm.io/gorm"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

const (
	ArticleStatusPublishing = "Publishing"
	ArticleStatusPublished  = "Published"
)

type Article struct {
	Id           string    `json:"id" gorm:"primary_key;type:varchar(50)"`
	ProjectId    string    `json:"project_id" gorm:"column:project_id;type:varchar(50)"`
	Name         string    `json:"name" gorm:"column:name;type:varchar(50)"`
	Abstract     string    `json:"abstract" gorm:"column:abstract;type:varchar(200)"`
	Content      string    `json:"content"  gorm:"column:content;type:MEDIUMTEXT;comment:文章内容"`
	Source       string    `json:"source" gorm:"column:source;type:varchar(50)"`
	CreateUser   string    `json:"create_user" gorm:"column:create_user;type:varchar(50)"`
	Status       string    `json:"status" gorm:"column:status;type:VARCHAR(50);not null; default:'Publishing';comment:文章状态,Publishing:发布中-待审核,Published:已发布-审核完毕""`
	CreateTime   time.Time `json:"create_time" gorm:"column:create_time;type:TIMESTAMP;not null;default:CURRENT_TIMESTAMP;index:index_created_at;comment:创建时间""`
	UpdateTime   time.Time `json:"update_time" gorm:"column:update_time;type:TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;default:CURRENT_TIMESTAMP;not null;index:index_updated_at;comment:更新时间""`
	CreateTimeMs int64     `json:"create_time_ms" gorm:"-"`
	UpdateTimeMs int64     `json:"update_time_ms" gorm:"-"`
}

func ListArticles(tx *gorm.DB, projectId string) ([]*Article, error) {
	articles := make([]*Article, 0)
	if err := tx.Where("project_id = ?", projectId).Find(&articles).Error; err != nil {
		return nil, err
	}
	for _, a := range articles {
		setTimeMs(a)
	}
	return articles, nil
}

func ListArticlesByStatus(tx *gorm.DB, projectId string, status string) ([]*Article, error) {
	articles := make([]*Article, 0)
	if status != ArticleStatusPublishing && status != ArticleStatusPublished {
		return nil, stderr.Error("the status is error")
	}
	if err := tx.Where("project_id = ?", projectId).
		Where("status = ?", status).Find(&articles).Error; err != nil {
		return nil, err
	}
	for _, a := range articles {
		setTimeMs(a)
	}
	return articles, nil
}

func CreateArticle(tx *gorm.DB, article *Article) error {
	return tx.Create(article).Error
}

func GetArticle(tx *gorm.DB, id string) (*Article, error) {
	article := new(Article)
	if err := tx.Where("id = ?", id).Find(article).Error; err != nil {
		return nil, err
	}
	setTimeMs(article)
	return article, nil
}

func UpdateArticle(tx *gorm.DB, id string, article *Article) error {
	article.Id = ""
	return tx.Model(Article{}).Where("id = ?", id).Updates(article).Error
}

func setTimeMs(article *Article) {
	article.CreateTimeMs = article.CreateTime.UnixMilli()
	article.UpdateTimeMs = article.UpdateTime.UnixMilli()
}
