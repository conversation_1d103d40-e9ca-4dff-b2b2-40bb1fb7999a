package rbac

import (
	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func CreateRole(tx *gorm.DB, role *models.Role) error {
	return tx.Create(role).Error
}

func CreateOrUpdateRole(tx *gorm.DB, role *models.Role) error {
	return tx.Where(models.Role{Name: role.Name, Type: role.Type}).Assign(models.Role{Description: role.Description}).
		FirstOrCreate(&role).Error
}

func UpdateRole(tx *gorm.DB, role *models.Role) error {
	return tx.Model(models.Role{}).Where("id = ?", role.Id).Updates(role).Error
}

func DeleteRoleById(tx *gorm.DB, roleId uint64) error {
	if err := tx.Where("id = ?", roleId).Delete(&models.Role{}).Error; err != nil {
		return err
	}
	return nil
}

func ListRoles(tx *gorm.DB, roleType string) ([]*models.Role, error) {
	roles := make([]*models.Role, 0)
	if roleType != "" {
		if err := tx.Where("type = ?", roleType).Find(&roles).Error; err != nil {
			return nil, err
		}
	} else {
		if err := tx.Find(&roles).Error; err != nil {
			return nil, err
		}
	}
	return roles, nil
}

func GetRoleByRoleId(tx *gorm.DB, roleId uint64) (*models.Role, error) {
	role := new(models.Role)
	if err := tx.Where("id = ?", roleId).Find(&role).Error; err != nil {
		return nil, err
	}
	return role, nil
}

func GetRolesByRoleIds(tx *gorm.DB, roleIds []uint64) ([]*models.Role, error) {
	roles := make([]*models.Role, 0)
	if err := tx.Where("id in (?)", roleIds).Find(&roles).Error; err != nil {
		return nil, err
	}
	return roles, nil
}

func BatchDeleteRolesByIds(tx *gorm.DB, ids []uint64) error {
	roles := make([]*models.Role, 0)
	if err := tx.Where("id in (?)", ids).Delete(&roles).Error; err != nil {
		return err
	}
	return nil
}

func GetRoleByNameAndType(tx *gorm.DB, name string, roleType models.RoleType) (*models.Role, error) {
	role := new(models.Role)
	if err := tx.Where("name = ? and type = ?", name, roleType).Find(&role).Error; err != nil {
		return nil, err
	}
	return role, nil
}

func GetRolesByNamesAndType(tx *gorm.DB, names []string, roleType models.RoleType) ([]*models.Role, error) {
	roles := make([]*models.Role, 0)
	if err := tx.Where("name in (?) and type = ?", names, roleType).Find(&roles).Error; err != nil {
		return nil, err
	}
	return roles, nil
}
