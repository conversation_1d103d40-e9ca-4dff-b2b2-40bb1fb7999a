package rbac

import (
	"gorm.io/gorm"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func InitGroups(tx *gorm.DB) error {

	// 初始化时，创建all_users用户组
	group := new(models.Group)
	group.Id = 1001
	group.Name = models.AllUsers.String()
	group.CreateUser = "admin"
	group.CreateTime = time.Now()
	if err := CreateGroup(tx, group); err != nil {
		stdlog.WithError(err).Errorf("failed to create all_users group")
		return err
	}

	// 初始化时将内置用户admin、 thinger、demo加入all_users用户组
	adminUserGroup := new(models.UserGroup)
	adminUserGroup.Id = 1002
	adminUserGroup.Username = "admin"
	adminUserGroup.GroupName = models.AllUsers.String()
	adminUserGroup.CreateTime = time.Now()
	if err := CreateUserGroup(tx, adminUserGroup); err != nil {
		stdlog.WithError(err).Errorf("failed to create admin -> all_users user group mapping")
		return err
	}

	thingerUserGroup := new(models.UserGroup)
	thingerUserGroup.Id = 1003
	thingerUserGroup.Username = "thinger"
	thingerUserGroup.GroupName = models.AllUsers.String()
	thingerUserGroup.CreateTime = time.Now()
	if err := CreateUserGroup(tx, thingerUserGroup); err != nil {
		stdlog.WithError(err).Errorf("failed to create thinger -> all_users user group mapping")
		return err
	}

	demoUserGroup := new(models.UserGroup)
	demoUserGroup.Username = "demo"
	demoUserGroup.GroupName = models.AllUsers.String()
	demoUserGroup.CreateTime = time.Now()
	if err := CreateUserGroup(tx, demoUserGroup); err != nil {
		stdlog.WithError(err).Errorf("failed to create demo -> all_users user group mapping")
		return err
	}

	return nil
}

func CreateGroup(tx *gorm.DB, group *models.Group) error {
	return tx.Create(group).Error
}

func CreateOrUpdateGroup(tx *gorm.DB, group *models.Group) error {
	return tx.Where(models.Group{Name: group.Name}).Assign(models.Group{Description: group.Description}).
		FirstOrCreate(&group).Error
}

func UpdateGroup(tx *gorm.DB, group *models.Group) error {
	return tx.Updates(group).Error
}

func DeleteGroupByName(tx *gorm.DB, name string) error {
	if err := tx.Where("name = ?", name).Delete(&models.Group{}).Error; err != nil {
		return err
	}
	return nil
}

func BatchDeleteGroupsByNames(tx *gorm.DB, names []string) error {
	if err := tx.Where("name in (?)", names).Delete(&models.Group{}).Error; err != nil {
		return err
	}
	return nil
}

func ListGroups(tx *gorm.DB) ([]*models.Group, error) {
	groups := make([]*models.Group, 0)
	if err := tx.Find(&groups).Error; err != nil {
		return nil, err
	}
	return groups, nil
}

func GetGroup(tx *gorm.DB, name string) (*models.Group, error) {
	group := new(models.Group)
	if err := tx.Where("name = ?", name).Find(&group).Error; err != nil {
		return nil, err
	}
	return group, nil
}

func GetGroupById(tx *gorm.DB, id int64) (*models.Group, error) {
	group := new(models.Group)
	if err := tx.Where("id = ?", id).Find(&group).Error; err != nil {
		return nil, err
	}
	return group, nil
}
