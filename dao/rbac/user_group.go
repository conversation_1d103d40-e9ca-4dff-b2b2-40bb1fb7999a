package rbac

import (
	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func GetUserGroupsByUsername(tx *gorm.DB, username string) ([]*models.UserGroup, error) {
	userGroups := make([]*models.UserGroup, 0)
	if err := tx.Where("username in (?)", username).Find(&userGroups).Error; err != nil {
		return nil, err
	}
	return userGroups, nil
}

func BatchGetUserGroupsByUserNames(tx *gorm.DB, usernames []string) ([]*models.UserGroup, error) {
	userGroups := make([]*models.UserGroup, 0)
	if err := tx.Where("username in (?)", usernames).Find(&userGroups).Error; err != nil {
		return nil, err
	}
	return userGroups, nil
}

func BatchGetUserGroupsByGroupNames(tx *gorm.DB, groupNames []string) ([]*models.UserGroup, error) {
	userGroups := make([]*models.UserGroup, 0)
	if err := tx.Where("group_name in (?)", groupNames).Find(&userGroups).Error; err != nil {
		return nil, err
	}
	return userGroups, nil
}

func CreateUserGroup(tx *gorm.DB, userGroup *models.UserGroup) error {
	return tx.Create(userGroup).Error
}

func CreateOrUpdateUserGroup(tx *gorm.DB, userGroup *models.UserGroup) error {
	return tx.Where(models.UserGroup{Username: userGroup.Username, GroupName: userGroup.GroupName}).
		FirstOrCreate(&userGroup).Error
}

func DeleteUserGroupsByUsername(tx *gorm.DB, username string) error {
	userGroups := make([]*models.UserGroup, 0)
	if err := tx.Where("username = ?", username).Delete(&userGroups).Error; err != nil {
		return err
	}
	return nil
}

func BatchDeleteUserGroupsByUsernames(tx *gorm.DB, usernames []string) error {
	userGroups := make([]*models.UserGroup, 0)
	if err := tx.Where("username in (?)", usernames).Delete(&userGroups).Error; err != nil {
		return err
	}
	return nil
}

func DeleteUserGroupsByGroupName(tx *gorm.DB, groupName string) error {
	//userGroups := make([]*models.UserGroup, 0)
	if err := tx.Where("group_name = ?", groupName).Delete(&models.UserGroup{}).Error; err != nil {
		return err
	}
	return nil
}

func BatchDeleteUserGroupsByGroupNames(tx *gorm.DB, groupNames []string) error {
	//userGroups := make([]*models.UserGroup, 0)
	if err := tx.Where("group_name in (?)", groupNames).Delete(&models.UserGroup{}).Error; err != nil {
		return err
	}
	return nil
}
