package rbac

import (
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/clients/cas"
)

type RbacObject struct {
	Id         uint64      `gorm:"primaryKey;type:bigint UNSIGNED"`
	ExternalId string      `gorm:"uniqueIndex:idx_id_type;type:varchar(255);not null"` // 外部资源的id
	ObjType    cas.ObjType `gorm:"uniqueIndex:idx_id_type;type:varchar(63);not null"`
	CreatedAt  time.Time

	RbacPolicies []*RbacPolicy `gorm:"-"`
}

func (RbacObject) TableName() string {
	return "rbac_object"
}
