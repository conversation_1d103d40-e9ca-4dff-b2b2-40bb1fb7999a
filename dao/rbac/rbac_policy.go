package rbac

import (
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/clients/cas"
)

type RbacPolicy struct {
	Id        uint64      `gorm:"primaryKey;type:bigint UNSIGNED"`
	SubType   cas.SubType `gorm:"uniqueIndex:idx_policy;type:varchar(15);not null"`     // 主体类型 user:Username必填, group:GroupId必填
	Username  string      `gorm:"uniqueIndex:idx_policy;type:varchar(255);not null"`    // 用户名: thinger
	GroupId   uint64      `gorm:"uniqueIndex:idx_policy;type:bigint UNSIGNED;not null"` // 用户组id
	ObjId     uint64      `gorm:"uniqueIndex:idx_policy;type:bigint UNSIGNED;not null"` // 操作对象id
	ProjectId string      `gorm:"uniqueIndex:idx_policy;type:varchar(255);not null"`    // 空间id: assets
	Act       cas.Act     `gorm:"type:varchar(15);not null"`                            // 允许的操作
	CreatedAt time.Time

	RbacObject RbacObject `gorm:"-"`
}

func (RbacPolicy) TableName() string {
	return "rbac_policy"
}
