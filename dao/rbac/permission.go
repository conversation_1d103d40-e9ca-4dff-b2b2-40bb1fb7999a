package rbac

import (
	"fmt"

	"github.com/samber/lo"
	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func AllPermissionMap(db *gorm.DB) (
	permissionIdMap map[uint64]*models.Permission,
	permissionCodeMap map[string][]*models.Permission,
	permissionCodeActionMap map[string]*models.Permission,
	err error,
) {
	// map[id]permission
	permissionIdMap = make(map[uint64]*models.Permission)
	// map[code]permission
	permissionCodeMap = make(map[string][]*models.Permission)
	// map[code:action]permission
	permissionCodeActionMap = make(map[string]*models.Permission)
	// 全量权限点数据
	all, err := ListPermissions(db, "")
	if err != nil {
		return nil, nil, nil, err
	}
	// 用于获取父级权限id
	pareIdMap := lo.FilterSliceToMap(all, func(p *models.Permission) (string, uint64, bool) {
		return p.Code, p.ID, p.Action == "access"
	})
	for _, permission := range all {
		permission.ParentId = pareIdMap[permission.Parent]

		permissionIdMap[permission.ID] = permission
		permissionCodeActionMap[FormatCodeAction(permission.Code, permission.Action)] = permission
		permissionCodeMap[permission.Code] = append(permissionCodeMap[permission.Code], permission)
	}

	return permissionIdMap, permissionCodeMap, permissionCodeActionMap, nil
}

func FormatCodeAction(code string, action string) string {
	return fmt.Sprintf("%s:%s", code, action)
}

func ListPermissions(tx *gorm.DB, permissionType string) ([]*models.Permission, error) {
	permissions := make([]*models.Permission, 0)
	var err error
	if permissionType != "" {
		err = tx.Where("type = ?", permissionType).Order("id asc").Find(&permissions).Error
	} else {
		err = tx.Order("id asc").Find(&permissions).Error
	}
	if err != nil {
		return nil, err
	}
	return permissions, nil
}

func BatchGetPermissionsByIds(tx *gorm.DB, permissionIds []uint64) ([]*models.Permission, error) {
	permissions := make([]*models.Permission, 0)
	if err := tx.Where("id in (?)", permissionIds).Find(&permissions).Error; err != nil {
		return nil, err
	}
	return permissions, nil
}

func CreatePermission(tx *gorm.DB, permission *models.Permission) error {
	return tx.Create(permission).Error
}

func CreateOrUpdatePermission(tx *gorm.DB, permission *models.Permission) error {
	return tx.Where(models.Permission{Code: permission.Code, Action: permission.Action, Type: permission.Type}).
		Assign(models.Permission{Name: permission.Name, Action: permission.Action, Type: permission.Type, Parent: permission.Parent}).
		FirstOrCreate(&permission).Error
}

func GetPermissionsByPermissionCode(tx *gorm.DB, permissionCode string) ([]*models.Permission, error) {
	permissions := make([]*models.Permission, 0)
	if err := tx.Where("code = ?", permissionCode).Find(&permissions).Error; err != nil {
		return nil, err
	}
	return permissions, nil
}

func GetPermissionsByPermissionCodeAction(tx *gorm.DB, permissionCode string, permissionAction string) (*models.Permission, error) {
	permission := new(models.Permission)
	if err := tx.Where("code = ? and action = ?", permissionCode, permissionAction).First(&permission).Error; err != nil {
		return nil, err
	}
	return permission, nil
}

func BatchDeletePermissionsByIds(tx *gorm.DB, permissionIds []uint64) error {
	if err := tx.Where("id in (?)", permissionIds).Delete(&models.Permission{}).Error; err != nil {
		return err
	}
	return nil
}
