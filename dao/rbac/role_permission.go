package rbac

import (
	"time"

	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func SavePermissionsByRoleID(db *gorm.DB, roleID uint64, permissions *[]models.Permission) error {
	// 使用事务 即使调用者已经使用
	return db.Transaction(func(tx *gorm.DB) error {
		err := DeleteRolePermissionsByRoleId(tx, roleID)
		if err != nil {
			return err
		}
		var (
			rps = make([]*models.RolePermission, 0, len(*permissions))
			now = time.Now()
		)
		for _, p := range *permissions {
			rps = append(rps, &models.RolePermission{
				RoleId:       roleID,
				PermissionId: p.ID,
				CreateTime:   now,
				UpdateTime:   now,
			})
		}
		err = CreateRolePermissions(tx, rps)
		if err != nil {
			return err
		}
		return nil
	})
}

func GetRolePermissionsByRoleId(tx *gorm.DB, roleId uint64) ([]*models.RolePermission, error) {
	rolePermissions := make([]*models.RolePermission, 0)
	if err := tx.Where("role_id = ?", roleId).Find(&rolePermissions).Error; err != nil {
		return nil, err
	}
	return rolePermissions, nil
}

func BatchGetRolePermissionsByRoleIds(tx *gorm.DB, roleIds []uint64) ([]*models.RolePermission, error) {
	rolePermissions := make([]*models.RolePermission, 0)
	if err := tx.Where("role_id in (?)", roleIds).Find(&rolePermissions).Error; err != nil {
		return nil, err
	}
	return rolePermissions, nil
}

func CreateRolePermission(tx *gorm.DB, rolePermission *models.RolePermission) error {
	return tx.Create(rolePermission).Error
}

func CreateOrUpdateRolePermission(tx *gorm.DB, rolePermission *models.RolePermission) error {
	return tx.Where(models.RolePermission{RoleId: rolePermission.RoleId, PermissionId: rolePermission.PermissionId}).
		Assign(models.RolePermission{UpdateTime: rolePermission.UpdateTime}).FirstOrCreate(&rolePermission).Error
}

func CreateRolePermissions(tx *gorm.DB, rolePermissions []*models.RolePermission) error {
	return tx.Create(rolePermissions).Error
}

func UpdateRolePermission(tx *gorm.DB, roleId uint64, rolePermission *models.RolePermission) error {
	if err := tx.Model(models.RolePermission{}).Where("role_id = ?", roleId).Updates(rolePermission).Error; err != nil {
		return err
	}
	return nil
}

func UpdateRolePermissions(tx *gorm.DB, roleId uint64, rolePermissions []*models.RolePermission) error {
	return tx.Model(&models.RolePermission{RoleId: roleId}).Updates(rolePermissions).Error
}

func DeleteRolePermissionsByRoleId(tx *gorm.DB, roleId uint64) error {
	rolePermissions := make([]*models.RolePermission, 0)
	if err := tx.Where("role_id = ?", roleId).Delete(&rolePermissions).Error; err != nil {
		return err
	}
	return nil
}

func BatchDeleteRolePermissionsByRoleIds(tx *gorm.DB, roleIds []uint64) error {
	rolePermissions := make([]*models.RolePermission, 0)
	if err := tx.Where("role_id in (?)", roleIds).Delete(&rolePermissions).Error; err != nil {
		return err
	}
	return nil
}

func GetRolePermissionsByPermissionIds(tx *gorm.DB, permissionIds []uint64) ([]*models.RolePermission, error) {
	rolePermissions := make([]*models.RolePermission, 0)
	if err := tx.Where("permission_id in (?)", permissionIds).Find(&rolePermissions).Error; err != nil {
		return nil, err
	}
	return rolePermissions, nil
}

func ExistsRolePermission(tx *gorm.DB) (bool, error) {
	var count int64
	if err := tx.Model(&models.RolePermission{}).Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}
