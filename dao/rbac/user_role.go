package rbac

import (
	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func CreateUserRole(tx *gorm.DB, userRole *models.UserRole) error {
	return tx.Create(userRole).Error
}

func CreateOrUpdateUserRole(tx *gorm.DB, userRole *models.UserRole) error {
	return tx.Where("name = ? and role_id = ? and bind_type = ? and project_id = ?", userRole.Name, userRole.RoleId,
		userRole.BindType, userRole.ProjectId).Assign(models.UserRole{UpdateTime: userRole.UpdateTime}).
		FirstOrCreate(&userRole).Error
}

func CreateUserRoles(tx *gorm.DB, userRoles []*models.UserRole) error {
	return tx.Create(userRoles).Error
}

func UpdateUserRole(tx *gorm.DB, userRole *models.UserRole) error {
	return tx.Model(&models.UserRole{}).Where("project_id = ? and name = ? and bind_type = ?",
		userRole.ProjectId, userRole.Name, userRole.BindType).Update("role_id", userRole.RoleId).Error
}

func UpdateUserRoles(tx *gorm.DB, userRoles []*models.UserRole) error {
	return tx.Model(&models.UserRole{}).Select("role_id").Updates(userRoles).Error
	//return tx.Updates(userRoles).Error
}

func UpdateUserRoleByName(tx *gorm.DB, userRole *models.UserRole, name string) error {
	return tx.Model(&models.UserRole{}).Where("project_id = ? and name = ? and bind_type = ?",
		userRole.ProjectId, name, userRole.BindType).Updates(userRole).Error
}

func DeleteUserRole(tx *gorm.DB, userRole *models.UserRole) error {
	if err := tx.Where("name = ? and project_id = ? and bind_type = ?", userRole.Name,
		userRole.ProjectId, userRole.BindType).Delete(&models.UserRole{}).Error; err != nil {
		return err
	}
	return nil
}

func DeleteUserRoles(tx *gorm.DB, projectId string) error {
	if err := tx.Where("project_id = ?", projectId).Delete(&models.UserRole{}).Error; err != nil {
		return err
	}
	return nil
}

func DeleteUserRolesByRoleId(tx *gorm.DB, roleID uint64) error {
	if err := tx.Where("role_id = ?", roleID).Delete(&models.UserRole{}).Error; err != nil {
		return err
	}
	return nil
}

func DeleteUserRolesByName(tx *gorm.DB, name string, bindType string) error {
	if err := tx.Where("name = ? and bind_type = ?", name, bindType).
		Delete(&models.UserRole{}).Error; err != nil {
		return err
	}
	return nil
}

func DeleteUserRolesByProjectId(tx *gorm.DB, projectId string) error {
	if err := tx.Where("project_id = ?", projectId).Delete(&models.UserRole{}).Error; err != nil {
		return err
	}
	return nil
}

func BatchDeleteUserRolesByNames(tx *gorm.DB, names []string, bindType string) error {
	if err := tx.Where("name in (?) and bind_type = ?", names, bindType).
		Delete(&models.UserRole{}).Error; err != nil {
		return err
	}
	return nil
}

func GetUserRolesByProjectId(tx *gorm.DB, projectId string) ([]*models.UserRole, error) {
	userRoles := make([]*models.UserRole, 0)
	if err := tx.Where("project_id = ?", projectId).Find(&userRoles).Error; err != nil {
		return nil, err
	}
	return userRoles, nil
}

func GetUserRoles(tx *gorm.DB, names []string, bindType models.BindType, projectID string) ([]*models.UserRole, error) {
	userRoles := make([]*models.UserRole, 0)
	if projectID == "" {
		if err := tx.Where("name in (?) and bind_type = ? and project_id = '' ", names, bindType).
			Find(&userRoles).Error; err != nil {
			return nil, err
		}
	} else {
		if err := tx.Where("name in (?) and bind_type = ? and project_id in (?)", names,
			bindType, []string{projectID, ""}).Find(&userRoles).Error; err != nil {
			return nil, err
		}
	}
	return userRoles, nil
}

func GetUserRolesByProjectAndRoleIds(tx *gorm.DB, projectId string, roleIds []uint64) ([]*models.UserRole, error) {
	userRoles := make([]*models.UserRole, 0)
	if err := tx.Where("project_id = ? and role_id in (?)", projectId, roleIds).
		Find(&userRoles).Error; err != nil {
		return nil, err
	}
	return userRoles, nil
}

func BatchDeleteUserRolesByRoleIds(tx *gorm.DB, roleIds []uint64) error {
	if err := tx.Where("role_id in (?)", roleIds).Delete(&models.UserRole{}).Error; err != nil {
		return err
	}
	return nil
}

func GetPlatformUserRoles(tx *gorm.DB) ([]*models.UserRole, error) {
	userRoles := make([]*models.UserRole, 0)
	if err := tx.Where("project_id = ''").Find(&userRoles).Error; err != nil {
		return nil, err
	}
	return userRoles, nil
}

func GetUserRolesByRoleIds(tx *gorm.DB, roleIds []uint64) ([]*models.UserRole, error) {
	userRoles := make([]*models.UserRole, 0)
	if err := tx.Where("role_id in (?)", roleIds).Find(&userRoles).Error; err != nil {
		return nil, err
	}
	return userRoles, nil
}
