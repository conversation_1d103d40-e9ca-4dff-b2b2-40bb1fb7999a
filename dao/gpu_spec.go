package dao

import "gorm.io/gorm"

type GpuSpec struct {
	Id            string `json:"id" gorm:"primary_key;type:varchar(50)"`
	UseGpuUUID    string `json:"use_gpu_uuid" gorm:"column:use_gpu_uuid;type:TEXT;"`
	NotUseGpuUUID string `json:"not_use_gpu_uuid" gorm:"column:not_use_gpu_uuid;type:TEXT;"`
	UseGpuType    string `json:"use_gpu_type" gorm:"column:use_gpu_type;type:varchar(64);"`
	NotUseGpuType string `json:"not_use_gpu_type" gorm:"column:not_use_gpu_type;type:varchar(64);"`
}

func CreateGpuSpec(tx *gorm.DB, gpuSpec *GpuSpec) error {
	return tx.Create(gpuSpec).Error
}

func GetGpuSpec(tx *gorm.DB, id string) (*GpuSpec, error) {
	gpuSpec := new(GpuSpec)
	if err := tx.Where("id = ?", id).First(gpuSpec).Error; err != nil {
		return nil, err
	}
	return gpuSpec, nil

}

func ListGpuSpecs(tx *gorm.DB) ([]*GpuSpec, error) {
	gpuSpecs := make([]*GpuSpec, 0)
	if err := tx.Find(&gpuSpecs).Error; err != nil {
		return nil, err
	}
	return gpuSpecs, nil
}

func DeleteGpuSpec(tx *gorm.DB, id string) error {
	return tx.Where("id = ?", id).Delete(&GpuSpec{}).Error

}

func UpdateGpuSpec(tx *gorm.DB, gpuSpec *GpuSpec, id string) error {
	return tx.Model(GpuSpec{}).Where("id = ?", id).Updates(gpuSpec).Error
}
