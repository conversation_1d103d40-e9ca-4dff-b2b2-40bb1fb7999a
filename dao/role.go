package dao

import (
	"gorm.io/gorm"
)

func CreateRole(tx *gorm.DB, role *Role) error {
	return tx.Create(role).Error
}

func GetRoleByID(tx *gorm.DB, roleID string) (*Role, error) {
	role := new(Role)
	if err := tx.Where("id = ?", roleID).Find(role).Error; err != nil {
		return nil, err
	}
	return role, nil
}

func GetRoleByName(tx *gorm.DB, name string) (*Role, error) {
	role := new(Role)
	if err := tx.Where("name = ?", name).Find(role).Error; err != nil {
		return nil, err
	}
	return role, nil
}

func ListRoles(tx *gorm.DB) ([]*Role, error) {
	roles := make([]*Role, 0)
	if err := tx.Find(&roles).Error; err != nil {
		return nil, err
	}
	return roles, nil
}

func DeleteRoleByID(tx *gorm.DB, roleID string) error {
	if err := tx.Where("id = ?", roleID).Delete(&Role{}).Error; err != nil {
		return err
	}
	return nil
}

func DeleteRoleByName(tx *gorm.DB, name string) error {
	if err := tx.Where("name = ?", name).Delete(&Role{}).Error; err != nil {
		return err
	}
	return nil
}

type Role struct {
	gorm.Model

	Name  string  `json:"name" gorm:"type:varchar(500);column:name;unique;"`
	Users []*User `json:"-" gorm:"-"`
}
