package dao

import (
	"fmt"

	"github.com/samber/lo"
	"gorm.io/gorm"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type CustomConfigService interface {
	ListConfigs(lang stdsrv.Language) []*pb.DynamicParam
	GetConfigValue() (*models.CustomConfig, error)
	UpdateConfigValue(config *models.CustomConfig) error
	Check() error
}

func GetCustomConfigService() CustomConfigService {
	return &configService{
		db: GetDB(),
	}
}

func MustCheckAndGetCustomConfigService() CustomConfigService {
	cs := GetCustomConfigService()
	err := cs.Check()
	if err != nil {
		panic(stderr.Wrap(err, "failed to check custom config"))
	}
	return cs
}

type configService struct {
	db *gorm.DB
}

func (c *configService) ListConfigs(lang stdsrv.Language) []*pb.DynamicParam {
	if lang == stdsrv.LanguageEnglish {
		return supportedCustomConfigsEn
	}
	return supportedCustomConfigs
}

func (c *configService) GetConfigValue() (*models.CustomConfig, error) {
	var config models.CustomConfig
	result := c.db.Where(models.CustomConfig{System: models.True}).First(&config)
	if result.Error != nil {
		return nil, stderr.Internal.Cause(result.Error, "Get config error.")
	}
	return &config, nil
}

func (c *configService) UpdateConfigValue(config *models.CustomConfig) error {
	config.System = models.True
	result := c.db.Model(&models.CustomConfig{}).Where(&models.CustomConfig{System: models.True}).Updates(config)
	if result.Error != nil {
		return stderr.Internal.Cause(result.Error, "Update config error.")
	}
	return nil
}

func (c *configService) Check() error {
	var configs []*models.CustomConfig
	err := c.db.Debug().Where(&models.CustomConfig{System: models.True}).Find(&configs).Error
	if err != nil {
		return stderr.Internal.Cause(err, "Check config error.")
	}

	if len(configs) == 0 {
		// 新增一条数据
		err = c.db.Create(&models.CustomConfig{
			System: models.True,
			Value: map[string]interface{}{
				HelpDocumentID: DefaultHelpDocument,
				TabTitleID:     DefaultTabTitle,
			},
		}).Error
		if err != nil {
			return stderr.Internal.Cause(err, "add config error.")
		}
	}
	return nil
}

func UpdateProjectPermission(c CustomConfigService, actions []string) error {
	config, err := c.GetConfigValue()
	if err != nil {
		return fmt.Errorf("get config: %w", err)
	}
	config.Value["PERMISSION_PROJECT_ACTIONS"] = actions

	err = c.UpdateConfigValue(config)
	if err != nil {
		return fmt.Errorf("update config: %w", err)
	}
	return nil
}

func GetProjectPermission(c CustomConfigService) ([]string, error) {
	config, err := c.GetConfigValue()
	if err != nil {
		return nil, fmt.Errorf("get config: %w", err)
	}
	acts, ok := config.Value["PERMISSION_PROJECT_ACTIONS"]
	if !ok {
		stdlog.Warn("PERMISSION_PROJECT_ACTIONS not found")
		return nil, nil
	}
	actsAny, ok := acts.([]any)
	if !ok {
		return nil, fmt.Errorf("PERMISSION_PROJECT_ACTIONS is not a any array: %T", acts)
	}
	actsStr, ok := lo.FromAnySlice[string](actsAny)
	if !ok {
		return nil, fmt.Errorf("PERMISSION_PROJECT_ACTIONS is not a string array: %T", acts)
	}
	return actsStr, nil
}
