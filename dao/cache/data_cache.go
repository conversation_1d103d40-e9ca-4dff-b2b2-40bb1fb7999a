package cache

import (
	"github.com/patrickmn/go-cache"
	"strings"
	"sync"
	"time"
	"transwarp.io/aip/llmops-common/pb"
)

type DataCache interface {
	// Get 尝试从缓存获取ImageBytesAndMeta
	Get(module string, projectId string) (*pb.Group, error)
	// Set 向缓存中存放image meta信息
	Set(module string, projectId string, data *pb.Group)
	// Search 尝试从缓存获取ImageBytesAndMeta
	Search(module string, projectId string, key string) (*pb.Group, error)
}

// memoryDataCache 使用go-cache作为缓存
type memoryDataCache struct {
	Cache *cache.Cache
}

var (
	ic     DataCache
	icOnce sync.Once
)

func GetDataCache() DataCache {
	icOnce.Do(func() {
		ic = &memoryDataCache{
			Cache: newCache(),
		}
	})
	return ic
}

func (i memoryDataCache) Get(module string, projectId string) (*pb.Group, error) {
	data, found := i.Cache.Get(dataCacheKey(module, projectId))
	if !found || data == nil {
		return nil, nil
	}

	group := data.(*pb.Group)

	return group, nil
}

func (i memoryDataCache) Search(module string, projectId string, key string) (*pb.Group, error) {
	data, found := i.Cache.Get(dataCacheKey(module, projectId))
	if !found || data == nil {
		return nil, nil
	}

	ret := data.(*pb.Group)

	items := make([]*pb.Item, 0)

	for _, item := range ret.Items {

		condition := strings.Contains(item.Id, key) || strings.Contains(item.Name, key) ||
			strings.Contains(item.Creator, key) || strings.Contains(item.Desc, key)

		if !condition {
			for labelKey, labelValue := range item.Labels {
				if strings.Contains(labelKey, key) || strings.Contains(labelValue, key) {
					condition = true
				}
			}
		}

		if condition {
			items = append(items, item)
		}
	}

	ret.Items = items

	return ret, nil
}

func (i memoryDataCache) Set(module string, projectId string, data *pb.Group) {
	realKey := dataCacheKey(module, projectId)
	i.Cache.Set(realKey, data, cache.DefaultExpiration)
}

func newCache() *cache.Cache {
	return cache.New(30*time.Second, 30*time.Second)
}

func dataCacheKey(module string, projectId string) string {
	return strings.Join([]string{
		module,
		projectId,
	}, ":")
}
