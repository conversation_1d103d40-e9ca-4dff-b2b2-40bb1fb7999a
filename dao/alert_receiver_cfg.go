package dao

import (
	"gorm.io/gorm/clause"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type AlertReceiverCfgSvc struct {
}

func (c *AlertReceiverCfgSvc) UpsertAlertReceiverCfg(cfg *models.AlertReceiverConfig) error {
	err := GetDB().Model(models.AlertReceiverConfig{}).Clauses(clause.OnConflict{
		UpdateAll: true, // 更新所有字段
	}).Create(cfg)
	return err.Error
}

func (c *AlertReceiverCfgSvc) GetAlertReceiverCfg(svcID string) (*models.AlertReceiverConfig, error) {
	var configs []*models.AlertReceiverConfig
	err := GetDB().Model(models.AlertReceiverConfig{}).Where(models.AlertReceiverConfig{ServiceID: svcID}).Find(&configs)
	if err.Error != nil {
		return nil, err.Error
	}
	if len(configs) == 0 {
		stdlog.Warnf("can not find alert receiver config by svc id :%v", svcID)
		return nil, nil
	}
	return configs[0], nil
}
