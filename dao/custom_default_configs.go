package dao

import "transwarp.io/aip/llmops-common/pb"

type DynamicParam pb.DynamicParam

const (
	DefaultHelpDocument = "true"
	DefaultTabTitle     = "机器学习管理工作平台"
	DefaultLoginTitle   = "机器学习管理工作平台"

	HelpDocumentID = "help_document"
	TabTitleID     = "tab_title"
	LoginTitleID   = "login_title"
)

var (
	supportedCustomConfigs = []*pb.DynamicParam{
		HelpDocument,
		TabTitle,
		LoginTitle,
		MWHEnvHFToken,
		MWHEnvHFEndpoint,
		MWHProxySwitch,
		MWHEnvHttpProxy,
		MWHEnvHttpsProxy,
		CVATEnvNlpAnnotationEndpoint,
		CVATEnvUserTokenEndpoint,
		MODULE_ORDER,
		DisablePasswdUpdateButton,
		DisableLogoutButton,
		PermissionProjectActions,
	}
	supportedCustomConfigsEn = []*pb.DynamicParam{
		HelpDocumentEn,
		TabTitleEn,
		LoginTitleEn,
		MWHEnvHFTokenEn,
		MWHEnvHFEndpointEn,
		MWHEnvHttpProxyEn,
		MWHEnvHttpsProxyEn,
		CVATEnvNlpAnnotationEndpointEn,
		CVATEnvUserTokenEndpointEn,
		MODULE_ORDEREn,
		DisablePasswdUpdateButtonEn,
		DisableLogoutButtonEn,
		PermissionProjectActionsEn,
	}
)

var (
	HelpDocument = &pb.DynamicParam{
		Id:           HelpDocumentID,
		Name:         "是否显示帮助文档",
		Desc:         "是否显示帮助文档",
		Type:         pb.DynamicParam_TYPE_SWITCH,
		DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
		Required:     true,
		DefaultValue: DefaultHelpDocument,
	}
	TabTitle = &pb.DynamicParam{
		Id:           TabTitleID,
		Name:         "标签页标题",
		Desc:         "标签页标题",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		Required:     true,
		DefaultValue: DefaultTabTitle,
		CompProps:    "{\"maxLength\": 56}",
	}
	LoginTitle = &pb.DynamicParam{
		Id:           LoginTitleID,
		Name:         "登录页标题",
		Desc:         "CAS单点登陆页面展示的标题内容",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		Required:     true,
		DefaultValue: DefaultLoginTitle,
		CompProps:    "{\"maxLength\": 56}",
	}
	MWHEnvHFToken = &pb.DynamicParam{
		Id:           "HF_TOKEN",
		Name:         "模型仓库环境变量HF_TOKEN",
		Desc:         "模型仓库用于huggingface下载模型时认证",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		Required:     true,
		DefaultValue: "*************************************",
	}
	MWHEnvHFEndpoint = &pb.DynamicParam{
		Id:           "HF_ENDPOINT",
		Name:         "模型仓库环境变量HF_ENDPOINT",
		Desc:         "模型仓库下载huggingface模型时使用的地址",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		DefaultValue: "https://hf-mirror.com",
	}
	MWHProxySwitch = &pb.DynamicParam{
		Id:           "PROXY_ENABLE",
		Name:         "是否开启网络代理",
		Desc:         "获取hugginface,modelscope模型是否使用代理",
		Type:         pb.DynamicParam_TYPE_SWITCH,
		DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
		Required:     true,
		DefaultValue: "false",
	}
	MWHEnvHttpProxy = &pb.DynamicParam{
		Id:           "HTTP_PROXY",
		Name:         "模型仓库环境变量HTTP_PROXY",
		Desc:         "模型仓库联网下载模型时使用的代理",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		DefaultValue: "",
	}
	MWHEnvHttpsProxy = &pb.DynamicParam{
		Id:           "HTTPS_PROXY",
		Name:         "模型仓库环境变量HTTPS_PROXY",
		Desc:         "模型仓库联网下载模型时使用的代理",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		DefaultValue: "",
	}
	CVATEnvNlpAnnotationEndpoint = &pb.DynamicParam{
		Id:           "NLP_ANNO_ENDPOINT",
		Name:         "语料环境变量NLP_ANNO_ENDPOINT",
		Desc:         "语料标注传统NLP标注免密跳转地址",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		DefaultValue: "https://kg-node43:8066/gateway/user/api/users/password-free/login?token=Bearer%20eyJhbGciOiJIUzUxMiJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIl0iLCJzY29wZSI6ImV4dGVybmFsIiwiZXhwIjo0ODA5NDg3ODQwLCJpYXQiOjE2NTU4ODc4NDB9.A8CysBGeYGRQeuY47LmgyTrCoN8nnZzVlgmZC_Yy8QFl_x0nnfaijP3_6ezzwCJn0UUAFpMLjFyqK0oQDlSyNg&redirect=https://kg-node43:8066/nlp/annotation_task/admin/task_list&refer=https://kg-node43/error.html",
	}
	CVATEnvUserTokenEndpoint = &pb.DynamicParam{
		Id:           "USER_TOKEN_ENDPOINT",
		Name:         "语料环境变量USER_TOKEN_ENDPOINT",
		Desc:         "语料标注获取user token的地址",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		DefaultValue: "https://kg-node43:8066/gateway/user/api/admins/token",
	}
	MODULE_ORDER = &pb.DynamicParam{
		Id:           "MODULE_ORDER",
		Name:         "模块展示顺序配置",
		Desc:         "模块排序，用于模块显示顺序",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		DefaultValue: "{\"mw\":0,\"applet\":1,\"corpus\":2,\"knowledge\":3}",
	}
	DisablePasswdUpdateButton = &pb.DynamicParam{
		Id:           "DISABLE_PASSWD_UPDATE_BUTTON",
		Name:         "禁用修改密码按钮",
		Desc:         "开启后禁用修改密码按钮",
		Type:         pb.DynamicParam_TYPE_SWITCH,
		DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
		DefaultValue: "false",
	}
	DisableLogoutButton = &pb.DynamicParam{
		Id:           "DISABLE_LOGOUT_BUTTON",
		Name:         "禁用退出按钮",
		Desc:         "开启后禁用退出按钮",
		Type:         pb.DynamicParam_TYPE_SWITCH,
		DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
		DefaultValue: "false",
	}
	PermissionProjectActions = &pb.DynamicParam{
		Id:           "PERMISSION_PROJECT_ACTIONS",
		Name:         "空间管理权限点勾选的 actions",
		Desc:         "超管对管理员赋予的空间管理权限json格式",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		DefaultValue: "false",
		Hidden:       true,
	}
)

var (
	HelpDocumentEn = &pb.DynamicParam{
		Id:           HelpDocumentID,
		Name:         "Show Help Document",
		Desc:         "Toggle display of help documentation",
		Type:         pb.DynamicParam_TYPE_SWITCH,
		DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
		Required:     true,
		DefaultValue: DefaultHelpDocument,
	}
	TabTitleEn = &pb.DynamicParam{
		Id:           TabTitleID,
		Name:         "Tab Title",
		Desc:         "Title displayed in browser tab",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		Required:     true,
		DefaultValue: DefaultTabTitle,
		CompProps:    "{\"maxLength\": 56}",
	}
	LoginTitleEn = &pb.DynamicParam{
		Id:           LoginTitleID,
		Name:         "Login Page Title",
		Desc:         "Title displayed on CAS Single Sign-On page",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		Required:     true,
		DefaultValue: DefaultLoginTitle,
		CompProps:    "{\"maxLength\": 56}",
	}
	MWHEnvHFTokenEn = &pb.DynamicParam{
		Id:           "HF_TOKEN",
		Name:         "Model Warehouse Env HF_TOKEN",
		Desc:         "Authentication token for HuggingFace model downloads in Model Warehouse",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		Required:     true,
		DefaultValue: "*************************************",
	}
	MWHEnvHFEndpointEn = &pb.DynamicParam{
		Id:           "HF_ENDPOINT",
		Name:         "Model Warehouse Env HF_ENDPOINT",
		Desc:         "Endpoint for downloading HuggingFace models in Model Warehouse",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		DefaultValue: "https://hf-mirror.com",
	}
	MWHEnvHttpProxyEn = &pb.DynamicParam{
		Id:           "HTTP_PROXY",
		Name:         "Model Warehouse Env HTTP_PROXY",
		Desc:         "HTTP proxy for model downloads in Model Warehouse",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		DefaultValue: "",
	}
	MWHEnvHttpsProxyEn = &pb.DynamicParam{
		Id:           "HTTPS_PROXY",
		Name:         "Model Warehouse Env HTTPS_PROXY",
		Desc:         "HTTPS proxy for model downloads in Model Warehouse",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		DefaultValue: "",
	}
	CVATEnvNlpAnnotationEndpointEn = &pb.DynamicParam{
		Id:           "NLP_ANNO_ENDPOINT",
		Name:         "Corpus Env NLP_ANNO_ENDPOINT",
		Desc:         "Password-free redirect URL for traditional NLP annotation in Corpus Annotation",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		DefaultValue: "https://kg-node43:8066/gateway/user/api/users/password-free/login?token=Bearer%20eyJhbGciOiJIUzUxMiJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIl0iLCJzY29wZSI6ImV4dGVybmFsIiwiZXhwIjo0ODA5NDg3ODQwLCJpYXQiOjE2NTU4ODc4NDB9.A8CysBGeYGRQeuY47LmgyTrCoN8nnZzVlgmZC_Yy8QFl_x0nnfaijP3_6ezzwCJn0UUAFpMLjFyqK0oQDlSyNg&redirect=https://kg-node43:8066/nlp/annotation_task/admin/task_list&refer=https://kg-node43/error.html",
	}
	CVATEnvUserTokenEndpointEn = &pb.DynamicParam{
		Id:           "USER_TOKEN_ENDPOINT",
		Name:         "Corpus Env USER_TOKEN_ENDPOINT",
		Desc:         "Endpoint for obtaining user tokens in Corpus Annotation",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		DefaultValue: "https://kg-node43:8066/gateway/user/api/admins/token",
	}
	MODULE_ORDEREn = &pb.DynamicParam{
		Id:           "MODULE_ORDER",
		Name:         "Module Display Order Configuration",
		Desc:         "Configuration for module display sequence",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		DefaultValue: "{\"mw\":0,\"applet\":1,\"corpus\":2,\"knowledge\":3}",
	}
	DisablePasswdUpdateButtonEn = &pb.DynamicParam{
		Id:           "DISABLE_PASSWD_UPDATE_BUTTON",
		Name:         "Disable password update button",
		Desc:         "Disables the update password button when enabled",
		Type:         pb.DynamicParam_TYPE_SWITCH,
		DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
		DefaultValue: "false",
	}
	DisableLogoutButtonEn = &pb.DynamicParam{
		Id:           "DISABLE_LOGOUT_BUTTON",
		Name:         "Disable logout button",
		Desc:         "Disable logout button when enabled",
		Type:         pb.DynamicParam_TYPE_SWITCH,
		DataType:     pb.DynamicParam_DATA_TYPE_BOOLEAN,
		DefaultValue: "false",
	}
	PermissionProjectActionsEn = &pb.DynamicParam{
		Id:           "PERMISSION_PROJECT_ACTIONS",
		Name:         "空间管理权限点勾选的 actions",
		Desc:         "超管对管理员赋予的空间管理权限json格式",
		Type:         pb.DynamicParam_TYPE_INPUT,
		DataType:     pb.DynamicParam_DATA_TYPE_STRING,
		DefaultValue: "false",
		Hidden:       true,
	}
)
