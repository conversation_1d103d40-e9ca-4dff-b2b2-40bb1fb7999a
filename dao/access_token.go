package dao

import (
	"fmt"
	"gorm.io/gorm"
)

type AccessTokenType string

const (
	AccessTokenTypeInvalid = "invalid"
	AccessTokenTypeUsing   = "using"
)

func CreateAccessToken(tx *gorm.DB, token *AccessToken) error {
	return tx.Create(token).Error
}
func DeleteAccessTokenByID(tx *gorm.DB, userID string) error {
	if err := tx.Where("id = ?", userID).Delete(&AccessToken{}).Error; err != nil {
		return err
	}
	return nil
}
func DeleteAccessTokenByUsername(tx *gorm.DB, username string) error {
	if err := tx.Where("username = ?", username).Delete(&AccessToken{}).Error; err != nil {
		return err
	}
	return nil
}
func UpdateAccessToken(tx *gorm.DB, token *AccessToken) error {
	if err := tx.Model(&AccessToken{ID: token.ID}).Updates(token).Error; err != nil {
		return err
	}
	return nil
}
func GetAccessTokenByID(tx *gorm.DB, tokenID string) (*AccessToken, error) {
	token := new(AccessToken)
	if err := tx.Where("id = ?", tokenID).Find(token).Error; err != nil {
		return nil, err
	}
	return token, nil
}
func GetAccessTokenByUsername(tx *gorm.DB, username string) ([]*AccessToken, error) {
	tokens := make([]*AccessToken, 0)
	if err := tx.Where("username = ?", username).Find(&tokens).Error; err != nil {
		return nil, err
	}
	return tokens, nil
}
func GetAccessTokenByToken(tx *gorm.DB, token string) (*AccessToken, error) {
	tokens := make([]*AccessToken, 0)
	if err := tx.Where("token = ?", token).Find(&tokens).Error; err != nil {
		return nil, err
	}
	if len(tokens) < 1 {
		return nil, fmt.Errorf("token not found")
	}
	return tokens[0], nil
}

type AccessToken struct {
	ID         string `json:"id"`
	Creator    string `json:"creator"`
	CreateTime string `json:"createTime"` // 2022-06-08T06:22:03.516Z
	Desc       string `json:"desc"`
	Username   string `json:"username"`
	Status     string `json:"status"`
	Token      string `json:"token"`
}

func (at AccessToken) Valid() bool {
	return at.Status == AccessTokenTypeUsing
}
