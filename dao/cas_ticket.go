package dao

import (
	"fmt"
	"math/rand"
	"time"

	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/utils"
)

func CreateServiceTicket(tx *gorm.DB, st *ServiceTicket) error {
	return tx.Create(st).Error
}
func GetServiceTicket(tx *gorm.DB, ticket string) (*ServiceTicket, error) {
	st := new(ServiceTicket)
	if err := tx.Where("ticket = ?", ticket).Find(st).Error; err != nil {
		return nil, err
	}
	return st, nil
}
func DeleteServiceTicket(tx *gorm.DB, ticket string) error {
	if err := tx.Where("ticket = ?", ticket).Delete(&ServiceTicket{}).Error; err != nil {
		return err
	}
	return nil
}

func NewServiceTicket(tgt string, svc string, sso bool) *ServiceTicket {
	return &ServiceTicket{
		Ticket:   generateTicket("ST", 32),
		Service:  svc,
		Tgt:      tgt,
		Validity: time.Unix(time.Now().Unix()+int64(conf.C.Auth.CAS.TicketValidity.ServiceTicket), 0),
		FromSso:  sso,
	}
}

type ServiceTicket struct {
	gorm.Model

	Ticket   string    `json:"ticket" gorm:"column:ticket;type:varchar(64);unique;"`
	Service  string    `json:"service" gorm:"column:service;type:text;"`
	Tgt      string    `json:"tgt" gorm:"column:tgt;type:text;"`
	Validity time.Time `json:"validity" gorm:"column:validity"`
	FromSso  bool      `json:"from_sso" gorm:"column:from_sso"`
}

func NewTicketGrantingTicket(username string, host string) *TicketGrantingTicket {
	return &TicketGrantingTicket{
		Ticket:   generateTicket("TGT", 32),
		Username: username,
		Host:     utils.IPAddr(host),
		Validity: time.Unix(time.Now().Unix()+int64(conf.C.Auth.CAS.TicketValidity.ServiceTicket), 0),
	}
}

func CreateTicketGrantingTicket(tx *gorm.DB, tgt *TicketGrantingTicket) error {
	return tx.Create(tgt).Error
}
func GetTicketGrantingTicket(tx *gorm.DB, ticket string) (*TicketGrantingTicket, error) {
	st := new(TicketGrantingTicket)
	if err := tx.Where("ticket = ?", ticket).Find(st).Error; err != nil {
		return nil, err
	}
	return st, nil
}
func DeleteTicketGrantingTicket(tx *gorm.DB, ticket string) error {
	if err := tx.Where("ticket = ?", ticket).Delete(&TicketGrantingTicket{}).Error; err != nil {
		return err
	}
	return nil
}

func DeleteTicketGrantingTicketByUsername(tx *gorm.DB, username string) error {
	if err := tx.Where("username = ? and deleted_at is null", username).Delete(&TicketGrantingTicket{}).Error; err != nil {
		return err
	}
	return nil
}

type TicketGrantingTicket struct {
	gorm.Model

	Ticket   string    `json:"ticket" gorm:"column:ticket;type:varchar(64);unique;"`
	Username string    `json:"username" gorm:"column:username"`
	Host     string    `json:"host" gorm:"column:host"`
	Validity time.Time `json:"validity" gorm:"column:validity"`
}

var Runes = []rune("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789")

func generateTicket(prefix string, length int) string {
	ticket := make([]rune, length)
	for i := range ticket {
		ticket[i] = Runes[rand.Intn(len(Runes))]
	}
	return fmt.Sprintf("%s-%s", prefix, string(ticket))
}
