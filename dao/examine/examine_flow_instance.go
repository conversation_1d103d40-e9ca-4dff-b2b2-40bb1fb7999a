package examine

import (
	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type ExamineFlowInstanceDao interface {
	CreateInstance(tx *gorm.DB, instance *models.ExamineFlowInstance) error
	UpdateInstance(tx *gorm.DB, instance *models.ExamineFlowInstance) error
	ListInstances(tx *gorm.DB, projectID string) ([]*models.ExamineFlowInstance, error)
	DeleteInstances(tx *gorm.DB, ids ...uint) error
	GetInstance(tx *gorm.DB, id uint) (*models.ExamineFlowInstance, error)
}

func NewExamineFlowInstanceDao() ExamineFlowInstanceDao {
	return &examineFlowInstanceDao{}
}

type examineFlowInstanceDao struct {
}

func (e examineFlowInstanceDao) GetInstance(tx *gorm.DB, id uint) (*models.ExamineFlowInstance, error) {
	var instance models.ExamineFlowInstance
	result := tx.First(&instance, id)
	if err := result.Error; err != nil {
		return nil, err
	}
	return &instance, nil
}

func (e examineFlowInstanceDao) CreateInstance(tx *gorm.DB, instance *models.ExamineFlowInstance) error {
	result := tx.Create(instance)
	if err := result.Error; err != nil {
		return err
	}
	return nil
}

func (e examineFlowInstanceDao) UpdateInstance(tx *gorm.DB, instance *models.ExamineFlowInstance) error {
	result := tx.Model(instance).Updates(instance)
	if err := result.Error; err != nil {
		return err
	}
	return nil
}

func (e examineFlowInstanceDao) ListInstances(tx *gorm.DB, projectID string) ([]*models.ExamineFlowInstance, error) {
	instances := make([]*models.ExamineFlowInstance, 0)
	condition := tx
	if projectID != "" {
		condition = tx.Where(&models.ExamineFlowInstance{ProjectId: projectID})
	}
	result := condition.Find(&instances)
	if err := result.Error; err != nil {
		return nil, err
	}
	return instances, nil
}

func (e examineFlowInstanceDao) DeleteInstances(tx *gorm.DB, ids ...uint) error {
	var instances []models.ExamineFlowInstance
	result := tx.Delete(instances, ids)
	if err := result.Error; err != nil {
		return err
	}
	return nil
}
