package examine

import (
	"gorm.io/gorm"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type ExamineRecordDao interface {
	CreateRecord(tx *gorm.DB, record *models.ExamineRecord) error
	ListRecordsByInstanceID(tx *gorm.DB, id uint) ([]*models.ExamineRecord, error)
	DeleteRecords(tx *gorm.DB, ids ...uint) error
	ListRecordsByUser(tx *gorm.DB, user string) ([]*models.ExamineRecord, error)
}

func NewExamineRecordDao() ExamineRecordDao {
	return &examineRecordDao{}
}

type examineRecordDao struct{}

func (e examineRecordDao) ListRecordsByUser(tx *gorm.DB, user string) ([]*models.ExamineRecord, error) {
	records := make([]*models.ExamineRecord, 0)
	result := tx.Where(&models.ExamineRecord{Creator: user}).Find(&records)
	if err := result.Error; err != nil {
		return nil, err
	}
	return records, nil
}

func (e examineRecordDao) DeleteRecords(tx *gorm.DB, ids ...uint) error {
	var records []models.ExamineRecord
	result := tx.Delete(records, ids)
	if err := result.Error; err != nil {
		return err
	}
	return nil
}

func (e examineRecordDao) CreateRecord(tx *gorm.DB, record *models.ExamineRecord) error {
	result := tx.Create(record)
	if err := result.Error; err != nil {
		return err
	}
	return nil
}

func (e examineRecordDao) ListRecordsByInstanceID(tx *gorm.DB, id uint) ([]*models.ExamineRecord, error) {
	records := make([]*models.ExamineRecord, 0)
	result := tx.Where(&models.ExamineRecord{InstanceID: id}).Find(&records)
	if err := result.Error; err != nil {
		return nil, err
	}
	return records, nil
}
