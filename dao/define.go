package dao

import (
	"gorm.io/gorm"

	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/database/migration"
	"transwarp.io/applied-ai/aiot/vision-std/database/mysql"
	"transwarp.io/applied-ai/aiot/vision-std/database/sqlite"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	c "transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao/rbac"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/models/alerting"
)

type DBType string

const (
	MySQL  DBType = "mysql"
	SQLite DBType = "sqlite"

	TableColumnCreatedAt string = "created_at"

	OrderASC       string = "ASC"
	OrderDESC      string = "DESC"
	DefaultOrderBy string = TableColumnCreatedAt
)

var gdb *gorm.DB

// GetDB return the global db var with a new session (why? )
// NOTICE: ConnectDB must be called before you getting db
func GetDB() *gorm.DB {
	return gdb.Session(&gorm.Session{NewDB: true})
}

func ConnectDB(cfg *c.DBConfig) (db *gorm.DB, err error) {
	switch DBType(cfg.Type) {
	case MySQL:
		db, err = ConnectMySQL(cfg.MySQL)
		if err != nil {
			return nil, err
		}
		stdlog.Infof("DB - MySQL: %+v", cfg.MySQL)
	default:
		db, err = ConnectSQLite(cfg.SQLite)
		if err != nil {
			return nil, err
		}
		stdlog.Infof("DB - SQLite: %+v", cfg.SQLite)
	}

	db.Debug()
	migs := []any{
		&User{},
		&Role{},
		&ServiceTicket{},
		&TicketGrantingTicket{},
		&AccessToken{},
		&models.Group{},
		&models.UserGroup{},
		&models.Role{},
		&models.UserRole{},
		&models.Permission{},
		&models.RolePermission{},
		&models.Project{},
		&models.ProjectMember{},
		&models.ProjectCategory{},
		&models.Tenant{},
		&Article{},
		&PortalInfo{},
		&models.ExamineFlowInstance{},
		&models.ExamineRecord{},
		&models.CustomLogo{},
		&models.CustomNotice{},
		&models.CustomConfig{},
		&models.CustomIcon{},
		&models.AlertReceiverConfig{},
		&alerting.AlertRule{},
		&rbac.RbacObject{},
		&rbac.RbacPolicy{},
	}
	for _, mig := range migs {
		if err = db.AutoMigrate(mig); err != nil {
			stdlog.WithError(err).Errorf("auto migrage of %T", mig)
			return nil, err
		}
	}

	err = migration.UpdateDB(db, 0)
	if err != nil {
		return db, err
	}
	gdb = db
	return db, nil
}

func ConnectMySQL(cfg *conf.MysqlConfig) (*gorm.DB, error) {
	return mysql.CreateGorm2DB(*cfg), nil
}

func ConnectSQLite(cfg *conf.SqliteConfig) (*gorm.DB, error) {
	return sqlite.CreateFileGorm2DB(cfg.File, cfg.BusyTimeoutMs)
}

func Recover(tx *gorm.DB) {
	if r := recover(); r != nil {
		tx.Rollback()
	}
}
