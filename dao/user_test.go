package dao

import (
	"fmt"
	"testing"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/conf"
)

func TestCreateUser(t *testing.T) {
	db, err := ConnectSQLite(&conf.SqliteConfig{
		File: "./data/sqlite.db",
		BusyTimeoutMs: 5000,
	})
	if err != nil {
		t.<PERSON>al(err)
	}
	db.AutoMigrate(&AccessToken{})
	for i := 1; i <= 500; i++ {
		token := &AccessToken{
			ID:         fmt.Sprintf("token-%d", i),
			Creator:    "thinger",
			CreateTime: time.Now().String(),
			Desc:       "token",
			Username:   "thinger",
			Status:     "using",
			Token:      "123",
		}
		go func() {
			if err = CreateAccessToken(db, token); err != nil {
				t.Fatal(err)
			}

			token.Status = "expired"
			if err = UpdateAccessToken(db, token); err != nil {
				t.<PERSON><PERSON>(err)
			}
		}()
	}

	time.Sleep(5 * time.Minute)
}
