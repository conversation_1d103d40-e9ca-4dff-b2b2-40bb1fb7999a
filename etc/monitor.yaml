monitor:
  metrics: # 指标定义
    # invoke metrics
    - id: M_SVC_TOTAL_VISIT_COUNT # 指标id,需要保持唯一
      name: 总访问量 # 指标名称
      description: 服务的累计总访问次数 # 指标的详细描述
      type: counter # 指标类型; gauge:可增可减，表示瞬时值; counter:单调递增，适用于记录累计值; reducer:聚合时间段的归约值
      query:
        expr: llmops_svc_visit_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"}
        legendFormat: "总访问量: {{ref_version_id}}-{{ref_pod_short_name}}"
    - id: M_SVC_VISIT_COUNT_INCREMENT
      name: 访问次数增量
      description: 服务的访问次数增量
      type: reducer
      query:
        expr: llmops_svc_visit_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} - llmops_svc_visit_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} offset {{.Step}}
        legendFormat: "访问次数增量: {{ref_version_id}}-{{ref_pod_short_name}}"
    - id: M_SVC_AVG_DURATION
      name: 平均响应时间
      description: 服务的平均响应时间
      type: reducer
      query:
        expr: (llmops_svc_duration_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} - llmops_svc_duration_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} offset {{.Step}}) / (llmops_svc_visit_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} - llmops_svc_visit_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} offset {{.Step}})
        legendFormat: "平均响应时间: {{ref_version_id}}-{{ref_pod_short_name}}"
    - id: M_SVC_AVG_TIME_TO_FIRST_TOKEN
      name: 平均首字延时
      description: 服务的平均首字延时
      type: reducer
      query:
        expr: (llmops_svc_first_token_time_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} - llmops_svc_first_token_time_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} offset {{.Step}}) / (llmops_svc_visit_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} - llmops_svc_visit_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} offset {{.Step}})
        legendFormat: "平均首字延时: {{ref_version_id}}-{{ref_pod_short_name}}"
    - id: M_SVC_AVG_INPUT_TOKENS
      name: 平均输入token数
      description: 服务的平均输入token数量
      type: reducer
      query:
        expr: (llmops_svc_input_token_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} - llmops_svc_input_token_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} offset {{.Step}}) / (llmops_svc_visit_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} - llmops_svc_visit_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} offset {{.Step}})
        legendFormat: "平均输入token数: {{ref_version_id}}-{{ref_pod_short_name}}"
    - id: M_SVC_AVG_OUTPUT_TOKENS
      name: 平均输出token数
      description: 服务的平均输出token数量
      type: reducer
      query:
        expr: (llmops_svc_output_token_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} - llmops_svc_output_token_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} offset {{.Step}}) / (llmops_svc_visit_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} - llmops_svc_visit_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} offset {{.Step}})
        legendFormat: "平均输出token数: {{ref_version_id}}-{{ref_pod_short_name}}"
    - id: M_SVC_TOTAL_INPUT_TOKENS
      name: 总输入token数
      description: 服务的累计总输入token数量
      type: counter
      query:
        expr: llmops_svc_input_token_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"}
        legendFormat: "总输入token数: {{ref_version_id}}-{{ref_pod_short_name}}"
    - id: M_SVC_TOTAL_OUTPUT_TOKENS
      name: 总输出token数
      description: 服务的累计总输出token数量
      type: counter
      query:
        expr: llmops_svc_output_token_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"}
        legendFormat: "总输出token数: {{ref_version_id}}-{{ref_pod_short_name}}"
    # device metrics
    - id: M_SVC_AVG_CPU_USAGE_PERC
      name: CPU平均使用率
      description: 服务的CPU使用率(在聚合时间段内平均)
      type: reducer
      query:
        expr: 100 * rate(llmops_container_cpu_usage_seconds_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"}[{{.Step}}])
        legendFormat: "CPU平均使用率: {{ref_version_id}}-{{ref_pod_short_name}}"
    - id: M_SVC_MEMORY_USAGE
      name: 内存使用量(MB)
      description: 服务的内存使用量(MB)
      type: gauge
      query:
        expr: llmops_container_memory_working_set_bytes{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} / 1048576
        legendFormat: "内存使用量(MB): {{ref_version_id}}-{{ref_pod_short_name}}"
    - id: M_SVC_VGPU_CORE_USED
      name: 算力使用率
      description: 服务的算力使用率(按Pod统计)
      type: gauge
      query:
        expr: sum(llmops_hami_container_core_used{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"}) by (ref_version_id,ref_pod_short_name)
        legendFormat: "算力使用率: {{ref_version_id}}-{{ref_pod_short_name}}"
    - id: M_SVC_VGPU_MEMORY_USED
      name: 显存使用量(MB)
      description: 服务的显存使用量(MB)(按Pod统计)
      type: gauge
      query:
        expr: sum(llmops_hami_container_memory_used{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"}) by (ref_version_id,ref_pod_short_name)
        legendFormat: "显存使用量(MB): {{ref_version_id}}-{{ref_pod_short_name}}"
    - id: M_SVC_VGPU_MEMORY_ALLOCATED
      name: 已分配显存量(MB)
      description: 服务已分配的显存量(MB)
      type: gauge
      disable_alerting: True
      query:
        expr: sum(llmops_hami_container_vmemory_allocated{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"}) by (ref_version_id,ref_pod_short_name)
        legendFormat: "已分配显存量(MB): {{ref_version_id}}-{{ref_pod_short_name}}"
    - id: M_SVC_VGPU_CORE_ALLOCATED
      name: 已分配算力
      description: 服务已分配的算力
      type: gauge
      disable_alerting: True
      query:
        expr: sum(llmops_hami_container_vcore_allocated{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"}) by (ref_version_id,ref_pod_short_name)
        legendFormat: "已分配算力: {{ref_version_id}}-{{ref_pod_short_name}}"
    - id: M_SVC_TOTAL_KNOW_DOC_COUNT
      name: 召回文档总量
      description: 服务的召回文档总量
      type: counter
      query:
        expr: sum(llmops_know_doc_count{ref_id=~"{{.ServiceId}}", namespace=~"{{.Namespace}}"})
        legendFormat: "召回文档总量: {{ref_id}}"

    - id: M_SVC_HEALTH_DEGREE
      name: 健康度
      description: 总分100,由50%可用性、30%服务性能、20%资源使用情况构成. 可用性：聚合时间段内的服务请求成功率; 服务性能：暂未评估，保持满分; 资源：显存使用率(占分配量)低于85%时满分，85-100按比例扣分直至0
      type: reducer
      query:
        expr: (50 * ((sum(llmops_svc_visit_succeed_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} - llmops_svc_visit_succeed_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} offset {{.Step}}) / sum(llmops_svc_visit_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} - llmops_svc_visit_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} offset {{.Step}})) and (sum(llmops_svc_visit_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} - llmops_svc_visit_count_total{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"} offset {{.Step}})!= 0) or on() vector(1))) + 30 + min(clamp_min( ( (avg(max_over_time(llmops_hami_container_memory_util{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"}[{{.Step}}])) by (ref_id,ref_version_id) <= bool 85) * 20 + (avg(max_over_time(llmops_hami_container_memory_util{ref_id="{{.ServiceId}}", ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"}[{{.Step}}])) by (ref_id,ref_version_id) > bool 85) * (20 * (100 - avg(max_over_time(llmops_hami_container_memory_util{ref_id="{{.ServiceId}}",ref_version_id=~"{{.VersionIds}}", ref_type="service", namespace=~"{{.Namespace}}"}[{{.Step}}])) by (ref_id,ref_version_id)) / 15) ), 0 ) or vector(20))
        legendFormat: "健康度: {{ref_version_id}}"
    - id: M_GPU_CARD_TOTAL
      name: gpu算力总卡数
      description: gpu资源概述-算力卡数总量
      type: counter
      query:
        expr: count (sum by (deviceuuid, devicetype) (hami_vgpu_count{node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"}))
        legendFormat: "算力卡数总量"

    - id: M_GPU_CARD_USED
      name: gpu算力已使用卡数
      description: gpu资源概述-算力卡数已使用数量
      type: counter
      query:
        expr: count (sum by (deviceuuid, devicetype) (hami_container_vgpu_allocated{namespace_name=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"}))
        legendFormat: "算力卡数已使用数量"

    # 算力满载卡数（核心和显存均达上限）
    - id: M_GPU_FULLY_LOADED
      name: gpu算力满载卡数
      description: gpu资源概述-算力满载卡数
      type: counter
      query:
        expr: count( ( (sum by (deviceuuid) (hami_container_vcore_allocated{namespace_name=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"})== on(deviceuuid) group_left() sum by (deviceuuid) (hami_core_size{provider=~"{{.Provider}}", namespace=~"{{.Namespace}}"}))  and (sum by (deviceuuid) (hami_container_vmemory_allocated{namespace_name=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"}) == on(deviceuuid) group_left() sum by (deviceuuid) (hami_vmemory_size{provider=~"{{.Provider}}", namespace=~"{{.Namespace}}"}) ) )  )
        legendFormat: "算力满载卡数"

    - id: M_GPU_PARTIALLY_LOAD
      name: gpu算力部分负载卡数
      description: gpu资源概述-部分负载
      type: counter
      query:
        expr: count(( (sum by (deviceuuid) (hami_container_vcore_allocated{namespace_name=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"}) < on(deviceuuid) group_left() sum by (deviceuuid) (hami_core_size{provider=~"{{.Provider}}", namespace=~"{{.Namespace}}"}))and(sum by (deviceuuid) (hami_container_vmemory_allocated{namespace_name=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"})  < on(deviceuuid) group_left() sum by (deviceuuid) (hami_vmemory_size{provider=~"{{.Provider}}", namespace=~"{{.Namespace}}"})  ) ) )
        legendFormat: "算力部分负载卡数"

    - id: M_GPU_RESOURCE_BOTTLENECK
      name: gpu算力资源瓶颈卡数
      description: gpu资源概述-资源瓶颈卡数
      type: counter
      query:
        expr: count( (  ( (sum by (deviceuuid) (hami_container_vcore_allocated{namespace_name=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"}) < on(deviceuuid) group_left() sum by (deviceuuid) (hami_core_size{provider=~"{{.Provider}}", namespace=~"{{.Namespace}}"}) ) and (sum by (deviceuuid) (hami_container_vmemory_allocated{namespace_name=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"}) == on(deviceuuid) group_left() sum by (deviceuuid) (hami_vmemory_size{provider=~"{{.Provider}}", namespace=~"{{.Namespace}}"}) )) or( (sum by (deviceuuid) (hami_container_vcore_allocated{namespace_name=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"}) == on(deviceuuid) group_left() sum by (deviceuuid) (hami_core_size{provider=~"{{.Provider}}", namespace=~"{{.Namespace}}"})) and (sum by (deviceuuid) (hami_container_vmemory_allocated{namespace_name=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"}) < on(deviceuuid) group_left() sum by (deviceuuid) (hami_vmemory_size{provider=~"{{.Provider}}", namespace=~"{{.Namespace}}"}))) ) )
        legendFormat: "算力资源瓶颈卡数"

    - id: M_GPU_RESOURCE_IDLE
      name: gpu算力资源空闲卡数
      description: gpu资源概述-空闲卡数
      type: counter
      query:
        expr: count(sum by (deviceuuid) (hami_core_size) unless on(deviceuuid) sum by (deviceuuid) (hami_container_vcore_allocated))
        legendFormat: "算力资源空闲卡数"

    - id: M_GPU_VCORE_USAGE
      name: gpu算力已使用资源
      description: gpu资源概述-已使用资源
      type: counter
      query:
        expr: sum(clamp_max(llmops_hami_container_core_used{ref_pod_namespace=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}",deviceuuid=~"{{.DeviceUUID}}", namespace=~"{{.Namespace}}"},100))
        legendFormat: "算力资源已使用资源"

    - id: M_GPU_VCORE_ALLOCATED
      name: gpu算力已分配资源
      description: gpu资源概述-已分配资源
      type: counter
      query:
        expr: sum(llmops_hami_container_vcore_allocated{ref_pod_namespace=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"})
        legendFormat: "算力资源已分配资源"

    - id: M_GPU_VCORE_TOTAL
      name: gpu算力资源总量
      description: gpu资源概述-总量
      type: counter
      query:
        expr: sum(hami_core_size{node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"})
        legendFormat: "算力资源总量"

    - id: M_GPU_MEMORY_USAGE
      name: gpu显存已使用资源
      description: gpu显存资源概述-已使用资源
      type: counter
      query:
        expr: sum(llmops_hami_container_memory_used{ref_pod_namespace=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}",deviceuuid=~"{{.DeviceUUID}}", namespace=~"{{.Namespace}}"}) / 1024
        legendFormat: "显存资源已使用资源"

    - id: M_GPU_MEMORY_ALLOCATED
      name: gpu显存已分配资源
      description: gpu显存资源概述-已分配资源
      type: counter
      query:
        expr: sum(llmops_hami_container_vmemory_allocated{ref_pod_namespace=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"}) / 1024
        legendFormat: "显存资源已分配资源"

    - id: M_GPU_MEMORY_TOTAL
      name: gpu显存资源总量
      description: gpu显存资源概述-总量
      type: counter
      query:
        expr: sum(hami_vmemory_size{node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"}) / 1024
        legendFormat: "显存资源总量"


    - id: M_GPU_TENANT_DISTRIBUTION
      name: gpu算力按照租户分布
      description: gpu算力按照租户分布
      type: counter
      query:
        expr: sum by(ref_pod_namespace) (llmops_hami_container_vcore_allocated{ref_pod_namespace=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"})
        legendFormat: "空间: {{ref_pod_namespace}}"


    - id: M_GPU_NODE_DISTRIBUTION
      name: gpu算力按照节点分布
      description: gpu算力按照节点分布
      type: counter
      query:
        expr: sum by(node) (llmops_hami_container_vcore_allocated{ref_pod_namespace=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"})
      legendFormat: "节点: {{node}}"

    - id: M_GPU_CARD_DISTRIBUTION
      name: gpu算力按照卡类型分布
      description: gpu算力按照卡类型分布
      type: counter
      query:
        expr: sum by(provider, devicetype) (llmops_hami_container_vcore_allocated{ref_pod_namespace=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}", namespace=~"{{.Namespace}}"})
        legendFormat: "卡类型: {{provider}}-{{devicetype}}"

    - id: M_GPU_TREND_VCORE
      name: 算力使用率
      description: 算力使用率
      type: gauge
      query:
        expr: round(clamp_max(sum by (deviceuuid,node)  (llmops_hami_container_core_used{ref_pod_namespace=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}",deviceuuid=~"{{.DeviceUUID}}", namespace=~"{{.Namespace}}"}),100),0.01)
        legendFormat: "{{node}}_{{deviceuuid}}"

    - id: M_GPU_TREND_MEMORY
      name: 显存使用量
      description: 显存使用量
      type: gauge
      query:
        expr: round(sum by (deviceuuid,node)  (llmops_hami_container_memory_used{ref_pod_namespace=~"{{.NamespaceName}}",node=~"{{.Node}}",provider=~"{{.Provider}}",devicetype=~"{{.DeviceType}}",deviceuuid=~"{{.DeviceUUID}}", namespace=~"{{.Namespace}}"}),0.01)
        legendFormat: "{{node}}_{{deviceuuid}}"


    - id: M_BASE_CPU_TOTAL
      name: 基础资源cpu总核数
      description: 基础资源cpu总核数
      type: gauge
      query:
        expr: sum(llmops_tenant_cpu_quota{exported_namespace=~"{{.NamespaceName}}" , namespace=~"{{.Namespace}}"})
        legendFormat: "基础资源cpu总核数: {{exported_namespace}}"

    - id: M_BASE_CPU_USAGE
      name: 基础资源cpu使用量
      description: 基础资源cpu使用量
      type: gauge
      query:
        expr: sum(llmops_tenant_cpu_usage{exported_namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"})
        legendFormat: "基础资源cpu使用量: {{exported_namespace}}"

    - id: M_BASE_MEMORY_TOTAL
      name: 基础资源内存总数
      description: 基础资源内存总数
      type: gauge
      query:
        expr: sum(llmops_tenant_memory_quota{exported_namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"})
        legendFormat: "基础资源内存总数: {{exported_namespace}}"

    - id: M_BASE_MEMORY_USAGE
      name: 基础资源内存使用量
      description: 基础资源内存使用量
      type: gauge
      query:
        expr: sum(llmops_tenant_memory_usage{exported_namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"})
        legendFormat: "基础资源内存使用量: {{exported_namespace}}"

    - id: M_BASE_DISK_TOTAL
      name: 基础资源磁盘总数
      description: 基础资源磁盘总数
      type: gauge
      query:
        expr: sum(llmops_tenant_disk_quota{exported_namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"})
        legendFormat: "基础资源磁盘总数: {{exported_namespace}}"

    - id: M_BASE_DISK_USAGE
      name: 基础资源磁盘使用量
      description: 基础资源磁盘使用量
      type: gauge
      query:
        expr: sum(llmops_tenant_disk_usage{exported_namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"})
        legendFormat: "基础资源磁盘使用量: {{exported_namespace}}"


    - id: M_BASE_TREND_DISK
      name: 基础资源磁盘占比
      description: 基础资源磁盘占用比
      type: gauge
      query:
        expr: round(llmops_tenant_disk_usage{exported_namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"} / llmops_tenant_disk_quota{exported_namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"} * 100,0.01)
        legendFormat: "{{exported_namespace}}"

    - id: M_BASE_TREND_MEMORY
      name: 基础资源内存占比
      description: 基础资源内存占用比
      type: gauge
      query:
        expr: round(llmops_tenant_memory_usage{exported_namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"} / llmops_tenant_memory_quota{exported_namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"} * 100,0.01)
        legendFormat: "{{exported_namespace}}"

    - id: M_BASE_TREND_CPU
      name: 基础资源CPU占比
      description: 基础资源CPU占用比
      type: gauge
      query:
        expr: round(llmops_tenant_cpu_usage{exported_namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"} / llmops_tenant_cpu_quota{exported_namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"} * 100,0.01)
        legendFormat: "{{exported_namespace}}"


    - id: M_BASE_RANKING_VCORE
      name: 算力使用排行
      description: 算力使用排行
      type: gauge
      query:
        expr: topk({{.TopK}},sum by (ref_pod_namespace) (clamp_max(llmops_hami_container_vcore_allocated{ref_pod_namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"},100)))
        legendFormat: "{{ref_pod_namespace}}"

    - id: M_BASE_RANKING_CPU
      name: CPU使用排行
      description: CPU使用排行
      type: gauge
      query:
        expr: topk({{.TopK}}, sum by (exported_namespace) (llmops_tenant_cpu_usage{exported_namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"}))
        legendFormat: "{{exported_namespace}}"

    - id: M_BASE_RANKING_DISK
      name: 磁盘使用排行
      description: 磁盘使用排行
      type: gauge
      query:
        expr: topk({{.TopK}}, sum by (exported_namespace) (llmops_tenant_disk_quota{namespace=~"{{.Namespace}}",exported_namespace=~"{{.NamespaceName}}"}))
        legendFormat: "{{exported_namespace}}"

    - id: M_SERVICE_RANKING_VCORE
      name: 服务算力使用排行
      description: 服务算力使用排行
      type: gauge
      query:
        expr: topk({{.TopK}}, sum by (ref_pod_namespace) (clamp_max(llmops_hami_container_vcore_allocated{namespace=~"{{.Namespace}}", ref_type="service",ref_pod_namespace=~"{{.NamespaceName}}"},100)))
        legendFormat: "{{ref_pod_namespace}}"

    - id: M_RANKING_VMEMORY
      name: 显存使用排行
      description: 显存使用排行
      type: gauge
      query:
        expr: topk({{.TopK}}, sum by (ref_pod_namespace) (llmops_hami_container_vmemory_allocated{namespace=~"{{.Namespace}}", ref_type=~"{{.Service}}",ref_pod_namespace=~"{{.NamespaceName}}"})) / 1024
        legendFormat: "{{ref_pod_namespace}}"

    - id: M_SERVICE_RANKING_CPU
      name: 服务CPU使用排行
      description: 服务CPU使用排行
      type: gauge
      query:
        expr: topk({{.TopK}}, sum by (ref_pod_namespace) (llmops_container_spec_cpu_quota{namespace=~"{{.Namespace}}", ref_type="service",ref_pod_namespace=~"{{.NamespaceName}}"} )) / 100000
        legendFormat: "{{ref_pod_namespace}}"

    - id: M_SERVICE_RANKING_MEMORY
      name: 服务内存使用排行
      description: 服务内存使用排行
      type: gauge
      query:
        expr: topk({{.TopK}}, sum by (ref_pod_namespace) (llmops_container_spec_memory_limit_bytes{namespace=~"{{.Namespace}}", ref_type="service",ref_pod_namespace=~"{{.NamespaceName}}"})) / 1048576 / 1024
        legendFormat: "{{ref_pod_namespace}}"


    - id: M_GPU_USED_VCORE
      name: 算力使用率
      description: 算力使用率
      type: counter
      query:
        expr: avg(sum(hami_core_util{deviceuuid=~"{{.DeviceUUID}}", namespace=~"{{.Namespace}}"}) by (instance))
        legendFormat: "算力使用率: {{deviceuuid}}"

    - id: M_GPU_USED_VMEMORY
      name: 显存使用量
      description: 显存使用量
      type: counter
      query:
        expr: avg(sum(hami_memory_used{deviceuuid=~"{{.DeviceUUID}}", namespace=~"{{.Namespace}}"}) by (instance)) / 1024
        legendFormat: "显存使用量: {{deviceuuid}}"

    - id: M_GPU_ALLOCATED_VCORE
      name: 算力分配率
      description: 算力分配率
      type: counter
      query:
        expr: avg(sum(hami_container_vcore_allocated{deviceuuid=~"{{.DeviceUUID}}", namespace=~"{{.Namespace}}"}) by (instance))
        legendFormat: "算力分配率: {{deviceuuid}}"
    - id: M_GPU_ALLOCATED_VMEMORY
      name: 显存分配率
      description: 显存分配率
      type: counter
      query:
        expr: avg(sum(hami_container_vmemory_allocated{deviceuuid=~"{{.DeviceUUID}}", namespace=~"{{.Namespace}}"}) by (instance)) / 1024
        legendFormat: "显存分配率: {{deviceuuid}}"

    - id: M_SVC_TENANT_DISTRIBUTION
      name: 服务资源池分布
      description: 服务资源池分布
      type: counter
      query:
        expr: count by (ref_project_id, namespace) (llmops_svc_state{namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"})
        legendFormat: "{{ref_project_id}}"

    - id: M_SVC_COMPUTE_TYPE_DISTRIBUTION
      name: 算力类型分布
      description: 算力类型分布
      type: counter
      query:
        expr: count by (compute_type) (llmops_svc_state{namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"})
        legendFormat: "{{compute_type}}"

    - id: M_SVC_SOURCE_TYPE_DISTRIBUTION
      name: 服务类型分布
      description: 服务类型分布
      type: counter
      query:
        expr: count by (svc_type) (llmops_svc_state{namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"})
        legendFormat: "{{svc_type}}"

    - id: M_NPU_INFO
      name: NPU资源信息
      description: NPU资源信息
      type: counter
      query:
        expr: npu_chip_info_name
        legendFormat: "{{vdie_id}}"


    - id: M_SVC_SOURCE_STATUS_DISTRIBUTION
      name: 服务状态分布
      description: 服务状态分布
      type: counter
      query:
        expr: count_values("value", llmops_svc_state{namespace=~"{{.NamespaceName}}", namespace=~"{{.Namespace}}"})
        legendFormat: "{{value}}"

    - id: M_SVC_LIST
      name: 服务列表查询
      description: 服务列表查询
      type: guage
      query:
        expr: (llmops_svc_state{ref_name=~"{{.RefName}}", namespace=~"{{.Namespace}}"})  * on(ref_id,ref_name) group_right(ref_creator)  ( (llmops_hami_container_vcore_allocated{deviceuuid=~"{{.DeviceUUID}}", namespace=~"{{.Namespace}}"} > bool 0) * 1)
        legendFormat: "pod_name: {{ref_pod_name}}"

  panels: # 图表定义
    - id: P_SVC_VISIT_COUNT
      name: 总访问量
      metrics:
        - id: M_SVC_TOTAL_VISIT_COUNT
      unit: 次
      style: stackbar
    - id: P_SVC_VISIT_COUNT_INCREMENT
      name: 访问次数增量
      metrics:
        - id: M_SVC_VISIT_COUNT_INCREMENT
      unit: 次
      style: stackbar
    - id: P_SVC_AVG_DURATION
      name: 平均响应时间
      metrics:
        - id: M_SVC_AVG_DURATION
      unit: 毫秒(ms)
      style: line
    - id: P_SVC_AVG_TIME_TO_FIRST_TOKEN
      name: 平均首字延时
      metrics:
        - id: M_SVC_AVG_TIME_TO_FIRST_TOKEN
      unit: 毫秒(ms)
      style: line
    - id: P_SVC_AVG_INPUT_TOKENS
      name: 平均输入token数
      metrics:
        - id: M_SVC_AVG_INPUT_TOKENS
      unit: 个
      style: stackbar
    - id: P_SVC_AVG_OUTPUT_TOKENS
      name: 平均输出token数
      metrics:
        - id: M_SVC_AVG_OUTPUT_TOKENS
      unit: 个
      style: stackbar
    - id: P_SVC_TOTAL_INPUT_TOKENS
      name: 总输入token数
      metrics:
        - id: M_SVC_TOTAL_INPUT_TOKENS
      unit: 个
      style: stackbar
    - id: P_SVC_TOTAL_OUTPUT_TOKENS
      name: 总输出token数
      metrics:
        - id: M_SVC_TOTAL_OUTPUT_TOKENS
      unit: 个
      style: stackbar
    # device metrics panels
    - id: P_SVC_AVG_CPU_USAGE_PERC
      name: CPU平均使用率
      metrics:
        - id: M_SVC_AVG_CPU_USAGE_PERC
      unit: "%"
      style: line
    - id: P_SVC_MEMORY_USAGE
      name: 内存使用量(MB)
      metrics:
        - id: M_SVC_MEMORY_USAGE
      unit: MB
      style: line
    - id: P_SVC_VGPU_MEMORY_USED_AND_ALLOCATED
      name: 显存使用/分配(MB)
      metrics:
        - id: M_SVC_VGPU_MEMORY_USED
        - id: M_SVC_VGPU_MEMORY_ALLOCATED
      unit: MB
      style: line
    - id: P_SVC_VGPU_CORE_USED_AND_ALLOCATED
      name: 算力使用/分配
      metrics:
        - id: M_SVC_VGPU_CORE_USED
        - id: M_SVC_VGPU_CORE_ALLOCATED
      unit: 百分比(%)
      style: line
    - id: P_SVC_kNOW_DOC_COUNT
      name: 召回文档量
      metrics:
        - id: M_SVC_TOTAL_KNOW_DOC_COUNT
      unit: 个
      style: stackbar
    - id: P_SVC_HEALTH_DEGREE
      name: 健康度
      metrics:
        - id: M_SVC_HEALTH_DEGREE
      unit: 百分比(%)
      style: line
    - id: P_GPU_OVERVIEW
      name: GPU资源概述
      metrics:
        - id: M_GPU_CARD_TOTAL
        - id: M_GPU_CARD_USED
        - id: M_GPU_FULLY_LOADED
        - id: M_GPU_PARTIALLY_LOAD
        - id: M_GPU_RESOURCE_BOTTLENECK
        - id: M_GPU_RESOURCE_IDLE
        - id: M_GPU_VCORE_USAGE
        - id: M_GPU_VCORE_ALLOCATED
        - id: M_GPU_VCORE_TOTAL
        - id: M_GPU_MEMORY_USAGE
        - id: M_GPU_MEMORY_ALLOCATED
        - id: M_GPU_MEMORY_TOTAL
        - id: M_GPU_TENANT_DISTRIBUTION
        - id: M_GPU_NODE_DISTRIBUTION
        - id: M_GPU_CARD_DISTRIBUTION
      unit: 张
      style: bar
    - id: P_GPU_TREND
      name: GPU资源使用趋势
      metrics:
        - id: M_GPU_TREND_MEMORY
        - id: M_GPU_TREND_VCORE
      unit: MiB / 百分比(%)
      style: line

    - id: P_BASE_OVERVIEW
      name: 基础资源概览
      metrics:
        - id: M_BASE_CPU_TOTAL
        - id: M_BASE_CPU_USAGE
        - id: M_BASE_MEMORY_TOTAL
        - id: M_BASE_MEMORY_USAGE
        - id: M_BASE_DISK_TOTAL
        - id: M_BASE_DISK_USAGE
      unit: 个
      style: bar

    - id: P_BASE_TREND
      name: 基础资源趋势
      metrics:
        - id: M_BASE_TREND_DISK
        - id: M_BASE_TREND_MEMORY
        - id: M_BASE_TREND_CPU
      unit: MiB / 百分比(%)
      style: line

    - id: P_BASE_RANKING
      name: 基础资源排行
      metrics:
        - id: M_BASE_RANKING_VCORE
        - id: M_BASE_RANKING_CPU
        - id: M_BASE_RANKING_DISK
        - id: M_RANKING_VMEMORY
      unit: 个
      style: bar

    - id: P_SERVICE_RANKING
      name: 服务资源排行
      metrics:
        - id: M_SERVICE_RANKING_VCORE
        - id: M_RANKING_VMEMORY
        - id: M_SERVICE_RANKING_CPU
        - id: M_SERVICE_RANKING_MEMORY
      unit: GB
      style: bar

    - id: P_GPU_USAGE
      name: GPU资源使用情况
      metrics:
        - id: M_GPU_USED_VCORE
        - id: M_GPU_USED_VMEMORY
        - id: M_GPU_ALLOCATED_VCORE
        - id: M_GPU_ALLOCATED_VMEMORY
      unit: 个
      style: bar

    - id: P_SVC_OVERVIEW
      name: 服务资源概览
      metrics:
        - id: M_SVC_TENANT_DISTRIBUTION
        - id: M_SVC_COMPUTE_TYPE_DISTRIBUTION
        - id: M_SVC_SOURCE_TYPE_DISTRIBUTION
        - id: M_SVC_SOURCE_STATUS_DISTRIBUTION
      unit: 个
      style: bar

    - id: P_SVC_LIST
      name: 服务资源列表查询
      metrics:
        - id: M_SVC_LIST
      unit: 个
      style: bar

    - id: P_GPU_USED
      name: GPU资源已使用总量
      metrics:
        - id: M_GPU_MEMORY_USAGE
        - id: M_GPU_VCORE_USAGE
      unit: 个
      style: bar


    - id: P_NPU_INFO
      name: NPU资源信息
      metrics:
        - id: M_NPU_INFO
      unit: 个
      style: bar

  svc_monitor_config:
    default_panels: # 未指定的服务默认展示哪些图表
      - P_SVC_VISIT_COUNT
      - P_SVC_AVG_DURATION
    panels_by_source_type: # 根据 service_source_type 决定服务展示哪些图表
      SOURCE_TYPE_MODEL_CUBE:
        - P_SVC_VISIT_COUNT
        - P_SVC_VISIT_COUNT_INCREMENT
        - P_SVC_AVG_DURATION
        - P_SVC_AVG_TIME_TO_FIRST_TOKEN
        - P_SVC_AVG_INPUT_TOKENS
        - P_SVC_AVG_OUTPUT_TOKENS
        - P_SVC_TOTAL_INPUT_TOKENS
        - P_SVC_TOTAL_OUTPUT_TOKENS
        - P_SVC_HEALTH_DEGREE
      SOURCE_TYPE_KNOWLEDGE:
        - P_SVC_VISIT_COUNT
        - P_SVC_AVG_DURATION
        - P_SVC_kNOW_DOC_COUNT
      SOURCE_TYPE_REMOTE:
        - P_SVC_VISIT_COUNT
        - P_SVC_VISIT_COUNT_INCREMENT
        - P_SVC_AVG_DURATION
        - P_SVC_AVG_TIME_TO_FIRST_TOKEN
        - P_SVC_AVG_INPUT_TOKENS
        - P_SVC_AVG_OUTPUT_TOKENS
        - P_SVC_TOTAL_INPUT_TOKENS
        - P_SVC_TOTAL_OUTPUT_TOKENS
      SOURCE_TYPE_CUSTOM:
        - P_SVC_VISIT_COUNT
        - P_SVC_VISIT_COUNT_INCREMENT
        - P_SVC_AVG_DURATION
        - P_SVC_AVG_TIME_TO_FIRST_TOKEN
        - P_SVC_AVG_INPUT_TOKENS
        - P_SVC_AVG_OUTPUT_TOKENS
        - P_SVC_TOTAL_INPUT_TOKENS
        - P_SVC_TOTAL_OUTPUT_TOKENS
        - P_SVC_HEALTH_DEGREE
  dashboard_config:
    panels_by_tab:
      DASHBOARD_TAB_TYPE_DEVICE: # dashboard tab type
        - P_SVC_AVG_CPU_USAGE_PERC # panels
        - P_SVC_MEMORY_USAGE
        - P_SVC_VGPU_MEMORY_USED_AND_ALLOCATED
        - P_SVC_VGPU_CORE_USED_AND_ALLOCATED
      DASHBOARD_TAB_TYPE_DEVICE_WO_GPU:
        - P_SVC_AVG_CPU_USAGE_PERC
        - P_SVC_MEMORY_USAGE
  global_config:
    gpu_overview_panels:
      - P_GPU_OVERVIEW
    gpu_trend_panels:
      - P_GPU_TREND
    base_overview_panels:
      - P_BASE_OVERVIEW
    base_trend_panels:
      - P_BASE_TREND
    base_ranking_panels:
      - P_BASE_RANKING
    service_ranking_panels:
      - P_SERVICE_RANKING
    gpu_resource_usage_panels:
      - P_GPU_USAGE
    service_overview_panels:
      - P_SVC_OVERVIEW
    service_list_panels:
      - P_SVC_LIST
    gpu_used_panels:
      - P_GPU_USED
    npu_info_panels:
      - P_NPU_INFO


