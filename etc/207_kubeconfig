apiVersion: v1
clusters:
- cluster:
    certificate-authority: /srv/kubernetes/ca.pem
    server: https://172.17.120.207:6443
  name: kubernetes
contexts:
- context:
    cluster: kubernetes
    user: k8s
  name: kubelet-to-kubernetes
current-context: kubelet-to-kubernetes
kind: Config
preferences: {}
users:
- name: k8s
  user:
    client-certificate: /srv/kubernetes/admin.pem
    client-key: /srv/kubernetes/admin-key.pem