- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/family
  description: 创建标注集
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/annotation/family
  description: 批量删除标注集
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/family/test/cal
  description: 测试集切分数量计算, 不需要fid，直接传数量
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}
  description: 获取标注集
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/family/{fid}
  description: 修改标注集
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/annotation/family/{fid}
  description: 删除标注集
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/augmentation
  description: 获取标注集数据增强详情
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/family/{fid}/augmentation
  description: 开始标注集数据增强
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/family/{fid}/augmentation/stop
  description: 停止标注集数据增强
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/augmentation/zip
  description: 下载增强的标注集数据
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/family/{fid}/clone
  description: 克隆标注集到指定项目
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/clones/name
  description: 获取克隆的新标注集的默认名称
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/filter-obj
  description: 根据条件获取标注集下的标注对象列表
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/id-list
  description: 根据条件获取标注集下的标注对象id列表
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/labels
  description: 按照标注集id获取标签列表
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/members
  description: 获取标注集下可能的成员列表
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/obj/{oid}
  description: 根据标注集id和指定图片id获取标注图片
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/obj/{oid}/original
  description: 获取图片的原始数据（即图片数据, 经过base64编码）
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/set
  description: 获取标注集下所有标注任务
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/set/zip
  description: 导出标注集中的标注任务的标注结果（算法调用）
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/family/{fid}/set/{sid}/import
  description: 向指定目录上传图片集，分片上传，需要前端将文件分片
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/family/{fid}/set/{sid}/import/{md5}
  description: 合并上传的标注集分片
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/set/{sid}/{md5}/{chunk}
  description: 校验chunk
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/stats
  description: 获取标注集下标签的使用情况
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/test
  description: 下载测试集（算法调用）
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/family/{fid}/test/cal
  description: 测试集切分数量计算
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/family/{fid}/test/change
  description: 移入移出测试图片
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/test/list
  description: 提供前端已选测试集和未选测试集的列表
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/family/{fid}/test/split
  description: 测试集切分
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/family/{fid}/zip
  description: 导出标注集的标注结果
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/import/all
  description: 导入标注任务结果并创建标注任务
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/label
  description: 创建label，如猫，人脸，车等
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/annotation/label
  description: 批量删除label
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/label/group
  description: 更新标签组
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/label/group
  description: 创建标签组
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/annotation/label/group
  description: 批量删除标签组
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/annotation/label/group/{gid}
  description: 删除标签组
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/label/group/{groupId}
  description: 按照标签分组获取标签列表
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/label/groups
  description: 获取标签组列表
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/label/{lid}
  description: 获取指定label
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/label/{lid}
  description: 更新指定label
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/annotation/label/{lid}
  description: 删除指定label
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/label/{lid}/sets
  description: 根据标签id获取所有用到此标签的标注任务
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/labels
  description: 根据标签类别获取所有的label
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/labels/group
  description: 根据标签类别获取所有的label并分组
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set
  description: 获取所有标注任务
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set
  description: 创建标注任务（人工标注和智能标注）
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/annotation/set
  description: 批量删除标注任务
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/-/columns
  description: 获取所有文本标注任务的列名信息
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/-/template:execute
  description: 根据传的标注文件和prompt模板，生成新文件
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/-/versions
  description: 获取所有文本标注集的所有版本
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/-/versions:merge
  description: 根据标注集和版本，合并生成新文件
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/anno-task/{tid}/annotated-number
  description: 统计协同标注作业已经标注的图片数量
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/set/import
  description: 导入标注压缩包，分片上传，需要前端将文件分片
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/set/import/{md5}
  description: 合并上传的导入标注分片，并创建标注任务
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/import/{md5}/{chunk}
  description: 校验chunk
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/pages
  description: 获取标注集列表，分页排序
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/stats
  description: 统计指定标注任务中label使用情况
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/usages
  description: 获取标注集统计信息
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}
  description: 获取标注任务
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/annotation/set/{sid}
  description: 删除标注任务
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/anno-task/id-list
  description: 根据条件获取图片列表
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/anno-task/{tid}
  description: 获取协同标注作业
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/{sid}/anno-task/{tid}/confirm
  description: 批量审核剩余的图片（状态修改为 verified）
  perm_code: corpus.annotation.*
  perm_action: examine
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/{sid}/anno-task/{tid}/obj/{oid}/ignore
  description: 忽略标注此图片
  perm_code: corpus.annotation.*
  perm_action: annotation
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/{sid}/anno-task/{tid}/recovery
  description: 所有条目恢复默认状态（状态修改为 unconfirmed）
  perm_code: corpus.annotation.*
  perm_action: annotation
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/{sid}/anno-task/{tid}/refuse
  description: 批量退回剩余的图片（状态修改为 rejected）
  perm_code: corpus.annotation.*
  perm_action: examine
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/anno-tasks
  description: 获取标注任务下所有的协同标注作业
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/anno-tasks/{tid}/commit
  description: 批量提交剩余的图片（状态修改为 verifying）
  perm_code: corpus.annotation.*
  perm_action: annotation
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/{sid}/finish
  description: 完成标注任务
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/images/{file}/original
  description: 获取文本标注集中的图片原始数据
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/{sid}/instance/{instance}/import
  description: 从代码实例添加文件
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/members
  description: 获取可能的成员列表
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/annotation/set/{sid}/obj
  description: 批量删除标注对象
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/obj/{oid}
  description: 根据标注任务id和指定图片id获取标注图片
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/{sid}/obj/{oid}/label-anno
  description: 保存标注结果
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/obj/{oid}/original
  description: 获取图片的原始数据（即图片数据, 经过base64编码）
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/{sid}/obj/{oid}/pre-recognize/ocr
  description: OCR标注图片预识别，根据标注框的位置返回识别结果
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/{sid}/obj/{oid}/pre-recognize/seg
  description: 分割标注图片预识别，根据标注框的位置返回识别结果
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/objs
  description: 根据标注任务id和指定图片id数组获取标注图片列表
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/stats
  description: 统计指定标注任务中label使用情况
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/test
  description: 下载测试集（算法调用）
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/texts:export
  description: 导出数据集中的文本文件，获取文件流
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/versions
  description: 获取标注集的所有版本
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/{sid}/versions
  description: 创建一个新版本
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/set/{sid}/versions/{vid}
  description: 更新标注集中的版本，修改版本名称和描述
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/annotation/set/{sid}/versions/{vid}
  description: 删除标注集中的版本
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/{sid}/versions/{vid}/clone
  description: 克隆文本标注集的版本到指定项目，在指定项目下新建标注集或添加到指定标注集的新版本中
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/versions/{vid}/files
  description: 获取标注集中文件列表
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/annotation/set/{sid}/versions/{vid}/files
  description: 从数据集删除文本文件
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/versions/{vid}/files/all
  description: 获取标注集中文件列表
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/{sid}/versions/{vid}/files/{fid}
  description: 编辑文件
  perm_code: corpus.annotation.*
  perm_action: annotation
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/versions/{vid}/files/{id}/data
  description: 获取文本文件数据记录
  perm_code: corpus.annotation.*
  perm_action: annotation
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/{sid}/versions/{vid}/files:import
  description: 上传文件
  perm_code: corpus.annotation.*
  perm_action: annotation
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/set/{sid}/versions/{vid}/splicing
  description: 将文本标注集的版本和Prompt拼接，并将结果作为草稿版本保存到新的标注集中
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/zip
  description: 导出标注任务的标注结果
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/set/{sid}/{nth}/nth/obj
  description: 获取标注集第n张图片(n>=1)
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/sets/{sid}/data
  description: 向标注集添加数据，支持从标注任务/本地上传方式添加
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/tasks/edit-file/list
  description: 获取重构任务列表
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/tasks/jobs/objs/{objId}/details
  description: 获取标注作业和文件资产详情，不分页
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/tasks/jobs/objs/{tid}/pause
  description: 暂停标注，更新AnnotationTextObj的已耗时时间
  perm_code: corpus.annotation.*
  perm_action: annotation
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/tasks/jobs/objs/{tid}/start
  description: 开始标注，更新AnnotationTextObj的开始时间
  perm_code: corpus.annotation.*
  perm_action: annotation
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/tasks/pages
  description: 根据条件分页获取标注任务
  perm_code: corpus.annotation.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/tasks/{taskId}
  description: 更新标注任务
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/tasks/{taskId}/cancel
  description: 取消任务，更新任务的状态并通知
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/tasks/{taskId}/jobs/objs
  description: 批量更新标注对象的标注状态
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/tasks/{taskId}/jobs/{jid}
  description: 更新标注作业的标注人
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/tasks/{taskId}/jobs/{jid}/transfer
  description: 将当前标注作业下的标注对象转移给其他用户
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/tasks/{taskId}/members
  description: 获取指定标注任务配置的标注人员列表，如果存在用户组需要拍平返回
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/tasks/{taskId}/reviewers
  description: 更新AnnotationTaskReviewer的审核人
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/annotation/tasks/{tid}/jobs/{jid}/config
  description: 更新标注任务的配置
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/annotation/tasks/{tid}/jobs/{jid}/obj/{oid}/generate
  description: 自动生成文本标注，根据标注作业的prompt返回标注结果
  perm_code: corpus.annotation.*
  perm_action: annotation
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/tasks/{tid}/jobs/{jid}/obj:lock
  description: 锁定obj的output内容无法修改
  perm_code: corpus.annotation.*
  perm_action: annotation
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/tasks/{tid}/jobs/{jid}/obj:unlock
  description: 解锁obj的output内容
  perm_code: corpus.annotation.*
  perm_action: annotation
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/annotation/tasks/{tid}/jobs/{jid}/text-objs
  description: 根据条件获取文本标注记录列表，分页
  perm_code: corpus.annotation.*
  perm_action: annotation-manage
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/asset-attr
  description: 属性列表查看
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/assets/asset-attr
  description: 属性新增
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/asset-attr/{fid}
  description: 属性更新
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/assets/asset-attr/{fid}
  description: 属性删除
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/asset-trash
  description: 查询回收站列表
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/asset-trash
  description: 回收站恢复功能
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/asset-trash/info/{assetId}
  description: 回收站资产详情功能
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/assets/asset-trash/{fid}
  description: 根据资产id删除当前资产的回收站信息
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/asset/{assetId}/version/{versionId}
  description: 更新指定文件资产版本为最新版本
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/assets/metafile/check
  description: 文件资产元信息表校验
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/assets/stats
  description: 获得资产概览
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/daily-stats
  description: 获取资产统计信息
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/assets/directory
  description: 新增子编目
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/assets/directory
  description: 删除子编目
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/directory/asset
  description: 编目挂载资产分页查询
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/directory/asset
  description: 批量新增挂载资产
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/assets/directory/asset
  description: 删除挂载资产
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/directory/assets/create
  description: 资产新增列表展示
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/directory/info/{dirId}
  description: 根据编目ID获得编目下的信息
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/directory/move
  description: 移动子编目
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/directory/system
  description: 目录编目的分页查询
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/assets/directory/system
  description: 新增编目
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/assets/directory/system
  description: 批量删除编目
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/directory/system/all
  description: 资产筛选框，获得项目下的所有编目体系
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/directory/system/export
  description: 编目导出
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/directory/system/tree
  description: 编目详情，获得子菜单树
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/directory/system/{fid}
  description: 修改编目系统
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/directory/{fid}
  description: 修改子编目
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/draft/file-assets
  description: 查询文件资产草稿列表，分页
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/assets/draft/file-assets
  description: 新建文件资产草稿，批量创建时自动分配一个draftId
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/assets/draft/file-assets/onestep
  description: 简化版批量创建，快速创建并保存文件资产，工作流使用
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/draft/{draftId}/file-assets
  description: 根据创建时的draftId查询文件资产草稿列表
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/assets/draft/{draftId}/file-assets
  description: 批量保存文件资产草稿为正式资产
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/assets/draft/{draftId}/file-assets
  description: 删除/批量删除文件资产草稿
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/draft/{draftId}/file-assets/association
  description: 根据创建时的draftId查询文件资产草稿关系列表
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/assets/draft/{draftId}/file-assets/association
  description: 新建文件资产关系草稿，根据Excel创建
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/assets/draft/{draftId}/file-assets/association/{associateId}
  description: 删除/批量删除文件资产草稿
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/draft/{draftId}/file-assets/pages
  description: 根据创建时的draftId查询文件资产草稿列表，分页查询
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/draft/{draftId}/file-assets/parse-strategy
  description: 更新文件资产草稿的解析策略
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/draft/{draftId}/file-assets/record/{id}
  description: 更新文件资产草稿
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/draft/{draftId}/file-assets/{assetId}/save
  description: 保存单个文件资产草稿为正式资产
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/draft/{draftId}/operate/progress
  description: 根据创建时的draftId查询文件资产创建进度
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/draft/{draftId}/progress
  description: 根据创建时的draftId查询文件资产校验进度
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-asset/export
  description: 文件资产导出
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-asset/page-total/{assetId}
  description: 根据资产path获取pdf文件的总页数
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-asset/view
  description: 展示用户资产视图
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/file-asset/view
  description: 用户更改用户-资产视图
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-asset/{assetId}/export
  description: 文件资产导出
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-assets
  description: 查询文件资产列表
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/assets/file-assets
  description: 删除/批量删除文件资产
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-assets/all
  description: 查询所有文件资产列表，不分页
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-assets/association-attr
  description: 获取文件资产关系属性列表
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/assets/file-assets/association-attr
  description: 新建文件资产关系属性
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/file-assets/association-attr/{id}
  description: 更新文件资产关系属性
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/assets/file-assets/association-attr/{id}
  description: 批量删除文件资产关系属性
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-assets/download/excel-meta
  description: 下载资产批量创建元信息表
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-assets/download/excel-relationship
  description: 下载资产关系模板
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/assets/file-assets/examine
  description: 重复文件资产审批
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/assets/file-assets/examine/flow
  description: 批量发起重复文件资产审批，由后端发起审批
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/assets/file-assets/reject
  description: 重复文件资产审批拒绝
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-assets/{assetId}
  description: 获取文件资产详情
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/file-assets/{assetId}
  description: 更新文件资产基本信息
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-assets/{assetId}/application
  description: 查询文件资产关联的应用列表
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-assets/{assetId}/association/associated
  description: 查询文件资产被关联的关系列表
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-assets/{assetId}/association:export
  description: 导出文件资产关联关系表
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/file-assets/{assetId}/file
  description: 替换文件资产中的文件
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-assets/{assetId}/no-perm
  description: 获取文件资产详情，忽略权限
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-assets/{assetId}/versions
  description: 获取文件资产版本列表
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/file-assets/{assetId}/versions/{versionId}
  description: 获取文件资产版本详细信息
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/file-assets/{assetId}/versions/{versionId}
  description: 更新文件资产版本
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/assets/file-assets/{assetId}/versions/{versionId}
  description: 删除文件资产版本
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/assets/labels
  description: 标签批量新增
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/labels/info
  description: 标签列表展示
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/labels/name/{fid}
  description: 根据标签名id更新
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/assets/labels/name/{fid}
  description: 根据标签名批量删除
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/assets/labels/value/{fid}
  description: 根据标签值id更新
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/assets/labels/value/{fid}
  description: 根据标签值批量删除
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/assets/parse/debug/{assetId}
  description: 调试预览文件资产，暂时仅支持pdf文件
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/assets/parse/json
  description: 分页保存更改后的json
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/parse/json-to-docx
  description: 从json文件转化为docx文件的下载接口
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/assets/parse/{assetId}
  description: 解析文件至json
  perm_code: corpus.catalogs.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/parse/{assetId}/content
  description: 分页返回文件资产源文件的内容，暂时只支持pdf和docx，docx先转成pdf再分页
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/parse/{versionId}/json
  description: 分页返回解析后的json和全部目录以及解析状态
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/assets/stats
  description: 获取资产统计信息
  perm_code: corpus.catalogs.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/data/connections
  description: 获取数据连接列表
  perm_code: mlops.datasource.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/data/connections
  description: 新建数据连接
  perm_code: mlops.datasource.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/data/connections/status
  description: 测试数据连接
  perm_code: mlops.datasource.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/data/connections/{id}
  description: 根据id获取数据连接
  perm_code: mlops.datasource.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/data/connections/{id}
  description: 更新数据连接
  perm_code: mlops.datasource.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/data/connections/{id}
  description: 删除数据连接
  perm_code: mlops.datasource.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/data/connections/{id}/buckets
  description: 获取文件对象存储连接下的Bucket列表
  perm_code: mlops.datasource.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/data/connections/{id}/file
  description: 获取文件对象存储下的文件
  perm_code: mlops.datasource.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/data/connections/{id}/files
  description: 获取文件对象存储连接下的文件列表
  perm_code: mlops.datasource.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/data/connections/{id}/status
  description: 根据连接id测试数据连接
  perm_code: mlops.datasource.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/points/details
  description: 获取指定空间积分明细
  perm_code: project.points.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/points/precalculate
  description: 是否启用积分系统
  perm_code: project.points.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/points/statistics/list
  description: 列出所有空间积分统计数据
  perm_code: project.points.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/points/system/enable
  description: 是否启用积分系统
  perm_code: project.points.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/points/system/rule
  description: 获取积分规则text
  perm_code: project.points.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/assets/changes
  description: 获取资产变更记录
  perm_code: knowledge.manage.change.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/process/assets/changes
  description: 忽略/批量忽略资产变更记录
  perm_code: knowledge.manage.change.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/assets/changes
  description: 批量处理资产变更记录
  perm_code: knowledge.manage.change.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/assets/changes/{changeId}
  description: 一键处理资产变更记录
  perm_code: knowledge.manage.change.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/review
  description: 审核子任务列表
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/review
  description: 创建审核子任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/review/assigned
  description: 是否已有审核任务
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/process/review/content/{contentId}
  description: 更新审核内容
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/process/review/content/{contentId}
  description: 删除审核内容
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/review/content/{contentId}/ban
  description: 禁用表格对象
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/review/content/{contentId}/examine
  description: 开始审核
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/review/content/{contentId}/remove
  description: 表格类型文档删除内容
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/review/import
  description: 获取入库审核任务
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/review/{id}
  description: 单个审核子任务
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/review/{id}/asset/all
  description: 一键通过
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/review/{id}/asset/{assetId}
  description: 审核通过
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/process/review/{id}/asset/{assetId}
  description: 删除文档
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/review/{id}/asset/{assetId}/file-preview
  description: 预览文件
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/review/{id}/asset/{assetId}/{auditField}/content
  description: 获取审核内容
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/review/{id}/asset/{assetId}/{auditField}/table
  description: 获取表格入库审核内容
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/review/{id}/complete
  description: 完成审核
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/tasks
  description: 获取任务列表
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/tasks
  description: 创建任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/process/tasks
  description: 批量删除任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/tasks/assets/processed-status
  description: 查询资产处理状态
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/tasks/details
  description: 获取任务详情
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/process/tasks/files
  description: 删除任务文件
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/tasks/from-task
  description: 从任务创建任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/tasks/result:export
  description: 导出任务结果
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/tasks/result:export
  description: 缓存任务结果md5
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/process/tasks/{taskId}
  description: 修改任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/tasks/{taskId}/clear-step-data
  description: 清除任务步骤数据
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/tasks/{taskId}/detail
  description: 获取任务详情
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/tasks/{taskId}/event
  description: 获取任务事件
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/tasks/{taskId}/export
  description: 导出任务最终数据到文件系统
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/tasks/{taskId}/gpu-check
  description: 检查任务所需GPU资源
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/tasks/{taskId}/log
  description: 获取任务日志
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/tasks/{taskId}/schema
  description: 获取任务的schema
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/tasks/{taskId}/start
  description: 启动任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/tasks/{taskId}/stop
  description: 停止任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/tasks/{taskId}/update-import-status
  description: 更新入库状态
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/process/tasks/{taskId}/update-sub-step
  description: 保存子步骤
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/templates
  description: 获取模板列表
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/process/templates
  description: 更新模板
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/templates
  description: 创建模板
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/process/templates
  description: 批量删除模板
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/process/templates/detail
  description: 获取模板详情
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/process/templates/from-task
  description: 从任务创建模板
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/process/templates/{templateId}/publish
  description: 设置发布状态
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/operators
  description: 根据阶段分页获取算子列表
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/operators/get-operator
  description: 获取内置算子配置信息
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/operators/insert-or-update
  description: 更新算子信息
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/operators/samples/get-by-filepath
  description: 根据文件内容获取算子样例
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/operators/samples/get-by-name
  description: 获取内置算子样例
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/operators/samples/insert-or-update
  description: 插入或更新算子样例
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/operators/{operatorId}/config
  description: 获取算子配置
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/operators/{operatorId}/sample
  description: 获取算子样例
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks
  description: 获取数据处理任务列表
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/tasks
  description: 新建数据处理任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/processing/tasks
  description: 删除数据处理任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/audio/download
  description: 下载音视频文件
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/file-preview
  description: 数据处理中预览文件
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/res-config/default
  description: 获取数据处理任务默认资源配置
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}
  description: 获取数据处理任务详细信息
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/processing/tasks/{tid}
  description: 修改/配置数据处理任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}/audio
  description: 获取音视频处理任务信息
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}/audio/sample-result
  description: 获取音视频处理任务样例结果
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}/debug/status
  description: 获取调试任务状态
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}/event
  description: 获取指定任务的pod事件
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}/file-chunking/data
  description: 获取分片处理调试数据
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/tasks/{tid}/file-chunking/debug
  description: 开启分片处理debug任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}/file-parser-detail
  description: 获取文档解析阶段任务信息（老版本）
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}/file-parsing
  description: 获取文档解析阶段任务信息
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}/file-parsing/data
  description: 获取文档解析数据
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/tasks/{tid}/file-parsing/debug
  description: 开启文档解析debug任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}/file-parsing/debug/progress
  description: 获取文档解析调试任务进度
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}/gpu-check
  description: 获取任务所需gpu数量
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}/gpu-model
  description: 查看指定任务GPU和模型信息（模型是否健康，GPU配置是否满足）
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}/log
  description: 查看指定任务的log
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}/pipeline/debug
  description: 获取算子调试数据
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/tasks/{tid}/pipeline/debug
  description: 阶段调试算子配置
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/processing/tasks/{tid}/pipeline/{pid}
  description: 分阶段保存任务算子配置
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}/progress
  description: 获取任务处理进度百分比
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/processing/tasks/{tid}/res-config
  description: 修改/配置数据处理任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/tasks/{tid}/resume
  description: 恢复数据处理任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/tasks/{tid}/save-result
  description: 任务数据另存为数据集
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/tasks/{tid}/start
  description: 启动数据处理任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/tasks/{tid}/stop
  description: 停止数据处理任务
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/tasks/{tid}/tds/file-sync
  description: 数据处理中预览/调试TDS编目文件前同步文件
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/templates
  description: 分页获取模板列表
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/processing/templates
  description: 删除模板
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/templates/checkNameUnique
  description: 名称唯一性校验
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/templates/get-by-name
  description: 获取内置模板信息
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/templates/insert-or-update
  description: 插入或更新内置模板信息
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/templates/versions
  description: 创建版本
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/templates/versions/all
  description: 项目下所有版本
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/templates/versions/get-by-name
  description: 获取内置模板版本信息
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/templates/versions/insert-or-update
  description: 插入或更新内置模板版本信息
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/processing/templates/{tmpl_id}
  description: 编辑模板
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/templates/{tmpl_id}/versions
  description: 模板下所有版本
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/templates/{tmpl_id}/versions/{id}
  description: 版本详情
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/processing/templates/{tmpl_id}/versions/{id}
  description: 删除版本
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/ud-operators
  description: 获取自定义算子列表
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/ud-operators
  description: 创建自定义算子
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/processing/ud-operators
  description: 删除自定义算子
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/ud-operators/checkName
  description: 检测英文名是否唯一
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/ud-operators/image/publish
  description: 发布镜像
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/ud-operators/image/publish/init
  description: 初始化项目镜像
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/ud-operators/image/publish/list
  description: 发布镜像记录列表
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/processing/ud-operators/image/publish/set-default-template/{id}
  description: 设置默认镜像模板
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/ud-operators/image/publish/{id}/status
  description: 获取镜像测试状态
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/ud-operators/image/template/list
  description: 获取镜像模板列表
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/ud-operators/installed-list/apt/{id}
  description: 镜像内已安装Apt包
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/ud-operators/installed-list/pypi/{id}
  description: 镜像内已安装Python包
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/ud-operators/requirement-sample/download
  description: Python包依赖文件示例下载
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/ud-operators/supported-publish-types/{id}
  description: 可发布的任务类型
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/ud-operators/{id}
  description: 获取单个自定义算子
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/processing/ud-operators/{id}
  description: 更新自定义算子
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/ud-operators/{id}/publish
  description: 自定义算子发布
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/ud-operators/{id}/save-output
  description: 获取自定义算子测试结果
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/ud-operators/{id}/share
  description: 自定义算子分享
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/ud-operators/{id}/start
  description: 打开自定义算子
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/ud-operators/{id}/stop
  description: 关闭自定义算子
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/ud-operators/{id}/test
  description: 测试自定义算子
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/ud-operators/{id}/test/result
  description: 获取自定义算子测试结果
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/processing/ud-operators/{id}/test/status
  description: 获取自定义算子测试状态
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/ud-operators/{id}/unpublish
  description: 取消自定义算子发布
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/processing/ud-operators/{id}/unshare
  description: 取消自定义算子分享
  perm_code: corpus.process.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/prompt/conversations
  description: 使用prompt与LLM对话
  perm_code: mw.prompts.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/prompt/scenes
  description: 查询提示场景列表
  perm_code: mw.prompts.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/prompt/scenes
  description: 新建提示场景
  perm_code: mw.prompts.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/prompt/scenes/-/templates
  description: 获取所有的模板列表，不分页(应用链调用)
  perm_code: mw.prompts.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/prompt/scenes/-/templates/-/paradigms
  description: 获取模板范式的模板列表
  perm_code: mw.prompts.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/prompt/scenes/all
  description: 获取所有的场景列表，包含模板列表，不分页
  perm_code: mw.prompts.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/prompt/scenes/{sceneId}
  description: 获取提示场景详细信息
  perm_code: mw.prompts.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/prompt/scenes/{sceneId}
  description: 更新提示场景
  perm_code: mw.prompts.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/prompt/scenes/{sceneId}
  description: 删除提示场景
  perm_code: mw.prompts.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/prompt/scenes/{sceneId}/clone
  description: 克隆提示场景到指定项目
  perm_code: mw.prompts.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/prompt/scenes/{sceneId}/clones/name
  description: 获取克隆的新场景的默认名称
  perm_code: mw.prompts.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/prompt/scenes/{sceneId}/templates
  description: 查询提示模板列表
  perm_code: mw.prompts.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/prompt/scenes/{sceneId}/templates
  description: 新建提示模板
  perm_code: mw.prompts.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/prompt/scenes/{sceneId}/templates/{templateId}
  description: 获取提示模板详细信息
  perm_code: mw.prompts.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/prompt/scenes/{sceneId}/templates/{templateId}
  description: 更新提示模板
  perm_code: mw.prompts.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/prompt/scenes/{sceneId}/templates/{templateId}
  description: 删除提示模板
  perm_code: mw.prompts.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/prompt/scenes/{sceneId}/templates/{templateId}/publish
  description: 发布提示模板
  perm_code: mw.prompts.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/prompt/scenes/{sceneId}/templates/{templateId}/records
  description: 查询模板测试记录列表
  perm_code: mw.prompts.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/prompt/scenes/{sceneId}/templates/{templateId}/records
  description: 新建模板测试记录
  perm_code: mw.prompts.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/prompt/scenes/{sceneId}/templates/{templateId}/records/{recordId}
  description: 获取提示模板测试记录详细信息
  perm_code: mw.prompts.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/prompt/scenes/{sceneId}/templates/{templateId}/records/{recordId}
  description: 更新/保存模板测试记录
  perm_code: mw.prompts.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/prompt/scenes/{sceneId}/templates/{templateId}/unpublish
  description: 取消发布提示模板
  perm_code: mw.prompts.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/prompt/scenes/{sceneId}/templates:batchDelete
  description: 批量删除提示模板
  perm_code: mw.prompts.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/prompt/scenes:batchDelete
  description: 批量删除提示场景
  perm_code: mw.prompts.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/report/{tid}/eval-summary
  description: 获取评估任务报告的任务summary
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/report/{tid}/filter/{filter_name}/detail
  description: 获取过滤算子的折线图详情
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/report/{tid}/result
  description: 获取算子采样数据
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/report/{tid}/statistics
  description: 获取任务阶段算子全局统计
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/report/{tid}/summary
  description: 获取任务报告的任务summary
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/report/{tid}/{name}/result
  description: 获取算子采样数据
  perm_code: corpus.process.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/sample/location
  description: 获取所有的目录列表
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/sample/location/{id}/set
  description: 获取指定空间下所有的图片集列表
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/sample/location/{id}/set
  description: 删除图片集
  perm_code: corpus.img-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/sample/location/{id}/set/check
  description: 检查特定的图片集是否存在
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/sample/location/{id}/set/{setId}
  description: 向指定图片集上传新图片
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/sample/location/{id}/set/{setId}
  description: 更新图片集
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/sample/location/{id}/set/{setId}/annotation
  description: 获取图片集相关的标注集
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/sample/location/{id}/set/{setId}/ids
  description: 图片集里的全部图片id
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/sample/location/{id}/set/{setId}/obj
  description: 获取指定图片集下的图片列表
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/sample/location/{id}/set/{setId}/obj
  description: 删除图片
  perm_code: corpus.img-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/sample/location/{id}/set/{setId}/obj/{objId}
  description: 更新图片信息
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/sample/location/{id}/set/{setId}/obj/{objId}/original
  description: 获取图片的原始数据（即图片数据, 经过base64编码）和名称
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/sample/location/{id}/set/{setId}/zip
  description: 导出图片集为zip
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/sample/location/{id}/upload
  description: 将上传的图像集zip导入
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets
  description: 获取数据集列表
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/datasets
  description: 新建数据集
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/samplemgr/datasets
  description: 删除数据集
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/-/search-data
  description: 全局搜索样本仓库中的标注集
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/datasets/-/template:execute
  description: 根据传的数据集文件和prompt模板，生成新文件
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/-/types
  description: 获取数据类型列表
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/-/versions/latest
  description: 获取指定数据集的最新版本
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/all
  description: 获取所有数据集，不分页
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/all/versionEmpty
  description: 获取所有数据集，不分页
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/daily-stats
  description: 获取语料统计信息
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/examples
  description: 下载数据集模板文件
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/datasets/init
  description: 新建并初始化一个数据集
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/preview/pdf
  description: 预览PDF前N页
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/preview/text
  description: 预览数据集（仅支持纯文本）
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/stats
  description: 获取语料统计信息
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}
  description: 获取数据集
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/samplemgr/datasets/{sid}
  description: 更新数据集
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/datasets/{sid}/clone
  description: 克隆数据集到指定项目
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/clones/name
  description: 获取克隆的新数据集的默认名称
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/datasets/{sid}/csm/{instance}/import
  description: 向数据集添加文件
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/images
  description: 获取数据集图片列表
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/samplemgr/datasets/{sid}/images
  description: 从数据集中删除图片
  perm_code: corpus.img-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/images:export
  description: 导出数据集中的图片，获取文件流
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/datasets/{sid}/images:export
  description: 导出数据集中的图片，发送请求参数
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/datasets/{sid}/images:import
  description: 向数据集添加图片
  perm_code: corpus.img-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/samplemgr/datasets/{sid}/permission
  description: 授权访问公共空间的数据集权限
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/datasets/{sid}/reprocess
  description: 重新智识图片(后端用)
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/sharing/source
  description: 获取共享数据集的空间来源列表
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/samplemgr/datasets/{sid}/texts
  description: 从数据集删除文本文件
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/texts/{id}/data:export
  description: 获取指定文本文件的文件流
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/texts/{id}/meta
  description: 获取指定文本文件的元文件信息(llm1.3版本中已废弃)
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/datasets/{sid}/texts:import
  description: 已废弃，基于文件系统添加数据(llm1.2新增语料库接口)
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/versions
  description: 获取数据集的所有版本
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/datasets/{sid}/versions
  description: 新建数据集版本（新建空版本或者继承已有版本）
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/datasets/{sid}/versions/draft
  description: 新建数据集草稿版本
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/versions/name/next
  description: 获取新的数据集版本名称
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}
  description: 获取数据集版本详情
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}
  description: 修改数据集版本名称和描述
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}
  description: 删除数据集版本
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/clone
  description: 克隆文本数据集的版本到指定项目空间，在指定项目下新建数据集或添加到指定数据集的新版本中
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/files
  description: 获取指定数据集本版中的文件列表，分页
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/files
  description: 从数据集版本中删除文本文件
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/files/all
  description: 获取指定数据集本版中的文件列表，不分页
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/files/format
  description: 获取指定数据集本版中的所有文件格式
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/files/{id}/arrow
  description: 获取文本文件数据记录，仅支持parquet和arrow格式文件
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/files:import
  description: 上传版本文件到数据集的指定版本中
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/meta
  description: 获取指定数据集版本的元文件信息
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/publish
  description: 发布/取消发布数据集版本
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/relations
  description: 获取数据集版本的血缘关系
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/schema
  description: 获取数据集版本的schema
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/schema
  description: 修改数据集版本的schema
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/schema
  description: 生成可解析文件的schema
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/texts/{id}/meta
  description: 获取指定文本文件的元文件信息
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/texts:export
  description: 导出数据集版本中的文件，获取文件流
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/samplemgr/datasets/{sid}/versions/{vid}/validation
  description: 校验数据集版本的schema
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/datasets:export
  description: 导出数据集，获取文件流
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/datasets:export
  description: 导出数据集，发送请求参数
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/dataviews
  description: 数据视图列表
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/dataviews
  description: 数据视图创建
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/samplemgr/dataviews
  description: 数据视图删除
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/dataviews/{id}
  description: 数据视图详情
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/samplemgr/dataviews/{id}
  description: 数据视图更新
  perm_code: corpus.text-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/dataviews/{id}/graph
  description: 数据视图图表（list + heap map）
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/files/datasets
  description: 检查文件系统中的文件是否在数据集中，并返回对应的数据集列表
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/files/datasets/format:check
  description: 检查文件格式是否符合数据解析任务要求
  perm_code: corpus.text-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/images/{file}
  description: 获取图片原始信息
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/images/{ids}/similars:view
  description: 图片id寻找相似图片
  perm_code: corpus.img-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/images/{id}/meta
  description: 获取图片元数据信息
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/images/{id}/original
  description: 获取图片原始base64
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/images/{id}/original:operate
  description: 对图片应用算子
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/images/{id}/thumbnail
  description: 获取图片缩略图信息
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/images:view
  description: 筛选获取图片列表
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/operators
  description: 获取算子列表
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/stats
  description: 获取资源统计信息
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/views
  description: 获取视图列表
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/views
  description: 新建视图
  perm_code: corpus.img-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: DELETE
  uri: /api/samplemgr/views
  description: 删除视图
  perm_code: corpus.img-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/views/all
  description: 获取所有视图，不分页
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/views/range
  description: 获取视图筛选范围
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/views/statistics
  description: 视图统计分析
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/views/{vid}
  description: 获取视图
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: PUT
  uri: /api/samplemgr/views/{vid}
  description: 更新视图
  perm_code: corpus.img-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: GET
  uri: /api/samplemgr/views:export
  description: 视图图片导出，获取文件流
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/views:export
  description: 视图图片导出，发送请求参数
  perm_code: corpus.img-datasets.*
  perm_action: read
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/views:reprocess
  description: 重新智识图片
  perm_code: corpus.img-datasets.*
  perm_action: '*'
- module: 语料仓库
  route: cv
  method: POST
  uri: /api/samplemgr/views:saveAs
  description: 视图保存为新数据集
  perm_code: corpus.img-datasets.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/chatmgr/conversations
  description: 获取与模型的对话
  perm_code: mw.experience.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: DELETE
  uri: /api/v1/mwh/chatmgr/conversations
  description: 删除对话
  perm_code: mw.experience.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/chatmgr/conversations/answers/{answer_id}
  description: 评价模型响应,点赞或点踩
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/accelerator-types
  description: 获取硬件加速卡 加速类型 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/api-method
  description: 获取所有api method
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/api-spec
  description: 获取所有api spec
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/api-type
  description: 获取所有api type
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/asset-type
  description: 获取所有 资产市场 资源类型
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/cpu-arches
  description: 获取CPU硬件的 架构类型 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/data-types
  description: 获取所有模型输入输出相关的 数据类型 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/dynamic-param-styles
  description: 获取 动态参数样式 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/dynamic-param-types
  description: 获取 动态参数类型 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/endpoint-type
  description: 获取所有endpoint类型
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/eval-dataset-scopes
  description: 获取 模型评估数据集来源 范围
  perm_code: mw.evaluation.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/evaluation-metrics-kinds
  description: 获取 模型评估指标 划分
  perm_code: mw.evaluation.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/evaluation-stages
  description: 获取 模型评估阶段 划分
  perm_code: mw.evaluation.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/http-methods
  description: 获取所有 远成模型请求方法 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/llm-rating
  description: 获取所有 大模型对话评价的范围
  perm_code: mw.experience.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/model-kinds
  description: 获取模型 数据模态 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/model-kinds/{kind-name}/sub-kinds
  description: 获取特定数据模态下 任务类型 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/model-types
  description: 获取所有 模型类型 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/model-types/{type-name}/sub-types
  description: 获取所有模型类型下 模型子类型 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/module
  description: 获取所有 模块 类型
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/param-formats
  description: 获取所有模型输入输出的 参数格式 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/proxy-schemes
  description: 获取所有 代理类型 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/relation-types
  description: 获取所有实体关联关系的 关联类型 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/release-statues
  description: 获取 模型版本状态 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/rsc-type
  description: 获取所有资源类型
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/rsc-types
  description: 获取所有 系统资源类型 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/runtime-types
  description: 获取 模型运行时类型 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/schedule-modes
  description: 获取所有模型 调度方式 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/enums/training-template
  description: 获取所有 训练模板 范围
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/evalmgr/missions
  description: 获取评估任务列表
  perm_code: mw.evaluation.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/evalmgr/missions
  description: 创建评估任务
  perm_code: mw.evaluation.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: DELETE
  uri: /api/v1/mwh/evalmgr/missions
  description: 删除评估任务
  perm_code: mw.evaluation.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: PUT
  uri: /api/v1/mwh/evalmgr/missions/{id}
  description: 更新评估任务
  perm_code: mw.evaluation.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/evalmgr/missions/{id}/result
  description: 获取评估结果
  perm_code: mw.evaluation.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/evalmgr/missions:batchStart
  description: 批量启动评估任务
  perm_code: mw.evaluation.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/evalmgr/missions:batchStop
  description: 批量停止评估任务
  perm_code: mw.evaluation.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/evalmgr/missions:checkExistence
  description: 检查评估任务名称是否存在
  perm_code: mw.evaluation.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/evalmgr/templates
  description: 获取评估模板列表
  perm_code: mw.evaluation.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/evalmgr/templates
  description: 创建评估模板
  perm_code: mw.evaluation.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: DELETE
  uri: /api/v1/mwh/evalmgr/templates
  description: 删除评估模板
  perm_code: mw.evaluation.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: PUT
  uri: /api/v1/mwh/evalmgr/templates/{id}
  description: 更新评估模板
  perm_code: mw.evaluation.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/existence
  description: 检查特定类型的资源名称是否已存在（仅当前用户空间下）
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/file-model/parse
  description: 解析文件模型输入输出信息,整合为api
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/hf-models
  description: 获取huggingface模型列表
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/openai/v1/audio/transcriptions
  description: openai格式,音频转文本
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/openai/v1/audio/translations
  description: openai格式,音频翻译
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/openai/v1/chat/completions
  description: openai格式,文本生成chat-completions
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/openai/v1/completions
  description: openai格式,文本生成completions
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/openai/v1/embeddings
  description: openai格式,文本向量模型
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/openai/v1/images/generations
  description: openai格式,图像生成模型
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/std/v1/audio/transcriptions
  description: std-triton格式,音频转文本
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/std/v1/audio/translations
  description: std-triton格式,音频翻译
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/std/v1/chat/completions
  description: std-triton格式,文本生成模型
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/std/v1/chat/completions:stop
  description: std-triton格式,停止流式推理
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/std/v1/cv/all
  description: std-triton格式,传统cv图像相关模型
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/std/v1/embeddings
  description: std-triton格式,文本向量模型
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/std/v1/entity/recognition
  description: std-triton格式,实体识别模型
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/std/v1/images/generations
  description: std-triton格式,文本生成图像
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/std/v1/ml/all
  description: std-triton格式,机器学习相关模型
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/infergw/std/v1/rerank
  description: 重排模型
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/labels
  description: 获取模型的标签列表, 返回格式为：MAP>
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/manager/grafana-alerts
  description: 接收Grafana的告警, 转换为系统推送通知发送到前端
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/manager/notifications
  description: 像前端界面广播一条消息推送
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models
  description: 查询模型列表
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/models
  description: 创建新模型
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/-/disk-usage
  description: 统计模型磁盘占用
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/-/search-data
  description: 全局搜索模型仓库的数据
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/-/validation
  description: 校验是否为平台支持的模型类型与任务类型的组合
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/built-in/training-templates
  description: get built-in training templates获取内置的训练模板
  perm_code: mw.train.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/daily-stats
  description: daily counting models and releases 热门模型
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: PUT
  uri: /api/v1/mwh/models/modelPy
  description: 按照要求替换model.py文件
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/relations:notAllowDel
  description: 获取不允许删除的模型版本所具备的关系类型
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/stats
  description: counting models and releases 模型与版本计数
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}
  description: 查询模型列表
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: PUT
  uri: /api/v1/mwh/models/{model-id}
  description: 更新指定模型（全量数据）
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: DELETE
  uri: /api/v1/mwh/models/{model-id}
  description: 删除指定模型
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}/baselines
  description: 返回指定模型的各个框架的基线版本，返回值为 CPUArch -> ReleaseID
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: PUT
  uri: /api/v1/mwh/models/{model-id}/baselines/{release-id}
  description: 将指定的模型版本设置为基线版本，同时将同架构的其它基线版本取消
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: DELETE
  uri: /api/v1/mwh/models/{model-id}/baselines/{release-id}
  description: 取消将指定的模型版本设置为基线版本
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}/default-param
  description: 查询模型的默认参数如 默认部署参数,默认推理参数等
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}/readme
  description: 获取基线/最新模型版本的readme
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}/releases
  description: 获取指定模型的所有版本
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/models/{model-id}/releases
  description: 为指定模型创建新版本
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}
  description: 获取指定的模型版本
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: PUT
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}
  description: 更新指定模型的特定版本（全量数据）
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: DELETE
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}
  description: 删除指定模型的特定版本
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/base-services
  description: 获取peft(lora)模型能部署的模型服务
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/deploy-template
  description: 获取模型部署成MLOps服务的默认配置
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/download-jobs
  description: 创建模型文件的下载的任务
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/files
  description: 获取指定模型版本的文件列表
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/files/{file-id}
  description: 获取指定模型版本的指定文件内容
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: PUT
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/files/{file-id}
  description: 更新指定模型版本的指定文件内容
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/files:delete
  description: 删除文件或者文件夹
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/files:move
  description: 移动文件或者文件夹
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/folders
  description: 获取指定模型版本下文件夹
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/history-files
  description: 获取指定模型版本的历史文件
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/lora-services
  description: 将lora模型动态加载到指定的base模型服务
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/relation-graph
  description: 获取指定模型版本的关系图
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/sub-models
  description: 组装模型中新增子模型
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: PUT
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/sub-models/{alias}
  description: 更新组装模型中指定子模型定义
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: DELETE
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/sub-models/{alias}
  description: 删除组装模型中指定子模型
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/sub-models/{alias}/files
  description: 组装模型中某个子模型（Python代码/原子模型）中新增文件（不含内容）
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: PUT
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/sub-models/{alias}/files/{file-id}
  description: 组装模型中某个子模型（Python代码/原子模型）中更新某个文件（不含内容）
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: DELETE
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/sub-models/{alias}/files/{file-id}
  description: 删除组装模型中某个子模型中的文件
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/sub-models/{alias}/files/{file-id}/content
  description: 获取组装模型中子模型中某个文件内容
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: PUT
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/sub-models/{alias}/files/{file-id}/content
  description: 更新组装模型中子模型中某个文件内容
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/sub-models/{alias}/files:rename
  description: 更新指定子模型目录中某个文件的名称（填充id=file_id,new_name=new_file_name）
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/models/{model-id}/releases/{release-id}/sub-models:rename
  description: 更新指定子模型所在的目录名称(填充id=old_alias,new_name=new_alias)
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/models/{model-id}/releases:commit
  description: 为指定模型创建新版本
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}/request-templates
  description: 查询模型请求模板
  perm_code: mw.evaluation.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/models/{model-id}/usage-count
  description: 获取模型访问量计数
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: PUT
  uri: /api/v1/mwh/models/{model-id}/usage-count
  description: 更新模型用量计数+1
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/models:check
  description: 检查要导入的模型是否存在
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/models:export
  description: 导出模型
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/models:import
  description: 导入模型
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/models:migrate
  description: 迁移模型到不同用户
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/ms-models
  description: 获取modelscope模型列表
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/onnx/parse
  description: parse onnx model file
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/pmml/parse
  description: parse pmml model file
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/popular-models
  description: 查询受欢迎的模型列表
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/quant/quantizations
  description: 获取量化任务
  perm_code: mw.quantization.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/quant/quantizations
  description: 创建量化任务
  perm_code: mw.quantization.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/quant/quantizations/-/strategies
  description: 获取量化策略
  perm_code: mw.quantization.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: PUT
  uri: /api/v1/mwh/quant/quantizations/{id}
  description: 更新量化任务
  perm_code: mw.quantization.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/quant/quantizations/{id}/results
  description: 上架量化结果
  perm_code: mw.quantization.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: DELETE
  uri: /api/v1/mwh/quant/quantizations:batchDel
  description: 删除量化任务
  perm_code: mw.quantization.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/quant/quantizations:batchStart
  description: 启动量化任务
  perm_code: mw.quantization.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/quant/quantizations:batchStop
  description: 停止量化任务
  perm_code: mw.quantization.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/rscmgr/resources/models:migratefiles
  description: 迁移模型文件，迁移到按照租户分割的文件夹中
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/rscmgr/resources:clone
  description: 在项目之间clone资源
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/rscmgr/resources:migrate
  description: 迁移项目资源,从一个项目到另一个项目
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/rscmgr/resources:migratedb
  description: 迁移数据库从badger到mysql，仅可执行一次
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/sophon32x/sophon-model-id-map
  description: sophon3.2模型id到LLM模型分类的映射
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/sophon32x/sophon-model-kind-map
  description: sophon3.2模型分类到llm模型分类的映射，如果不能按照类别整体映射请补充sophon3.2模型id到LLM模型分类的映射
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/sophon32x/sophon-models
  description: 手动导入sophon3.2的模型元数据
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/sophon32x/sophon-models/autoImport
  description: 自动导入模型仓库的模型，需要设置sophon模型仓库的配置与模型分类映射,并完成文件下载
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/sophon32x/sophon-models/{sophon-model-id}/sophon-releases
  description: 手动导入sophon3.2的模型版本元数据
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/sophon32x/sophon-mw-config
  description: 设置sophon3.2模型仓库的相关配置,这些配置不会保存
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/svcmgr/models/{model-id}/release/{release-id}/infer
  description: 体验推理，返回结果根据体验模型不同而不同
  perm_code: mw.evaluation.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/svcmgr/remote-services
  description: 获取远程模型(服务)
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/svcmgr/remote-services
  description: 添加一个远程模型(服务)
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: DELETE
  uri: /api/v1/mwh/svcmgr/remote-services
  description: 批量删除远程模型(服务)
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/svcmgr/remote-services/curl-command
  description: 获取远程模型curl命令
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/svcmgr/remote-services/curl-gen
  description: 从curl命令产生远程模型配置
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/svcmgr/remote-services/interface-specs
  description: 获取远程模型支持的接口规范
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/svcmgr/remote-services/labels
  description: 获取远程模型(服务)的标签列表, 返回格式为：MAP>
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/svcmgr/remote-services/request-convertors
  description: 校验远程模型的请求转换脚本
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/svcmgr/remote-services/{id}
  description: 更新远程模型(服务)
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/svcmgr/remote-services/{id}/default-publish-api
  description: 获取远程模型发布的默认api
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/svcmgr/remote-services/{id}/infer
  description: 测试远程模型(服务)
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/svcmgr/remote-services/{id}/publish
  description: 将远程模型发布为服务列表的一个服务
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/svcmgr/remote-services:batchExport
  description: 批量导出远程模型
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/svcmgr/remote-services:batchImport
  description: 批量导入远程模型
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/svcmgr/remote-services:check
  description: 检查远程模型(服务)是否可达
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/svcmgr/remote-services:importCheck
  description: 导入远程模型检查是否重复
  perm_code: mw.manage.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/svcmgr/services
  description: 获取服务信息列表(id为空获取全部)
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: GET
  uri: /api/v1/mwh/svcmgr/services/labels
  description: 获取模型服务的标签列表, 返回格式为： MAP>
  perm_code: mw.manage.*
  perm_action: read
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/svcmgr/services/{id}/infer
  description: 测试推理
  perm_code: mw.experience.*
  perm_action: '*'
- module: 模型仓库
  route: mw
  method: POST
  uri: /api/v1/mwh/svcmgr/services/{id}/infer:forward
  description: 供外部用户调用的推理接口
  perm_code: mw.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/app/applications
  description: 获取应用列表(应用链形式以及应用助手形式)
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/app/applications/-/kb-services
  description: 应用中心-获取与知识库相关的服务
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/app/applications/-/labels
  description: 应用中心-获取服务体验列表的所有标签信息
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/app/applications/-/service-tools
  description: 应用中心-服务工具列表-
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/app/applications/-/services
  description: 应用中心-服务体验列表
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/app/applications/-/services/health-info
  description: 获取所有部署服务的健康信息,较慢
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/app/applications/{id}/count
  description: 统计应用的访问量等信息,type限定为:CHAIN_VISIT, CHAIN_CLONE, CHAIN_EXECUTE
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/app/applications/{id}/experiment
  description: 将获取应用的体验信息:如图片、开场白等
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: PUT
  uri: /api/v1/app/applications/{id}/experiment
  description: 更新应用的体验信息:如图片、开场白等
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/app/applications/{id}/knowledge
  description: 应用中心-查询使用了指定知识库的应用
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/app/applications/{id}/service
  description: 应用中心-单个服务信息
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/app/applications:cancel
  description: 将已发布的应用取消,传入应用链ID
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/app/applications:publish
  description: 将已保存应用（应用助手以及应用链）使用默认配置发布到服务管理,以及重新上线
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/app/applications:publish-callback
  description: 将已保存应用（应用助手以及应用链）使用默认配置发布到服务管理,以及重新上线
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/app/assistants
  description: 创建新的智能助手
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: PUT
  uri: /api/v1/app/assistants/{id}
  description: 更新智能助手定义（非发布）
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/app/assistants/{id}/chain
  description: 获取创建应用智能体时所填信息
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: PUT
  uri: /api/v1/app/assistants/{id}/chats/{chat_id}/state
  description: 更新智能体调试结果,此时需智能体已创建
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/app/assistants:debug
  description: 智能体调试测试
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/app/assistants:nodes-info
  description: 获取智能助手对应的节点信息
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/app/chain-templates
  description: 查询应用链模板列表
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/app/chain-templates/{id}
  description: 获取单个应用链模板
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/app/external/apps
  description: 获取外部应用列表
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/app/external/apps
  description: 创建一个外部应用
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: PUT
  uri: /api/v1/app/external/apps/{id}
  description: 更新外部应用定义
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: DELETE
  uri: /api/v1/app/external/apps/{id}
  description: 删除外部应用定义
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/app/template-groups
  description: 查询应用链模板分组
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: DELETE
  uri: /api/v1/applet/applet-experiments
  description: 删除对应的体验
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/applet-experiments/labels
  description: 查询当前项目应用体验的所有标签
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/applet-experiments:cancel
  description: 取消体验发布,取消部署
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/applet-experiments:create
  description: 创建应用体验
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/applet-experiments:publish
  description: 将体验发布,对体验进行部署
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/applet-experiments:search
  description: 获取当前项目下的应用体验
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/chains
  description: 查询应用链列表
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/chains
  description: 创建应用链应用链（基础必填信息）
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/chains/-/data-types
  description: 查询传输类型具体信息
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/chains/-/mode-types
  description: 查询传输方式具体信息
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/chains/-/runs/{run_id}/cancel
  description: 取消（cancel）对话后需要将debugMessage写入调试历史记录中
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/chains/clone
  description: 复制到project
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/chains/params/transfer
  description: 前端调试入参转换为应用链服务的入参
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/chains/{id}
  description: 获取指定应用链详情
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: DELETE
  uri: /api/v1/applet/chains/{id}
  description: 删除指定应用链
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: PATCH
  uri: /api/v1/applet/chains/{id}/base-info
  description: 更新应用链定义（基础信息）
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: PUT
  uri: /api/v1/applet/chains/{id}/chats/{chat_id}/state
  description: 更新调试状态
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/chains/{id}/debug-histories
  description: 获取某个链的调试记录
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/chains/{id}/debug-histories/{run_id}
  description: 获取某个链的某条调试记录详情
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: DELETE
  uri: /api/v1/applet/chains/{id}/debug-histories/{run_id}
  description: 删除某个链的某条调试记录详情
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: PATCH
  uri: /api/v1/applet/chains/{id}/debug-info
  description: 更新应用链调试信息
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/chains/{id}/deploy-cfg
  description: 获取指定应用链详情
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: PATCH
  uri: /api/v1/applet/chains/{id}/node-info
  description: 更新应用链定义（算子编排数据）
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/chains/{id}/params
  description: 前端调试入参转换为应用链服务的入参
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/chains/{id}/runs/{run_id}/logs
  description: 查看日志
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/chains/{id}/runs/{run_id}/nodes/{node_id}/logs
  description: 查看日志
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/chains/{id}/script
  description: 获取指定应用链的脚本描述
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/chains/{id}/snapshots/{snap_id}
  description: 获取某个链的某个快照定义
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/chains:batch-delete
  description: 批量删除应用链
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/chains:debug
  description: debug = (注册脚本 + 执行一次 + 删除脚本)
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/chains:generate-code
  description: 生成应用链代码
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/chains:run
  description: 运行
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/chains:test-code
  description: 测试代码
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/custom-widgets
  description: 创建自定义算子,并指定是否立即启动
  perm_code: applet.operators.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: DELETE
  uri: /api/v1/applet/custom-widgets
  description: 删除指定的自定义算子
  perm_code: applet.operators.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: PATCH
  uri: /api/v1/applet/custom-widgets
  description: 更新指定id的自定义算子
  perm_code: applet.operators.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/custom-widgets/labels
  description: 查询当前项目下自定义算子的所有标签
  perm_code: applet.operators.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/custom-widgets:search
  description: 查询当前项目下特定的自定义算子
  perm_code: applet.operators.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/custom-widgets:start
  description: 启动指定的自定义算子
  perm_code: applet.operators.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/custom-widgets:stop
  description: 停止指定的自定义算子
  perm_code: applet.operators.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/dynamic_widgets/{dynamic_widget_type}
  description: 获取动态算子列表
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/dynamic_widgets/{dynamic_widget_type}/{dynamic_widget_key}
  description: 获取动态算子
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/labels
  description: 获取所有可用的标签
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/labels
  description: 保存标签
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/models/-/search-data
  description: 全局搜索应用仓库的数据
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/stats
  description: 应用链状态
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/applet/test-widgets
  description: 创建或更新测试算子
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: DELETE
  uri: /api/v1/applet/test-widgets
  description: 清空测试算子
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/widget-groups
  description: 获取所有可用算子分组列表
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/widget-groups/text
  description: 获取所有可用算子分组列表
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/applet/widgets/datasource
  description: 动态datasource
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/dialog/applications
  description: 根据project_id获取所有应用
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/dialog/applications/chats
  description: 获取所有会话
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: PUT
  uri: /api/v1/dialog/applications/{app_id}
  description: 根据project_i添加应用
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: DELETE
  uri: /api/v1/dialog/applications/{app_id}
  description: 根据project_id删除应用链
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/dialog/applications/{app_id}/chats/{chat_id}
  description: 根据app_id 和 chat_id获取单个对话内的历史记录
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: DELETE
  uri: /api/v1/dialog/applications/{app_id}/chats/{chat_id}
  description: 根据 chat_id 清空单条历史记录
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/dialog/applications:downloads
  description: 下载对话
  perm_code: applet.experience.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/global-llm/project-llm/agent_create/one_more_example
  description: 创建Agent的更多引导示例
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: PUT
  uri: /api/v1/global-llm/projects-llm
  description: 创建或更新模型
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/global-llm/projects-llm/agent_create
  description: 创建Agent
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/global-llm/projects-llm/image_gen
  description: 生成图片
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/global-llm/projects-llm/probe
  description: 生成追问问题
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/global-llm/projects-llm/prompt_optimize
  description: 提示词优化
  perm_code: applet.manage.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/guardrails/projects-safety/-/{id}/guardrail
  description: 根据id获取安全围栏配置
  perm_code: mlops.security.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/guardrails/projects-safety/guardrail/all
  description: 获取所有的安全围栏配置
  perm_code: mlops.security.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/guardrails/projects-safety/{pid}/guardrail
  description: 根据项目id获取安全围栏配置
  perm_code: mlops.security.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: PUT
  uri: /api/v1/guardrails/projects-safety/{pid}/guardrail
  description: 更新安全围栏配置
  perm_code: mlops.security.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/guardrails/projects-safety:postCheck
  description: 服务输出安全校验
  perm_code: mlops.security.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/guardrails/projects-safety:preCheck
  description: 服务输入安全校验
  perm_code: mlops.security.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/chunks:add
  description: 手动新增分段
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/chunks:remove
  description: 删除指定分段
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/chunks:update
  description: 编辑更新指定分段
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/connection:detail
  description: 数据连接导入-获取数据连接的库表列表, 包含表描述信息
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/doc-proc:preview
  description: 预览文档加工效果(分段预览)
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/docs:is-existent
  description: 判断文档是否存在于知识库
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/docs:remove
  description: 从知识库移除文档
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/docs:retry-submit
  description: 重试文档处理流程, 异步处理
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/docs:submit
  description: 新增文档到知识库(异步处理),允许自动创建知识库
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/docs:sync-submit
  description: 新增文档到知识库(同步处理)，可指定文档处理配置或按照知识库默认值，可批量处理，处理中推送进度
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/kbs
  description: 获取知识库列表
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/kbs
  description: 创建知识库
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/kbs/-/search-data
  description: 获取知识库
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/kbs/assets:export
  description: 获取文档的文件资产
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/kbs/assets:export/{base_id}
  description: 获取文档的文件资产
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/kbs/assets:meta
  description: 获取文档的文件资产信息
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/kbs/assets:meta/{base_id}
  description: 获取文档的文件资产信息
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/kbs/assets:query/{base_id}
  description: 根据标签查询获取资产列表
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/kbs/healthz/state
  description: 获取知识库的健康检测运行状态
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/kbs/healthz:refresh
  description: 刷新知识库的健康检测状态(立即执行,涉及全部知识库)
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/kbs/qa:export
  description: 导出知识库的qa对
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/kbs/{base_id}
  description: 根据id获取知识库
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: PUT
  uri: /api/v1/knowlhub/kbs/{base_id}
  description: 更新知识库
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: DELETE
  uri: /api/v1/knowlhub/kbs/{base_id}
  description: 删除知识库
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/kbs/{base_id}/doc-tree
  description: 获取知识库的文档列表，文档树形式
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: PUT
  uri: /api/v1/knowlhub/kbs/{base_id}/doc:disable
  description: 启用/禁用 知识库文档
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/kbs/{base_id}/docs
  description: 获取知识库的文档列表，平铺形式
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/kbs/{base_id}/docs/retrieval-status
  description: 启用禁用文档
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/kbs/{base_id}/docs/{doc_id}
  description: 获取知识库的文档详情
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/kbs/{base_id}/docs/{doc_id}/chunks
  description: 获取知识库文档的分段内容列表，支持排序
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/kbs/{base_id}/docs/{doc_id}/chunks/retrieval-status
  description: 启用禁用切片
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/kbs/{base_id}/docs:fast
  description: 获取知识库的文档列表，平铺形式
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/kbs/{base_id}/publish
  description: 将知识库服务发布到服务列表
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/kbs/{base_id}/rebuild-index
  description: 重建知识库索引
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: PUT
  uri: /api/v1/knowlhub/kbs/{base_id}/share
  description: 设置知识库是否共享到公共空间
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: PUT
  uri: /api/v1/knowlhub/kbs/{base_id}/state
  description: 更新知识库状态(可见、可检索)
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/kbs/{base_id}/tasks/{task_id}/assets/{asset_id}
  description: 根据task和asset获取知识库文档信息
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/kbs:cross-retrieve
  description: 跨知识库检索
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/kbs:doc-restore
  description: 文档重新入库
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/kbs:doc-trees
  description: 获取多个知识库的文档树
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/kbs:docs-retry
  description: 文档失败切片重试
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/kbs:retrieve
  description: 检索知识库
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/kbs:retrieve/{base_id}
  description: 检索知识库
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/kbs:task-store
  description: 知识库任务入库
  perm_code: knowledge.manage.list.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/popular-kbs
  description: 获取最受欢迎的知识库
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/prompts
  description: 获取知识增强的默认提示词
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/stats/storage
  description: 统计知识库的存储开销
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/knowlhub/tkh/repos
  description: 获取TKH所有文本类型的知识库列表
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/knowlhub/trace:elements
  description: 溯源文档元素
  perm_code: knowledge.manage.list.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/portal-info/popular-experis
  description: 查询受欢迎的体验对象
  perm_code: applet.manage.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/tool/collections
  description: 工具集列表
  perm_code: applet.plugins.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/tool/collections
  description: 创建工具集
  perm_code: applet.plugins.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/tool/collections-describer
  description: 工具集列表-智能体使用的结构
  perm_code: applet.plugins.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/tool/collections/-/demos
  description: 工具集示例
  perm_code: applet.plugins.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: GET
  uri: /api/v1/tool/collections/{id}
  description: 查看工具集详情
  perm_code: applet.plugins.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: PUT
  uri: /api/v1/tool/collections/{id}
  description: 修改工具集
  perm_code: applet.plugins.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: DELETE
  uri: /api/v1/tool/collections/{id}
  description: 删除工具集
  perm_code: applet.plugins.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/tool/collections:batch_delete
  description: 批量删除工具集
  perm_code: applet.plugins.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/tool/collections:cancel_publish
  description: 取消发布工具集
  perm_code: applet.plugins.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/tool/collections:parser_meta_api
  description: 解析原始API信息（yaml/json）
  perm_code: applet.plugins.*
  perm_action: read
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/tool/collections:publish
  description: 发布工具集
  perm_code: applet.plugins.*
  perm_action: '*'
- module: 应用仓库
  route: applet
  method: POST
  uri: /api/v1/tool/collections:test_api
  description: 工具测试
  perm_code: applet.plugins.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: GET
  uri: /api/v1/alerting/alerts
  description: 获取预警列表
  perm_code: mlops.services.*
  perm_action: read
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/alerting/alerts/batch-delete
  description: 批量删除预警消息
  perm_code: mlops.services.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/alerting/alerts/receive
  description: 接受预警信息
  perm_code: mlops.services.*
  perm_action: read
- module: 空间管理
  route: cas
  method: GET
  uri: /api/v1/alerting/alerts/receiver-cfg/{svc_id}
  description: 获取消息配置
  perm_code: mlops.services.*
  perm_action: read
- module: 空间管理
  route: cas
  method: PUT
  uri: /api/v1/alerting/alerts/receiver-cfg/{svc_id}
  description: 修改消息设置
  perm_code: mlops.services.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/alerting/alerts/{id}/mark-as-read
  description: 消息已读
  perm_code: mlops.services.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: GET
  uri: /api/v1/alerting/rules
  description: 获取预警规则列表
  perm_code: mlops.services.*
  perm_action: read
- module: 空间管理
  route: cas
  method: PUT
  uri: /api/v1/alerting/rules
  description: 更新预警规则
  perm_code: mlops.services.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/alerting/rules
  description: 创建预警规则
  perm_code: mlops.services.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: DELETE
  uri: /api/v1/alerting/rules
  description: 删除预警规则
  perm_code: mlops.services.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/alerting/rules/exists
  description: 判断预警规则是否存在
  perm_code: mlops.services.*
  perm_action: read
- module: 空间管理
  route: cas
  method: PATCH
  uri: /api/v1/alerting/rules/paused
  description: 更新预警规则的停用状态
  perm_code: mlops.services.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: GET
  uri: /api/v1/audit/records
  description: 获取审计事件列表
  perm_code: project.audit-logs.*
  perm_action: read
- module: 空间管理
  route: cas
  method: GET
  uri: /api/v1/examine/flows
  description: 获取审批列表
  perm_code: project.approval.*
  perm_action: read
- module: 空间管理
  route: cas
  method: GET
  uri: /api/v1/examine/flows/count
  description: 获取列表数量
  perm_code: project.approval.*
  perm_action: read
- module: 空间管理
  route: cas
  method: GET
  uri: /api/v1/examine/flows/{instance-id}
  description: 获取审批单详情
  perm_code: project.approval.*
  perm_action: read
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/examine/flows/{instance-id}/node:approve
  description: 通过审批流程的当前节点
  perm_code: project.approval.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/examine/flows/{instance-id}/node:reject
  description: 拒绝审批流程的当前节点
  perm_code: project.approval.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/projmgr/categories
  description: 新建空间类别
  perm_code: project.manage.*
  perm_action: create
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/projmgr/categories/rebuild
  description: 重建空间类别
  perm_code: project.manage.*
  perm_action: create
- module: 空间管理
  route: cas
  method: GET
  uri: /api/v1/projmgr/categories/{id}
  description: 获取空间类别
  perm_code: project.manage.*
  perm_action: read
- module: 空间管理
  route: cas
  method: PUT
  uri: /api/v1/projmgr/categories/{id}
  description: 更新空间类别
  perm_code: project.manage.*
  perm_action: create
- module: 空间管理
  route: cas
  method: DELETE
  uri: /api/v1/projmgr/categories/{id}
  description: 删除空间类别
  perm_code: project.manage.*
  perm_action: create
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/projmgr/projects
  description: 新建空间
  perm_code: project.manage.*
  perm_action: create
- module: 空间管理
  route: cas
  method: PUT
  uri: /api/v1/projmgr/projects/{pid}
  description: 编辑空间
  perm_code: project.manage.*
  perm_action: create
- module: 空间管理
  route: cas
  method: DELETE
  uri: /api/v1/projmgr/projects/{pid}
  description: 删除空间
  perm_code: project.manage.*
  perm_action: create
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/projmgr/projects/{pid}/examine
  description: 开启或关闭该空间的审批
  perm_code: project.approval.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: GET
  uri: /api/v1/projmgr/projects/{pid}/exists
  description: 空间id是否已存在
  perm_code: project.manage.*
  perm_action: read
- module: 空间管理
  route: cas
  method: PUT
  uri: /api/v1/projmgr/projects/{pid}/members
  description: 编辑空间成员
  perm_code: project.member.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/projmgr/projects/{pid}/members
  description: 添加空间成员
  perm_code: project.member.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: DELETE
  uri: /api/v1/projmgr/projects/{pid}/members
  description: 删除空间成员
  perm_code: project.member.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/projmgr/projects/{pid}/members:batchDel
  description: 批量删除空间成员
  perm_code: project.member.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/usermgr/groups
  description: 添加新的用户组
  perm_code: manage-center.users.groups.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: PUT
  uri: /api/v1/usermgr/groups/{groupName}
  description: 编辑已有用户组
  perm_code: manage-center.users.groups.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: DELETE
  uri: /api/v1/usermgr/groups/{groupName}
  description: 删除指定的用户组
  perm_code: manage-center.users.groups.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/usermgr/groups:batchDel
  description: 批量删除用户组
  perm_code: manage-center.users.groups.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/usermgr/roles
  description: 添加空间角色
  perm_code: manage-center.role.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: PUT
  uri: /api/v1/usermgr/roles/{rid}
  description: 编辑空间角色
  perm_code: manage-center.role.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: DELETE
  uri: /api/v1/usermgr/roles/{rid}
  description: 删除空间角色
  perm_code: manage-center.role.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/usermgr/roles:batchDel
  description: 批量删除空间角色
  perm_code: manage-center.role.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/usermgr/users
  description: 添加新用户
  perm_code: manage-center.users.users.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/usermgr/users/import
  description: 用户导入
  perm_code: manage-center.users.users.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/usermgr/users/import/validate
  description: 用户导入校验
  perm_code: manage-center.users.users.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/usermgr/users/status
  description: 用户状态修改
  perm_code: manage-center.users.users.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: PUT
  uri: /api/v1/usermgr/users/{username}
  description: 编辑用户信息
  perm_code: manage-center.users.users.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: DELETE
  uri: /api/v1/usermgr/users/{username}
  description: 删除指定用户
  perm_code: manage-center.users.users.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/usermgr/users:batchDel
  description: 批量删除用户
  perm_code: manage-center.users.users.*
  perm_action: '*'
- module: 空间管理
  route: cas
  method: POST
  uri: /api/v1/users
  description: 创建新的用户
  perm_code: manage-center.users.users.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/codehub/k8s/instance/start
  description: 创建一个k8s实例
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/codehub/k8s/instance/{name}/stop
  description: 停止一个k8s实例
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/codespace/apps/widget
  description: 创建应用链算子代码实例
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/build/records
  description: 获取固化中的列表
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/build/{id}/logs
  description: 获取固化中记录的日志
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/images
  description: 获取镜像列表
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/images/{id}
  description: 获取镜像详情
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: PUT
  uri: /api/v1/codespace/images/{id}
  description: 编辑镜像
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: DELETE
  uri: /api/v1/codespace/images/{id}
  description: 删除镜像
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/codespace/images/{id}/clone
  description: 克隆镜像到另一个项目
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/images/{id}/clone/name
  description: 获取镜像克隆的名称
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/instances
  description: 获取代码实例列表
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/codespace/instances
  description: 创建代码实例
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/instances/templates
  description: 获取代码模板列表
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/instances/{id}
  description: 获取代码实例详情
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: PUT
  uri: /api/v1/codespace/instances/{id}
  description: 修改代码实例详情
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: DELETE
  uri: /api/v1/codespace/instances/{id}
  description: 删除代码实例
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/codespace/instances/{id}/commit
  description: 固化此实例的镜像
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/instances/{id}/dockerfile
  description: 获取实例的dockerfile模板
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/instances/{id}/events
  description: 获取代码实例启动事件
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/codespace/instances/{id}/image:build
  description: 构建此实例的镜像
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/codespace/instances/{id}/image:commit
  description: 固化此实例的镜像
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/codespace/instances/{id}/image:start
  description: 启动一个镜像代码实例
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/instances/{id}/logs
  description: 获取代码实例启动日志
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/instances/{id}/output
  description: 获取实例数据存放路径
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/instances/{id}/pod
  description: 获取实例的pod信息，只有运行中的实例可以获取到
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/instances/{id}/services
  description: 获取实例的nodeport service列表
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/codespace/instances/{id}/services
  description: 创建一个该实例的node port service, body中nodePort可以不填，且如果填的话，nodePort的范围为30000-32767; name不需要填; innerPort必填
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: DELETE
  uri: /api/v1/codespace/instances/{id}/services/{name}
  description: 删除一个该实例的node port service
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/codespace/instances/{id}/start
  description: 启动代码实例
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/codespace/instances/{id}/stop
  description: 停止代码实例
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/k8s/gpus
  description: 获取k8s的gpu信息
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/codespace/stats
  description: 统计接口
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: PUT
  uri: /api/v1/imagehub/images
  description: 编辑镜像
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/imagehub/images
  description: 创建Docker镜像
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/imagehub/images/exist
  description: 获取镜像是否存在
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/imagehub/images/infos
  description: 获取某仓库下所有镜像信息
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/imagehub/images/process
  description: 获取当前项目下所有镜像创建进度
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/imagehub/images/process/{id}
  description: 获取单个镜像创建进度
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: DELETE
  uri: /api/v1/imagehub/images/process/{id}
  description: 删除镜像创建条目信息
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/imagehub/images/process/{id}/logs
  description: 获取镜像创建日志
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/imagehub/images/process/{id}/retry
  description: 失败的镜像创建重试
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/imagehub/images/share
  description: 共享Docker镜像
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/imagehub/images/tags
  description: 获取所有镜像tag
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/imagehub/images/{digest}
  description: 获取指定镜像详情
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: DELETE
  uri: /api/v1/imagehub/images/{digest}
  description: 删除镜像
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/imagehub/templates
  description: 模板列表
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/imagehub/templates
  description: 创建模板
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: DELETE
  uri: /api/v1/imagehub/templates
  description: 删除模板，body中传需要删除的模板id, string数组
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /api/v1/imagehub/templates/{id}
  description: 获取模板
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/imagehub/templates/{id}
  description: 更新模板
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/imagehub/templates:clone
  description: 克隆模板
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/imagehub/templates:publish
  description: 发布模板
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: POST
  uri: /api/v1/imagehub/templates:save
  description: 保存模板
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /apidocs
  description: No description available
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /apidocs/swagger.json
  description: No description available
  perm_code: csm.*
  perm_action: '*'
- module: 代码空间
  route: csm
  method: GET
  uri: /apidocs/{subpath}
  description: No description available
  perm_code: csm.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v1/expense/detail
  description: 获取请求token计费明细
  perm_code: project.charging.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v1/expense/group
  description: 获取服务调用聚合结果列表
  perm_code: project.charging.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v1/expense/ratio
  description: 统计累计(消费/收入)的服务调用占比
  perm_code: project.charging.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v1/expense/rule
  description: 查询请求计费规则
  perm_code: project.charging.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v1/expense/summary
  description: 统计请求token计费明细
  perm_code: project.charging.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v1/expense/summary/graph
  description: 统计累计收入堆积图
  perm_code: project.charging.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/applet-chains/daily-user
  description: 按天聚合用户统计数据
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/count/token
  description: 统计模型服务TOKEN
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/dashboard/curve
  description: '[获取服务数量趋势，包括服务总量、上线、下线数量，按时间过滤](dashboard curve)'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/dashboard/first_token_time/bar
  description: '[获取首字时延指标监控指标趋势图](dashboard request bar overview)'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/dashboard/knowledge/docs
  description: 统计知识库文档id总量,按照时间过滤
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/dashboard/overview
  description: '[获取仪表盘-服务数量明细-当前上下线的服务数量，新上下线的服务数量](dashboard overview)'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/dashboard/request/bar
  description: '[获取仪表盘的访问量&响应时间趋势图，按时间过滤，条形图方式展示](dashboard request bar overview)'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/dashboard/request/count
  description: '[获取仪表盘的调用记录总量等](dashboard request records overview)'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/dashboard/request/daily
  description: '[统计使用度，按当天过滤](dashboard request daily overview)'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/dashboard/request/overview
  description: '[获取仪表盘的请求信息包含今日访问量，今日平均响应时间等(Pv,NewPV,Rtt等)](dashboard request overview)'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/dashboard/request/rank
  description: '[获取仪表盘的访问量排名](dashboard request rank overview)'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/dashboard/request/record/detail/{id}
  description: '[获取仪表盘的某条调用记录详情](dashboard request records detail)'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/dashboard/request/records
  description: '[获取仪表盘的调用记录](dashboard request records detail)'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: POST
  uri: /api/v2/audit/dashboard/request/records/download
  description: '[下载仪表盘的访问记录信息-导出记录](download service visit record)'
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/dashboard/token/bar
  description: 获取模型服务TOKEN趋势图，按时间过滤，条形图方式展示
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/dashboard/{type}/usage
  description: '[获取审计信息的资源使用情况(cpu使用率，内存使用率)](dashboard resource usage curve)'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: POST
  uri: /api/v2/audit/evaluate/answer
  description: 点赞or点踩
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/audit/gpu/{type}/usage
  description: '[查找资源的使用情况，支持按照 内存和利用率查找 ](dashboard resource usage curve)'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: POST
  uri: /api/v2/service
  description: 创建服务
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/service/config
  description: '[服务配置] service config'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: POST
  uri: /api/v2/service/evaluation/evaluation-mock
  description: '[服务测评]service evaluation mock'
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/service/evaluation/history-mock
  description: '[服务测试] service evaluation-history mock'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/service/evaluation/{serviceID}/history
  description: '[服务测试] service evaluation-history'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: POST
  uri: /api/v2/service/evaluation/{serviceID}}
  description: '[服务测评]service evaluation'
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/service/list
  description: 获取服务列表
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/service/options/hpa-metrics
  description: 获取扩缩容的指标选项
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: POST
  uri: /api/v2/service/remote
  description: 创建远程服务
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/service/unique
  description: '[检测服务名字是否唯一](check whether service name is unique'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: POST
  uri: /api/v2/service/{id}/code-tpls:lookup
  description: 获取服务的调用示例代码
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/service/{serviceID}
  description: 获取服务详情（包含服务与服务版本信息）
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: PUT
  uri: /api/v2/service/{serviceID}
  description: 更新服务基础信息（不包括服务版本信息）
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: DELETE
  uri: /api/v2/service/{serviceID}
  description: 删除服务(包括服务版本)
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/service/{serviceID}/api_unique
  description: '[检测服务api是否唯一](check whether service api is unique'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: PUT
  uri: /api/v2/service/{serviceID}/approval-state
  description: '[更新审批状态]update approval call'
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: POST
  uri: /api/v2/service/{serviceID}/call-api
  description: '[服务测试]api call'
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: POST
  uri: /api/v2/service/{serviceID}/call-stream-api
  description: '[服务测试]api call'
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/service/{serviceID}/events
  description: '[获取指定服务的events(和pod 解耦)](get service events)'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: POST
  uri: /api/v2/service/{serviceID}/get-api-type
  description: '[服务测试]api call'
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/service/{serviceID}/runtime_info
  description: 获取服务运行时信息：包括服务状态、pod状态等）
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/service/{serviceID}/service-state
  description: '[流式获取服务状态]'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: POST
  uri: /api/v2/service/{serviceID}/service_version
  description: 创建服务版本
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: PUT
  uri: /api/v2/service/{serviceID}/service_version/{serviceVersionID}
  description: 更新服务版本信息
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: DELETE
  uri: /api/v2/service/{serviceID}/service_version/{serviceVersionID}
  description: 删除服务版本
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: POST
  uri: /api/v2/service/{serviceID}/start
  description: 部署服务
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: POST
  uri: /api/v2/service/{serviceID}/stop
  description: 停止服务
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/service/{serviceID}/yaml
  description: 获取yaml配置
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: PUT
  uri: /api/v2/service/{serviceID}/yaml
  description: 更新yaml配置
  perm_code: mlops.services.*
  perm_action: '*'
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/service/{serviceID}/{podName}/events
  description: '[获取指定pod的events(describe pod )](get describe pod events)'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/service/{serviceID}/{podName}/pod-events
  description: '[获取指定pod的events(describe pod )](get describe pod events)'
  perm_code: mlops.services.*
  perm_action: read
- module: 服务管理
  route: serving
  method: GET
  uri: /api/v2/service/{serviceID}/{podName}/{containerID}/logs
  description: '[获取指定container的log](get pod logs)'
  perm_code: mlops.services.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v1/pipeline
  description: 获取项目下所有工作流
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v1/pipeline
  description: 创建工作流
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v1/pipeline/batch/delete
  description: delete pipeline by ids
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v1/pipeline/components
  description: 获取所有组件(流程)，组件即工作流节点的模板，节点的默认信息由组件信息填充
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v1/pipeline/unique
  description: check pipeline name is unique
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v1/pipeline/version
  description: 创建工作流版本
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v1/pipeline/version/batch/delete
  description: delete pipeline versions by ids
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v1/pipeline/version/from-yaml
  description: 通过导入pipeline yaml创建工作流版本
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v1/pipeline/version/unique
  description: check pipeline version name is unique
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v1/pipeline/version/{id}
  description: 根据id获取工作流版本
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: DELETE
  uri: /api/v1/pipeline/version/{id}
  description: delete pipeline version by id
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v1/pipeline/version/{id}/export
  description: 以yaml的形式导出工作流版本
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v1/pipeline/version/{id}/once
  description: 执行一次手动调度类型的工作流版本
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v1/pipeline/version/{id}/start
  description: 开启定时调度类型的工作流版本
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v1/pipeline/version/{id}/stop
  description: 关闭定时调度类型的工作流版本
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v1/pipeline/version/{version_id}/save
  description: 修改工作流版本的流程图和调度信息
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v1/pipeline/{id}
  description: 根据id获取工作流
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: PUT
  uri: /api/v1/pipeline/{id}
  description: 修改工作流
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: DELETE
  uri: /api/v1/pipeline/{id}
  description: 删除工作流
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v1/pipeline/{pipeline_id}/version
  description: 获取工作流下所有版本
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v1/run
  description: 获取所有run
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v1/run
  description: 提交run
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v1/run/batch/delete
  description: 批量删除run
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v1/run/events/labels
  description: 根据label获取所有event时间
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v1/run/{run_id}
  description: 根据id获取run
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: DELETE
  uri: /api/v1/run/{run_id}
  description: 根据id删除run
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v1/run/{run_id}/node/{node_id}/artifact/{artifact}
  description: 获取run中某个节点的输出文件
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v1/run/{run_id}/node/{node_id}/events
  description: 获取运行时任务中节点对应的pod事件
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v1/run/{run_id}/node/{node_id}/logs
  description: 获取run中某个节点的日志
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v1/run/{run_id}/node/{node_id}/pod
  description: 获取运行时任务中节点对应的pod信息
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v1/run/{run_id}/retry
  description: 根据id重试失败或者中止的run
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v1/run/{run_id}/terminate
  description: 根据id中止run
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v2/pipeline/version/{id}
  description: 根据id获取工作流版本
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v2/pipeline/version/{version_id}/save
  description: 修改工作流版本的流程图和调度信息
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v2/run
  description: 获取所有run
  perm_code: mlops.tasks.*
  perm_action: read
- module: 任务管理
  route: pipeline
  method: POST
  uri: /api/v2/run
  description: 提交run
  perm_code: mlops.tasks.*
  perm_action: '*'
- module: 任务管理
  route: pipeline
  method: GET
  uri: /api/v2/run/{run_id}
  description: 根据id获取run
  perm_code: mlops.tasks.*
  perm_action: read
- module: 计量计费
  route: expense
  method: GET
  uri: /api/v1/expense
  description: 消费明细分页查询
  perm_code: project.charging.*
  perm_action: '*'
- module: 计量计费
  route: expense
  method: POST
  uri: /api/v1/expense/disk
  description: 存储资源变更记录
  perm_code: project.charging.*
  perm_action: read
- module: 计量计费
  route: expense
  method: GET
  uri: /api/v1/expense/download
  description: 消费记录下载
  perm_code: project.charging.*
  perm_action: read
- module: 计量计费
  route: expense
  method: POST
  uri: /api/v1/expense/pre-calculate
  description: pod资源预估费用计算
  perm_code: project.charging.*
  perm_action: read
- module: 计量计费
  route: expense
  method: POST
  uri: /api/v1/expense/rule
  description: 计费规则新增
  perm_code: project.charging.*
  perm_action: '*'
- module: 计量计费
  route: expense
  method: PUT
  uri: /api/v1/expense/rule/base
  description: 修改基础资源单价
  perm_code: project.charging.*
  perm_action: '*'
- module: 计量计费
  route: expense
  method: PUT
  uri: /api/v1/expense/rule/base/gpu
  description: 修改gpu资源单价
  perm_code: project.charging.*
  perm_action: '*'
- module: 计量计费
  route: expense
  method: POST
  uri: /api/v1/expense/rule/base/pre-calculate
  description: 预计算策略单价
  perm_code: project.charging.*
  perm_action: '*'
- module: 计量计费
  route: expense
  method: PUT
  uri: /api/v1/expense/rule/{id}
  description: 计费规则修改
  perm_code: project.charging.*
  perm_action: '*'
- module: 计量计费
  route: expense
  method: DELETE
  uri: /api/v1/expense/rule/{id}
  description: 计费规则删除
  perm_code: project.charging.*
  perm_action: '*'
- module: 计量计费
  route: expense
  method: PATCH
  uri: /api/v1/expense/rule/{id}
  description: 计费规则修改-增量修改
  perm_code: project.charging.*
  perm_action: '*'
- module: 计量计费
  route: expense
  method: GET
  uri: /api/v1/expense/summary
  description: 计费统计信息查询
  perm_code: project.charging.*
  perm_action: read
- module: 计量计费
  route: expense
  method: GET
  uri: /api/v1/expense/summary/graph
  description: 计费统计信息堆积柱状图(消费)
  perm_code: project.charging.*
  perm_action: read
- module: 计量计费
  route: expense
  method: GET
  uri: /api/v1/report/hippoCache
  description: 资源用量排行
  perm_code: project.resource.*
  perm_action: read
- module: 计量计费
  route: expense
  method: GET
  uri: /api/v1/report/metrics/gpu/core
  description: '[GPU指标 ](gpu metrics)'
  perm_code: project.resource.*
  perm_action: read
- module: 计量计费
  route: expense
  method: GET
  uri: /api/v1/report/metrics/gpu/memory
  description: '[GPU指标 ](gpu metrics)'
  perm_code: project.resource.*
  perm_action: read
- module: 计量计费
  route: expense
  method: GET
  uri: /api/v1/report/range
  description: 资源占用时间范围内数据查询
  perm_code: project.resource.*
  perm_action: read
- module: 计量计费
  route: expense
  method: GET
  uri: /api/v1/report/rank
  description: 资源用量排行
  perm_code: project.resource.*
  perm_action: read
- module: 计量计费
  route: expense
  method: GET
  uri: /api/v1/resource/node-gpus
  description: 集群节点(仅包含存在gpu的节点)和节点下gpu列表查询
  perm_code: project.resource.*
  perm_action: read
- module: 计量计费
  route: expense
  method: GET
  uri: /api/v1/resource/node/labelselector
  description: 获取选择计算节点的 node list 配置
  perm_code: project.resource.*
  perm_action: read
- module: 计量计费
  route: expense
  method: GET
  uri: /api/v1/resource/npu-id-info
  description: npu模板
  perm_code: project.resource.*
  perm_action: read
- module: 计量计费
  route: expense
  method: GET
  uri: /api/v1/resource/npu-id-info/{id}
  description: npu模板
  perm_code: project.resource.*
  perm_action: read
- module: 计量计费
  route: expense
  method: GET
  uri: /api/v1/resource/npu-template
  description: npu模板
  perm_code: project.resource.*
  perm_action: read
- module: 计量计费
  route: expense
  method: GET
  uri: /api/v1/resource/pod-max
  description: pod最大可用资源量查询
  perm_code: project.resource.*
  perm_action: read
