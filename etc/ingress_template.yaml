apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/affinity: cookie
    nginx.ingress.kubernetes.io/use-port-in-redirects: "true"
    nginx.ingress.kubernetes.io/redirect-by-referer: "true"
    nginx.ingress.kubernetes.io/redirect-by-service-domain: "true"
    nginx.ingress.kubernetes.io/upstream-vhost: autocv-portal-service.{{.Namespace}}.svc:443
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    nginx.ingress.kubernetes.io/limit-connections: "10000"
    nginx.ingress.kubernetes.io/limit-rps: "10000"
    nginx.ingress.kubernetes.io/proxy-buffering: "on"
    nginx.ingress.kubernetes.io/limit-rate-after: "10240"
    nginx.ingress.kubernetes.io/limit-rate: {{.LimitRate}}
    nginx.ingress.kubernetes.io/proxy-body-size: "1024m"
  name: {{.Name}}
  namespace: {{.Namespace}}
spec:
  rules:
  - http:
      paths:
      - path: /llm{{.Path}}
        pathType: ImplementationSpecific
        backend:
          serviceName: {{.ServiceName}}
          servicePort: {{.ServicePort}}