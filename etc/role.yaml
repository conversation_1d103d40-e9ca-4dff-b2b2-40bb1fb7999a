# 角色信息与角色权限文件, 用于初始化和增改角色信息, 不建议在业务代码中修改
# 每次更新本文件, 将通过 id 覆盖库内数据
# 需要删除角色时, 将 isdeleted = true, 被删除角色入库后的下个版本才可从文件中删除

- id: 1001
  isdeleted: false
  name: 普通用户
  type: platform
  description: 仅支持访问指定的协作项目（被添加至项目成员时），以及平台的帮助文档。
  createuser: thinger
  permissions:
    - help-center:read
    - notifications.service-alerts.*:read
    - notifications.service-alerts.*:*
    - notifications.service-approval.*:read
    - notifications.service-approval.*:*
  namelocals:
    en: "Common User"
  desclocals:
    en: "Supports access to specified collaborative projects (when added as a project member) and the platform's help documentation"

- id: 1002
  name: 管理员
  type: platform
  description: 仅支持访问指定的协作空间（被添加至空间成员时）。同时，支持访问管理中心（创建并管理用户及角色）。
  createuser: thinger
  permissions:
    - help-center:read
    - manage-center.users.users.*:read
    - manage-center.users.users.*:*
    - manage-center.users.groups.*:read
    - manage-center.users.groups.*:*
    - manage-center.role.project.*:read
    - manage-center.role.project.*:*
    - manage-center.role.platform.*:read
    - manage-center.role.platform.*:*
    - manage-center.develop.package.*:read
    - manage-center.develop.package.*:*
    - manage-center.develop.repo.*:read
    - manage-center.develop.repo.*:*
    - notifications.service-alerts.*:read
    - notifications.service-alerts.*:*
    - notifications.service-approval.*:read
    - notifications.service-approval.*:*

  namelocals:
    en: "Administrator"
  desclocals:
    en: "Supports access to specified collaborative spaces (when added as a space member). Additionally, supports access to the management center (for creating and managing users and roles)."

- id: 1003
  name: 超级管理员
  type: platform
  description: 支持新建项目，并访问平台内所有用户创建的所有项目。同时，在任一项目内拥有全部功能权限（最高级别）。支持访问平台管理中心。
  createuser: thinger
  permissions:
    - help-center:read
    - manage-center.users.users.*:read
    - manage-center.users.users.*:*
    - manage-center.users.groups.*:read
    - manage-center.users.groups.*:*
    - manage-center.role.project.*:read
    - manage-center.role.project.*:*
    - manage-center.role.platform.*:read
    - manage-center.role.platform.*:*
    - manage-center.develop.package.*:read
    - manage-center.develop.package.*:*
    - manage-center.develop.repo.*:read
    - manage-center.develop.repo.*:*
    - manage-center.cluster.*:read
    - manage-center.cluster.*:*
    - manage-center.compute.services.*:read
    - manage-center.compute.services.*:*
    - manage-center.resource-groups.*:read
    - manage-center.resource-groups.*:*
    - manage-center.tenants.*:read
    - manage-center.tenants.*:*
    - manage-center.compute-specification.*:read
    - manage-center.compute-specification.*:*
    - manage-center.compute-monitoring.*:read
    - manage-center.compute-monitoring.*:*
    - manage-center.oem.*:read
    - manage-center.oem.*:*
    - manage-center.charging.*:read
    - manage-center.charging.*:*
    - manage-center.points.*:read
    - manage-center.points.*:*
    - notifications.service-alerts.*:read
    - notifications.service-alerts.*:*
    - notifications.service-approval.*:read
    - notifications.service-approval.*:*
    - project.manage.*:create
    - manage-center.static-assets.*:read
    - manage-center.static-assets.*:*
    - manage-center.compute-facility.*:*
    - manage-center.compute-facility.*:read
    - manage-center.basic-service.*:*
    - manage-center.basic-service.*:read

  namelocals:
    en: "Super Administrator"
  desclocals:
    en: "Supports creating new projects and accessing all projects created by all users within the platform. Additionally, has full functional permissions (highest level) in any project. Supports access to the platform management center."

- id: 2001
  name: 空间负责人
  type: project
  description: 支持访问项目内的所有页面，以及所有功能操作。
  createuser: thinger
  permissions:
    - space.home.*:*
    - space.home.*:read
    - mw.manage.my.*:*
    - mw.manage.my.*:read
    - mw.manage.remote.*:*
    - mw.manage.remote.*:read
    - mw.experience.*:*
    - mw.experience.*:read
    - mw.tools.train.*:*
    - mw.tools.train.*:read
    - mw.tools.evaluation.*:*
    - mw.tools.evaluation.*:read
    - mw.tools.quantization.*:*
    - mw.tools.quantization.*:read
    - applet.manage.*:*
    - applet.manage.*:read
    - applet.experience.*:*
    - applet.tools.plugins.*:*
    - applet.tools.plugins.*:read
    - applet.tools.operators.*:*
    - applet.tools.operators.*:read
    - corpus.manage.text-datasets.*:*
    - corpus.manage.text-datasets.*:read
    - corpus.manage.img-datasets.*:*
    - corpus.manage.img-datasets.*:read
    - corpus.tools.annotation.*:annotation-manage
    - corpus.tools.annotation.*:examine
    - corpus.tools.annotation.*:annotation
    - corpus.tools.process.*:*
    - corpus.tools.process.*:read
    - knowledge.manage.list.*:*
    - knowledge.manage.list.*:read
    - knowledge.manage.change.*:*
    - knowledge.manage.change.*:read
    - knowledge.experience.*:*
    - data.file-assets.overview.*:*
    - data.file-assets.overview.*:read
    - data.file-assets.list.*:*
    - data.file-assets.list.*:read
    - data.file-assets.catalog.*:*
    - data.file-assets.catalog.*:read
    - data.file-assets.fs.*:*
    - data.file-assets.fs.*:read
    - data.datasource.native.*:*
    - data.datasource.native.*:read
    - data.datasource.external.*:*
    - data.datasource.external.*:read
    - project.computing-power.services.*:*
    - project.computing-power.services.*:read
    - project.computing-power.tasks.*:*
    - project.computing-power.tasks.*:read
    - project.computing-power.resource.*:*
    - project.computing-power.resource.*:read
    - project.computing-power.charging.*:*
    - project.computing-power.charging.*:read
    - project.general-tools.csm.*:*
    - project.general-tools.images.*:*
    - project.general-tools.prompts.*:*
    - project.general-tools.prompts.*:read
    - project.general-tools.workflow.*:*
    - project.general-tools.workflow.*:read
    - project.general-tools.security.*:*
    - project.general-tools.security.*:read
    - project.project-manage.member.*:*
    - project.project-manage.member.*:read
    - project.project-manage.api-key.*:*
    - project.project-manage.api-key.*:read
    - project.project-manage.approval.*:*
    - project.project-manage.approval.*:read
    - project.project-manage.audit-logs.*:*
    - project.project-manage.audit-logs.*:read
    - project.project-manage.points.*:*
  namelocals:
    en: "Project Manager"
  desclocals:
    en: "Supports access to all pages and all functional operations within the project"

- id: 2002
  name: 访客
  type: project
  description: 支持访问项目内样本仓库模块的所有功能。
  createuser: thinger
  permissions:
    - space.home.*:read
    - mw.experience.*:*
    - applet.experience.*:*
    - knowledge.experience.*:*
  namelocals:
    en: "Guest"
  desclocals:
    en: "Supports access to all features in the sample repository module within the project"

- id: 2003
  name: 开发人员
  type: project
  description: 支持访问内样本仓库、模型微调、模型仓库模块的所有功能。
  createuser: thinger
  permissions:
    - space.home.*:*
    - space.home.*:read
    - mw.manage.my.*:*
    - mw.manage.my.*:read
    - mw.manage.remote.*:*
    - mw.manage.remote.*:read
    - mw.experience.*:*
    - mw.experience.*:read
    - mw.tools.train.*:*
    - mw.tools.train.*:read
    - mw.tools.evaluation.*:*
    - mw.tools.evaluation.*:read
    - mw.tools.quantization.*:*
    - mw.tools.quantization.*:read
    - applet.manage.*:*
    - applet.manage.*:read
    - applet.experience.*:*
    - applet.tools.plugins.*:*
    - applet.tools.plugins.*:read
    - applet.tools.operators.*:*
    - applet.tools.operators.*:read
    - corpus.manage.text-datasets.*:*
    - corpus.manage.text-datasets.*:read
    - corpus.manage.img-datasets.*:*
    - corpus.manage.img-datasets.*:read
    - corpus.tools.annotation.*:annotation-manage
    - corpus.tools.annotation.*:examine
    - corpus.tools.annotation.*:annotation
    - corpus.tools.process.*:*
    - corpus.tools.process.*:read
    - knowledge.manage.list.*:*
    - knowledge.manage.list.*:read
    - knowledge.manage.change.*:*
    - knowledge.manage.change.*:read
    - knowledge.experience.*:*
    - data.file-assets.overview.*:*
    - data.file-assets.overview.*:read
    - data.file-assets.list.*:*
    - data.file-assets.list.*:read
    - data.file-assets.catalog.*:*
    - data.file-assets.catalog.*:read
    - data.file-assets.fs.*:*
    - data.file-assets.fs.*:read
    - data.datasource.native.*:*
    - data.datasource.native.*:read
    - data.datasource.external.*:*
    - data.datasource.external.*:read
    - project.computing-power.services.*:*
    - project.computing-power.services.*:read
    - project.computing-power.tasks.*:*
    - project.computing-power.tasks.*:read
    - project.computing-power.resource.*:*
    - project.computing-power.resource.*:read
    - project.computing-power.charging.*:*
    - project.computing-power.charging.*:read
    - project.general-tools.csm.*:*
    - project.general-tools.images.*:*
    - project.general-tools.prompts.*:*
    - project.general-tools.prompts.*:read
    - project.general-tools.workflow.*:*
    - project.general-tools.workflow.*:read
    - project.general-tools.security.*:*
    - project.general-tools.security.*:read
    - project.project-manage.member.*:*
    - project.project-manage.member.*:read
    - project.project-manage.api-key.*:*
    - project.project-manage.api-key.*:read
    - project.project-manage.approval.*:*
    - project.project-manage.approval.*:read
    - project.project-manage.audit-logs.*:*
    - project.project-manage.audit-logs.*:read
    - project.project-manage.points.*:*
  namelocals:
    en: "Developer"
  desclocals:
    en: "Supports access to all features in the sample repository, model fine-tuning, and model repository modules within the project"

- id: 2004
  name: 内置数据共享
  type: project
  description: 支持查看内置数据所属模块，包括【样本仓库】标注集、【模型仓库】原子模型&模型方案、【代码空间】镜像管理。
  createuser: thinger
  permissions:
    - space.home.*:read
    - mw.manage.my.*:read
    - mw.manage.remote.*:read
    - mw.experience.*:*
    - applet.manage.*:read
    - applet.experience.*:*
    - knowledge.manage.list.*:read
    - knowledge.manage.change.*:read
    - knowledge.experience.*:*
    - data.file-assets.catalog.*:read
  namelocals:
    en: "Built-in Data Sharing"
  desclocals:
    en: "Supports viewing modules associated with built-in data, including [Sample Repository] annotation sets, [Model Repository] atomic models & model configurations, and [Code Space] image management"
