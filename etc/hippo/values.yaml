# Default values for charts.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

global:
  # images 所有镜像相关的操作
  images:
    # registry 所有镜像所在的Docker Registry / Harbor 地址
    registry: ***********
    # baseRepo 为镜像在 Registry/Harbor 中的首层目录
    baseRepo: aip
    # baseTag 所有相关镜像的基础 ImageTag, 一般用来控制使用镜像的版本
    baseTag: master
    # pullPolicy 为启动服务前的默认镜像下载策略，测试时通常为Always.
    pullPolicy: Always
    repo:
      depsHippo: deps/hippo-1.2.0

  # secrets 统一管理各个基础组件的用户名与密码
  secrets:

  storage:
    # nfs 为新建 NFS StorageClass 过程中的部分配置
    nfs:
      server: *************
      path: /mnt/nfs
      mountOptions:
    # sc 为 StorageClass 相关配置
    sc:
      create: true

      # Set StorageClass as the default StorageClass
      # Ignored if storageClass.create is false
      defaultClass: false

      ## Set the StorageClass name
      # Ignored if storageClass.create is false
      name: autocv-hippo-nfs-sc

      # Allow volume to be expanded dynamically
      allowVolumeExpansion: true

      # Method used to reclaim an obsoleted volume
      reclaimPolicy: Retain

      # When set to false your PVs will not be archived by the provisioner upon deletion of the PVC.
      archiveOnDelete: true

      # Set access mode - ReadWriteOnce, ReadOnlyMany or ReadWriteMany
      accessModes: ReadWriteMany
    pvName: autocv-hippo-pv
    pvcName: llmops-pvc
    # request 为申请的存储空间大小
    request: 100Mi
    accessModes: ReadWriteMany

tags:
  hippo-volume: false

replicaCount: 1

imagePullSecrets: []
nameOverride: "autocv-hippo"
fullnameOverride: "autocv-hippo"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: { }

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: NodePort
  port: 30306

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
      - path: /
        backend:
          serviceName: chart-example.local
          servicePort: 80
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 10000m
    memory: 10240Mi
  requests:
    cpu: 1000m
    memory: 1024Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

# nfs方式挂载
nfs:
  path: /opt/sophon-edge
  server: *************

password: password