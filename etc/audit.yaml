audit_record:
  apis:
  - api_desc: 新建空间{空间名称}，id为{空间ID}
    api_method: POST
    api_module: cas
    api_path: /api/v1/projmgr/projects
    condition: null
    go_template: 新建空间{{ .req_body.name }}，id为{{ .req_body.project_id }}
    module: SPACE
    op_type: CREATE
    sub_module: SPACE_INFO_MANAGEMENT
  - api_desc: 修改空间{旧-空间名称}的名称为{新-空间名称}
    api_method: PUT
    api_module: cas
    api_path: /api/v1/projmgr/projects/([^/]*)
    condition: null
    go_template: 修改空间{{ index .matched_paths 1 }}
    module: SPACE
    op_type: UPDATE
    sub_module: SPACE_INFO_MANAGEMENT
  - api_desc: 修改空间默认服务为{服务名称}
    api_method: PUT
    api_module: applet
    api_path: /api/v1/global-llm/projects-llm
    condition: null
    go_template: 修改空间默认服务为{{ .req_body.llm_model_svc.name }}
    module: SPACE
    op_type: UPDATE
    sub_module: SPACE_INFO_MANAGEMENT
  - api_desc: 删除空间{空间名称}
    api_method: DELETE
    api_module: cas
    api_path: /api/v1/projmgr/projects/([^/]*)
    condition: null
    go_template: 删除空间{{ index .matched_paths 1 }}
    module: SPACE
    op_type: DELETE
    sub_module: SPACE_INFO_MANAGEMENT
  - api_desc: 添加空间成员{用户名称}，并配置角色{角色名称}
    api_method: POST
    api_module: cas
    api_path: /api/v1/projmgr/projects/([^/]*)/members
    condition: null
    go_template: '{{- $length := len .req_body -}}{{- range $index, $item := .req_body
      -}}添加空间成员{{ $item.name }}，并配置角色{{ $item.role_id }}{{if lt $index (sub $length
      1)}}；{{end}}{{- end -}}'
    module: SPACE
    op_type: CREATE
    sub_module: SPACE_MEMBER_MANAGEMENT
  - api_desc: 修改空间成员{用户名称}的角色为{角色名称}
    api_method: PUT
    api_module: cas
    api_path: /api/v1/projmgr/projects/([^/]*)/members
    condition: null
    go_template: '{{- $length := len .req_body -}}{{- range $index, $item := .req_body
      -}}修改空间成员{{ $item.name }}的角色为{{ $item.role_id }}{{if lt $index (sub $length
      1)}}；{{end}}{{- end -}}'
    module: SPACE
    op_type: UPDATE
    sub_module: SPACE_MEMBER_MANAGEMENT
  - api_desc: 删除空间成员{用户名称}
    api_method: POST
    api_module: cas
    api_path: /api/v1/projmgr/projects/([^/]*)/members:batchDel
    condition: null
    go_template: 删除空间成员{{- $length := len .req_body -}}{{- range $index, $item :=
      .req_body -}}{{ $item.name }}{{if lt $index (sub $length 1)}}，{{end}}{{- end
      -}}
    module: SPACE
    op_type: DELETE
    sub_module: SPACE_MEMBER_MANAGEMENT
  - api_desc: 用户登录
    api_method: GET
    api_module: login
    api_path: /api/v1/auth/cas/login
    condition: null
    go_template: 用户登录
    module: SPACE
    op_type: CREATE
    sub_module: SPACE_MEMBER_MANAGEMENT
  - api_desc: 用户注销
    api_method: GET
    api_module: cas
    api_path: /api/logout
    condition: null
    go_template: 用户注销
    module: SPACE
    op_type: DELETE
    sub_module: SPACE_MEMBER_MANAGEMENT
  - api_desc: 新建模型{模型名称}
    api_method: POST
    api_module: mw
    api_path: /api/v1/mwh/models
    condition: null
    go_template: 新建模型{{.req_body.name}}
    module: MODEL
    op_type: CREATE
    sub_module: MODEL_MANAGEMENT
  - api_desc: 新建模型版本{模型名称}/{模型版本名称}
    api_method: POST
    api_module: mw
    api_path: /api/v1/mwh/models/([^/]*)/releases
    condition: null
    go_template: 新建模型版本{{ .req_body.release_base.model_id }}/{{ .req_body.release_base.name
      }}
    module: MODEL
    op_type: CREATE
    sub_module: MODEL_MANAGEMENT
  - api_desc: 新建远程模型{模型名称}
    api_method: POST
    api_module: mw
    api_path: /api/v1/mwh/svcmgr/remote-services
    condition: null
    go_template: 新建远程模型{{.req_body.name}}
    module: MODEL
    op_type: CREATE
    sub_module: MODEL_MANAGEMENT
  - api_desc: 修改模型{旧-模型名称}的名称为{新-模型名称}
    api_method: PUT
    api_module: mw
    api_path: /api/v1/mwh/models/([^/]*)
    condition: null
    go_template: 修改模型{{ index .matched_paths 1}}
    module: MODEL
    op_type: UPDATE
    sub_module: MODEL_MANAGEMENT
  - api_desc: 修改模型版本{模型名称}/{旧-模型版本名称}的名称为{新-模型版本名称}
    api_method: PUT
    api_module: mw
    api_path: /api/v1/mwh/models/([^/]*)/releases/([^/]*)
    condition: null
    go_template: 修改模型版本{{ index .matched_paths 1}}/{{ index .matched_paths 2}}
    module: MODEL
    op_type: UPDATE
    sub_module: MODEL_MANAGEMENT
  - api_desc: 导出模型{模型名称}
    api_method: POST
    api_module: mw
    api_path: /api/v1/mwh/models:export
    condition: null
    go_template: 导出模型
    module: MODEL
    op_type: UPDATE
    sub_module: MODEL_MANAGEMENT
  - api_desc: 共享模型版本{模型名称}/{模型版本名称}至公共空间
    api_method: POST
    api_module: mw
    api_path: /api/v1/mwh/rscmgr/resources:clone
    condition: '{{ eq .req_body.target_project "assets" }}'
    go_template: '{{- $req_body := .req_body -}}{{- $length := len .req_body.cloned_models
      -}}{{- range $index, $model := .req_body.cloned_models -}}{{- $release := index
      $req_body.cloned_specific_releases $index -}}共享模型版本{{ $model.id }}/{{ $release.id
      }}至公共空间{{if lt $index (sub $length 1)}}；{{end}}{{- end -}}'
    module: MODEL
    op_type: UPDATE
    sub_module: MODEL_MANAGEMENT
  - api_desc: 删除模型{模型名称}
    api_method: DELETE
    api_module: mw
    api_path: /api/v1/mwh/models/([^/]*)
    condition: null
    go_template: 删除模型{{ index .matched_paths 1}}
    module: MODEL
    op_type: DELETE
    sub_module: MODEL_MANAGEMENT
  - api_desc: 删除模型版本{模型名称}/{模型版本名称}
    api_method: DELETE
    api_module: mw
    api_path: /api/v1/mwh/models/([^/]*)/releases/([^/]*)
    condition: null
    go_template: 删除模型版本{{ index .matched_paths 1}}/{{ index .matched_paths 2}}
    module: MODEL
    op_type: DELETE
    sub_module: MODEL_MANAGEMENT
  - api_desc: 对模型{模型名称}提交访问请求
    api_method: POST
    api_module: mw
    api_path: /api/v1/mwh/svcmgr/services/([^/]*)/infer
    condition: null
    go_template: 对模型{{ .req_body.model_id }}/{{ .req_body.release_id }}提交访问请求
    module: MODEL
    op_type: CREATE
    sub_module: MODEL_EXPERIENCE
  - api_desc: 新建训练任务{任务名称}
    api_method: POST
    api_module: finetune
    api_path: /api/trainer/finetune
    condition: null
    go_template: 新建训练任务{{ .req_body.name }}
    module: MODEL
    op_type: CREATE
    sub_module: MODEL_TRAINING
  - api_desc: 停止训练任务{任务名称}
    api_method: POST
    api_module: finetune
    api_path: /api/trainer/taskversion/([^/]*)/stop
    condition: null
    go_template: 停止训练任务{{ index .matched_paths 1}}
    module: MODEL
    op_type: UPDATE
    sub_module: MODEL_TRAINING
  - api_desc: 从故障中恢复训练任务{任务名称}
    api_method: POST
    api_module: finetune
    api_path: /api/trainer/finetune/([^/]*)/re-train
    condition: null
    go_template: 从故障中恢复训练任务{{ index .matched_paths 1}}
    module: MODEL
    op_type: UPDATE
    sub_module: MODEL_TRAINING
  - api_desc: 删除训练任务{任务名称}
    api_method: DELETE
    api_module: finetune
    api_path: /api/trainer/taskversion/([^/]*)
    condition: null
    go_template: 删除训练任务{{ index .matched_paths 1}}
    module: MODEL
    op_type: DELETE
    sub_module: MODEL_TRAINING
  - api_desc: 新建评估任务{任务名称}
    api_method: POST
    api_module: mw
    api_path: /api/v1/mwh/evalmgr/missions
    condition: null
    go_template: 新建模型评估任务{{ .req_body.name }}
    module: MODEL
    op_type: CREATE
    sub_module: MODEL_EVALUATION
  - api_desc: 新建评估模板{模板名称}
    api_method: POST
    api_module: mw
    api_path: /api/v1/mwh/evalmgr/templates
    condition: null
    go_template: 新建模型评估模板{{ .req_body.name }}
    module: MODEL
    op_type: CREATE
    sub_module: MODEL_EVALUATION
  - api_desc: 修改评估任务{旧-任务名称}的名称为{新任务名称}
    api_method: PUT
    api_module: mw
    api_path: /api/v1/mwh/evalmgr/missions/([^/]*)
    condition: null
    go_template: 修改模型评估任务{{ index .matched_paths 1}}
    module: MODEL
    op_type: UPDATE
    sub_module: MODEL_EVALUATION
  - api_desc: 修改评估模板{模板名称}
    api_method: PUT
    api_module: mw
    api_path: /api/v1/mwh/evalmgr/templates/([^/]*)
    condition: null
    go_template: 修改模型评估模板{{ index .matched_paths 1}}
    module: MODEL
    op_type: UPDATE
    sub_module: MODEL_EVALUATION
  - api_desc: 删除评估任务{任务名称}
    api_method: DELETE
    api_module: mw
    api_path: /api/v1/mwh/evalmgr/missions
    condition: null
    go_template: 删除模型评估任务{{ .req_body.ids }}
    module: MODEL
    op_type: DELETE
    sub_module: MODEL_EVALUATION
  - api_desc: 新建Prompt模板{模板名称}
    api_method: POST
    api_module: cv
    api_path: /api/prompt/scenes/([^/]*)/templates
    condition: null
    go_template: 新建Prompt模板{{.req_body.name}}
    module: MODEL
    op_type: CREATE
    sub_module: PROMPT_ENGINEERING
  - api_desc: 修改Prompt模板{模板名称}的参数
    api_method: PUT
    api_module: cv
    api_path: /api/prompt/scenes/([^/]*)/templates/([^/]*)
    condition: null
    go_template: 修改Prompt模板{{ index .matched_paths 2 }}的参数
    module: MODEL
    op_type: UPDATE
    sub_module: PROMPT_ENGINEERING
  - api_desc: 发布Prompt模板{模板名称}
    api_method: POST
    api_module: cv
    api_path: /api/prompt/scenes/([^/]*)/templates/([^/]*)/publish
    condition: null
    go_template: 发布Prompt模板{{ index .matched_paths 2 }}
    module: MODEL
    op_type: UPDATE
    sub_module: PROMPT_ENGINEERING
  - api_desc: 删除Prompt模板{模板名称}
    api_method: DELETE
    api_module: cv
    api_path: /api/prompt/scenes/([^/]*)/templates/([^/]*)
    condition: null
    go_template: 删除Prompt模板{{ index .matched_paths 2 }}
    module: MODEL
    op_type: DELETE
    sub_module: PROMPT_ENGINEERING
  - api_desc: 新建应用{应用名称}
    api_method: POST
    api_module: applet
    api_path: /api/v1/applet/chains
    condition: null
    go_template: 新建应用链应用{{.req_body.name}}
    module: APP
    op_type: CREATE
    sub_module: APP_MANAGEMENT
  - api_desc: 新建应用{应用名称}
    api_method: POST
    api_module: applet
    api_path: /api/v1/app/assistants
    condition: null
    go_template: 新建智能助手应用{{.req_body.experiment_info.name}}
    module: APP
    op_type: CREATE
    sub_module: APP_MANAGEMENT
  - api_desc: 新建应用{应用名称}
    api_method: POST
    api_module: applet
    api_path: /api/v1/applet/chains/clone
    condition: '{{ ne .req_body.target_project_id "assets" }}'
    go_template: 克隆应用{{.req_body.target_chain_name}}至空间{{.req_body.target_project_id}}
    module: APP
    op_type: CREATE
    sub_module: APP_MANAGEMENT
  - api_desc: 修改应用{应用名称}的参数
    api_method: PATCH
    api_module: applet
    api_path: /api/v1/applet/chains/([^/]*)/base-info
    condition: null
    go_template: 修改应用{{ .req_body.name }}
    module: APP
    op_type: UPDATE
    sub_module: APP_MANAGEMENT
  - api_desc: 发布应用{应用名称}
    api_method: POST
    api_module: applet
    api_path: /api/v1/app/applications:publish
    condition: null
    go_template: 发布应用{{.req_body.id}}
    module: APP
    op_type: UPDATE
    sub_module: APP_MANAGEMENT
  - api_desc: 共享应用{应用名称}至公共空间
    api_method: POST
    api_module: applet
    api_path: /api/v1/applet/chains/clone
    condition: '{{ eq .req_body.target_project_id "assets" }}'
    go_template: 共享应用{.req_body.target_chain_name}}至公共空间
    module: APP
    op_type: UPDATE
    sub_module: APP_MANAGEMENT
  - api_desc: 删除应用{应用名称}
    api_method: POST
    api_module: applet
    api_path: /api/v1/applet/chains:batch-delete
    condition: null
    go_template: 删除应用{{- $length := len .req_body.ids -}}{{- range $index, $element
      := .req_body.ids -}}{{ $element }}{{if lt $index (sub $length 1)}},{{end}}{{-
      end -}}
    module: APP
    op_type: DELETE
    sub_module: APP_MANAGEMENT
  - api_desc: 对应用{应用名称}提交访问请求
    api_method: POST
    api_module: applet
    api_path: /api/v1/applet/chains:run
    condition: null
    go_template: 对应用{{ .req_body.chain_id }}提交访问请求
    module: APP
    op_type: CREATE
    sub_module: APP_EXPERIENCE
  - api_desc: 新建插件{插件名称}
    api_method: POST
    api_module: applet
    api_path: /api/v1/tool/collections
    condition: null
    go_template: 新建插件{{ .req_body.base_info.name }}
    module: APP
    op_type: CREATE
    sub_module: APP_PLUGIN_MANAGEMENT
  - api_desc: 修改插件{插件名称}的参数
    api_method: PUT
    api_module: applet
    api_path: /api/v1/tool/collections/([^/]*)
    condition: null
    go_template: 修改插件{{ .req_body.base_info.name }}的参数
    module: APP
    op_type: UPDATE
    sub_module: APP_PLUGIN_MANAGEMENT
  - api_desc: 发布插件{插件名称}
    api_method: POST
    api_module: applet
    api_path: /api/v1/tool/collections:publish
    condition: null
    go_template: 发布插件{{.req_body.id}}
    module: APP
    op_type: UPDATE
    sub_module: APP_PLUGIN_MANAGEMENT
  - api_desc: 取消发布插件{插件名称}
    api_method: POST
    api_module: applet
    api_path: /api/v1/tool/collections:cancel_publish
    condition: null
    go_template: 取消发布插件{{.req_body.id}}
    module: APP
    op_type: UPDATE
    sub_module: APP_PLUGIN_MANAGEMENT
  - api_desc: 删除插件{插件名称}
    api_method: POST
    api_module: applet
    api_path: /api/v1/tool/collections:batch_delete
    condition: null
    go_template: 删除插件{{- $length := len .req_body.ids -}}{{- range $index, $element
      := .req_body.ids -}}{{ $element }}{{if lt $index (sub $length 1)}},{{end}}{{-
      end -}}
    module: APP
    op_type: DELETE
    sub_module: APP_PLUGIN_MANAGEMENT
  - api_desc: 新建自定义算子{算子名称}
    api_method: POST
    api_module: applet
    api_path: /api/v1/applet/custom-widgets
    condition: null
    go_template: 新建自定义算子{{.req_body.custom_widget_info.name}}
    module: APP
    op_type: CREATE
    sub_module: CUSTOM_OPERATOR
  - api_desc: 修改自定义算子{算子名称}的参数
    api_method: PATCH
    api_module: applet
    api_path: /api/v1/applet/custom-widgets
    condition: null
    go_template: 修改自定义算子{{.req_body.custom_widget_info.name}}的参数
    module: APP
    op_type: UPDATE
    sub_module: CUSTOM_OPERATOR
  - api_desc: 删除自定义算子{算子名称}
    api_method: DELETE
    api_module: applet
    api_path: /api/v1/applet/custom-widgets
    condition: null
    go_template: 删除自定义算子{{ .req_body.id }}
    module: APP
    op_type: DELETE
    sub_module: CUSTOM_OPERATOR
  - api_desc: 新建知识库{知识库名称}
    api_method: POST
    api_module: applet
    api_path: /api/v1/knowlhub/kbs
    condition: null
    go_template: 新建知识库{{ .req_body.name }}
    module: KNOWLEDGE
    op_type: CREATE
    sub_module: KNOWLEDGE_MANAGEMENT
  - api_desc: 在知识库{知识库名称}中，新建知识入库
    api_method: POST
    api_module: applet
    api_path: /api/v1/knowlhub/docs:sync-submit
    condition: null
    go_template: 在知识库{{ .req_body.knowledge_base_id }}中，新建知识入库
    module: KNOWLEDGE
    op_type: CREATE
    sub_module: KNOWLEDGE_MANAGEMENT
  - api_desc: 在知识库{知识库名称}中，新建查询（召回）
    api_method: POST
    api_module: applet
    api_path: /api/v1/knowlhub/kbs:retrieve
    condition: null
    go_template: 在知识库{{ .req_body.knowledge_base_id }}中，新建查询（召回）
    module: KNOWLEDGE
    op_type: CREATE
    sub_module: KNOWLEDGE_MANAGEMENT
  - api_desc: 修改知识库{知识库名称}的参数
    api_method: PUT
    api_module: applet
    api_path: /api/v1/knowlhub/kbs/([^/]*)
    condition: null
    go_template: 修改知识库{{ index .matched_paths 1 }}的参数
    module: KNOWLEDGE
    op_type: UPDATE
    sub_module: KNOWLEDGE_MANAGEMENT
  - api_desc: 删除知识库{知识库名称}
    api_method: DELETE
    api_module: applet
    api_path: /api/v1/knowlhub/kbs/([^/]*)
    condition: null
    go_template: 删除知识库{{ index .matched_paths 1 }}
    module: KNOWLEDGE
    op_type: DELETE
    sub_module: KNOWLEDGE_MANAGEMENT
  - api_desc: 在知识库{知识库名称}中，删除文档{文档名称}
    api_method: POST
    api_module: applet
    api_path: /api/v1/knowlhub/docs:remove
    condition: null
    go_template: 在知识库{{ .req_body.knowledge_base_id }}中，删除文档{{ .req_body.doc_id }}
    module: KNOWLEDGE
    op_type: DELETE
    sub_module: KNOWLEDGE_MANAGEMENT
  - api_desc: 在知识库{知识库名称1；知识库名称2；知识库名称3}中，新建查询（召回）
    api_method: POST
    api_module: applet
    api_path: /api/v1/knowlhub/kbs:cross-retrieve
    condition: null
    go_template: 在知识库{{- $length := len .req_body.ranges -}}{{- range $index, $element
      := .req_body.ranges -}}{{ $element.knowledge_base_id }}{{if lt $index (sub $length
      1)}},{{end}}{{- end -}}中，新建查询（召回）
    module: KNOWLEDGE
    op_type: CREATE
    sub_module: KNOWLEDGE_EXPERIENCE
  - api_desc: 新建数据集{数据集名称}
    api_method: POST
    api_module: cv
    api_path: /api/samplemgr/datasets
    condition: null
    go_template: 新建数据集{{ .req_body.name }}
    module: CORPUS
    op_type: CREATE
    sub_module: CORPUS_MANAGEMENT
  - api_desc: 修改数据集{数据集名称}
    api_method: PUT
    api_module: cv
    api_path: /api/samplemgr/datasets/([^/]*)
    condition: null
    go_template: 修改数据集{{ index .matched_paths 1 }}
    module: CORPUS
    op_type: UPDATE
    sub_module: CORPUS_MANAGEMENT
  - api_desc: 批量删除数据集{数据集名称}
    api_method: DELETE
    api_module: cv
    api_path: /api/samplemgr/datasets
    condition: null
    go_template: 批量删除数据集{{ .query_params.sids }}
    module: CORPUS
    op_type: DELETE
    sub_module: CORPUS_MANAGEMENT
  - api_desc: 批量导出数据集{数据集名称}
    api_method: POST
    api_module: cv
    api_path: /api/samplemgr/datasets:export
    condition: null
    go_template: 批量导出数据集{{ .req_body.sids }}
    module: CORPUS
    op_type: CREATE
    sub_module: CORPUS_MANAGEMENT
  - api_desc: 克隆数据集{数据集名称}到新数据集{新数据集名称}
    api_method: POST
    api_module: cv
    api_path: /api/samplemgr/datasets/([^/]*)/clone
    condition: null
    go_template: 克隆数据集{{ index .matched_paths 1 }}到新数据集{{ .req_body.name }}
    module: CORPUS
    op_type: CREATE
    sub_module: CORPUS_MANAGEMENT
  - api_desc: 导出数据集版本中的文件{数据集名称}/{版本名称}
    api_method: GET
    api_module: cv
    api_path: /api/samplemgr/datasets/([^/]*)/versions/([^/]*)/texts:export
    condition: null
    go_template: 导出数据集版本中的文件{{ index .matched_paths 1 }}/{{ index .matched_paths 2
      }}
    module: CORPUS
    op_type: CREATE
    sub_module: CORPUS_MANAGEMENT
  - api_desc: 新建数据集版本{数据集名称}/{版本名称}
    api_method: POST
    api_module: cv
    api_path: /api/samplemgr/datasets/([^/]*)/versions
    condition: null
    go_template: 新建数据集版本{{ index .matched_paths 1 }}/{{ .req_body.name }}
    module: CORPUS
    op_type: CREATE
    sub_module: CORPUS_MANAGEMENT
  - api_desc: 修改数据集版本{数据集名称}/{版本名称}
    api_method: PUT
    api_module: cv
    api_path: /api/samplemgr/datasets/([^/]*)/versions/([^/]*)
    condition: null
    go_template: 修改数据集版本{{ index .matched_paths 1 }}/{{ index .matched_paths 2 }}
    module: CORPUS
    op_type: UPDATE
    sub_module: CORPUS_MANAGEMENT
  - api_desc: 删除数据集版本{数据集名称}/{版本名称}
    api_method: DELETE
    api_module: cv
    api_path: /api/samplemgr/datasets/([^/]*)/versions/([^/]*)
    condition: null
    go_template: 删除数据集版本{{ index .matched_paths 1 }}/{{ index .matched_paths 2 }}
    module: CORPUS
    op_type: DELETE
    sub_module: CORPUS_MANAGEMENT
  - api_desc: 添加数据集版本文件{数据集名称}/{版本名称}
    api_method: POST
    api_module: cv
    api_path: /api/samplemgr/datasets/([^/]*)/versions/([^/]*)/files:import
    condition: null
    go_template: 添加数据集版本文件{{ index .matched_paths 1 }}/{{ index .matched_paths 2 }}
    module: CORPUS
    op_type: CREATE
    sub_module: CORPUS_MANAGEMENT
  - api_desc: 删除数据集版本文件{数据集名称}/{版本名称}
    api_method: DELETE
    api_module: cv
    api_path: /api/samplemgr/datasets/([^/]*)/versions/([^/]*)/files
    condition: null
    go_template: 删除数据集版本文件{{ index .matched_paths 1 }}/{{ index .matched_paths 2 }}
    module: CORPUS
    op_type: DELETE
    sub_module: CORPUS_MANAGEMENT
  - api_desc: 修改数据集版本的Schema{数据集名称}/{版本名称}
    api_method: PUT
    api_module: cv
    api_path: /api/samplemgr/datasets/([^/]*)/versions/([^/]*)/schema
    condition: null
    go_template: 修改数据集版本的Schema{{ index .matched_paths 1 }}/{{ index .matched_paths
      2 }}
    module: CORPUS
    op_type: UPDATE
    sub_module: CORPUS_MANAGEMENT
  - api_desc: 发布/取消发布数据集版本{数据集名称}/{版本名称}
    api_method: PUT
    api_module: cv
    api_path: /api/samplemgr/datasets/([^/]*)/versions/([^/]*)/publish
    condition: null
    go_template: 发布/取消发布数据集版本{{ index .matched_paths 1 }}/{{ index .matched_paths
      2 }}
    module: CORPUS
    op_type: CREATE
    sub_module: CORPUS_MANAGEMENT
  - api_desc: 共享数据集版本{数据集名称}/{版本名称}到新数据集版本{新数据集名称}/{新版本名称}
    api_method: POST
    api_module: cv
    api_path: /api/samplemgr/datasets/([^/]*)/versions/([^/]*)/clone
    condition: null
    go_template: 共享数据集版本{{ index .matched_paths 1 }}/{{ index .matched_paths 2 }}到新数据集版本{{
      .req_body.sampleImageDataset.id }}/{{ .req_body.sampleImageDataset.name }}
    module: CORPUS
    op_type: CREATE
    sub_module: CORPUS_MANAGEMENT
  - api_desc: 新建语料处理任务{语料处理任务名称}
    api_method: POST
    api_module: cv
    api_path: /api/processing/tasks
    condition: null
    go_template: 新建语料处理任务{{ .req_body.name }}
    module: CORPUS
    op_type: CREATE
    sub_module: CORPUS_PROCESSING
  - api_desc: 修改语料处理任务{语料处理任务名称}的参数
    api_method: PUT
    api_module: cv
    api_path: /api/processing/tasks/([^/]*)
    condition: null
    go_template: 修改语料处理任务{{ .req_body.name }}的参数
    module: CORPUS
    op_type: UPDATE
    sub_module: CORPUS_PROCESSING
  - api_desc: 删除语料处理任务{语料处理任务名称}
    api_method: DELETE
    api_module: cv
    api_path: /api/processing/tasks
    condition: null
    go_template: 删除语料处理任务{{ .query_params.tids }}
    module: CORPUS
    op_type: DELETE
    sub_module: CORPUS_PROCESSING
  - api_desc: 新建语料标注任务{语料标注任务名称}
    api_method: POST
    api_module: cv
    api_path: /api/annotation/set
    condition: null
    go_template: 新建语料标注任务{{ .req_body.name }}
    module: CORPUS
    op_type: CREATE
    sub_module: CORPUS_LABELING
  - api_desc: 新建语料标注集{语料标注集名称}
    api_method: POST
    api_module: cv
    api_path: /api/annotation/family
    condition: null
    go_template: 新建语料标注集{{ .req_body.name }}
    module: CORPUS
    op_type: CREATE
    sub_module: CORPUS_LABELING
  - api_desc: 新建语料标注标签{语料标注标签名称}
    api_method: POST
    api_module: cv
    api_path: /api/annotation/label
    condition: null
    go_template: 新建语料标注标签{{ .req_body.name }}
    module: CORPUS
    op_type: CREATE
    sub_module: CORPUS_LABELING
  - api_desc: 新建语料标注标签组{语料标注标签组名称}
    api_method: POST
    api_module: cv
    api_path: /api/annotation/label/group
    condition: null
    go_template: 新建语料标注标签组{{ .req_body.name }}
    module: CORPUS
    op_type: CREATE
    sub_module: CORPUS_LABELING
  - api_desc: 修改语料标注任务{语料标注任务名称}的参数
    api_method: PUT
    api_module: cv
    api_path: /api/annotation/set/([^/]*)
    condition: null
    go_template: 修改语料标注任务{{ index .matched_paths 1 }}的参数
    module: CORPUS
    op_type: UPDATE
    sub_module: CORPUS_LABELING
  - api_desc: 修改语料标注集{语料标注集名称}的参数
    api_method: PUT
    api_module: cv
    api_path: /api/annotation/family/([^/]*)
    condition: null
    go_template: 修改语料标注集{{ index .matched_paths 1 }}的参数
    module: CORPUS
    op_type: UPDATE
    sub_module: CORPUS_LABELING
  - api_desc: 修改语料标注任务标签{语料标注标签名称}的参数
    api_method: PUT
    api_module: cv
    api_path: /api/annotation/label/([^/]*)
    condition: null
    go_template: 修改语料标注标签{{ index .matched_paths 1 }}的参数
    module: CORPUS
    op_type: UPDATE
    sub_module: CORPUS_LABELING
  - api_desc: 修改语料标注任务标签组{语料标注标签组名称}的参数
    api_method: PUT
    api_module: cv
    api_path: /api/annotation/label/group
    condition: null
    go_template: 修改语料标注标签组{{ .req_body.name }}的参数
    module: CORPUS
    op_type: UPDATE
    sub_module: CORPUS_LABELING
  - api_desc: 删除语料标注任务{语料标注任务名称}
    api_method: DELETE
    api_module: cv
    api_path: /api/annotation/set/([^/]*)
    condition: null
    go_template: 删除语料标注任务{{ index .matched_paths 1 }}
    module: CORPUS
    op_type: DELETE
    sub_module: CORPUS_LABELING
  - api_desc: 批量删除语料标注任务{语料标注任务名称}
    api_method: DELETE
    api_module: cv
    api_path: /api/annotation/set
    condition: null
    go_template: 批量删除语料标注任务{{ .query_params.sids }}
    module: CORPUS
    op_type: DELETE
    sub_module: CORPUS_LABELING
  - api_desc: 删除语料标注集{语料标注集名称}
    api_method: DELETE
    api_module: cv
    api_path: /api/annotation/family/([^/]*)
    condition: null
    go_template: 删除语料标注集{{ index .matched_paths 1 }}
    module: CORPUS
    op_type: DELETE
    sub_module: CORPUS_LABELING
  - api_desc: 批量删除语料标注集{语料标注集名称}
    api_method: DELETE
    api_module: cv
    api_path: /api/annotation/family
    condition: null
    go_template: 批量删除语料标注集{{ .query_params.fids }}
    module: CORPUS
    op_type: DELETE
    sub_module: CORPUS_LABELING
  - api_desc: 删除语料标注标签{语料标注标签名称}
    api_method: DELETE
    api_module: cv
    api_path: /api/annotation/([^/]*)
    condition: null
    go_template: 删除语料标注标签{{ index .matched_paths 1 }}
    module: CORPUS
    op_type: DELETE
    sub_module: CORPUS_LABELING
  - api_desc: 批量删除语料标注标签{语料标注标签名称}
    api_method: DELETE
    api_module: cv
    api_path: /api/annotation/label
    condition: null
    go_template: 批量删除语料标注标签{{ .query_params.ids }}
    module: CORPUS
    op_type: DELETE
    sub_module: CORPUS_LABELING
  - api_desc: 删除语料标注标签组{语料标注标签组名称}
    api_method: DELETE
    api_module: cv
    api_path: /api/annotation/label/group/([^/]*)
    condition: null
    go_template: 删除语料标注标签{{ index .matched_paths 1 }}
    module: CORPUS
    op_type: DELETE
    sub_module: CORPUS_LABELING
  - api_desc: 批量删除语料标注标签组{语料标注标签组名称}
    api_method: DELETE
    api_module: cv
    api_path: /api/annotation/label/group
    condition: null
    go_template: 批量删除语料标注标签{{ .query_params.ids }}
    module: CORPUS
    op_type: DELETE
    sub_module: CORPUS_LABELING
  - api_desc: 部署服务{服务名称}
    api_method: POST
    api_module: serving
    api_path: /api/v2/service
    condition: null
    go_template: 部署服务{{.req_body.name}}
    module: TOOL
    op_type: CREATE
    sub_module: SERVICE_DEPLOYMENT
  - api_desc: 部署服务版本{服务名称}/{服务版本名称}
    api_method: POST
    api_module: serving
    api_path: /api/v2/service/([^/]*)/service_version
    condition: null
    go_template: 部署服务版本{{.resp_body.service_id}}/{{.resp_body.service_version_id}}
    module: TOOL
    op_type: CREATE
    sub_module: SERVICE_DEPLOYMENT
  - api_desc: '上线服务{服务名称} '
    api_method: POST
    api_module: serving
    api_path: /api/v2/service/([^/]*)/start
    condition: null
    go_template: 上线服务{{ index .matched_paths 1 }}
    module: TOOL
    op_type: UPDATE
    sub_module: SERVICE_DEPLOYMENT
  - api_desc: 下线服务{服务名称}
    api_method: POST
    api_module: serving
    api_path: /api/v2/service/([^/]*)/stop
    condition: null
    go_template: 下线服务{{ index .matched_paths 1 }}
    module: TOOL
    op_type: UPDATE
    sub_module: SERVICE_DEPLOYMENT
  - api_desc: 修改服务版本{服务名称}/{旧-服务版本名称}的名称为{新-服务版本名称}
    api_method: PUT
    api_module: serving
    api_path: /api/v2/service/([^/]*)/service_version/([^/]*)
    condition: null
    go_template: 修改服务版本的名称为{{.req_body.name}}
    module: TOOL
    op_type: UPDATE
    sub_module: SERVICE_DEPLOYMENT
  - api_desc: 修改服务{服务名称}的参数
    api_method: PUT
    api_module: serving
    api_path: /api/v2/service/([^/]*)
    condition: null
    go_template: 修改服务{{.req_body.name}}的参数
    module: TOOL
    op_type: UPDATE
    sub_module: SERVICE_DEPLOYMENT
  - api_desc: 修改服务版本{服务名称}/{服务版本名称}的参数
    api_method: PUT
    api_module: serving
    api_path: /api/v2/service/([^/]*)/service_version/([^/]*)
    condition: null
    go_template: 修改服务版本{{.req_body.name}}的参数
    module: TOOL
    op_type: UPDATE
    sub_module: SERVICE_DEPLOYMENT
  - api_desc: 删除服务{服务名称}
    api_method: DELETE
    api_module: serving
    api_path: /api/v2/service/([^/]*)
    condition: null
    go_template: 删除服务{{ index .matched_paths 1 }}
    module: TOOL
    op_type: DELETE
    sub_module: SERVICE_DEPLOYMENT
  - api_desc: 删除服务版本{服务名称}/{服务版本名称}
    api_method: DELETE
    api_module: serving
    api_path: /api/v2/service/([^/]*)/service_version/([^/]*)
    condition: null
    go_template: 删除服务版本{{ index .matched_paths 1 }}/{{ index .matched_paths 2 }}
    module: TOOL
    op_type: DELETE
    sub_module: SERVICE_DEPLOYMENT
  - api_desc: 修改安全中心的参数
    api_method: PUT
    api_module: applet
    api_path: /api/v1/guardrails/projects-safety/([^/]*)/guardrail
    condition: null
    go_template: 修改安全中心的参数
    module: TOOL
    op_type: UPDATE
    sub_module: SECURITY_CENTER
  - api_desc: 新建代码实例{实例名称}
    api_method: POST
    api_module: csm
    api_path: /api/v1/codespace/instances
    condition: null
    go_template: 新建代码实例{{ .req_body.name }}
    module: TOOL
    op_type: CREATE
    sub_module: CODE_EXAMPLES
  - api_desc: 修改代码实例{实例名称}的参数
    api_method: PUT
    api_module: csm
    api_path: /api/v1/codespace/instances/([^/]*)
    condition: null
    go_template: 修改代码实例{{ index .matched_paths 1 }}的参数
    module: TOOL
    op_type: UPDATE
    sub_module: CODE_EXAMPLES
  - api_desc: 启动代码实例{实例名称}
    api_method: POST
    api_module: csm
    api_path: /api/v1/codespace/instances/([^/]*)/start
    condition: null
    go_template: 启动代码实例{{ index .matched_paths 1 }}
    module: TOOL
    op_type: UPDATE
    sub_module: CODE_EXAMPLES
  - api_desc: 停止代码实例{实例名称}
    api_method: POST
    api_module: csm
    api_path: /api/v1/codespace/instances/([^/]*)/stop
    condition: null
    go_template: 停止代码实例{{ index .matched_paths 1 }}
    module: TOOL
    op_type: UPDATE
    sub_module: CODE_EXAMPLES
  - api_desc: 固化代码实例{实例名称}
    api_method: POST
    api_module: csm
    api_path: /api/v1/codespace/instances/([^/]*)/image:build
    condition: null
    go_template: 固化代码实例{{ index .matched_paths 1 }}
    module: TOOL
    op_type: UPDATE
    sub_module: CODE_EXAMPLES
  - api_desc: 删除代码实例{实例名称}
    api_method: DELETE
    api_module: csm
    api_path: /api/v1/codespace/instances/([^/]*)
    condition: null
    go_template: 删除代码实例{{ index .matched_paths 1 }}
    module: TOOL
    op_type: DELETE
    sub_module: CODE_EXAMPLES
  - api_desc: 新建工作流任务{任务名称}
    api_method: POST
    api_module: pipeline
    api_path: /api/v1/pipeline
    condition: null
    go_template: 新建工作流任务{{.req_body.name}}
    module: TOOL
    op_type: CREATE
    sub_module: WORKFLOW_MANAGEMENT
  - api_desc: 修改工作流任务{任务名称}的名称为{新-任务名称}
    api_method: PUT
    api_module: pipeline
    api_path: /api/v1/pipeline/([^/]*)
    condition: null
    go_template: 修改工作流任务的名称为{{.req_body.name}}
    module: TOOL
    op_type: UPDATE
    sub_module: WORKFLOW_MANAGEMENT
  - api_desc: 删除工作流任务{任务名称}
    api_method: DELETE
    api_module: pipeline
    api_path: /api/v1/pipeline/([^/]*)
    condition: null
    go_template: 删除工作流任务{{ index .matched_paths 1 }}
    module: TOOL
    op_type: DELETE
    sub_module: WORKFLOW_MANAGEMENT
