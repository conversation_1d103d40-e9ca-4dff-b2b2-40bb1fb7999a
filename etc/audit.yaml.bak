# 审计事件 API配置
# module: SPACE,MODEL,APP,<PERSON><PERSON><PERSON><PERSON>DGE,CORPUS ,TOOL, 
# sub_module: SPACE_INFO_MANAGEMENT, SPACE_MEMBER_MANAGEMENT, <PERSON><PERSON><PERSON>_MANAGEMENT, MODEL_EXPERIENCE, MODEL_TRAINING, MOD<PERSON>_EVALUATION, PROMPT_ENGINEERING, APP_MANAGEMENT, APP_EXPERIENCE, APP_PLUGIN_MANAGEMENT, CUSTOM_OPERATOR, APP_EVALUATION, <PERSON><PERSON><PERSON><PERSON>DGE_MANAGEMENT, CORPUS_MANAGEMENT, SERVICE_DEPLOYMENT, SECURITY_CENTER, CODE_EXAMPLES, WOR<PERSON><PERSON>OW_MANAGEMENT
# op_type: CREATE, DELETE, UPDATE
audit_record:
  apis:
  # 空间管理
  # - 空间信息 ⭕️
  - module: SPACE
    sub_module: SPACE_INFO_MANAGEMENT
    op_type: CREATE
    api_method: POST
    api_path: /api/v1/projmgr/projects
    api_desc: 新建空间
    go_template: 新建空间{{ .req_body.name }}，id为{{ .req_body.project_id }}
  - module: SPACE
    sub_module: SPACE_INFO_MANAGEMENT
    op_type: UPDATE
    api_method: PUT
    api_path: /api/v1/projmgr/projects/([^/]*)
    api_desc: 修改空间
    go_template: 修改空间{{ index .matched_paths 1 }}
  - module: SPACE
    sub_module: SPACE_INFO_MANAGEMENT
    op_type: UPDATE
    api_method: PUT
    api_path: /api/v1/global-llm/projects-llm
    api_desc: 修改空间默认服务
    go_template: 修改空间{{ .query_params.project_id }}的默认服务
  - module: SPACE
    sub_module: SPACE_INFO_MANAGEMENT
    op_type: DELETE
    api_method: DELETE
    api_path: /api/v1/projmgr/projects/([^/]*)
    api_desc: 删除空间
    go_template: 删除空间{{ index .matched_paths 1 }}
  # - 成员管理
  - module: SPACE
    sub_module: SPACE_MEMBER_MANAGEMENT
    op_type: CREATE
    api_method: POST
    api_path: /api/v1/projmgr/projects/([^/]*)/members
    api_desc: 添加空间成员
    go_template: '{{- $length := len .req_body -}}{{- range $index, $item := .req_body -}}添加空间成员{{ $item.name }}，并配置角色{{ $item.role_id }}{{if lt $index (sub $length 1)}}；{{end}}{{- end -}}'
  - module: SPACE
    sub_module: SPACE_MEMBER_MANAGEMENT
    op_type: UPDATE
    api_method: PUT
    api_path: /api/v1/projmgr/projects/([^/]*)/members
    api_desc: 修改空间成员
    go_template: '{{- $length := len .req_body -}}{{- range $index, $item := .req_body -}}修改空间成员{{ $item.name }}，并配置角色{{ $item.role_id }}{{if lt $index (sub $length 1)}}；{{end}}{{- end -}}'
  - module: SPACE
    sub_module: SPACE_MEMBER_MANAGEMENT
    op_type: DELETE
    api_method: DELETE
    api_path: /api/v1/projmgr/projects/([^/]*)/members
    api_desc: 删除空间成员
    go_template: 删除空间成员{{ .req_body.name }}
  # 模型
  # - 模型管理
  - module: MODEL
    sub_module: MODEL_MANAGEMENT
    op_type: CREATE
    api_method: POST
    api_path: /api/v1/mwh/models
    api_desc: 新建模型
    go_template: 新建模型{{ .req_body.name }}
  - module: MODEL
    sub_module: MODEL_MANAGEMENT
    op_type: CREATE
    api_method: POST
    api_path: /api/v1/mwh/models/([^/]*)/releases
    api_desc: 新建模型版本
    go_template: 新建模型版本{{ .req_body.modelId }}/{{ .req_body.release_base.name }}
  - module: MODEL
    sub_module: MODEL_MANAGEMENT
    op_type: CREATE
    api_method: POST
    api_path: /api/v1/mwh/svcmgr/remote-services
    api_desc: 新建远程模型
    go_template: 新建远程模型{{.req_body.name}}
  - module: MODEL
    sub_module: MODEL_MANAGEMENT
    op_type: UPDATE
    api_method: PUT
    api_path: /api/v1/mwh/models/([^/]*)
    api_desc: 修改模型
    go_template: 修改模型{{ index .matched_paths 1}}
  - module: MODEL
    sub_module: MODEL_MANAGEMENT
    op_type: UPDATE
    api_method: PUT
    api_path: /api/v1/mwh/models/([^/]*)/releases/([^/]*)
    api_desc: 修改模型版本
    go_template: 修改模型版本{{ index .matched_paths 1}}/{{ index .matched_paths 2}}
  - module: MODEL
    sub_module: MODEL_MANAGEMENT
    op_type: UPDATE
    api_method: POST
    api_path: /api/v1/mwh/models:export
    api_desc: 导出模型
    go_template: 导出模型
  - module: MODEL
    sub_module: MODEL_MANAGEMENT
    op_type: UPDATE
    api_method: POST
    api_path: /api/v1/mwh/rscmgr/resources:clone
    api_desc: 共享模型至公共空间
    go_template: 共享模型至公共空间
  - module: MODEL
    sub_module: MODEL_MANAGEMENT
    op_type: DELETE
    api_method: DELETE
    api_path: /api/v1/mwh/models/([^/]*)
    api_desc: 删除模型
    go_template: 删除模型{{ index .matched_paths 1}}
  - module: MODEL
    sub_module: MODEL_MANAGEMENT
    op_type: DELETE
    api_method: DELETE
    api_path: /api/v1/mwh/models/([^/]*)/releases/([^/]*)
    api_desc: 删除模型版本
    go_template: 删除模型版本{{ index .matched_paths 1}}/{{ index .matched_paths 2}}
  # - 模型体验
  - module: MODEL
    sub_module: MODEL_EXPERIENCE
    op_type: CREATE
    api_method: POST
    api_path: /api/v1/mwh/svcmgr/services/([^/]*)/infer
    api_desc: 模型体验测试
    go_template: 对模型{{ .req_body.model_id }}/{{ .req_body.release_id }}提交访问请求
  # - 模型训练
  - module: MODEL
    sub_module: MODEL_TRAINING
    op_type: CREATE
    api_method: POST
    api_path: /api/trainer/finetune
    api_desc: 新建模型训练任务
    go_template: 新建训练任务{{ .req_body.name }}
  - module: MODEL
    sub_module: MODEL_TRAINING
    op_type: UPDATE
    api_method: POST
    api_path: /api/trainer/taskversion/([^/]*)/stop
    api_desc: 停止模型训练任务
    go_template: 停止训练任务{{ index .matched_paths 1}}
  - module: MODEL
    sub_module: MODEL_TRAINING
    op_type: UPDATE
    api_method: POST
    api_path: /api/trainer/finetune/([^/]*)/re-train
    api_desc: 恢复模型训练任务
    go_template: 从故障中恢复训练任务{{ index .matched_paths 1}}
  - module: MODEL
    sub_module: MODEL_TRAINING
    op_type: DELETE
    api_method: DELETE
    api_path: /api/trainer/taskversion/([^/]*)
    api_desc: 删除模型训练任务
    go_template: 删除训练任务{{ index .matched_paths 1}}
  - module: MODEL
    sub_module: MODEL_TRAINING
    op_type: UPDATE
    api_method: POST
    api_path: /api/trainer/finetune/([^/]*)/re-train
    api_desc: 恢复模型训练任务
    go_template: 从故障中恢复训练任务{{ index .matched_paths 1}}
  # - 模型评估
  - module: MODEL
    sub_module: MODEL_EVALUATION
    op_type: CREATE
    api_method: POST
    api_path: /api/v1/mwh/evalmgr/missions
    api_desc: 新建模型评估任务
    go_template: 新建模型评估任务{{ .req_body.name }}
  - module: MODEL
    sub_module: MODEL_EVALUATION
    op_type: CREATE
    api_method: POST
    api_path: /api/v1/mwh/evalmgr/templates
    api_desc: 新建模型评估模板
    go_template: 新建模型评估模板{{ .req_body.name }}
  - module: MODEL
    sub_module: MODEL_EVALUATION
    op_type: UPDATE
    api_method: PUT
    api_path: /api/v1/mwh/evalmgr/missions/([^/]*)
    api_desc: 修改模型评估任务
    go_template: 修改模型评估任务{{ index .matched_paths 1}}
  - module: MODEL
    sub_module: MODEL_EVALUATION
    op_type: UPDATE
    api_method: PUT
    api_path: /api/v1/mwh/evalmgr/templates/([^/]*)
    api_desc: 修改模型评估模板
    go_template: 修改模型评估模板{{ index .matched_paths 1}}
  - module: MODEL
    sub_module: MODEL_EVALUATION
    op_type: DELETE
    api_method: DELETE
    api_path: /api/v1/mwh/evalmgr/missions
    api_desc: 删除模型评估任务
    go_template: 删除模型评估任务{{ .req_body.ids }}
  # - 提示工程
  - module: MODEL
    sub_module: PROMPT_ENGINEERING
    op_type: CREATE
    api_method: POST
    api_path: /api/prompt/scenes/([^/]*)/templates
    api_desc: 新建Prompt模板
    go_template: 新建Prompt模板{{ .req_body.name }}
  - module: MODEL
    sub_module: PROMPT_ENGINEERING
    op_type: UPDATE
    api_method: PUT
    api_path: /api/prompt/scenes/([^/]*)/templates/([^/]*)
    api_desc: 修改Prompt模板
    go_template: 修改Prompt模板{{ index .matched_paths 2}}
  - module: MODEL
    sub_module: PROMPT_ENGINEERING
    op_type: UPDATE
    api_method: POST
    api_path: /api/prompt/scenes/([^/]*)/templates/([^/]*)/publish
    api_desc: 发布Prompt模板
    go_template: 发布Prompt模板{{ index .matched_paths 2}}
  - module: MODEL
    sub_module: PROMPT_ENGINEERING
    op_type: DELETE
    api_method: DELETE
    api_path: /api/prompt/scenes/([^/]*)/templates/([^/]*)
    api_desc: 删除Prompt模板
    go_template: 删除Prompt模板{{ index .matched_paths 2}}
  # 应用
  # - 应用管理
  # - 应用体验
  # - 应用插件
  # - 自定义算子
  # - 应用评估（待开发）
  # 知识库
  # - 知识管理
  - module: KNOWLEDGE
    sub_module: KNOWLEDGE_MANAGEMENT
    op_type: CREATE
    api_method: POST
    api_path: /api/v1/knowlhub/kbs
    api_desc: 创建知识库
    go_template: 新建知识库{{ .req_body.name }}
  - module: KNOWLEDGE
    sub_module: KNOWLEDGE_MANAGEMENT
    op_type: CREATE
    api_method: POST
    api_path: /api/v1/knowlhub/docs:sync-submit
    api_desc: 新增文档到知识库
    go_template: 在知识库{{ .req_body.knowledge_base_id }}中，新建知识入库
  - module: KNOWLEDGE
    sub_module: KNOWLEDGE_MANAGEMENT
    op_type: CREATE
    api_method: POST
    api_path: /api/v1/knowlhub/kbs:retrieve
    api_desc: 知识库检索
    go_template: 在知识库{{ .req_body.knowledge_base_id }}中，新建查询（召回）
  - module: KNOWLEDGE
    sub_module: KNOWLEDGE_MANAGEMENT
    op_type: UPDATE
    api_method: PUT
    api_path: /api/v1/knowlhub/kbs/([^/]*)
    api_desc: 更新知识库
    go_template: 修改知识库{{ index .matched_paths 1 }}的参数
  - module: KNOWLEDGE
    sub_module: KNOWLEDGE_MANAGEMENT
    op_type: DELETE
    api_method: DELETE
    api_path: /api/v1/knowlhub/kbs/([^/]*)
    api_desc: 删除知识库
    go_template: 删除知识库{{ index .matched_paths 1 }}
  - module: KNOWLEDGE
    sub_module: KNOWLEDGE_MANAGEMENT
    op_type: DELETE
    api_method: POST
    api_path: /api/v1/knowlhub/docs:remove
    api_desc: 知识库移除文档
    go_template: 在知识库{{ .req_body.knowledge_base_id }}中，删除文档{{ .req_body.doc_id }}
  # - 知识体验
  - module: KNOWLEDGE
    sub_module: KNOWLEDGE_MANAGEMENT
    op_type: CREATE
    api_method: POST
    api_path: /api/v1/knowlhub//kbs:cross-retrieve
    api_desc: (知识体验)跨知识库检索
    go_template: 在知识库{{- $length := len .req_body.ranges -}}{{- range $index, $element := .req_body.ranges -}}{{ $element.knowledge_base_id }}{{if lt $index (sub $length 1)}},{{end}}{{- end -}}中，新建查询（召回）
  # 运维工具
  # - 服务部署
  # - 安全中心
  - module: TOOL
    sub_module: SECURITY_CENTER
    op_type: UPDATE
    api_method: PUT
    api_path: /api/v1/guardrails/projects-safety/([^/]*)/guardrail
    api_desc: 修改安全中心的参数
    go_template: 修改安全中心的参数
  # - 代码实例
  # - 工作流 
  - module: TOOL
    sub_module: WORKFLOW_MANAGEMENT
    op_type: CREATE
    api_method: POST
    api_path: /api/v1/pipeline
    api_desc: 新建工作流任务
    go_template: 新建工作流任务{{ .req_body.name }}
  - module: TOOL
    sub_module: WORKFLOW_MANAGEMENT
    op_type: UPDATE
    api_method: PUT
    api_path: /api/v1/pipeline/([^/]*)
    api_desc: 更新工作流任务
    go_template: 修改工作流任务的名称为{{ .req_body.name }}
  - module: TOOL
    sub_module: WORKFLOW_MANAGEMENT
    op_type: DELETE
    api_method: DELETE
    api_path: /api/v1/pipeline/([^/]*)
    api_desc: 删除工作流任务
    go_template: 删除工作流任务{{ index .matched_paths 1}}