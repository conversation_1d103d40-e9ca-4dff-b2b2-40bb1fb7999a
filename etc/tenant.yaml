# guardian token from tdc tenant manger(login tenant guardian)
# tenant service base_url from service from tdcsys namespace with grep tenant
# broker service base_url from service from tdcsys namespace with grep broker
# hippo_service_id from broker swagger ui(/v2/catalog) result
# hippo_template_name from broker swagger ui(/v2/catalog) result which only contain default template,
# can also create template from tcc(meaning when install instance from tdc, can save resource as template)
tenant:
  strategy: tdc5 # tdc or k8s or tdc5
  llmops_queue:
    enabled: true
  ingress:
    service_name: autocv-portal-service
    service_port: 443
  hippo:
    enabled: true
  default_quota:
    limits_cpu: 32
    limits_memory: 128Gi
    requests_storage: 20Ti
    gpu: 200
    gpu_memory: 80Gi
    bandwidth: 2Gi
    knowledge_base_storage: 500Gi
    file_storage: 1000Gi
    pods: 1k
  tdc5:
    hippo_config:
      prod_meta_type: "HIPPO"
      prod_meta_version: "hippo-2.0"
      prod_instance_name: "llmops-hippo"
      component_type: "HIPPO"
      component_version: "hippo-2.0.0-final"
      component_enable_kerberos: false
      component_network_type: "OVERLAY"
    hippo_svc_labels: "io.transwarp.tdc/configuration-name=httpserver.port"
    x_cloud_access_token: "e0543650453a4fbe9e281329508c3105"
    #x_cloud_access_token: "53c7cfa5536f48d9a78c8c0b1e009a35"
    tenant_service:
      #base_url: "http://*************:9099"
      base_url: "https://*************/tdc/eco"
      #base_url: "https://*************/tdc/eco"
      creator: "llmops"
      cluster_id: "3e5edcb2-e98a-4515-bfc3-37d36960c15c"
      #cluster_id: "7d8ada5f-e32a-4fdc-ae32-b573df7cfe7d"
      create_tenant_path: "/api/v1/broker/tenant/create"
      get_tenant_path: "/api/v1/broker/tenant/%s"
      get_all_tenants_path: "/api/v1/broker/tenant"
  tdc:
    guardian_access_token: "T3jhf1x7sbavQVJjBIMy-TDCSYS.TDH"
    tenant_service:
      #base_url: "http://*************:32429"
      base_url: "http://**************:31145"
      #base_url: "http://tenantservice-hl-f7rzn.tdcsys.svc:8888"
      creator: "eco-admin"
      company: "transwarp"
      department: "sophon"
      password: "CKtvPn3d83ccuFjEeBFgmg=="
      user_fullname: "eco-admin"
      user_email: "<EMAIL>"
    broker_service:
      base_url: "http://**************:31379"
      #base_url: "http://tdc-broker.tdcsys.svc:8080"
      hippo_service_id: "4090a48118584ffb8fbd557e1466c062"
      hippo_template_name: "llmops"
      hippo_instance_name: "llmops-hippo"