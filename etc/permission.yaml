# 全量权限点文件, 用于初始化和修改权限点, 不建议在业务代码中修改
# 每次更新本文件, 将 truncate premission table

# TODO 改为树形结构

- code: manage-center
  name: 管理中心
  action: access
  type: platform
  namelocals:
    en: "Management Center"

- code: manage-center.compute
  name: 算力管理
  action: access
  type: platform
  parent: manage-center
  namelocals:
    en: "Compute Management"

- code: manage-center.compute.services.*
  name: 服务管理
  action: access
  type: platform
  parent: manage-center.compute
  namelocals:
    en: "Services"
  actions:
    - read
    - "*"

- code: manage-center.cluster.*
  name: 集群列表
  action: access
  type: platform
  parent: manage-center.compute
  namelocals:
    en: "Cluster List"
  actions:
    - read
    - "*"

- code: manage-center.resource-groups.*
  name: 资源组管理
  action: access
  type: platform
  parent: manage-center.compute
  namelocals:
    en: "Resource Group Management"
  actions:
    - read
    - "*"

- code: manage-center.tenants.*
  name: 租户管理
  action: access
  type: platform
  parent: manage-center.compute
  namelocals:
    en: "Tenant Management"
  actions:
    - read
    - "*"

- code: manage-center.compute-specification.*
  name: 算力规格
  action: access
  type: platform
  parent: manage-center.compute
  namelocals:
    en: "Computing Specifications"
  actions:
    - read
    - "*"

- code: manage-center.compute-monitoring.*
  name: 算力监控
  action: access
  type: platform
  parent: manage-center.compute
  namelocals:
    en: "Resource Monitoring"
  actions:
    - read
    - "*"

- code: manage-center.compute-facility.*
  name: 算力设施
  action: access
  type: platform
  parent: manage-center
  namelocals:
    en: "Compute Facility"
  actions:
    - read
    - "*"

- code: manage-center.basic-service.*
  name: 基础服务
  action: access
  type: platform
  parent: manage-center
  namelocals:
    en: "Base Service"
  actions:
    - read
    - "*"

- code: project.manage.*
  name: 空间管理
  action: access
  type: platform
  parent: manage-center
  namelocals:
    en: "Project Management"
  actions:
    - create

- code: manage-center.static-assets.*
  name: 静态资产
  action: access
  type: platform
  parent: manage-center
  namelocals:
    en: "Static Assets"
  actions:
    - read
    - "*"

- code: manage-center.users
  name: 用户管理
  action: access
  type: platform
  parent: manage-center
  namelocals:
    en: "Users"

- code: manage-center.users.users.*
  name: 用户
  action: access
  type: platform
  parent: manage-center.users
  namelocals:
    en: "User"
  actions:
    - read
    - "*"

- code: manage-center.users.groups.*
  name: 用户组
  action: access
  type: platform
  parent: manage-center.users
  namelocals:
    en: "User Group"
  actions:
    - read
    - "*"

- code: manage-center.role
  name: 角色管理
  action: access
  type: platform
  parent: manage-center
  namelocals:
    en: "Roles"

- code: manage-center.role.project.*
  name: 空间角色
  action: access
  type: platform
  parent: manage-center.role
  namelocals:
    en: "Space Role"
  actions:
    - read
    - "*"

- code: manage-center.role.platform.*
  name: 平台角色
  action: access
  type: platform
  parent: manage-center.role
  namelocals:
    en: "Platform Role"
  actions:
    - read
    - "*"

- code: manage-center.develop
  name: 开发管理
  action: access
  type: platform
  parent: manage-center
  namelocals:
    en: "Development Management"

- code: manage-center.develop.package.*
  name: 包管理
  action: access
  type: platform
  parent: manage-center.develop
  namelocals:
    en: "Package Catalog"
  actions:
    - read
    - "*"

- code: manage-center.develop.repo.*
  name: 软件包仓库地址
  action: access
  type: platform
  parent: manage-center.develop
  namelocals:
    en: "Software Package Repository Address"
  actions:
    - read
    - "*"

- code: manage-center.oem.*
  name: 定制化配置
  action: access
  type: platform
  parent: manage-center
  namelocals:
    en: "Configuration"
  actions:
    - read
    - "*"

- code: manage-center.points.*
  name: 空间积分
  action: access
  type: platform
  parent: manage-center
  namelocals:
    en: "Spatial Points"
  actions:
    - read
    - "*"

- code: notifications
  name: 我的消息
  action: access
  type: platform
  parent: ""
  namelocals:
    en: "My Messages"

- code: notifications.service-alerts.*
  name: 服务预警
  action: access
  type: platform
  parent: notifications
  namelocals:
    en: "Service Alert"
  actions:
    - "*"
    - read

- code: notifications.service-approval.*
  name: 服务审批
  action: access
  type: platform
  parent: notifications
  namelocals:
    en: "Service Approval"
  actions:
    - read
    - "*"

- code: help-center
  name: 帮助文档
  action: access
  type: platform
  namelocals:
    en: "Help Documentation"
  actions:
    - read

- code: space.home
  name: 首页
  action: access
  type: project
  namelocals:
    en: "Home"

- code: space.home.*
  name: 首页
  action: access
  type: project
  parent: space.home
  namelocals:
    en: "Home"
  actions:
    - "*"
    - read

- code: mw
  name: 模型
  action: access
  type: project
  namelocals:
    en: "Models"

- code: mw.manage
  name: 模型管理
  action: access
  type: project
  parent: mw
  namelocals:
    en: "Model Catalog"

- code: mw.manage.my.*
  name: 我的模型
  action: access
  type: project
  parent: mw.manage
  namelocals:
    en: "My Model"
  actions:
    - "*"
    - read

- code: mw.manage.remote.*
  name: 远程模型
  action: access
  type: project
  parent: mw.manage
  namelocals:
    en: "Remote Model"
  actions:
    - "*"
    - read

- code: mw.experience.*
  name: 模型体验
  action: access
  type: project
  parent: mw
  namelocals:
    en: "Playground"
  actions:
    - "*"

- code: mw.tools
  name: 模型工具
  action: access
  type: project
  parent: mw
  namelocals:
    en: "Tools"

- code: mw.tools.train.*
  name: 模型训练
  action: access
  type: project
  parent: mw.tools
  namelocals:
    en: "Model Training"
  actions:
    - "*"
    - read

- code: mw.tools.evaluation.*
  name: 模型评估
  action: access
  type: project
  parent: mw.tools
  namelocals:
    en: "Model Evaluation"
  actions:
    - "*"
    - read

- code: mw.tools.quantization.*
  name: 模型量化
  action: access
  type: project
  parent: mw.tools
  namelocals:
    en: "Quantizations"
  actions:
    - "*"
    - read

- code: applet
  name: 应用
  action: access
  type: project
  namelocals:
    en: "Apps"

- code: applet.manage.*
  name: 应用管理
  action: access
  type: project
  parent: applet
  namelocals:
    en: "Apps"
  actions:
    - "*"
    - read

- code: applet.experience.*
  name: 应用体验
  action: access
  type: project
  parent: applet
  namelocals:
    en: "App Playground"
  actions:
    - "*"

- code: applet.tools
  name: 应用工具
  action: access
  type: project
  parent: applet
  namelocals:
    en: "Tools"

- code: applet.tools.plugins.*
  name: 应用插件
  action: access
  type: project
  parent: applet.tools
  namelocals:
    en: "App Plugins"
  actions:
    - "*"
    - read

- code: applet.tools.operators.*
  name: 自定义算子
  action: access
  type: project
  parent: applet.tools
  namelocals:
    en: "Custom Operator"
  actions:
    - "*"
    - read

- code: corpus
  name: 语料
  action: access
  type: project
  namelocals:
    en: "Corpus"

- code: corpus.manage
  name: 语料管理
  action: access
  type: project
  parent: corpus
  namelocals:
    en: "Corpus"

- code: corpus.manage.text-datasets.*
  name: 文本数据集
  action: access
  type: project
  parent: corpus.manage
  namelocals:
    en: "Text Datasets"
  actions:
    - "*"
    - read

- code: corpus.manage.img-datasets.*
  name: 图像数据集
  action: access
  type: project
  parent: corpus.manage
  namelocals:
    en: "Image Datasets"
  actions:
    - "*"
    - read

- code: corpus.tools
  name: 语料工具
  action: access
  type: project
  parent: corpus
  namelocals:
    en: "Corpus Tools"

- code: corpus.tools.annotation.*
  name: 语料标注
  action: access
  type: project
  parent: corpus.tools
  namelocals:
    en: "Corpus Annotation"
  actions:
    - annotation-manage
    - examine
    - annotation

- code: corpus.tools.process.*
  name: 语料处理
  action: access
  type: project
  parent: corpus.tools
  namelocals:
    en: "Corpus Processing"
  actions:
    - "*"
    - read

- code: knowledge
  name: 知识
  action: access
  type: project
  namelocals:
    en: "Knowledge"

- code: knowledge.manage
  name: 知识管理
  action: access
  type: project
  parent: knowledge
  namelocals:
    en: "Knowledge Catalog"

- code: knowledge.manage.list.*
  name: 知识库列表
  action: access
  type: project
  parent: knowledge.manage
  namelocals:
    en: "Catalog"
  actions:
    - "*"
    - read

- code: knowledge.manage.change.*
  name: 文档变更
  action: access
  type: project
  parent: knowledge.manage
  namelocals:
    en: "Changing"
  actions:
    - "*"
    - read

- code: knowledge.experience.*
  name: 知识体验
  action: access
  type: project
  parent: knowledge
  namelocals:
    en: "Knowledge Experiment"
  actions:
    - "*"

- code: data
  name: 数据
  action: access
  type: project
  namelocals:
    en: "Data"

- code: data.file-assets
  name: 文件资产
  parent: data
  action: access
  type: project
  namelocals:
    en: "File Assets"

- code: data.file-assets.overview.*
  name: 概览统计
  parent: data.file-assets
  action: access
  type: project
  namelocals:
    en: "Overview"
  actions:
    - "*"
    - read

- code: data.file-assets.list.*
  name: 资产列表
  parent: data.file-assets
  action: access
  type: project
  namelocals:
    en: "Assets"
  actions:
    - "*"
    - read

- code: data.file-assets.catalog.*
  name: 编目管理
  parent: data.file-assets
  action: access
  type: project
  namelocals:
    en: "Catalog"
  actions:
    - "*"
    - read

- code: data.file-assets.fs.*
  name: 文件系统
  parent: data.file-assets
  action: access
  type: project
  namelocals:
    en: "File System"
  actions:
    - "*"
    - read

- code: data.datasource
  name: 数据源管理
  parent: data
  action: access
  type: project
  namelocals:
    en: "Data Sources"

- code: data.datasource.native.*
  name: 平台原生
  parent: data.datasource
  action: access
  type: project
  namelocals:
    en: "Native"
  actions:
    - "*"
    - read

- code: data.datasource.external.*
  name: 外部数据源
  parent: data.datasource
  action: access
  type: project
  namelocals:
    en: "External Data Source"
  actions:
    - "*"
    - read

- code: project
  name: 空间
  action: access
  type: project
  namelocals:
    en: "Space"

- code: project.computing-power
  name: 算力管理
  action: access
  type: project
  parent: project
  namelocals:
    en: "Computing Management"

- code: project.computing-power.services.*
  name: 服务管理
  action: access
  type: project
  parent: project.computing-power
  namelocals:
    en: "Services"
  actions:
    - "*"
    - read

- code: project.computing-power.tasks.*
  name: 任务管理
  action: access
  type: project
  parent: project.computing-power
  namelocals:
    en: "Tasks"
  actions:
    - "*"
    - read

- code: project.computing-power.resource.*
  name: 资源管理
  action: access
  type: project
  parent: project.computing-power
  namelocals:
    en: "Resource Management"
  actions:
    - "*"
    - read

- code: project.computing-power.charging.*
  name: 计费管理
  action: access
  type: project
  parent: project.computing-power
  namelocals:
    en: "Billing Management"
  actions:
    - "*"
    - read

- code: project.general-tools
  name: 通用工具
  action: access
  type: project
  parent: project
  namelocals:
    en: "Common Tools"

- code: project.general-tools.csm.*
  name: 代码实例
  action: access
  type: project
  parent: project.general-tools
  namelocals:
    en: "Code Instance"
  actions:
    - "*"

- code: project.general-tools.images.*
  name: 镜像管理
  action: access
  type: project
  parent: project.general-tools
  namelocals:
    en: "Image Management"
  actions:
    - "*"

- code: project.general-tools.prompts.*
  name: 提示工程
  action: access
  type: project
  parent: project.general-tools
  namelocals:
    en: "Prompt Engineering"
  actions:
    - "*"
    - read

- code: project.general-tools.workflow.*
  name: 工作流
  action: access
  type: project
  parent: project.general-tools
  namelocals:
    en: "Workflows"
  actions:
    - "*"
    - read

- code: project.general-tools.security.*
  name: 安全中心
  action: access
  type: project
  parent: project.general-tools
  namelocals:
    en: "Security Center"
  actions:
    - "*"
    - read

- code: project.project-manage
  name: 空间管理
  action: access
  type: project
  parent: project
  namelocals:
    en: "Workspace"

- code: project.project-manage.member.*
  name: 成员管理
  action: access
  type: project
  parent: project.project-manage
  namelocals:
    en: "Members"
  actions:
    - "*"
    - read

- code: project.project-manage.api-key.*
  name: API-KEY
  action: access
  type: project
  parent: project.project-manage
  namelocals:
    en: "API-KEY"
  actions:
    - "*"
    - read

- code: project.project-manage.approval.*
  name: 审批管理
  action: access
  type: project
  parent: project.project-manage
  namelocals:
    en: "Approvals"
  actions:
    - "*"
    - read

- code: project.project-manage.audit-logs.*
  name: 审计日志
  action: access
  type: project
  parent: project.project-manage
  namelocals:
    en: "Audit Logs"
  actions:
    - "*"
    - read

- code: project.project-manage.points.*
  name: 积分明细
  action: access
  type: project
  parent: project.project-manage
  namelocals:
    en: "Points Details"
  actions:
    - "*"
