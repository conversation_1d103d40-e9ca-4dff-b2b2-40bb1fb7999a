package main

import (
	"bufio"
	"fmt"
	"io/ioutil"
	"os"
	"regexp"
	"strings"

	"gopkg.in/yaml.v2"
)

type Metric struct {
	ID            string `yaml:"id"`
	Name          string `yaml:"name"`
	Description   string `yaml:"description"`
	Type          string `yaml:"type"`
	Query         Query  `yaml:"query"`
	DisableAlerting bool `yaml:"disable_alerting,omitempty"`
}

type Query struct {
	Expr         string `yaml:"expr"`
	LegendFormat string `yaml:"legendFormat"`
}

type Monitor struct {
	Metrics []Metric `yaml:"metrics"`
}

type Config struct {
	Monitor Monitor `yaml:"monitor"`
}

func main() {
	if len(os.Args) < 2 {
		printUsage()
		return
	}

	command := os.Args[1]
	configPath := "/etc/monitor.yaml"

	// 检查配置文件是否存在，如果不存在则使用当前目录下的etc/monitor.yaml
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		configPath = "etc/monitor.yaml"
	}

	config, err := loadConfig(configPath)
	if err != nil {
		fmt.Printf("错误：无法加载配置文件 %s: %v\n", configPath, err)
		return
	}

	switch command {
	case "list":
		listMetrics(config)
	case "query":
		if len(os.Args) < 3 {
			fmt.Println("错误：请提供指标ID")
			fmt.Println("用法: go run monitor-tool.go query <metric-id>")
			return
		}
		queryMetric(config, os.Args[2])
	case "render":
		if len(os.Args) < 3 {
			fmt.Println("错误：请提供指标ID")
			fmt.Println("用法: go run monitor-tool.go render <metric-id>")
			return
		}
		renderMetric(config, os.Args[2])
	default:
		printUsage()
	}
}

func printUsage() {
	fmt.Println("监控指标工具")
	fmt.Println("用法:")
	fmt.Println("  go run monitor-tool.go list                  # 列出所有指标定义")
	fmt.Println("  go run monitor-tool.go query <metric-id>     # 查询特定指标")
	fmt.Println("  go run monitor-tool.go render <metric-id>    # 渲染指标查询语句")
}

func loadConfig(path string) (*Config, error) {
	data, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, err
	}

	var config Config
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

func listMetrics(config *Config) {
	fmt.Printf("共找到 %d 个指标定义:\n\n", len(config.Monitor.Metrics))
	
	for i, metric := range config.Monitor.Metrics {
		fmt.Printf("%d. ID: %s\n", i+1, metric.ID)
		fmt.Printf("   名称: %s\n", metric.Name)
		fmt.Printf("   描述: %s\n", metric.Description)
		fmt.Printf("   类型: %s\n", metric.Type)
		if metric.DisableAlerting {
			fmt.Printf("   告警: 已禁用\n")
		}
		fmt.Printf("   查询表达式: %s\n", metric.Query.Expr)
		fmt.Printf("   图例格式: %s\n", metric.Query.LegendFormat)
		fmt.Println()
	}
}

func queryMetric(config *Config, metricID string) {
	for _, metric := range config.Monitor.Metrics {
		if metric.ID == metricID {
			fmt.Printf("指标详情:\n")
			fmt.Printf("ID: %s\n", metric.ID)
			fmt.Printf("名称: %s\n", metric.Name)
			fmt.Printf("描述: %s\n", metric.Description)
			fmt.Printf("类型: %s\n", metric.Type)
			if metric.DisableAlerting {
				fmt.Printf("告警状态: 已禁用\n")
			} else {
				fmt.Printf("告警状态: 启用\n")
			}
			fmt.Printf("查询表达式: %s\n", metric.Query.Expr)
			fmt.Printf("图例格式: %s\n", metric.Query.LegendFormat)
			return
		}
	}
	fmt.Printf("错误：未找到ID为 '%s' 的指标\n", metricID)
}

func renderMetric(config *Config, metricID string) {
	for _, metric := range config.Monitor.Metrics {
		if metric.ID == metricID {
			fmt.Printf("指标 %s (%s) 的查询表达式:\n", metric.ID, metric.Name)
			fmt.Printf("原始表达式: %s\n\n", metric.Query.Expr)
			
			// 提取变量
			variables := extractVariables(metric.Query.Expr)
			if len(variables) == 0 {
				fmt.Println("该查询表达式不包含变量，可直接使用。")
				return
			}

			fmt.Printf("检测到 %d 个变量需要提供值:\n", len(variables))
			for i, variable := range variables {
				fmt.Printf("%d. %s\n", i+1, variable)
			}
			fmt.Println()

			// 交互式输入变量值
			fmt.Println("请依次输入各变量的值:")
			reader := bufio.NewReader(os.Stdin)
			variableValues := make(map[string]string)

			for _, variable := range variables {
				fmt.Printf("请输入 %s 的值: ", variable)
				value, _ := reader.ReadString('\n')
				value = strings.TrimSpace(value)
				variableValues[variable] = value
			}

			// 替换变量生成最终查询语句
			finalExpr := metric.Query.Expr
			for variable, value := range variableValues {
				placeholder := "{{." + variable + "}}"
				finalExpr = strings.ReplaceAll(finalExpr, placeholder, value)
			}

			fmt.Printf("\n最终查询语句:\n%s\n", finalExpr)
			
			// 同样处理图例格式
			finalLegend := metric.Query.LegendFormat
			for variable, value := range variableValues {
				placeholder := "{{" + variable + "}}"
				finalLegend = strings.ReplaceAll(finalLegend, placeholder, value)
			}
			fmt.Printf("\n图例格式:\n%s\n", finalLegend)
			return
		}
	}
	fmt.Printf("错误：未找到ID为 '%s' 的指标\n", metricID)
}

func extractVariables(expr string) []string {
	// 使用正则表达式提取 {{.VariableName}} 格式的变量
	re := regexp.MustCompile(`\{\{\.(\w+)\}\}`)
	matches := re.FindAllStringSubmatch(expr, -1)
	
	variableSet := make(map[string]bool)
	var variables []string
	
	for _, match := range matches {
		if len(match) > 1 {
			variable := match[1]
			if !variableSet[variable] {
				variableSet[variable] = true
				variables = append(variables, variable)
			}
		}
	}
	
	return variables
}