from dataclasses import dataclass, asdict
import openpyxl
import re

@dataclass
class ExcelData:
    module: str
    sub_module: str
    op_type: str
    api_module: str
    api_desc: str
    api_method: str
    api_path: str
    go_template: str
    condition: str


def get_merged_cell_value(cell, sheet):
    for range_ in sheet.merged_cells.ranges:
        if cell.coordinate in range_:
            return sheet.cell(range_.min_row, range_.min_col).value
    return cell.value

def clean_at_sign(value):
    if value and "@" in value:
        return value.split("@")[0]
    return value

# 模块映射
module_mapping = {
    '空间管理': 'SPACE',
    '模型': 'MODEL',
    '应用': 'APP',
    '知识': 'KNOWLEDGE',
    '语料': 'CORPUS',
    '运维工具': 'TOOL'
}

# 功能菜单映射
sub_module_mapping = {
    '空间信息': 'SPACE_INFO_MANAGEMENT',
    '成员管理': 'SPACE_MEMBER_MANAGEMENT',
    '模型管理': 'MODEL_MANAGEMENT',
    '模型体验': 'MODEL_EXPERIENCE',
    '模型训练': 'MODEL_TRAINING',
    '模型评估': 'MODEL_EVALUATION',
    '提示工程': 'PROMPT_ENGINEERING',
    '应用管理': 'APP_MANAGEMENT',
    '应用体验': 'APP_EXPERIENCE',
    '应用插件': 'APP_PLUGIN_MANAGEMENT',
    '自定义算子': 'CUSTOM_OPERATOR',
    '应用评估': 'APP_EVALUATION',
    '知识管理': 'KNOWLEDGE_MANAGEMENT',
    '知识体验': 'KNOWLEDGE_EXPERIENCE',
    '语料管理': 'CORPUS_MANAGEMENT',
    '语料处理': 'CORPUS_PROCESSING',
    '语料标注': 'CORPUS_LABELING',
    '语料评测': 'CORPUS_EVALUATING',
    '服务部署': 'SERVICE_DEPLOYMENT',
    '安全中心': 'SECURITY_CENTER',
    '代码实例': 'CODE_EXAMPLES',
    '工作流': 'WORKFLOW_MANAGEMENT'
}

# 操作类型映射
op_type_mapping = {
    "新建": "CREATE",
    "编辑": "UPDATE",
    "删除": "DELETE"
}

# 打开xlsx文件
wb = openpyxl.load_workbook('审计事件.xlsx', data_only=True)
sheet = wb.active

# 假设表头在第二行，我们首先找到各个属性对应的列号
headers = {}
header_translations = {
    '模块': 'module',
    '功能菜单': 'sub_module',
    '操作类型': 'op_type',
    '事件描述': 'api_desc',
    'METHOD': 'api_method',
    'URI': 'api_path',
    'GO Template': 'go_template',
    'Condition': 'condition',
    'Module': 'api_module'
}

for column in range(1, sheet.max_column + 1):
    cell = sheet.cell(row=2, column=column)
    header = get_merged_cell_value(cell, sheet)
    if header in header_translations:
        headers[header_translations[header]] = column

# 提取数据
data = []
for row in range(3, sheet.max_row + 1):
    row_data = {}
    skip_row = False
    for header, column in headers.items():
        cell = sheet.cell(row=row, column=column)
        value = get_merged_cell_value(cell, sheet)
        
        # 去除@及其后面的字符
        if header in ['module', 'sub_module']:
            value = clean_at_sign(value)
        
        # 映射模块和功能菜单的值
        if header == 'module' and value in module_mapping:
            value = module_mapping[value]
        elif header == 'sub_module' and value in sub_module_mapping:
            value = sub_module_mapping[value]
        elif header == "op_type" and value in op_type_mapping:
            value = op_type_mapping[value]

        # 如果METHOD、URI、GO Template、Module中任意一个为空，则跳过这一行
        if header in ['api_method', 'api_path', 'go_template', 'api_module'] and not value:
            skip_row = True
            break
        
        if header == "api_path":
            value = re.sub(r'\{[^}]*\}', r'([^/]*)', value)


        row_data[header] = value
    
    if not skip_row:
        # 使用ExcelData类创建一个实例
        data.append(ExcelData(**row_data))

# 打印提取的数据
for item in data:
    print(item)

# 如果你需要进一步处理数据，比如保存到数据库或者转换成其他格式，你可以在这里添加代码
import yaml
with open('../etc/audit.yaml', 'w', encoding='utf-8') as yaml_file:
    yaml.dump({"audit_record":{"apis": list(map(asdict, data))}}, yaml_file, allow_unicode=True, default_flow_style=False)