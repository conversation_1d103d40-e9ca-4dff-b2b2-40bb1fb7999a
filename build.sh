#!/bin/bash

export PACKAGE=transwarp.io/applied-ai/central-auth-service
export GOARCH=arm64
export BUILD_VERSION=0.0.0
export BUILD_TIME=99999999
export CI_COMMIT_SHA=00000000
#export ARCH=arm64
export ARCH=amd64

go env -w GOPROXY=http://172.16.251.11:1111,https://goproxy.io/,https://mirrors.aliyun.com/goproxy/,https://goproxy.cn/,direct && go mod tidy &&\
   GO111MODULE=on \
   GOOS=linux \
   CGO_ENABLED=1 \
   CC=gcc \
   CXX=g++ \
   GOARCH=${ARCH} \
   go build -ldflags "-X ${PACKAGE}/version.BuildName=${PACKAGE} -X ${PACKAGE}/version.BuildVersion=${BUILD_VERSION} -X ${PACKAGE}/version.BuildTime=${BUILD_TIME} -X ${PACKAGE}/version.CommitID=${CI_COMMIT_SHA}" \
          -o cas ./main.go

#go env -w GOPROXY=http://172.16.251.11:1111,https://goproxy.io/,https://mirrors.aliyun.com/goproxy/,https://goproxy.cn/,direct && go mod tidy &&\
#   GO111MODULE=on \
#   GOOS=linux \
#   CGO_ENABLED=1 \
#   CC=aarch64-linux-gnu-gcc \
#   CXX=aarch64-linux-gnu-g++ \
#   GOARCH=${ARCH} \
#   go build -ldflags "-X ${PACKAGE}/version.BuildName=${PACKAGE} -X ${PACKAGE}/version.BuildVersion=${BUILD_VERSION} -X ${PACKAGE}/version.BuildTime=${BUILD_TIME} -X ${PACKAGE}/version.CommitID=${CI_COMMIT_SHA}" \
#          -o cas ./main.go
