[1mdiff --git a/service/statistics/client/applet_client_test.go b/service/statistics/client/applet_client_test.go[m
[1mindex 814eac3..7816721 100644[m
[1m--- a/service/statistics/client/applet_client_test.go[m
[1m+++ b/service/statistics/client/applet_client_test.go[m
[36m@@ -14,3 +14,10 @@[m [mfunc TestAppletClient(t *testing.T) {[m
 	}[m
 	fmt.Println(len(details))[m
 }[m
[32m+[m[32mfunc TestCorupsClient(t *testing.T) {[m
[32m+[m	[32mdetails, err := listFileAssets()[m
[32m+[m	[32mif err != nil {[m
[32m+[m		[32mstdlog.Errorln(err.Error())[m
[32m+[m	[32m}[m
[32m+[m	[32mfmt.Println(len(details))[m
[32m+[m[32m}[m
[1mdiff --git a/service/statistics/client/corups_client.go b/service/statistics/client/corups_client.go[m
[1mindex bdb20a3..27fd4c1 100644[m
[1m--- a/service/statistics/client/corups_client.go[m
[1m+++ b/service/statistics/client/corups_client.go[m
[36m@@ -15,6 +15,7 @@[m [mvar CorupsClient *HTTPClient[m
 [m
 func init() {[m
 	CorupsClient = NewHTTPClient([m
[32m+[m		[32m// WithBaseURL("http://172.17.124.25:31801"),[m
 		WithBaseURL(conf.C.Client.Prefix),[m
 		WithDefaultHeader("Authorization", "Bearer "+conf.C.Auth.Token),[m
 	)[m
[36m@@ -90,17 +91,23 @@[m [mfunc listDatasets() ([]*models.StaticAssetsDetail, error) {[m
 [m
 func listFileAssets() ([]*models.StaticAssetsDetail, error) {[m
 	path := "/cv/api/assets/file-assets"[m
[31m-	resp, err := CorupsClient.Get(path, NewRequestOptions())[m
[32m+[m	[32mresp, err := CorupsClient.Get(path,[m
[32m+[m		[32mNewRequestOptions().WithQueryParam("from", "0").WithQueryParam("size", "10000"),[m
[32m+[m	[32m)[m
 	if err != nil {[m
 		return nil, err[m
 	}[m
 [m
[31m-	fileAssetsResp := make([]*FileAsset, 0)[m
[32m+[m	[32mtype AssetsResp struct {[m
[32m+[m		[32mRecords []*FileAsset `json:"records"`[m
[32m+[m	[32m}[m
[32m+[m	[32mfileAssetsResp := AssetsResp{}[m
[32m+[m
 	if err := json.Unmarshal(resp.Body, &fileAssetsResp); err != nil {[m
 		return nil, err[m
 	}[m
 	details := make([]*models.StaticAssetsDetail, 0)[m
[31m-	for _, fileAssets := range fileAssetsResp {[m
[32m+[m	[32mfor _, fileAssets := range fileAssetsResp.Records {[m
 		details = append(details, &models.StaticAssetsDetail{[m
 			Name:          fileAssets.Name,[m
 			ProjectName:   "",[m
[1mdiff --git a/service/statistics/statistics_service.go b/service/statistics/statistics_service.go[m
[1mindex 8b8e97f..935f21c 100644[m
[1m--- a/service/statistics/statistics_service.go[m
[1m+++ b/service/statistics/statistics_service.go[m
[36m@@ -106,6 +106,7 @@[m [mfunc (s *StatisticsService) refreshCache(ctx context.Context) ([]*models.StaticA[m
 		username := helper.GetUsername(ctx)[m
 		projects, err := s.ps.ListProjects(username, "")[m
 		if err != nil {[m
[32m+[m			[32mstdlog.Errorf("list projects failed: %v", err)[m
 			projectsChan <- projectResult{nil, err}[m
 			return[m
 		}[m
