package conf

import (
	"net/url"
	"strings"

	"golang.org/x/oauth2"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type AuthMode string

const (
	AuthModeCAS    AuthMode = "cas" // cas authentication mode
	AuthModeOAuth2 AuthMode = "oauth2"
	AuthModeMixed  AuthMode = "mixed" // 混合登录模式, 内置CAS登录+Oauth2登录入口
)

func (c *Config) Oauth2Config() *oauth2.Config {
	return c.Oauth2ConfigWithRedirectParams(nil)
}
func (c *Config) Oauth2ConfigWithRedirectParam(key string, value string) *oauth2.Config {
	return c.Oauth2ConfigWithRedirectParams(map[string]string{key: value})
}
func (c *Config) Oauth2ConfigWithRedirectParams(kvs map[string]string) *oauth2.Config {
	if c == nil || c.Auth == nil {
		return nil
	}
	if c.Auth.Mode != AuthModeOAuth2 && c.Auth.Mode != AuthModeMixed {
		stdlog.Warnf("getting oauth2 config while auth mode not match: %s", c.Auth.Mode)
		return nil
	}
	if c.Auth.OAuth2 == nil {
		stdlog.Warnf("auth mode is oauth2 but config is empty")
		return nil
	}
	return c.Auth.OAuth2.WithRedirectParams(kvs)
}

// OAuth2DefaultUserConfig OAuth2 登录后,对于平台内部不存在的用户,默认的初始化行为
type OAuth2DefaultUserConfig struct {
	Password  string `json:"password,omitempty" yaml:"password"`
	Project   string `json:"project,omitempty" yaml:"project"`
	UserGroup string `json:"user_group,omitempty" yaml:"user_group"`
}

type TokenMode string

const (
	TokenModeTranswarp TokenMode = "transwarp"
	TokenModeZtej      TokenMode = "ztej"
	TokenModeZszq      TokenMode = "zszq"
	TokenModeCustom    TokenMode = "custom"
	TokenModeExternal  TokenMode = "external" // 新增模式：通过外部接口获取用户信息
)

type Oauth2UserInfoRespMap struct {
	Username string `json:"username" yaml:"username"`
	Fullname string `json:"fullname" yaml:"fullname"`
	Email    string `json:"email" yaml:"email"`
	Mobile   string `json:"mobile" yaml:"mobile"`
}

// UserInfoFetcherConfig 当Oauth2提供了额外的用户信息获取接口时,可以使用该配置进行用户信息提取
type UserInfoFetcherConfig struct {
	Endpoint    string                 `json:"endpoint" yaml:"endpoint"`         // 接口地址
	Method      string                 `json:"method" yaml:"method"`             // 请求方法
	Headers     map[string]string      `json:"headers" yaml:"headers"`           // 请求头
	QueryParams map[string]string      `json:"query_params" yaml:"query_params"` // 查询参数模板(参数名-> go template)
	Body        string                 `json:"body" yaml:"body"`                 // 请求体模板(go template)
	ResponseMap *Oauth2UserInfoRespMap `json:"response_map" yaml:"response_map"` // 字段映射(期望值 -> Response结构体字段提取代码Jsonnet)
}

// OAuth2Config describes a typical 3-legged OAuth2 flow, with both the
// client application information and the server's endpoint URLs.
// For the client credentials 2-legged OAuth2 flow, see the clientcredentials
// package (https://golang.org/x/oauth2/clientcredentials).
// Copy From oauth2.Config
// PS 原配置结构未提供 yaml tag,因此无法通过环境变量传入,故此重新定义
type OAuth2Config struct {
	PreCheckRedirect bool                     `json:"pre_check_redirect" yaml:"pre_check_redirect"`
	TokenMode        TokenMode                `json:"token_mode" yaml:"token_mode"`
	FieldsMap        map[string]string        `json:"fields_map" yaml:"fields_map"`
	DefaultUserConf  *OAuth2DefaultUserConfig `json:"default_user_conf" yaml:"default_user_conf"`
	UserInfoFetcher  *UserInfoFetcherConfig   `json:"user_info_fetcher" yaml:"user_info_fetcher"`

	// ClientID is the application's ID.
	ClientID string `json:"client_id,omitempty" yaml:"client_id"`

	// ClientSecret is the application's secret.
	ClientSecret string `json:"client_secret,omitempty" yaml:"client_secret"`

	// Endpoint contains the resource server's token endpoint
	// URLs. These are constants specific to each server and are
	// often available via site-specific packages, such as
	// google.Endpoint or github.Endpoint.
	Endpoint OAuth2Endpoint `json:"endpoint" yaml:"endpoint"`

	// RedirectURL is the URL to redirect users going through
	// the OAuth flow, after the resource owner's URLs.
	RedirectURL string `json:"redirect_url,omitempty" yaml:"redirect_url"`

	// Scope specifies optional requested permissions.
	Scopes []string `json:"scopes,omitempty" yaml:"scopes"`
}

func (oc *OAuth2Config) ToOAuth2Config() *oauth2.Config {
	if oc == nil {
		return nil
	}
	return &oauth2.Config{
		ClientID:     oc.ClientID,
		ClientSecret: oc.ClientSecret,
		Endpoint:     oc.Endpoint.ToOAuth2Endpoint(),
		RedirectURL:  oc.RedirectURL,
		Scopes:       oc.Scopes,
	}
}
func (oc *OAuth2Config) WithRedirectParam(key, value string) *oauth2.Config {
	return oc.WithRedirectParams(map[string]string{key: value})
}

func (oc *OAuth2Config) WithRedirectParams(kvs map[string]string) *oauth2.Config {
	if oc == nil {
		return nil
	}
	c := oc.ToOAuth2Config()
	vs := url.Values{}
	for k, v := range kvs {
		vs[k] = []string{v}
	}
	if strings.Contains(c.RedirectURL, "?") {
		c.RedirectURL += "&" + vs.Encode()
	} else {
		c.RedirectURL += "?" + vs.Encode()
	}
	return c
}

// OAuth2Endpoint represents an OAuth 2.0 provider's authorization and token
// endpoint URLs.
// Copy from oauth2.Endpoint
// PS 原配置结构未提供 yaml tag,因此无法通过环境变量传入,故此重新定义
type OAuth2Endpoint struct {
	AuthURL       string `json:"auth_url,omitempty" yaml:"auth_url"`
	LogoutURL     string `json:"logout_url,omitempty" yaml:"logout_url"`
	UserInfoURL   string `json:"user_info_url,omitempty" yaml:"user_info_url"`
	DeviceAuthURL string `json:"device_auth_url,omitempty" yaml:"device_auth_url"`
	TokenURL      string `json:"token_url,omitempty" yaml:"token_url"`

	// AuthStyle optionally specifies how the endpoint wants the
	// client ID & client secret sent. The zero value means to
	// auto-detect.
	AuthStyle oauth2.AuthStyle `json:"auth_style,omitempty" yaml:"auth_style"`
}

func (oe *OAuth2Endpoint) ToOAuth2Endpoint() (ep oauth2.Endpoint) {
	if oe == nil {
		return
	}
	return oauth2.Endpoint{
		AuthURL:       oe.AuthURL,
		TokenURL:      oe.TokenURL,
		AuthStyle:     oe.AuthStyle,
		DeviceAuthURL: oe.DeviceAuthURL,
	}
}
