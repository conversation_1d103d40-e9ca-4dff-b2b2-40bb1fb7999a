package conf

import "testing"

func TestCASConfig_AsExternalURL(t *testing.T) {
	c := &CASConfig{
		ServerUrl:                  "",
		EmbeddedServerHost:         "**********:8393",
		EmbeddedServerExternalHost: "https://llmops.transwarp.io/power/llm/zz/gateway/login/",
	}
	tests := []struct {
		name            string
		internalUrl     string
		wantExternalUrl string
		confExternalUrl string
		wantErr         bool
	}{
		{
			confExternalUrl: "https://llmops.transwarp.io/power/llm/zz/gateway/login/",
			internalUrl:     "https://**********:8393/v1/auth/cas/login?service=https%3A%2F%2F172.17.120.206%3A30775%2Fpower%2Fllm%2Fzz%2Fgateway%2Fcas%2Fapi%2Flogin%3Fredirect%3Dhttps%3A%2F%2F172.17.120.206%3A30775%2Fpower%2Fllm%2Fzz%2Fproject%2Fassets%2Fmw%2Flist",
			wantExternalUrl: "https://llmops.transwarp.io/power/llm/zz/gateway/login/v1/auth/cas/login?service=https%3A%2F%2F172.17.120.206%3A30775%2Fpower%2Fllm%2Fzz%2Fgateway%2Fcas%2Fapi%2Flogin%3Fredirect%3Dhttps%3A%2F%2F172.17.120.206%3A30775%2Fpower%2Fllm%2Fzz%2Fproject%2Fassets%2Fmw%2Flist",
			wantErr:         false,
		},
		{
			confExternalUrl: "https://llmops.transwarp.io/power/llm/zz/gateway/login",
			internalUrl:     "https://**********:8393/v1/auth/cas/logout?service=www.baidu.com",
			wantExternalUrl: "https://llmops.transwarp.io/power/llm/zz/gateway/login/v1/auth/cas/logout?service=www.baidu.com",
			wantErr:         false,
		},
		{
			confExternalUrl: "**************:27208", // 测试无协议前缀
			internalUrl:     "https://**********:8393/v1/auth/cas/logout?service=www.baidu.com",
			wantExternalUrl: "",
			wantErr:         true,
		},
		{
			confExternalUrl: "http://**************:27208/ddd",
			internalUrl:     "https://**********:8393/v1/auth/cas/logout?service=www.baidu.com",
			wantExternalUrl: "http://**************:27208/ddd/v1/auth/cas/logout?service=www.baidu.com",
			wantErr:         false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c.EmbeddedServerExternalHost = tt.confExternalUrl
			gotExternalUrl, err := c.HandleEmbeddedCasRedirectUrl(tt.internalUrl)
			if (err != nil) != tt.wantErr {
				t.Errorf("HandleEmbeddedCasRedirectUrl() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotExternalUrl != tt.wantExternalUrl {
				t.Errorf("HandleEmbeddedCasRedirectUrl() gotExternalUrl = \n\t%v, \nwant \n\t%v", gotExternalUrl, tt.wantExternalUrl)
			}
		})
	}
}
