package project

import "time"

type ProjectReq struct {
	Name        string              `json:"name"`
	Description string              `json:"description"`
	Labels      map[string][]string `json:"labels"`
}

type Project struct {
	Id          int64     `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Labels      string    `json:"labels"`
	CreateUser  string    `json:"create_user"`
	CreateTime  time.Time `json:"create_time"`
}

type ProjectResp struct {
	Id int64 `json:"id"`
	ProjectReq
	MemberCount int       `json:"member_count"`
	CreateUser  string    `json:"create_user"`
	CreateTime  time.Time `json:"create_time"`
}

type ProjectMemberResp struct {
	Id               int64     `json:"id"`
	Name             string    `json:"name"`
	UserType         string    `json:"user_type"`
	PlatformRoleName string    `json:"platform_role_name"`
	ProjectRoleName  string    `json:"project_role_name"`
	CreateUser       string    `json:"create_user"`
	CreateTime       time.Time `json:"create_time"`
}

type ProjectMemberReq struct {
	Id       int64  `json:"id"`
	RoleId   int64  `json:"role_id"`
	UserType string `json:"user_type"`
}
