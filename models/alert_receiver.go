package models

import (
	"github.com/aws/smithy-go/ptr"
	"gorm.io/gorm"
)

const (
	TableNameAlertReceiver = "alert_receiver_config"
)

type AlertReceiverConfig struct {
	gorm.Model      `json:"-"`
	ServiceID       string   `json:"service_id" description:"服务ID" gorm:"column:service_id;unique"`
	ServiceCreator  *string  `json:"service_creator" description:"预警创建人，如未选中则不传" gorm:"column:service_creator"`
	ProjectManager  []string `json:"project_manager" description:"空间管理员，数组，如未选中则不传或者传空数组" gorm:"column:project_manager;serializer:json"`
	TargetReceivers []string `json:"target_receivers" description:"指定接收人，数组，如未选中则不传或者传空数组" gorm:"column:target_receivers;serializer:json"`
}

func (*AlertReceiverConfig) TableName() string {
	return TableNameAlertReceiver
}

func (a *AlertReceiverConfig) GetReceiver() []string {
	res := make([]string, 0)
	set := make(map[string]struct{})
	if ptr.ToString(a.ServiceCreator) != "" {
		set[ptr.ToString(a.ServiceCreator)] = struct{}{}
	}
	for _, v := range a.TargetReceivers {
		set[v] = struct{}{}
	}
	for _, v := range a.ProjectManager {
		set[v] = struct{}{}
	}
	for k := range set {
		res = append(res, k)
	}
	return res

}
