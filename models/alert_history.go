package models

import (
	"encoding/json"
	"github.com/influxdata/influxdb/client"
	"github.com/jinzhu/copier"
	"strconv"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/database/influxdb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/models/monitor"
)

type AlertTag = string
type AlertField = string

const (
	QueryTypeUser = "user"
	QueryTypeSvc  = "service"

	AlertHistoryTable = "alert_history"

	AlertTagServiceID AlertTag = "serviceId"
	AlertTagID        AlertTag = "id"
	AlertTagProjectID AlertTag = "projectId"
	AlertTagReceivers AlertTag = "receivers"

	AlertFieldAlerts              AlertField = "alerts"
	AlertFieldServiceName         AlertTag   = "serviceName"
	AlertFieldLevel               AlertField = "level"
	AlertFieldHasBeenRead         AlertField = "hasBeenRead"
	AlertFieldConditionDesc       AlertField = "condition_desc"
	AlertFieldRelatedMetrics      AlertField = "related_metrics"
	AlertFieldAlertCreator        AlertField = "alert_creator"
	AlertFieldAlertRuleCreateTime AlertField = "create_time_sec"
	AlertFieldAlertName           AlertField = "alert_name"
	AlertFieldSubject             AlertField = "subject"
	AlertFieldProjectName         AlertField = "project_name"
)

var AlterFields = []string{AlertFieldAlerts}

type AlertsHistoryDTO struct {
	ID                string              `json:"id" description:"预警id"`                                // 预警id
	ProjectID         string              `json:"project_id" description:"项目id"`                        //项目id
	ProjectName       string              `json:"project_name" description:"项目名"`                      //项目名
	AlertName         string              `json:"alert_name" description:"预警名称"`                      //预警名称
	TriggerTimeSec    int64               `json:"trigger_time_sec" description:"预警触发时间，秒级时间戳"` //预警触发时间
	AlertSource       string              `json:"alert_source" description:"预警来源"`                    //预警来源
	Subject           string              `json:"subject" description:"预警主题"`                         //预警主题
	Creator           string              `json:"creator" description:"预警创建人"`                       //预警创建人
	RuleCreateTimeSec int                 `json:"create_time_sec" description:"预警创建时间，秒级时间戳"`  //预警创建时间
	ServiceName       string              `json:"service_name" description:"服务名称"`                    //服务名称
	ServiceID         string              `json:"service_id" description:"服务ID"`                        // 服务ID
	Level             int                 `json:"level" description:"预警等级, 0:一般, 1:中等, 2:严重"`   //预警等级
	Advice            string              `json:"advice" description:"处理建议"`                          //建议
	HasBeenRead       bool                `json:"has_been_read" description:"是否已读"`                   //是否已读
	ServiceAlertState string              `json:"service_alert_state" description:"服务预警状态"`         // 服务预警状态
	RelatedMetrics    []monitor.MetricRef `json:"related_metrics" description:"监控到的指标列表"`
	ConditionDesc     string              `json:"condition_desc"  description:"预警描述"`
}

type AlertsHistory struct {
	ID                string              `json:"id" ts:"column:id"`
	Alerts            []AlertHistory      `json:"alerts" ts:"column:alerts"`
	CommonAnnotations AlertAnnotation     `json:"commonAnnotations" ts:"column:commonAnnotations"`
	CommonLabels      map[string]string   `json:"commonLabels" ts:"column:commonLabels"`
	ExternalURL       string              `json:"externalURL" ts:"column:externalURL"`
	GroupKey          string              `json:"groupKey" ts:"column:groupKey"`
	GroupLabels       map[string]string   `json:"groupLabels" ts:"column:groupLabels"`
	Message           string              `json:"message" ts:"column:message"`
	OrgId             int                 `json:"orgId" ts:"column:orgId"`
	State             string              `json:"state" ts:"column:state"`
	Status            string              `json:"status" ts:"column:status"`
	Title             string              `json:"title" ts:"column:title"`
	TruncatedAlerts   int                 `json:"truncatedAlerts" ts:"column:truncatedAlerts"`
	Version           string              `json:"version" ts:"column:version"`
	ServiceID         string              `json:"serviceId" ts:"column:serviceId"`
	ServiceName       string              `json:"serviceName" ts:"column:serviceName"`
	ProjectID         string              `json:"projectId" ts:"column:projectId"`
	Receivers         []string            `json:"receivers" ts:"column:receivers"`
	Level             int                 `json:"level" ts:"column:level" description:"预警等级, 0:一般, 1:中等, 2:严重"` //预警等级
	HasBeenRead       bool                `json:"has_been_read"   description:"是否已读"`                                 //是否已读
	RelatedMetrics    []monitor.MetricRef `json:"related_metrics" description:"监控到的指标列表"`
	ConditionDesc     string              `json:"condition_desc"  description:"预警描述"`
	AlertCreator      string              `json:"alert_creator"  description:"预警创建人"`
	AlertSubject      string              `json:"alert_subject"  description:"预警主题"`
	RuleCreateTimeSec int64               `json:"create_time_sec" description:"预警创建时间，秒级时间戳"` //预警创建时间
	AlertName         string              `json:"alert_name" description:"预警名称"`
	ProjectName       string              `json:"project_name"`
}

func (a *AlertsHistory) GetRuleID() (int64, error) {
	if len(a.Alerts) == 0 {
		return -1, stderr.Internal.Errorf("no alerts")
	}
	desc := a.Alerts[0].Annotations.Description
	m := make(map[string]string, 0)
	if err := json.Unmarshal([]byte(desc), &m); err != nil {
		return -1, err
	}
	v, ok := m["rule_id"]
	if !ok {
		return -1, stderr.Internal.Errorf("no alerts")
	}
	ruleID, err := strconv.Atoi(v)
	if err != nil {
		return -1, err
	}
	return int64(ruleID), nil
}

func (a *AlertsHistory) TODTO() (*AlertsHistoryDTO, error) {
	res := &AlertsHistoryDTO{}
	if err := copier.Copy(res, a); err != nil {
		return nil, err
	}
	res.ServiceAlertState = "服务正在预警中"
	res.Advice = "调整服务资源"
	res.AlertSource = "Sophon平台-服务预警"
	timeNsec, err := strconv.ParseInt(a.ID, 10, 64)
	if err != nil {
		return nil, err
	}
	res.TriggerTimeSec = timeNsec / (1000 * 1000 * 1000)
	res.Creator = a.AlertCreator
	res.Subject = a.AlertSubject
	return res, nil
}

func (a *AlertsHistory) TOPO() (*AlertsHistoryPO, error) {
	res := &AlertsHistoryPO{}
	if err := copier.Copy(res, a); err != nil {
		return nil, err
	}
	if a.ID != "" {
		res.ID = a.ID
	} else {
		res.ID = strconv.Itoa(int(time.Now().UnixNano()))
	}
	if len(a.Alerts) > 0 {
		bytes, err := json.Marshal(a.Alerts)
		if err != nil {
			return nil, err
		}
		res.Alerts = string(bytes)
	}
	if len(a.Receivers) > 0 {
		bytes, err := json.Marshal(a.Receivers)
		if err != nil {
			return nil, err
		}
		res.Receivers = string(bytes)
	}
	res.HasBeenRead = strconv.FormatBool(a.HasBeenRead)
	res.Level = strconv.Itoa(a.Level)
	if len(a.RelatedMetrics) > 0 {
		bytes, err := json.Marshal(a.RelatedMetrics)
		if err != nil {
			return nil, err
		}
		res.RelatedMetrics = string(bytes)
	}
	if a.RuleCreateTimeSec > 0 {
		res.RuleCreateTimeSec = strconv.Itoa(int(a.RuleCreateTimeSec))
	}
	res.TruncatedAlerts = strconv.Itoa(a.TruncatedAlerts)
	res.Subject = a.AlertSubject
	return res, nil
}

type AlertsHistoryPO struct {
	ID                string `json:"id" ts:"column:id"`
	Alerts            string `json:"alerts" ts:"column:alerts"`
	CommonAnnotations string `json:"commonAnnotations" ts:"column:commonAnnotations"`
	CommonLabels      string `json:"commonLabels" ts:"column:commonLabels"`
	ExternalURL       string `json:"externalURL" ts:"column:externalURL"`
	GroupKey          string `json:"groupKey" ts:"column:groupKey"`
	GroupLabels       string `json:"groupLabels" ts:"column:groupLabels"`
	Message           string `json:"message" ts:"column:message"`
	OrgId             string `json:"orgId" ts:"column:orgId"`
	State             string `json:"state" ts:"column:state"`
	Status            string `json:"status" ts:"column:status"`
	Title             string `json:"title" ts:"column:title"`
	TruncatedAlerts   string `json:"truncatedAlerts" ts:"column:truncatedAlerts"`
	Version           string `json:"version" ts:"column:version"`
	ServiceID         string `json:"serviceId" ts:"column:serviceId"`
	ServiceName       string `json:"serviceName" ts:"column:serviceName"`
	ProjectID         string `json:"projectId" ts:"column:projectId"`
	Receivers         string `json:"receivers" ts:"column:receivers"`
	Level             string `json:"level" ts:"column:level"  description:"预警等级, 0:一般, 1:中等, 2:严重"` //预警等级
	HasBeenRead       string `json:"hasBeenRead" ts:"column:hasBeenRead" description:"是否已读"`              //是否已读
	ConditionDesc     string `json:"condition_desc"  ts:"column:condition_desc" description:"预警描述"`
	RelatedMetrics    string `json:"related_metrics" ts:"column:related_metrics" description:"监控到的指标列表"`
	RuleCreateTimeSec string `json:"create_time_sec" ts:"column:create_time_sec" description:"预警创建时间，秒级时间戳"` //预警创建时间
	AlertCreator      string `json:"alert_creator"  ts:"column:alert_creator" description:"预警创建人"`
	AlertName         string `json:"alert_name"  ts:"column:alert_name" description:"预警规则名称"`
	Subject           string `json:"subject" ts:"column:subject"  description:"预警主题"` //预警主题
	ProjectName       string `json:"project_name" ts:"column:project_name" description:"空间名"`
}

type AlertHistory struct {
	Annotations  AlertAnnotation        `json:"annotations"`
	DashboardURL string                 `json:"dashboardURL"`
	EndsAt       time.Time              `json:"endsAt"`
	Fingerprint  string                 `json:"fingerprint"`
	GeneratorURL string                 `json:"generatorURL"`
	Labels       map[string]string      `json:"labels"`
	PanelURL     string                 `json:"panelURL"`
	SilenceURL   string                 `json:"silenceURL"`
	StartsAt     time.Time              `json:"startsAt"`
	Status       string                 `json:"status"`
	ValueString  string                 `json:"valueString"`
	Values       map[string]interface{} `json:"values"`
}

type AlertAnnotation struct {
	Description string `json:"description"`
	Summary     string `json:"summary"`
}

func (a *AlertsHistoryPO) ToDO() (*AlertsHistory, error) {
	res := &AlertsHistory{}
	if err := copier.Copy(res, a); err != nil {
		return nil, err
	}
	alerts := make([]AlertHistory, 0)
	if a.Alerts != "" {
		if err := json.Unmarshal([]byte(a.Alerts), &alerts); err != nil {
			return nil, err
		}
	}
	res.Alerts = alerts
	hasBeenRead := false
	if a.HasBeenRead != "" {
		b, err := strconv.ParseBool(a.HasBeenRead)
		if err != nil {
			return nil, err
		}
		hasBeenRead = b
	}
	res.HasBeenRead = hasBeenRead
	level := 0
	if a.Level != "" {
		l, err := strconv.Atoi(a.Level)
		if err != nil {
			return nil, err
		}
		level = l
	}
	if a.RelatedMetrics != "" {
		mes := make([]monitor.MetricRef, 0)
		if err := json.Unmarshal([]byte(a.RelatedMetrics), &mes); err != nil {
			return nil, err
		}
		res.RelatedMetrics = mes
	}
	res.Level = level
	res.AlertSubject = a.Subject
	if a.RuleCreateTimeSec != "" {
		timeSec, err := strconv.Atoi(a.RuleCreateTimeSec)
		if err != nil {
			return nil, err
		}
		res.RuleCreateTimeSec = int64(timeSec)
	}
	return res, nil
}

func (a *AlertsHistoryPO) Time() time.Time {
	if a.ID != "" {
		nSec, err := strconv.ParseInt(a.ID, 10, 64)
		if err == nil {
			return time.Unix(0, nSec)
		}
	}
	return time.Now()
}

func (a *AlertsHistoryPO) Tags() map[string]string {
	return map[string]string{
		AlertTagProjectID: a.ProjectID,
		AlertTagServiceID: a.ServiceID,
		AlertTagID:        a.ID,
		AlertTagReceivers: a.Receivers,
	}
}

func (a *AlertsHistoryPO) Fields() map[string]interface{} {
	return map[string]interface{}{
		AlertFieldAlerts:              a.Alerts,
		AlertTagID:                    a.ID,
		AlertFieldLevel:               a.Level,
		AlertFieldHasBeenRead:         a.HasBeenRead,
		AlertTagReceivers:             a.Receivers,
		AlertFieldConditionDesc:       a.ConditionDesc,
		AlertFieldRelatedMetrics:      a.RelatedMetrics,
		AlertFieldServiceName:         a.ServiceName,
		AlertFieldAlertCreator:        a.AlertCreator,
		AlertFieldAlertRuleCreateTime: a.RuleCreateTimeSec,
		AlertFieldAlertName:           a.AlertName,
		AlertFieldSubject:             a.Subject,
		AlertFieldProjectName:         a.ProjectName,
	}
}

//func AlertHistoryCvtFromInfluxDB(raws []models.Row) ([]*AlertsHistory, error) {
//	res := make([]*AlertsHistory, 0)
//
//	return res, nil
//}
//
//func AlertHistoryCvtFromInfluxDBRaw(raw models.Row) ([]*AlertsHistory, error) {
//	res := make([]*AlertsHistory, 0)
//	kvs := make([]map[string]string,0)
//	for idx,v := range raw.Columns {
//		for _,vs := range raw.Values {
//			valueInterface := vs[idx]
//			value := ""
//			if v,ok := valueInterface.(string);ok {
//
//			}
//			kv[]
//		}
//	}
//
//	return res, nil
//}

func (a *AlertsHistoryPO) Measurement() string {
	return AlertHistoryTable
}

type IDs struct {
	IDs []string `json:"ids"`
}

func ParseAlertsHistory(res client.Result) ([]*AlertsHistoryPO, error) {
	alerts := make([]*AlertsHistoryPO, 0)
	for _, s := range res.Series {
		// 根据时间区间+实例ID等tag聚合后，可能告警分散在多个series返回，因此需要合并起来
		if err := influxdb.Scan(&s, &alerts); err != nil {
			return nil, stderr.Internal.Cause(err, "failed to get alerts from query result scanning")
		}
	}
	return alerts, nil
}
