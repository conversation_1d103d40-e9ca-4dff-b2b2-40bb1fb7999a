package monitor

import (
	"bytes"
	"text/template"
)

// TODO

type MetricType string

const (
	MetricTypeGauge   MetricType = "gauge"
	MetricTypeCounter MetricType = "counter"
	MetricTypeReduer  MetricType = "reducer"
)

// 监控图表
type Panel struct {
	Id         string       `json:"id" yaml:"id"`
	Name       string       `json:"name" yaml:"name"`
	Metrics    []*MetricRef `json:"metrics" yaml:"metrics" description:"图表用到的指标, 可配置多个"`
	Templating []*Variable  `json:"templating" yaml:"templating" description:"模板变量"`
	Unit       string       `json:"unit" yaml:"unit" description:"单位"`
	Style      string       `json:"style" yaml:"style" description:"图表样式: 折线图, 柱状图; 对应可选值: lines, bars"`
}

type Metric struct {
	Id              string         `json:"id" yaml:"id"`
	Name            string         `json:"name" yaml:"name"`
	Description     string         `json:"description" yaml:"description"`
	Type            MetricType     `json:"type" yaml:"type" description:"指标类型; gauge:可增可减，表示瞬时值; counter:单调递增，适用于记录累计值; reducer:聚合时间段的归约值"`
	Query           map[string]any `json:"query" yaml:"query" description:"查询请求, grafana datasource query的数据结构"`
	DisableAlerting bool           `json:"disable_alerting" yaml:"disable_alerting" description:"是否在告警的指标选项禁用"`
	// VariableNames []string       `json:"variable_names" yaml:"variable_names" description:"查询表达式包含的变量名"`
	exprTpl *template.Template `json:"-" yaml:"-" gorm:"-"`
}

func (m *Metric) Init() error {
	t, err := template.New("expr").Parse(m.Query["expr"].(string))
	if err != nil {
		return err
	}
	m.exprTpl = t
	return nil
}

func (m *Metric) RenderExpr(data any) (string, error) {
	if m.exprTpl == nil {
		if err := m.Init(); err != nil {
			return "", err
		}
	}
	var buf bytes.Buffer
	err := m.exprTpl.Execute(&buf, data)
	if err != nil {
		return "", err
	}
	return buf.String(), nil
}

type MetricRef struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}
