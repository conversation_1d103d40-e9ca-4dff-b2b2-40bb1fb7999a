package monitor

type VariableType string

const (
	// VariableTypeQuery    VariableType = "query"
	// VariableTypeCustom   VariableType = "custom"
	// VariableTypeConstant VariableType = "constant"
	VariableTypeInterval VariableType = "interval"
)

type Option struct {
	Selected bool   `json:"selected"`
	Text     string `json:"text"`
	Value    string `json:"value"`
}

type Variable struct {
	Name        string       `json:"name" yaml:"name"`
	Type        VariableType `json:"type" yaml:"type" description:"模板变量类型, 目前只支持interval"`
	Label       string       `json:"-"`
	Description string       `json:"description" yaml:"description"`
	Values      string       `json:"-" yaml:"values" description:"逗号分隔的字符串列表"`
	Options     []*Option    `json:"options" yaml:"options" description:"可选值列表"`
}

// ParseOptions parse options from values if Options if empty
func (v *Variable) ParseOptions() {
	if v == nil {
		return
	}
	// TODO
}
