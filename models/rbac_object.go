package models

import "transwarp.io/applied-ai/aiot/vision-std/clients/cas"

type PutObjectReq struct {
	SubType   cas.SubType `json:"sub_type"`   // 主体类型 user:Username必填, group:GroupId必填
	Username  string      `json:"username"`   // 用户名: thinger
	GroupId   uint64      `json:"group_id"`   // 用户组id
	ProjectId string      `json:"project_id"` // 空间id: assets
	Act       cas.Act     `json:"action"`     // 允许的操作
}

type GetObjectRsp struct {
	ObjType  cas.ObjType `json:"obj_type"`
	ObjId    string      `json:"obj_id"` // 操作对象id
	Policies []*Policy   `json:"policies"`
}

type Policy struct {
	SubType   cas.SubType `json:"sub_type"`   // 主体类型 user:Username必填, group:GroupId必填
	Username  string      `json:"username"`   // 用户名: thinger
	GroupId   uint64      `json:"group_id"`   // 用户组id
	ProjectId string      `json:"project_id"` // 空间id: assets
	Act       cas.Act     `json:"action"`     // 允许的操作
}

type ListObjectRsp struct {
	Objects    []*Object      `json:"objects"`
	AccessType cas.AccessType `json:"access_type"`
}
type Object struct {
	ObjType cas.ObjType `json:"obj_type"`
	ObjId   string      `json:"obj_id"` // 操作对象id
	Act     cas.Act     `json:"action"` // 允许的操作
}
