package models

import "transwarp.io/applied-ai/aiot/vision-std/clients/cas"

type PutObjectReq struct {
	SubType   cas.SubType `json:"sub_type" description:"主体类型 user:username, group:group_id必填"`
	Username  string      `json:"username" description:"用户名"`
	GroupId   uint64      `json:"group_id" description:"用户组id"`
	ProjectId string      `json:"project_id" description:"空间id"`
	Act       cas.Act     `json:"action" description:"允许的操作:readonly,all"`
}

type GetObjectRsp struct {
	ObjType  cas.ObjType `json:"obj_type"`
	ObjId    string      `json:"obj_id" description:"操作对象id"`
	Policies []*Policy   `json:"policies"`
}

type Policy struct {
	SubType   cas.SubType `json:"sub_type" description:"主体类型 user:username, group:group_id必填"`
	Username  string      `json:"username" description:"用户名"`
	GroupId   uint64      `json:"group_id" description:"用户组id"`
	GroupName string      `json:"group_name,omitempty" description:"用户组"`
	ProjectId string      `json:"project_id" description:"空间id"`
	Act       cas.Act     `json:"action" description:"允许的操作:readonly,all"`
}

type ListObjectRsp struct {
	Objects    []*Object      `json:"objects"`
	AccessType cas.AccessType `json:"access_type"`
}
type Object struct {
	ObjType cas.ObjType `json:"obj_type"`
	ObjId   string      `json:"obj_id"` // 操作对象id
	Act     cas.Act     `json:"action"` // 允许的操作
}
type ListPoliciesReq struct {
	ObjIds    []string
	ObjType   cas.ObjType
	ProjectId string
	Act       cas.Act
	SubType   cas.SubType
	Username  string
	GroupId   uint64
}
