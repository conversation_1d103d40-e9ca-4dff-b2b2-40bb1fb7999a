package models

import (
	"time"
)

func (Project) TableName() string {
	return "project"
}

func (ProjectMember) TableName() string {
	return "project_member"
}

func (ProjectMemberStatistics) TableName() string {
	return "project_member"
}

type ProjectReq struct {
	ProjectId   string              `json:"project_id"`
	Industry    string              `json:"industry"`
	Logo        string              `json:"logo"`
	Name        string              `json:"name"`
	Description string              `json:"description"`
	Labels      map[string][]string `json:"labels"`
	CreateUser  string              `json:"createUser"`
	TenantUid   string              `json:"tenant_uid"`
	Managers    []*ProjectMemberReq `json:"managers" description:"空间管理员" `
}

type Project struct {
	// gorm.Model
	Id          uint64    `json:"id" gorm:"primary_key"`
	ProjectId   string    `json:"project_id" gorm:"column:project_id;type:varchar(500);index;"`
	Name        string    `json:"name" gorm:"column:name;type:varchar(500);"`
	Description string    `json:"description" gorm:"column:description;type:text;"`
	Labels      string    `json:"labels" gorm:"column:labels;type:varchar(500);"`
	Industry    string    `json:"industry" gorm:"column:industry;type:varchar(500);"`
	Logo        string    `json:"logo" gorm:"column:logo;type:varchar(500);"`
	CreateUser  string    `json:"create_user" gorm:"column:create_user;type:varchar(500);"`
	CreateTime  time.Time `json:"create_time" gorm:"column:create_time"`
	UpdateTime  time.Time `json:"update_time" gorm:"column:update_time"`
	TenantUid   string    `json:"tenant_uid"  gorm:"column:tenant_uid;type:varchar(500);index;"`
	Examine     int       `json:"examine" gorm:"column:examine"` // 审批开关
}

type ProjectResp struct {
	// Id        uint64 `json:"id"`
	ProjectId string `json:"project_id"`
	ProjectReq
	MemberCount   uint64            `json:"member_count"`
	Disabled      bool              `json:"disabled"`
	CreateUser    string            `json:"create_user"`
	CreateTime    time.Time         `json:"create_time"`
	Industry      string            `json:"industry"`
	ResourceQuota ResourceQuotaSpec `json:"resource_quota"`
	Logo          string            `json:"logo"`
	Examine       int               `json:"examine"`
}

func ProjectGroupByTenant(projects []*ProjectResp) map[string][]*ProjectResp {
	res := make(map[string][]*ProjectResp)
	for _, project := range projects {
		if v, ok := res[project.TenantUid]; ok {
			res[project.TenantUid] = append(v, project)
		} else {
			res[project.TenantUid] = []*ProjectResp{project}
		}
	}
	return res
}

type ProjectMember struct {
	Id         uint64    `json:"id" gorm:"primary_key"`
	ProjectId  string    `json:"project_id" gorm:"column:project_id;type:varchar(500)"`
	Name       string    `json:"name" gorm:"column:name;type:varchar(500)"`
	UserType   BindType  `json:"user_type" gorm:"column:user_type;type:varchar(30)"`
	CreateUser string    `json:"create_user" gorm:"column:create_user;type:varchar(500)"`
	CreateTime time.Time `json:"create_time" gorm:"column:create_time;autoCreateTime"`
	UpdateTime time.Time `json:"update_time" gorm:"column:update_time;autoUpdateTime"`
}

type ProjectMemberStatistics struct {
	ProjectId   string `json:"project_id" gorm:"column:project_id;type:string"`
	MemberCount uint64 `json:"member_count" gorm:"column:member_count;type:int"`
}
type ProjectMemberListReq struct {
	Name, FullName         string
	PhoneNumber, ProjectId string
}

type ProjectMemberResp struct {
	// Id               uint64    `json:"id"`
	ProjectId        string    `json:"project_id"`
	Name             string    `json:"name"`
	UserType         BindType  `json:"user_type"`
	PlatformRoleName string    `json:"platform_role_name"`
	ProjectRoleId    uint64    `json:"project_role_id"`
	ProjectRoleName  string    `json:"project_role_name"`
	CreateUser       string    `json:"create_user"`
	CreateTime       time.Time `json:"create_time"`
	FullName         string    `json:"full_name"`
	Email            string    `json:"email"`
	PhoneNumber      string    `json:"phone_number" description:"手机号"`
}

type ProjectMemberReq struct {
	Name     string   `json:"name"`
	RoleId   uint64   `json:"role_id"`
	UserType BindType `json:"user_type"`
}

// IsMembersUniq 每个成员是否只出现一次
//
//	@return string 重复成员
//	@return bool false: 存在重复成员
func IsMembersUniq(mems []*ProjectMemberReq) (*ProjectMemberReq, bool) {
	m := make(map[BindType]map[string]uint)
	for _, mem := range mems {
		_, ok := m[mem.UserType]
		if !ok {
			m[mem.UserType] = make(map[string]uint)
		}
		if m[mem.UserType][mem.Name] == 1 {
			return mem, false
		}
		m[mem.UserType][mem.Name]++
	}
	return nil, true
}

type ProjectExistsRsp struct {
	Exists bool `json:"exists"`
}

type ProjectCategory struct {
	// gorm.Model
	Id          uint64 `json:"id" gorm:"primary_key"`
	Name        string `json:"name" gorm:"unique;not null;column:name;type:varchar(500);"`
	Description string `json:"description" gorm:"column:description;type:text;"`
}
