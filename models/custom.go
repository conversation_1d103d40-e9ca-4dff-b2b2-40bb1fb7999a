package models

import (
	"gorm.io/gorm"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	TableNameCustomLogo   = "custom_logo"
	TableNameCustomConfig = "custom_config"
	TableNameNotice       = "notice"
	TableNameCustomIcon   = "custom_icon"
)

type CustomLogo struct {
	gorm.Model
	System    Boolean `json:"-" gorm:"column:system"`
	Default   Boolean `json:"default" gorm:"column:default"`
	Path      string  `json:"path" gorm:"column:path"`
	UpdatedBy string  `json:"updatedBy" gorm:"column:updated_by"`
}

func (*CustomLogo) TableName() string {
	return TableNameCustomLogo
}

type CustomIcon struct {
	gorm.Model
	System    Boolean `json:"-" gorm:"column:system"`
	Default   <PERSON>olean `json:"default" gorm:"column:default"`
	Path      string  `json:"path" gorm:"column:path"`
	UpdatedBy string  `json:"updatedBy" gorm:"column:updated_by"`
}

func (*CustomIcon) TableName() string {
	return TableNameCustomIcon
}

type CustomConfig struct {
	gorm.Model
	System    Boolean                `json:"-" gorm:"column:system"`
	Value     map[string]interface{} `json:"value" gorm:"column:value;serializer:json"`
	UpdatedBy string                 `json:"updatedBy" gorm:"column:updated_by"`
}

func getValue[T any](cfg *CustomConfig, key string) (v T, ok bool) {
	if cfg == nil || cfg.Value == nil {
		return
	}
	rawV, ok := cfg.Value[key]
	if !ok {
		return
	}
	v, ok = rawV.(T)
	if !ok {
		stdlog.Warnf("found custom config value of key=%s: %+v, but the value's type is unexpected: exp=%T,act=%T",
			key, rawV, v, rawV)
	}
	return
}

func (c *CustomConfig) GetStringValue(key string) (string, bool) {
	return getValue[string](c, key)
}

func (*CustomConfig) TableName() string {
	return TableNameCustomConfig
}

type CustomNotice struct {
	gorm.Model
	Title     string  `json:"title" gorm:"column:title;type:varchar(255)"`
	Content   string  `json:"content" gorm:"column:content"`
	Online    Boolean `json:"online" gorm:"column:online"`
	CreatedBy string  `json:"createdBy" gorm:"column:created_by"`
	UpdatedBy string  `json:"updatedBy" gorm:"column:updated_by"`
}

func (*CustomNotice) TableName() string {
	return TableNameNotice
}

type Boolean int

const (
	True  Boolean = 1
	False Boolean = 0
)
