package models

import (
	"encoding/json"
	"fmt"
	"github.com/jinzhu/gorm"
	"io"
	"net/http"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/examine"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	TableNameExamineFlowInstance = "examine_flow_instance"
	TableNameExamineRecord       = "examine_record"
)

func (*ExamineFlowInstance) TableName() string {
	return TableNameExamineFlowInstance
}

func (*ExamineRecord) TableName() string {
	return TableNameExamineRecord
}

// ExamineFlow 审批配置
// 每种审批流程都会对应一个ExamineFlowConfig
// 审批配置仅仅只是审批流程和节点的配置
// 每个审批流程需要绑定到具体的任务才可以触发
type ExamineFlow struct {
	Name      string             `json:"name"` // name同时作为流程的唯一标识
	Abbr      string             `json:"abbr"` // 缩写，必须是大写的两个字母，用于生成流程单号
	Nodes     []*ExamineNode     `json:"nodes"`
	Callbacks []FlowFunctionCall `json:"-"` // 流程结束后的回调
}

// ExamineNode 审批节点配置
type ExamineNode struct {
	Index       int32              `json:"index"`       // 节点索引，第一个节点必须是1
	PrevIndex   int32              `json:"prevIndex"`   // 上一个节点
	NextIndex   int32              `json:"nextIndex"`   // 下一个节点
	RoleRequest []*RoleRequest     `json:"roleRequest"` // 节点的角色控制
	Type        string             `json:"type"`        // 节点类型，目前只有一种base
	Callbacks   []NodeFunctionCall `json:"-"`           // 节点的回调
}

type NodeFunctionCall struct {
	Result   ExamineResult
	Callback func(req CallRequest) CallResponse
}
type FlowFunctionCall struct {
	Status   ExamineStatus
	Callback func(req CallRequest) CallResponse
}

type CallRequest interface{}

type HttpCallRequest struct {
	HttpInfo *examine.HttpInfo
	Token    string
}

type CallResponse interface {
	Message() string
	Error() error
}

func NewErrorResponse(err error) *ErrorResponse {
	return &ErrorResponse{
		err: err,
	}
}

type ErrorResponse struct {
	err error
}

func (e ErrorResponse) Message() string {
	return e.err.Error()
}

func (e ErrorResponse) Error() error {
	return e.err
}

func NewHttpResponse(res *http.Response) *HttpResponse {
	return &HttpResponse{
		response: res,
	}
}

type HttpResponse struct {
	response *http.Response
	err      error
	data     []byte
}

func (h *HttpResponse) Message() string {
	if h.data != nil {
		return string(h.data)
	} else {
		if h.response.StatusCode < http.StatusMultipleChoices && h.response.StatusCode >= http.StatusOK {
			h.data, _ = io.ReadAll(h.response.Body)
			return string(h.data)
		} else {
			return ""
		}
	}
}

func (h *HttpResponse) Error() error {
	if h.err != nil {
		return h.err
	}
	if h.response.StatusCode >= http.StatusMultipleChoices || h.response.StatusCode < http.StatusOK {
		data, err := io.ReadAll(h.response.Body)
		if err != nil {
			stdlog.WithError(err).Warnf("Read body error.")
		}
		h.err = fmt.Errorf("http call error. status code is [%d], body is %s", h.response.StatusCode, string(data))
		return h.err
	} else {
		return nil
	}
}

// RoleRequest 角色控制
// 只配置空间角色，平台角色超管拥有所有权限，
type RoleRequest struct {
	Type     RoleRequestType `json:"type"`
	RoleName string          `json:"roleName"`
	Name     string          `json:"name"` // 权限展示名称
}

type RoleRequestType string

const (
	RoleRequestAssets  RoleRequestType = "assets"
	RoleRequestProject RoleRequestType = "project"
)

// FormColumn
// 返回前端需要表单展示的字段
// 前端需要将表单通过map[string]interface{} 的结构返回
// 该结构会结合golang template 生成审批流程对象名
type FormColumn struct {
	Name string `yaml:"name" json:"name"`
	Type string `yaml:"type" json:"type"`
}

// ExamineFlowInstance 流程实例
// 每开始一个新的流程，都会创建一个流程实例
type ExamineFlowInstance struct {
	gorm.Model
	OrderNumber     string                        `json:"orderNumber" gorm:"column:order_number;type:varchar(100);uniqueIndex"`
	Object          json.RawMessage               `json:"object" gorm:"column:object"`
	Module          string                        `json:"module" gorm:"column:module;type:varchar(100)"`
	Binding         *examine.ExamineBindingConfig `json:"binding" gorm:"column:binding;serializer:json"`
	Flow            *ExamineFlow                  `json:"flow" gorm:"column:flow;serializer:json"`
	CurrentIndex    int32                         `json:"currentIndex" gorm:"column:current_index"`
	Status          ExamineStatus                 `json:"status" gorm:"column:status;type:varchar(100)"`
	Detail          string                        `json:"detail" gorm:"column:detail"`
	Creator         string                        `json:"creator" gorm:"column:creator;type:varchar(100)"`
	HttpInfo        *examine.HttpInfo             `json:"httpInfos" gorm:"column:http_infos;serializer:json"`
	RejectHttpInfo  *examine.HttpInfo             `json:"rejectHttpInfos" gorm:"column:reject_http_infos;serializer:json"`
	ProjectId       string                        `json:"projectId" gorm:"column:project_id;type:varchar(100)"`
	ProjectName     string                        `json:"projectName" gorm:"project_name"` // 冗余存一下空间名称
	CallBackResults []*CallbackResult             `json:"callBackResults" gorm:"column:call_back_results;serializer:json"`
}

type CallbackResult struct {
	Message string         `json:"message"`
	Status  CallbackStatus `json:"status"`
}

// ExamineRecord 审批记录
// 每一个节点审批会新增一条记录
// 退回和通过都会新增
type ExamineRecord struct {
	gorm.Model
	InstanceID uint                 `json:"instanceId" gorm:"column:instance_id"`
	Index      int32                `json:"index" gorm:"column:index"`
	Result     ExamineResult        `json:"result" gorm:"column:result;type:varchar(100)"`
	Message    string               `json:"message" gorm:"column:message"`
	Creator    string               `json:"creator" gorm:"column:creator;type:varchar(100);index"`
	Instance   *ExamineFlowInstance `gorm:"foreignKey:InstanceID;references:ID"`
	ProjectId  string               `json:"projectId" gorm:"column:project_id;type:varchar(100)"`
}

//type ExamineToken struct {
//	gorm.Model
//	Token      string    `json:"token" gorm:"column:token;type:varchar(100);index"`
//	InstanceID uint      `json:"instanceId" gorm:"column:instance_id"`
//	ExpireTime time.Time `json:"expireTime" gorm:"column:expire_time"`
//	Used       int32     `json:"used" gorm:"column:used"`
//}

type ExamineResult string
type ExamineStatus string
type CallbackStatus string

const (
	Approve ExamineResult = "approve" // 通过
	Reject  ExamineResult = "reject"  // 退回
	Waiting ExamineResult = "waiting" // 待审核

	Draft      ExamineStatus = "draft"      // 代表流程还没有正式开始，仅仅是个草稿状态
	Processing ExamineStatus = "processing" // 代表流程已开始，进行中
	Finished   ExamineStatus = "finished"   // 代表流程正常结束
	Rejected   ExamineStatus = "rejected"   // 流程被拒绝，流 // 程失败
	Discarded  ExamineStatus = "discarded"  // 代表流程已废弃

	Failed  CallbackStatus = "failed"  // 回调失败
	Success CallbackStatus = "success" // 回调成功
)

// 请求结构体

type NodeReq struct {
	ID      uint   `json:"id"`
	Message string `json:"message"`
	User    string `json:"-"`
}

func (e *ExamineFlowInstance) ToDetail() *FlowInfo {
	// 不包含records和nodeinfo
	return &FlowInfo{
		ID:              e.ID,
		Flow:            e.Flow,
		OrderNumber:     e.OrderNumber,
		Object:          e.Object,
		Module:          e.Module,
		Type:            e.Binding.Type,
		CurrentIndex:    e.CurrentIndex,
		Status:          e.Status,
		Detail:          e.Detail,
		Creator:         e.Creator,
		CreatedAt:       e.CreatedAt,
		UpdatedAt:       e.UpdatedAt,
		ProjectId:       e.ProjectId,
		ProjectName:     e.ProjectName,
		CallbackResults: e.CallBackResults,
	}
}

// FlowInfo 返回给前端的flow详情
type FlowInfo struct {
	Flow            *ExamineFlow      `json:"flow"`
	ID              uint              `json:"id"`
	OrderNumber     string            `json:"orderNumber"`
	Object          json.RawMessage   `json:"object"`
	Module          string            `json:"module"`
	Type            string            `json:"type"`
	CurrentIndex    int32             `json:"currentIndex"`
	Status          ExamineStatus     `json:"status"`
	Detail          string            `json:"detail"`
	Permission      bool              `json:"permission"`
	Creator         string            `json:"creator"`
	CreatedAt       time.Time         `json:"createdAt"`
	UpdatedAt       time.Time         `json:"updatedAt"`
	Records         []*ExamineRecord  `json:"records"`
	NodeInfos       []*NodeInfo       `json:"nodeInfos"`
	ProjectId       string            `json:"projectId"`
	ProjectName     string            `json:"projectName"`
	CallbackResults []*CallbackResult `json:"callbackResults"`
}

type Count struct {
	Num         int         `json:"num"`
	Alert       *ReadStatus `json:"alert"`
	ExamineFlow *ReadStatus `json:"examineFlow"`
}

type ReadStatus struct {
	Read   int `json:"read"`
	UnRead int `json:"unRead"`
}

// NodeInfo 返回给前端的node详情
type NodeInfo struct {
	Index   int32            `json:"index"`
	Node    *ExamineNode     `json:"node"`
	Result  ExamineResult    `json:"result"`
	Records []*ExamineRecord `json:"records"`
}

type ListFlowReq struct {
	Types []ListType `json:"types"`
	User  string     `json:"user"`
}

type ListType string

const (
	ListTypeCreated   ListType = "created"
	ListTypeWaiting   ListType = "waiting"
	ListTypeProceeded ListType = "proceeded"
)
