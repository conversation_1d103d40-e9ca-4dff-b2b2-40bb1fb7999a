package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"testing"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	conf2 "transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao"
	_ "transwarp.io/applied-ai/central-auth-service/migrations"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/project"
	"transwarp.io/applied-ai/central-auth-service/service/rbac"
)

func TestConnectDB(t *testing.T) {
	dbConfig := conf.DBConfig{
		Type:   "mysql",
		SQLite: nil,
		MySQL: &conf2.MysqlConfig{
			Username:       "root",
			Password:       "123456",
			Host:           "localhost",
			Port:           "3306",
			DBName:         "central_auth_service",
			MaxIdle:        10,
			MaxConn:        50,
			NotPrintSql:    false,
			NotCreateTable: false,
		},
		Debug: true,
	}
	_, err := dao.ConnectDB(&dbConfig)
	if err != nil {
		println(err.Error())
	}
	assert.Nil(t, err)
}

func TestConnectDB2(t *testing.T) {
	dbConfig := conf.DBConfig{
		Type:   "mysql",
		SQLite: nil,
		MySQL: &conf2.MysqlConfig{
			Username:       "root",
			Password:       "123456",
			Host:           "localhost",
			Port:           "3306",
			DBName:         "central_auth_service",
			MaxIdle:        10,
			MaxConn:        50,
			NotPrintSql:    false,
			NotCreateTable: false,
		},
		Debug: true,
	}
	tx, err := dao.ConnectDB(&dbConfig)
	if err != nil {
		println(err.Error())
	}
	assert.Nil(t, err)

	var projects []*models.Project
	err = tx.Session(&gorm.Session{NewDB: true}).Model(models.Project{}).Where("tenant_uid", "").Find(&projects).Error
	assert.Nil(t, err)

	marshal, _ := json.Marshal(projects)
	fmt.Println(string(marshal))

}

func TestConnectDBSqlite(t *testing.T) {
	dbConfig := conf.DBConfig{
		Type: "sqlite",
		SQLite: &conf2.SqliteConfig{
			File:          "C:\\Users\\<USER>\\docker\\sqlite\\sqlite.db",
			BusyTimeoutMs: 0,
		},
		Debug: true,
	}
	_, err := dao.ConnectDB(&dbConfig)
	if err != nil {
		println(err.Error())
	}
	assert.Nil(t, err)
}

func TestQueryTree(t *testing.T) {
	dbConfig := conf.DBConfig{
		Type:   "mysql",
		SQLite: nil,
		MySQL: &conf2.MysqlConfig{
			Username:       "root",
			Password:       "123456",
			Host:           "localhost",
			Port:           "3306",
			DBName:         "central_auth_service",
			MaxIdle:        10,
			MaxConn:        50,
			NotPrintSql:    false,
			NotCreateTable: false,
		},
		Debug: true,
	}
	db, err := dao.ConnectDB(&dbConfig)
	if err != nil {
		println(err.Error())
	}
	assert.Nil(t, err)

	permissionService := rbac.NewPermissionService(db)
	permissions, err := permissionService.ListPermissions("project", "")
	assert.Nil(t, err)
	permissionJson, _ := json.Marshal(permissions)
	stdlog.Info(fmt.Sprintf("permissions:%s", permissionJson))
}

func TestProfile(t *testing.T) {
	dbConfig := conf.DBConfig{
		Type:   "mysql",
		SQLite: nil,
		MySQL: &conf2.MysqlConfig{
			Username:       "root",
			Password:       "123456",
			Host:           "localhost",
			Port:           "3306",
			DBName:         "central_auth_service",
			MaxIdle:        10,
			MaxConn:        50,
			NotPrintSql:    false,
			NotCreateTable: false,
		},
		Debug: true,
	}
	db, err := dao.ConnectDB(&dbConfig)
	if err != nil {
		println(err.Error())
	}
	assert.Nil(t, err)

	userService := rbac.NewUserService(db)

	profile, err := userService.GetUserProfile("thinger", "default", nil, stdsrv.LanguageChinese)
	assert.Nil(t, err)
	profileJson, _ := json.Marshal(profile)
	stdlog.Info(fmt.Sprintf("profile:%s", profileJson))
}

// 迁移207环境dev分支数据到新版
func TestMigrationData(t *testing.T) {
	dbConfig := conf.DBConfig{
		Type:   "mysql",
		SQLite: nil,
		MySQL: &conf2.MysqlConfig{
			Username:       "root",
			Password:       "Warp!CV@2022#",
			Host:           "**************",
			Port:           "32037",
			DBName:         "metastore_cas_rbac",
			MaxIdle:        10,
			MaxConn:        50,
			NotPrintSql:    false,
			NotCreateTable: false,
		},
		Debug: true,
	}
	db, err := dao.ConnectDB(&dbConfig)
	// 配置GORM日志模式
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // 使用默认的 logger，并设置为 Info 级别
		logger.Config{
			SlowThreshold: time.Second, // 慢查询阈值为1秒
			LogLevel:      logger.Info, // 打印所有的 SQL
			Colorful:      true,        // 开启彩色打印
		},
	)

	db.Config.Logger = newLogger
	if err != nil {
		println(err.Error())
	}
	assert.Nil(t, err)

	users := []*dao.User{}
	err = json.Unmarshal([]byte(Users), &users)
	assert.Nil(t, err)
	err = db.Session(&gorm.Session{NewDB: true}).Debug().CreateInBatches(users, 100).Error
	assert.Nil(t, err)

	projects := []*models.Project{}
	err = json.Unmarshal([]byte(Projects), &projects)
	assert.Nil(t, err)
	for _, project := range projects {
		project.CreateTime = time.Now()
		project.UpdateTime = time.Now()
	}
	err = db.Session(&gorm.Session{NewDB: true}).Debug().CreateInBatches(projects, 100).Error
	assert.Nil(t, err)

	members := []*models.ProjectMember{}
	err = json.Unmarshal([]byte(ProjectMembers), &members)
	assert.Nil(t, err)
	for _, member := range members {
		member.CreateTime = time.Now()
		member.UpdateTime = time.Now()
	}
	err = db.Session(&gorm.Session{NewDB: true}).Debug().CreateInBatches(members, 100).Error
	assert.Nil(t, err)
}

var (
	Projects       = "[{\"project_id\":\"cc0909b9-77aa-4f38-aaff-3a2d44854f5a\",\"name\":\"test_zx_rc1\",\"description\":\"123\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":\"futures\",\"resource_quota\":\"{\\\"limit_gpu\\\":\\\"\\\",\\\"limit_cpu\\\":\\\"\\\",\\\"limit_memory\\\":\\\"\\\",\\\"limit_disk\\\":\\\"\\\"}\",\"logo\":\"sfs:\\/\\/\\/temp\\/82dd9539-52f9-4e3e-b048-e74b0699ea0f_猫猫.jpeg\"},{\"project_id\":\"3947dca8-c8e1-4f9a-a5f8-23867bd60734\",\"name\":\"test1\",\"description\":\"您可以在本空间内，查看Sophon平台官方预置的训练数据\\/大语言模型\\/提示工程\\/应用链等默认数据，作为最佳实践案例。同时，您还可以通过克隆功能将内置数据应用到您自己的开发空间中。\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":\"1111\",\"resource_quota\":\"{\\\"limit_gpu\\\":\\\"4\\\",\\\"limit_cpu\\\":\\\"2\\\",\\\"limit_memory\\\":\\\"3\\\",\\\"limit_disk\\\":\\\"1\\\"}\",\"logo\":\"sfs:\\/\\/\\/temp\\/fca2d63a-00e5-4e6b-b916-9d75b29d8456_酷家乐装修网-金地都会艺境-副本-户型图.jpg\"},{\"project_id\":\"5de6f0b4-f9da-4a74-8b56-90c3cf62ed79\",\"name\":\"zy-QA验证\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"zys\",\"industry\":\"\",\"resource_quota\":\"{\\\"limit_gpu\\\":\\\"\\\",\\\"limit_cpu\\\":\\\"\\\",\\\"limit_memory\\\":\\\"\\\",\\\"limit_disk\\\":\\\"\\\"}\",\"logo\":\"\"},{\"project_id\":\"df1a3682-8cf5-4bbe-b605-f2d2a09baf05\",\"name\":\"zff_test\",\"description\":\"\",\"labels\":\"{\\\"场景\\\":[\\\"数据分析报告\\\"]}\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"cf162591-f66b-4e2c-a3cd-77832ad2cbde\",\"name\":\"应用链最佳实践DEMO\",\"description\":\"\",\"labels\":\"{\\\"场景\\\":[\\\"数据分析报告\\\"]}\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"57895905-6e7c-43ab-9e2c-3878be5a8c83\",\"name\":\"wz_test\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"9192b7a3-bcaf-4ada-add3-705edc559aa9\",\"name\":\"yx_test\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"c0dc246f-57a3-4dd9-b9c1-7cd032e8833a\",\"name\":\"rlhf-demo\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"677c426d-9e10-4898-a561-bd1809684b7f\",\"name\":\"ZSZQ空间测试-勿动\",\"description\":\"\",\"labels\":\"{\\\"维护人\\\":[\\\"lfx\\\"],\\\"空间\\\":[\\\"招商\\\"]}\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"b3be1808-6ef4-4696-8bb2-b72c9e9456f4\",\"name\":\"ll_test\",\"description\":\"\",\"labels\":\"{\\\"场景\\\":[\\\"test\\\"],\\\"维护人\\\":[\\\"nero\\\"],\\\"空间\\\":[\\\"model\\\"]}\",\"create_user\":\"nero\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"1a9e0da4-1ec0-4bdb-9dc8-a290dbe9db9e\",\"name\":\"多模态demo\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"c79688fe-edde-4b58-b4e7-c3f0c107b2cf\",\"name\":\"hfy-chatdoc\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"ebae709a-a1d5-4511-ab46-cb40142832bc\",\"name\":\"Solar\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"6d7d03fc-f37b-4461-a206-dae086f73c2e\",\"name\":\"多模态test\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"86071a32-59d1-4498-9052-b9ce1f9da638\",\"name\":\"llm1.2开发\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"45d37f6f-b54e-4c83-a505-ffe617308e08\",\"name\":\"tsr_test_可删\",\"description\":\"仅测试用，可按需删除\",\"labels\":\"{}\",\"create_user\":\"tsr\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"bc562766-db50-4fd9-9189-cb0d2cb2d9c3\",\"name\":\"tool-call-and-code-interpreter\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"98ac50c4-a368-4585-9676-e31615b342c2\",\"name\":\"sh_test\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"8f0f7193-de35-4421-8af6-1a451c843228\",\"name\":\"MOE模型\",\"description\":\"包含所有MOE模型，例如：DeepSeek_MOE 8*2B\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"f418fb4f-a1f5-46b9-b25b-18f297d23372\",\"name\":\"求索大模型官方空间\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"assets\",\"name\":\"资产市场\",\"description\":\"仅用于存储资产市场内的各类数据对象\",\"labels\":\"\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"98274574-b066-4669-a1b1-cd9343e29931\",\"name\":\"tks测试空间\",\"description\":\"tks对接LLMOPS测试空间\",\"labels\":\"{\\\"空间\\\":[\\\"tks\\\"]}\",\"create_user\":\"tks\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"e25dcc01-ba83-4b70-8a19-5a6e3f421caa\",\"name\":\"gupengtest\",\"description\":\"\",\"labels\":\"{}\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"65ac8a52-e65f-4aac-8fa1-d455d194b365\",\"name\":\"unstructured\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"55bdf5df-c3c0-462a-be8b-f90bdebd78b2\",\"name\":\"llava1.5_13B_v1_0319\",\"description\":\"第一轮自训练的llava1.5_13B在0319日的上架测试\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"7501bad4-b2e0-46f3-a2fc-db01e165f8d6\",\"name\":\"solar上架\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"a314e253-cea9-4a32-95b7-46d48de7e159\",\"name\":\"testqqq\",\"description\":\"\",\"labels\":\"{}\",\"create_user\":\"ggg\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"f1b64c73-79c1-4f7f-b362-b24b81a44658\",\"name\":\"HGG\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"a8c7d5a1-cfb2-4ad1-9707-46a8a50b74f9\",\"name\":\"深交所\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"5bc58f67-a5af-4ddf-897d-beee2fd51ad0\",\"name\":\"dj\",\"description\":\"测试\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"73cd1e26-59d0-4b44-a2ac-c542584a20cb\",\"name\":\"体验空间\",\"description\":\"可克隆复制快速开始语料，模型，应用链在此处修改创新构建自己需要的大模型应用\",\"labels\":\"null\",\"create_user\":\"tiyan0425\",\"industry\":null,\"resource_quota\":null,\"logo\":null},{\"project_id\":\"9b0991b0-6726-44a9-bc9e-9cce1506ed5d\",\"name\":\"华泰PoC\",\"description\":\"华泰演示poc空间\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":\"\",\"resource_quota\":\"{\\\"limit_gpu\\\":\\\"4\\\",\\\"limit_cpu\\\":\\\"2\\\",\\\"limit_memory\\\":\\\"3\\\",\\\"limit_disk\\\":\\\"1\\\"}\",\"logo\":\"sfs:\\/\\/\\/temp\\/8a4ba72e-9548-49f2-86b6-e9ba9bcf16f0_logo800_16491620782575300.png\"},{\"project_id\":\"asdasdasd\",\"name\":\"test-l\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":\"\",\"resource_quota\":\"{\\\"limit_gpu\\\":\\\"\\\",\\\"limit_cpu\\\":\\\"\\\",\\\"limit_memory\\\":\\\"\\\",\\\"limit_disk\\\":\\\"\\\"}\",\"logo\":\"\"},{\"project_id\":\"asdasdasd111234\",\"name\":\"test-lxy\",\"description\":\"\",\"labels\":\"{}\",\"create_user\":\"thinger\",\"industry\":\"\",\"resource_quota\":\"{\\\"limit_gpu\\\":\\\"\\\",\\\"limit_cpu\\\":\\\"\\\",\\\"limit_memory\\\":\\\"\\\",\\\"limit_disk\\\":\\\"\\\"}\",\"logo\":\"sfs:\\/\\/\\/temp\\/e64aa376-3330-4b5f-beb2-ad819bcaa591_signal.png\"},{\"project_id\":\"qwert\",\"name\":\"微调流程demo\",\"description\":\"上山打老虎\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":\"金融\",\"resource_quota\":\"{\\\"limit_gpu\\\":\\\"4\\\",\\\"limit_cpu\\\":\\\"2\\\",\\\"limit_memory\\\":\\\"3\\\",\\\"limit_disk\\\":\\\"4\\\"}\",\"logo\":\"\"},{\"project_id\":\"transwarp-it\",\"name\":\"IT内测空间-勿动\",\"description\":\"\",\"labels\":\"null\",\"create_user\":\"thinger\",\"industry\":\"\",\"resource_quota\":\"{\\\"limit_gpu\\\":\\\"\\\",\\\"limit_cpu\\\":\\\"\\\",\\\"limit_memory\\\":\\\"\\\",\\\"limit_disk\\\":\\\"\\\"}\",\"logo\":\"sfs:\\/\\/\\/temp\\/0094155c-eda6-4131-8189-ea8643c54d45_transwarp.png\"}]"
	ProjectMembers = "[{\"project_id\":\"5de6f0b4-f9da-4a74-8b56-90c3cf62ed79\",\"name\":\"zh01\",\"user_type\":\"user\",\"create_user\":\"zy3\"},{\"project_id\":\"c0dc246f-57a3-4dd9-b9c1-7cd032e8833a\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"677c426d-9e10-4898-a561-bd1809684b7f\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"b3be1808-6ef4-4696-8bb2-b72c9e9456f4\",\"name\":\"nero\",\"user_type\":\"user\",\"create_user\":\"nero\"},{\"project_id\":\"1a9e0da4-1ec0-4bdb-9dc8-a290dbe9db9e\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"c79688fe-edde-4b58-b4e7-c3f0c107b2cf\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"6d7d03fc-f37b-4461-a206-dae086f73c2e\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"86071a32-59d1-4498-9052-b9ce1f9da638\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"bc562766-db50-4fd9-9189-cb0d2cb2d9c3\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"98ac50c4-a368-4585-9676-e31615b342c2\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"f418fb4f-a1f5-46b9-b25b-18f297d23372\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"55bdf5df-c3c0-462a-be8b-f90bdebd78b2\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"8f0f7193-de35-4421-8af6-1a451c843228\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"8f0f7193-de35-4421-8af6-1a451c843228\",\"name\":\"kaka\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"3947dca8-c8e1-4f9a-a5f8-23867bd60734\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"3947dca8-c8e1-4f9a-a5f8-23867bd60734\",\"name\":\"kaka\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"45d37f6f-b54e-4c83-a505-ffe617308e08\",\"name\":\"tsr\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"45d37f6f-b54e-4c83-a505-ffe617308e08\",\"name\":\"kaka1\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"98274574-b066-4669-a1b1-cd9343e29931\",\"name\":\"tks\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"98274574-b066-4669-a1b1-cd9343e29931\",\"name\":\"kaka1\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"df1a3682-8cf5-4bbe-b605-f2d2a09baf05\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"df1a3682-8cf5-4bbe-b605-f2d2a09baf05\",\"name\":\"demo\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"df1a3682-8cf5-4bbe-b605-f2d2a09baf05\",\"name\":\"ZFF\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"df1a3682-8cf5-4bbe-b605-f2d2a09baf05\",\"name\":\"kaka1\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"cc0909b9-77aa-4f38-aaff-3a2d44854f5a\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"cc0909b9-77aa-4f38-aaff-3a2d44854f5a\",\"name\":\"demo\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"cc0909b9-77aa-4f38-aaff-3a2d44854f5a\",\"name\":\"zx\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"cc0909b9-77aa-4f38-aaff-3a2d44854f5a\",\"name\":\"kaka1\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"65ac8a52-e65f-4aac-8fa1-d455d194b365\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"65ac8a52-e65f-4aac-8fa1-d455d194b365\",\"name\":\"kaka1\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"57895905-6e7c-43ab-9e2c-3878be5a8c83\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"57895905-6e7c-43ab-9e2c-3878be5a8c83\",\"name\":\"kaka1\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"ebae709a-a1d5-4511-ab46-cb40142832bc\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"ebae709a-a1d5-4511-ab46-cb40142832bc\",\"name\":\"shuzhitiyan\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"ebae709a-a1d5-4511-ab46-cb40142832bc\",\"name\":\"kaka\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"ebae709a-a1d5-4511-ab46-cb40142832bc\",\"name\":\"kaka1\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"a314e253-cea9-4a32-95b7-46d48de7e159\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"a314e253-cea9-4a32-95b7-46d48de7e159\",\"name\":\"kaka\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"a314e253-cea9-4a32-95b7-46d48de7e159\",\"name\":\"kaka1\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"a314e253-cea9-4a32-95b7-46d48de7e159\",\"name\":\"ggg\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"9192b7a3-bcaf-4ada-add3-705edc559aa9\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"9192b7a3-bcaf-4ada-add3-705edc559aa9\",\"name\":\"kaka1\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"cf162591-f66b-4e2c-a3cd-77832ad2cbde\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"cf162591-f66b-4e2c-a3cd-77832ad2cbde\",\"name\":\"ZFF\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"cf162591-f66b-4e2c-a3cd-77832ad2cbde\",\"name\":\"kaka\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"e25dcc01-ba83-4b70-8a19-5a6e3f421caa\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"e25dcc01-ba83-4b70-8a19-5a6e3f421caa\",\"name\":\"kaka1\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"assets\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"assets\",\"name\":\"all_users\",\"user_type\":\"user_group\",\"create_user\":\"thinger\"},{\"project_id\":\"7501bad4-b2e0-46f3-a2fc-db01e165f8d6\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"f1b64c73-79c1-4f7f-b362-b24b81a44658\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"a8c7d5a1-cfb2-4ad1-9707-46a8a50b74f9\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"5bc58f67-a5af-4ddf-897d-beee2fd51ad0\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"73cd1e26-59d0-4b44-a2ac-c542584a20cb\",\"name\":\"tiyan0425\",\"user_type\":\"user\",\"create_user\":\"tiyan0425\"},{\"project_id\":\"asdasdasd\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"asdasdasd111234\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"default\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"default\",\"name\":\"demo\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"default\",\"name\":\"ZFF\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"default\",\"name\":\"tanshengru\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"default\",\"name\":\"tsr\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"default\",\"name\":\"tiyan0425\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"default\",\"name\":\"all_users\",\"user_type\":\"user_group\",\"create_user\":\"thinger\"},{\"project_id\":\"qwert\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"},{\"project_id\":\"9b0991b0-6726-44a9-bc9e-9cce1506ed5d\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"htzq\"},{\"project_id\":\"9b0991b0-6726-44a9-bc9e-9cce1506ed5d\",\"name\":\"htzq\",\"user_type\":\"user\",\"create_user\":\"htzq\"},{\"project_id\":\"9b0991b0-6726-44a9-bc9e-9cce1506ed5d\",\"name\":\"htzq-demo\",\"user_type\":\"user\",\"create_user\":\"htzq\"},{\"project_id\":\"transwarp-it\",\"name\":\"thinger\",\"user_type\":\"user\",\"create_user\":\"thinger\"}]"
	Users          = "[{\"deleted_at\":null,\"name\":\"wwy\",\"password\":\"123\",\"confirm_password\":\"123\",\"secret\":\"ca591df6783ea2d6df02ad0f9a96b5b7\",\"full_name\":\"wwy\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"zx\",\"password\":\"123\",\"confirm_password\":\"123\",\"secret\":\"5462dc796b9e039a5cde26bd15477f41\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"zh01\",\"password\":\"1\",\"confirm_password\":\"1\",\"secret\":\"64432add9d20e48392b2718a40112d79\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"ZFF\",\"password\":\"demo1234\",\"confirm_password\":\"demo1234\",\"secret\":\"7f3755570b4c7abc2b1e52bc472e6330\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"jw\",\"password\":\"1\",\"confirm_password\":\"1\",\"secret\":\"592afe7941f869524447b1ae810f9d50\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"zx_admin\",\"password\":\"123\",\"confirm_password\":\"123\",\"secret\":\"12ee5736d8e550a49810659d8f0a8420\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"s\",\"password\":\"1\",\"confirm_password\":\"1\",\"secret\":\"2a83e7c3d2c464536fe31325bd5c0f0c\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"zys\"},{\"deleted_at\":null,\"name\":\"普1\",\"password\":\"1\",\"confirm_password\":\"1\",\"secret\":\"6201bba0250c237b0590fd12b0aaa42b\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"zys\"},{\"deleted_at\":null,\"name\":\"nero\",\"password\":\"nero\",\"confirm_password\":\"nero\",\"secret\":\"6f09f44d33677ea3a179865208ae13b3\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"admin\",\"password\":\"admin\",\"confirm_password\":\"\",\"secret\":\"a3175a452c7a8fea80c62a198a40f6c9\",\"full_name\":\"\",\"email\":\"\",\"create_user\":\"\"},{\"deleted_at\":null,\"name\":\"yy\",\"password\":\"1\",\"confirm_password\":\"1\",\"secret\":\"639c71a663b0caff7a1fd95800577821\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"yx\",\"password\":\"123\",\"confirm_password\":\"123\",\"secret\":\"b02db9923efc38b2cae33638eb479920\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"tanshengru\",\"password\":\"123\",\"confirm_password\":\"123\",\"secret\":\"744e64ee588f572dc72868f617eda19c\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"shuzhitiyan\",\"password\":\"Shuzhi@tiyan\",\"confirm_password\":\"Shuzhi@tiyan\",\"secret\":\"64ae130f7986c2f8b222c7346f61092e\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"tsr\",\"password\":\"123\",\"confirm_password\":\"123\",\"secret\":\"278dd3d06c56b243268c34b5708ca68e\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"zg\",\"password\":\"zg\",\"confirm_password\":\"zg\",\"secret\":\"305c2f048339cc220a3aac76d72f1d9a\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"wz\",\"password\":\"123\",\"confirm_password\":\"123\",\"secret\":\"a85362b2b00d23a3e6863edba1c1abbb\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"tks\",\"password\":\"123456\",\"confirm_password\":\"123456\",\"secret\":\"e88cd271f9e237639e9861db24e7958a\",\"full_name\":\"tks\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"zyp\",\"password\":\"1\",\"confirm_password\":\"1\",\"secret\":\"4a450dbad9c7eef95375b330c94e7ce7\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"zyp2\",\"password\":\"1\",\"confirm_password\":\"1\",\"secret\":\"c378bda0b6fa8b3c2a2fa027d8054577\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"kaka\",\"password\":\"123456\",\"confirm_password\":\"123456\",\"secret\":\"24f49931e93be711b73204bd5a84b420\",\"full_name\":\"s\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"kaka1\",\"password\":\"123456\",\"confirm_password\":\"123456\",\"secret\":\"8957363c93fc755b415706035d622e51\",\"full_name\":\"ss\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"zys\",\"password\":\"123\",\"confirm_password\":\"123\",\"secret\":\"660e0e271839eb4d35a2e23e41828b16\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"ggg\",\"password\":\"123456\",\"confirm_password\":\"123456\",\"secret\":\"4ae92b6bde57a6edb97659853942dfce\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"lxy\",\"password\":\"1\",\"confirm_password\":\"1\",\"secret\":\"f4e6bd9cbf5181f8b67372f982413e71\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"liubin\",\"password\":\"123\",\"confirm_password\":\"123\",\"secret\":\"2959f74192a6f47f708159687dfe2462\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"fuxin.li\",\"password\":\"transwarp123\",\"confirm_password\":\"transwarp123\",\"secret\":\"bf159cca80be7fc05dd168e980aa0a40\",\"full_name\":\"李付鑫\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"hao\",\"password\":\"123123\",\"confirm_password\":\"123123\",\"secret\":\"73c6b95627cda42be38436f70a6385d0\",\"full_name\":\"zhaohao\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"tiyan0425\",\"password\":\"Tiyan@0425\",\"confirm_password\":\"Tiyan@0425\",\"secret\":\"14fc8732292a5bc788691a5b2483a6fa\",\"full_name\":\"体验\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"htzq\",\"password\":\"htzq@2024\",\"confirm_password\":\"htzq@2024\",\"secret\":\"284fe7085d71eeb82f5fa9218d626a15\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"},{\"deleted_at\":null,\"name\":\"htzq-demo\",\"password\":\"htzq@2024\",\"confirm_password\":\"htzq@2024\",\"secret\":\"e924018866a5e78afab96f7372ad5d7c\",\"full_name\":\"\",\"email\":\"<EMAIL>\",\"create_user\":\"thinger\"}]"
)

func TestQueryProject(t *testing.T) {
	dbConfig := conf.DBConfig{
		Type:   "mysql",
		SQLite: nil,
		MySQL: &conf2.MysqlConfig{
			Username:       "root",
			Password:       "Warp!CV@2022#",
			Host:           "**************",
			Port:           "32037",
			DBName:         "metastore_cas_rbac",
			MaxIdle:        10,
			MaxConn:        50,
			NotPrintSql:    false,
			NotCreateTable: false,
		},
		Debug: true,
	}
	db, err := dao.ConnectDB(&dbConfig)
	assert.Nil(t, err)
	projectService := project.NewProjectService(db)

	m := make(map[string]interface{})
	m["tenant_uid"] = "llmops-default-9989"
	list, err := projectService.QueryList(m)
	assert.Nil(t, err)

	marshal, err := json.Marshal(list)
	assert.Nil(t, err)
	fmt.Println(string(marshal))
}
