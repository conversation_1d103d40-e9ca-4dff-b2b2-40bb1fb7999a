# Central Authentication Service

Sophon 内部轻量 CAS（Central Authentication Service） 服务，提供组件间的统一用户管理、登录、认证等功能。

## 研发规程

> https://sophon-edge.yuque.com/ta73lw/eqpq5x/id5e4l

## CAS 实现参考

基于 [CAS Protocol 3.0 Specification](https://apereo.github.io/cas/6.4.x/protocol/CAS-Protocol-Specification.html) 实现了 CAS
认证服务。

## Docker Compose 配置示例

```
version: "2.3"

services:
  aip-cas:
    image: ***********/aip/central-auth-service:master
    container_name: aip-cas
    environment:
      US_AUTH.CAS.MODE: cas
      # US_AUTH.CAS.SERVER_URL: https://tw-node3228:8393/cas
      US_AUTH.CAS.EMBEDDED_SERVER_HOST: *************:27280
    volumes:
      - "$PWD/data/sqlite.db:/cas/data/sqlite.db"
    ports:
      - "27280:27280"
      - "27281:27281"
    # extra_hosts:
    #   - "tw-node3228:************"
    restart: on-failure

networks:
  default:
    external:
      name: vision-node
```