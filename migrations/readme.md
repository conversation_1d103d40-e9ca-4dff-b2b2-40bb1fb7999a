# 数据库迁移

**空间启动时自动根据版本执行 DDL 或者 DML SQL, 默认执行所有版本**

> 参考文档位于空间 vision-std：[database/migration/readme.md](https://gitlab.transwarp.io/applied-ai/aiot/vision-std/-/blob/b3fc49456612adaa4e110818e105467523bde11f/database/migration/readme.md)

## 权限与角色数据
> 权限数据文件: [permission.yaml](etc/permission.yaml)  
> 角色数据文件: [role.yaml](etc/role.yaml)

权限更新将 truncate table, 再新增所有权限  
角色更新将通过 id 覆盖旧数据, 删除需要通过 isdeleted 标识

修改以上两个文件后, 新增迁移版本号, 并将版本号绑定到迁移函数上

```go
// 新增前
const (
	v_aip103907 = 10014 + iota
	v_aip104750
	v_aip104559
	v_aip104937
	v_aip104991
	v_aip105730
	v_aip106393
	v_updatePermission135 // llm-1.3.5 新增权限: 空间积分
	v_updatePermission140 // llm-1.4.0 新增权限: 定制化配置, 修改权限 code: 服务管理,资源定价
	// 权限点与角色信息版本, 每次修改权限点与角色信息时递增, 递增时创建新版本号常量并修改[initPermissionsWithRoles]绑定的版本号
	permissionRoleVersion1
)
    .
    .
    .
	err = migration.RegisterUpdate(initPermissionsWithRoles, permissionRoleVersion1)
	if err != nil {
		panic(err)
	}
    .
    .
    .
```

```go
// 新增版本号, 并修改迁移函数绑定的版本号
const (
	v_aip103907 = 10014 + iota
	v_aip104750
	v_aip104559
	v_aip104937
	v_aip104991
	v_aip105730
	v_aip106393
	v_updatePermission135 // llm-1.3.5 新增权限: 空间积分
	v_updatePermission140 // llm-1.4.0 新增权限: 定制化配置, 修改权限 code: 服务管理,资源定价
	// 权限点与角色信息版本, 每次修改权限点与角色信息时递增, 递增时创建新版本号常量并修改[initPermissionsWithRoles]绑定的版本号
	permissionRoleVersion1
    v_otherVersion // 其他修改迁移
    v_otherVersion1
    permissionRoleVersion2 // 修改了权限
)
    .
    .
    .
	err = migration.RegisterUpdate(initPermissionsWithRoles, permissionRoleVersion2)
	if err != nil {
		panic(err)
	}
    .
    .
    .
```