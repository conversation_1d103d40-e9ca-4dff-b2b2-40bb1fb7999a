package migrations

import (
	"gorm.io/gorm"
	"time"
	"transwarp.io/applied-ai/aiot/vision-std/database/migration"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func init() {
	err := migration.RegisterUpdate(deleteAdmin, 10011)
	if err != nil {
		panic(err)
	}
	err = migration.RegisterUpdate(initDefaultProject, 10012)
	if err != nil {
		panic(err)
	}
}

func deleteAdmin(tx *gorm.DB) error {
	err := clone(tx).Where("name = ?", "admin").Delete(&dao.User{}).Error
	if err != nil {
		return err
	}
	err = clone(tx).Where("name = ?", "admin").Delete(&models.UserRole{}).Error
	if err != nil {
		return err
	}
	err = clone(tx).Where("username = ?", "admin").Delete(&models.UserGroup{}).Error
	if err != nil {
		return err
	}
	err = clone(tx).Where("name = ? and user_type = ?", "admin", "user").Delete(&models.ProjectMember{}).Error
	if err != nil {
		return err
	}

	err = createMember(tx, "default", "thinger", helper.ProjectOwnerID)
	if err != nil {
		return err
	}
	err = createMember(tx, "assets", "thinger", helper.ProjectOwnerID)
	if err != nil {
		return err
	}
	return nil
}

func createMember(db *gorm.DB, projectId string, username string, roleId helper.RoleID) error {
	cnt := int64(0)
	err := clone(db).Model(&models.ProjectMember{}).Where("project_id = ? and name = ?", projectId, username).Count(&cnt).Error
	if err != nil {
		return err
	}
	if cnt > 0 {
		return nil
	}
	err = clone(db).Save(&models.ProjectMember{
		ProjectId:  projectId,
		Name:       username,
		UserType:   models.UserType,
		CreateUser: "thinger",
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}).Error
	if err != nil {
		return err
	}
	err = clone(db).Save(&models.UserRole{
		UserRoleReq: models.UserRoleReq{
			ProjectId: projectId,
			RoleId:    uint64(roleId),
			Name:      username,
			BindType:  models.UserType,
		},
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}).Error
	if err != nil {
		return err
	}
	return nil
}

func initDefaultProject(db *gorm.DB) error {
	err := clone(db).Model(&dao.User{}).Where("default_project is null or default_project = ''").
		Update("default_project", "default").Error
	if err != nil {
		return err
	}

	err = clone(db).Model(&dao.User{}).Where("name in ?", []string{"thinger", "demo"}).
		Update("default_project", "assets").Error
	if err != nil {
		return err
	}
	return nil
}

func clone(db *gorm.DB) *gorm.DB {
	return db.Session(&gorm.Session{NewDB: true}).Debug()
}
