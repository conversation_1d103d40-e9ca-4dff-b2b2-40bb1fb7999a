package migrations

import (
	"fmt"

	"gorm.io/gorm"
	"transwarp.io/applied-ai/aiot/vision-std/database/migration"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/dao/rbac"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func init() {
	// fk_examine_record_instance
	// fk_rbac_object_rbac_policies
	// fk_user_roles_role
	// fk_user_roles_user
	exr := &models.ExamineRecord{}
	poc := &rbac.RbacPolicy{}
	ur := &dao.UserRole{}
	err := migration.RegisterUpdate(func(tx *gorm.DB) error {
		if tx.Migrator().HasConstraint(exr, "fk_examine_record_instance") {
			err := tx.Migrator().DropConstraint(exr, "fk_examine_record_instance")
			if err != nil {
				return fmt.Errorf("drop fk_examine_record_instance: %w", err)
			}
		}
		if tx.Migrator().HasConstraint(poc, "fk_rbac_object_rbac_policies") {
			err := tx.Migrator().DropConstraint(poc, "fk_rbac_object_rbac_policies")
			if err != nil {
				return fmt.Errorf("drop fk_rbac_object_rbac_policies: %w", err)
			}
		}
		if tx.Migrator().HasConstraint(ur, "fk_user_roles_role") {
			err := tx.Migrator().DropConstraint(ur, "fk_user_roles_role")
			if err != nil {
				return fmt.Errorf("drop fk_user_roles_role: %w", err)
			}
		}
		if tx.Migrator().HasConstraint(ur, "fk_user_roles_user") {
			err := tx.Migrator().DropConstraint(ur, "fk_user_roles_user")
			if err != nil {
				return fmt.Errorf("drop fk_user_roles_user: %w", err)
			}
		}
		return nil
	}, delFK)
	if err != nil {
		panic(err)
	}
}
