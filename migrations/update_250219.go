package migrations

import (
	"gorm.io/gorm"

	"transwarp.io/applied-ai/aiot/vision-std/database/migration"
	"transwarp.io/applied-ai/central-auth-service/dao"
)

func init() {
	type User struct {
		dao.User
		Password        string `gorm:"type:varchar(500);column:password;"`
		ConfirmPassword string `gorm:"type:varchar(500);column:confirm_password;"`
	}
	user := &User{}
	err := migration.RegisterUpdate(func(tx *gorm.DB) error {
		if tx.Migrator().HasColumn(user, "Password") {
			err := tx.Migrator().DropColumn(user, "Password")
			if err != nil {
				return err
			}
		}
		if tx.Migrator().HasColumn(user, "ConfirmPassword") {
			err := tx.Migrator().DropColumn(user, "ConfirmPassword")
			if err != nil {
				return err
			}
		}
		return nil
	}, delPassword)
	if err != nil {
		panic(err)
	}
}
