package migrations

import (
	"context"
	"errors"
	"fmt"
	"os"
	"slices"
	"time"

	"gopkg.in/yaml.v3"

	"transwarp.io/aip/llmops-common/pkg/client"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/helper"

	"gorm.io/gorm"

	"transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/database/migration"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/dao/project"
	"transwarp.io/applied-ai/central-auth-service/dao/rbac"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/tenant"
	customtypes "transwarp.io/applied-ai/central-auth-service/service/tenant/types"
)

// iota 递增常量, 不要在常量之间插入新常量, 应在末尾追加
const (
	v_aip103907 = 10014 + iota
	v_aip104750
	v_aip104559
	v_aip104937
	v_aip104991
	v_aip105730
	v_aip106393
	v_updatePermission135 // llm-1.3.5 新增权限: 空间积分
	v_updatePermission140 // llm-1.4.0 新增权限: 定制化配置, 修改权限 code: 服务管理,资源定价
	v_updateNotification  // 消息通知
	// 权限点与角色信息版本, 每次修改权限点与角色信息时递增, 递增时创建新版本号常量并修改[initPermissionsWithRoles]绑定的版本号
	permissionRoleVersion1
	permissionRoleVersion2  // 添加我的消息权限
	delPassword             // 删除用户表[dao.User]中的密码字段
	permissionRoleVersion3  // 删除语料评测权限点
	permissionRoleVersion4  // 修复权限点与配置表不一致问题
	permissionRoleVersion5  // 删除应用评估, 增加模型量化, 调整权限顺序
	permissionRoleVersion6  // 语料标注权限调整
	permissionRoleVersion7  // 增加静态资产
	permissionRoleVersion8  // 删除用户管理的 * action
	permissionRoleVersion9  // 知识库管理权限调整
	permissionRoleVersion10 // 恢复用户管理的 * action
	permissionRoleVersion11 // 文件编目改为资产管理
	permissionRoleVersion12 // 分离用户和用户组权限点
	permissionRoleVersion13 // 用户/用户组权限 code 补 .*
	delFK                   // 删除外键
	permissionRoleVersion14 // 权限点英文与前端对应
	permissionRoleVersion15 // 增加 算力设施, 基础服务
)

func init() {
	if conf.IsDevMode() {
		stdlog.Warnf("!!!! skipping deps (mysql) initiation in development mode")
		return
	}

	err := migration.RegisterUpdate(initData, 10002)
	if err != nil {
		panic(err)
	}
	err = migration.RegisterUpdate(initTenant, 10010)
	if err != nil {
		panic(err)
	}
	err = migration.RegisterUpdate(initPermissionsWithRoles, permissionRoleVersion15)
	if err != nil {
		panic(err)
	}

	err = initDeviceTypes()
	if err != nil {
		panic(err)
	}
}

func initData(tx *gorm.DB) error {
	predefinedUsers := []*dao.User{
		{
			Name: "thinger",
			Roles: []*dao.Role{
				{Name: auth.UserRoleSophonAdmin},
				{Name: auth.UserRoleAdmin},
				{Name: auth.UserRoleCVAdmin},
			},
		},
		{
			Name: "demo",
			Roles: []*dao.Role{
				{Name: auth.UserRoleSophonBasic},
				{Name: auth.UserRoleGuest},
				{Name: auth.UserRoleCVBasic},
			},
		},
	}

	for _, predefinedUser := range predefinedUsers {
		if err := dao.CreateOrUpdateUser(tx, predefinedUser, "transwarp123"); err != nil {
			stdlog.WithError(err).Errorf("failed to create a pre-defined user: %+v", predefinedUser)
			return err
		}
	}
	err := initPermissions(tx)
	if err != nil {
		return err
	}
	err = initRoles(tx)
	if err != nil {
		return err
	}
	err = rbac.InitGroups(tx)
	if err != nil {
		return err
	}
	err = project.InitProjectCategories(tx)
	if err != nil {
		return err
	}
	var (
		now      = time.Now()
		initdata = conf.C.InitData
		projs    = make([]*models.Project, 0, 2)
	)
	// 使用配置的数据完成初始化
	if initdata.IsCustom() {
		for _, p := range initdata.Tenants {
			proj := &models.Project{
				ProjectId:  p.ProjectID,
				Name:       p.ProjectID,
				Industry:   "交易所",
				CreateUser: "thinger",
				CreateTime: now,
				UpdateTime: now,
			}
			err = project.CreateProject(tx, proj)
			if err != nil {
				return fmt.Errorf("create project %s error: %w", p.ProjectID, err)
			}
			projs = append(projs, proj)
		}
	} else {
		// 创建内置空间
		projs = []*models.Project{
			{
				Name:        "快速开始_演示demo",
				ProjectId:   "default",
				Description: "您可以在本空间内，查看Sophon平台官方预置的训练数据/大语言模型/提示工程/应用链等默认数据，作为最佳实践案例。同时，您还可以通过克隆功能将内置数据应用到您自己的开发空间中。",
				CreateUser:  "thinger",
				Industry:    "交易所",
				CreateTime:  now,
				UpdateTime:  now,
			}, {
				Name:        "公共空间",
				ProjectId:   "assets",
				Description: "仅用于存储和展示公共空间内的各类数据资产，由超级管理员维护",
				CreateUser:  "thinger",
				Industry:    "交易所",
				CreateTime:  now,
				UpdateTime:  now,
			},
		}
		if err = tx.Create(&projs).Error; err != nil {
			return fmt.Errorf("failed to create 快速开始_演示demo, 公共空间 project: %w", err)
		}
	}
	// 绑定内置用户与空间
	{
		// 将 thinger 用户设置为超级管理员用户
		if err = rbac.CreateUserRole(tx, &models.UserRole{
			UserRoleReq: models.UserRoleReq{
				Name:     "thinger",
				RoleId:   uint64(helper.SuperAdminUserID),
				BindType: models.UserType,
			},
			CreateTime: now,
			UpdateTime: now,
		}); err != nil {
			return fmt.Errorf("failed to create thinger user superAdmin role: %w", err)
		}

		// 将 demo 用户设置为普通用户
		if err = rbac.CreateUserRole(tx, &models.UserRole{
			UserRoleReq: models.UserRoleReq{
				Name:     "demo",
				RoleId:   uint64(helper.GeneraUserID),
				BindType: models.UserType,
			},
			CreateTime: now,
			UpdateTime: now,
		}); err != nil {
			return fmt.Errorf("failed to create demo user generalUser role: %w", err)
		}

		// 将 all_users 用户组绑定普通用户的平台角色
		if err = rbac.CreateUserRole(tx, &models.UserRole{
			UserRoleReq: models.UserRoleReq{
				Name:     models.AllUsers.String(),
				RoleId:   uint64(helper.GeneraUserID),
				BindType: models.UserGroupType,
			},
			CreateTime: now,
			UpdateTime: now,
		}); err != nil {
			return fmt.Errorf("failed to create all_users platform role: %w", err)
		}
		// all_users 用户组绑定为空间成员, 设置空间角色
		for _, proj := range projs {
			// 添加为空间成员
			if err = project.CreateProjectMember(tx, &models.ProjectMember{
				Name:       models.AllUsers.String(),
				ProjectId:  proj.ProjectId,
				UserType:   models.UserGroupType,
				CreateUser: "thinger",
				CreateTime: now,
				UpdateTime: now,
			}); err != nil {
				return fmt.Errorf("failed to create all_users project member: %s: %w", proj.ProjectId, err)
			}

			// 绑定为内置数据共享的空间角色 如果是内置数据 default 空间则绑定为访客
			deRoleId := uint64(helper.DataShareUserID)
			if proj.ProjectId == "default" && len(initdata.Tenants) > 0 {
				deRoleId = uint64(helper.CustomUserID)
			}
			if err = rbac.CreateUserRole(tx, &models.UserRole{
				UserRoleReq: models.UserRoleReq{
					Name:      models.AllUsers.String(),
					RoleId:    deRoleId,
					BindType:  models.UserGroupType,
					ProjectId: "default",
				},
				CreateTime: now,
				UpdateTime: now,
			}); err != nil {
				return fmt.Errorf("failed to create all_users project role: %s: %w", proj.ProjectId, err)
			}
		}
	}
	return nil
}

func initTenant(tx *gorm.DB) error {
	projs := make([]*models.Project, 0, 10)
	err := tx.Session(&gorm.Session{NewDB: true}).Model(models.Project{}).Where("tenant_uid = '' or tenant_uid is null").Find(&projs).Error
	if err != nil {
		return fmt.Errorf("find projects with empty tenant_uid: %w", err)
	}
	ptMap := make(map[string]string, len(conf.C.InitData.Tenants))
	if conf.C.InitData.IsCustom() {
		for _, t := range conf.C.InitData.Tenants {
			ptMap[t.ProjectID] = t.TenantUID
		}
	}

	tenSvc := tenant.NewTenantService(tx.Session(&gorm.Session{NewDB: true}))
	defQuota := tenSvc.GetDefaultResourceQuota()
	labels := map[string]string{
		customtypes.NamespaceLableNsType: string(customtypes.SystemNs),
	}
	for _, proj := range projs {
		if conf.C.InitData.IsCustom() {
			proj.TenantUid = ptMap[proj.ProjectId]
		} else {
			proj.TenantUid = proj.ProjectId
		}
		// 允许 expense http 调用失败
		created, err := tenSvc.CreateTenant(helper.SetToken(context.Background(),
			client.Encode(&client.TokenInfo{UserName: "thinger"})),
			&models.Tenant{
				TenantName:   proj.Name,
				TenantUid:    proj.TenantUid,
				TenantLogo:   proj.Logo,
				TenantQuotas: *defQuota,
				Creator:      proj.CreateUser,
				CreateTime:   uint64(time.Now().Unix()),
			}, labels, true)
		if err != nil {
			return fmt.Errorf("create tenant %s: %s: %w", proj.Name, proj.TenantUid, err)
		}
		err = tx.Session(&gorm.Session{NewDB: true}).Model(proj).Update("TenantUid", created.TenantUid).Error
		if err != nil {
			return fmt.Errorf("update project TenantUid %s: %w", proj.Name, err)
		}
	}
	return nil
}

// initPermissionsWithRoles 同时初始化或更新权限与角色
func initPermissionsWithRoles(tx *gorm.DB) error {
	err := initPermissions(tx)
	if err != nil {
		return fmt.Errorf("initPermissions: %w", err)
	}
	err = initRoles(tx)
	if err != nil {
		return fmt.Errorf("initRoles: %w", err)
	}
	return nil
}

type permission struct {
	*models.Permission `yaml:",inline"`
	Actions            []string `yaml:"actions,omitempty"`
}

func newPermission(name, action string, nameLocals map[string]string) permission {
	return permission{
		Permission: &models.Permission{
			Name:       name,
			Action:     action,
			NameLocals: nameLocals,
		},
	}
}

var actMap = map[string]permission{
	"*":                 newPermission("全部", "*", map[string]string{"en": "All"}),
	"read":              newPermission("查看", "read", map[string]string{"en": "View"}),
	"create":            newPermission("新建空间", "create", map[string]string{"en": "Create Project"}),
	"annotation-manage": newPermission("标注管理", "annotation-manage", map[string]string{"en": "Annotation Management"}),
	"examine":           newPermission("审核", "examine", map[string]string{"en": "Examine"}),
	"annotation":        newPermission("标注", "annotation", map[string]string{"en": "Annotation"}),
	"knowledge-manage":  newPermission("知识库管理", "knowledge-manage", map[string]string{"en": "Knowledge Manage"}),
	"process-examine":   newPermission("过程审核", "process-examine", map[string]string{"en": "Process Examine"}),
}

// initPermissions 初始化或更新权限点 permission.yaml
func initPermissions(tx *gorm.DB) error {
	file, err := os.ReadFile("./etc/permission.yaml")
	if err != nil {
		return fmt.Errorf("read permission.yaml: %w", err)
	}
	ps := make([]*permission, 0)
	err = yaml.Unmarshal(file, &ps)
	if err != nil {
		return fmt.Errorf("unmarshal permissions: %w", err)
	}
	perms := make([]*models.Permission, 0, len(ps))
	for _, p := range ps {
		perms = append(perms, p.Permission)
		for _, a := range p.Actions {
			m, ok := actMap[a]
			if !ok {
				panic(fmt.Errorf("act map not found: %v", a))
			}
			perms = append(perms, &models.Permission{
				Code:        p.Code,
				Name:        m.Name,
				NameLocals:  m.NameLocals,
				Action:      a,
				Type:        p.Type,
				Description: p.Description,
				DescLocals:  m.DescLocals,
				Parent:      p.Code,
			})
		}
	}
	err = tx.Exec(fmt.Sprintf("TRUNCATE TABLE %s", models.Permission{}.TableName())).Error
	if err != nil {
		return fmt.Errorf("truncate permission table: %w", err)
	}
	err = tx.Create(perms).Error
	if err != nil {
		return fmt.Errorf("create or update permission into db: %w", err)
	}
	return nil
}

// initRoles 初始化角色 role.yaml
// 对 空间管理 权限有特殊处理(从配置中获取勾选数据, 每次 init 时使用)
func initRoles(tx *gorm.DB) error {
	file, err := os.ReadFile("./etc/role.yaml")
	if err != nil {
		return fmt.Errorf("read role: %w", err)
	}
	roles := make([]*RoleWithDelete, 0)
	err = yaml.Unmarshal(file, &roles)
	if err != nil {
		return fmt.Errorf("unmarshal roles: %w", err)
	}

	for _, role := range roles {
		r := *role
		if r.IsDeleted {
			err = tx.Delete(&r.Role).Error
			if err != nil {
				return fmt.Errorf("delete role: %d: %w", r.Id, err)
			}
			err = rbac.DeleteRolePermissionsByRoleId(tx, r.Id)
			if err != nil {
				return fmt.Errorf("delete role permissions: %d: %w", r.Id, err)
			}
		} else {
			if err = tx.Assign(r.Role).FirstOrCreate(&role.Role).Error; err != nil {
				return fmt.Errorf("update role: %d: %w", r.Id, err)
			}
		}
	}
	roles = slices.DeleteFunc(roles, func(r *RoleWithDelete) bool {
		return r.IsDeleted
	})
	roles = slices.Clip(roles)
	// 绑定权限和角色
	_, _, codeActionMap, err := rbac.AllPermissionMap(tx)
	if err != nil {
		return fmt.Errorf("get all permission map: %w", err)
	}
	// 管理员的空间管理权限要在数据库中持久化, 防止更新权限时丢失
	// 从 db 中获取管理员对空间管理的权限勾选状态
	proActs, err := dao.GetProjectPermission(dao.GetCustomConfigService())
	if err != nil {
		return fmt.Errorf("get project permission actions: %w", err)
	}
	stdlog.Infof("permission action config: %v", proActs)
	if len(proActs) > 0 {
		idx := slices.IndexFunc(roles, func(role *RoleWithDelete) bool { return role.Id == uint64(helper.AdminUserID) })
		if idx < 0 {
			return errors.New("get admin role: not found")
		}
		for _, pa := range proActs {
			p := fmt.Sprintf("project.manage.*:%s", pa)
			if !slices.Contains(roles[idx].Permissions, p) {
				roles[idx].Permissions = append(roles[idx].Permissions, p)
			}
		}
	}

	for _, role := range roles {
		// 保存角色的所有权限点
		var perms []models.Permission
		for _, codeAction := range role.Permissions {
			if p, ok := codeActionMap[codeAction]; ok {
				// 对管理员的空间管理特殊处理
				if role.Id == uint64(helper.AdminUserID) &&
					p.Code == "project.manage.*" &&
					!slices.Contains(proActs, p.Action) {
					stdlog.Warnf("permission action not found for project create: %s %s", p.Code, p.Action)
					continue
				}
				perms = append(perms, *p)
			}
		}
		err = rbac.SavePermissionsByRoleID(tx, role.Id, &perms)
		if err != nil {
			return fmt.Errorf("save role permissions: %w", err)
		}
	}
	return nil
}

type RoleWithDelete struct {
	models.Role `yaml:",inline"`
	IsDeleted   bool // true: 删除此角色
}

func initDeviceTypes() error {
	file, err := os.ReadFile("./etc/device_types.yaml")
	if err != nil {
		return fmt.Errorf("read device_types.yaml: %w", err)
	}
	perms := make([]*models.DeviceType, 0)
	err = yaml.Unmarshal(file, &perms)
	if err != nil {
		return fmt.Errorf("unmarshal permissions: %w", err)
	}
	if models.DeviceTypeMap == nil {
		models.DeviceTypeMap = make(map[string]models.DeviceType)
	}
	for _, p := range perms {
		models.DeviceTypeMap[p.DeviceTypeName] = *p
	}

	return nil
}
