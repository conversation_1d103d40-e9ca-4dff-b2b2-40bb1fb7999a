PACKAGE := $(shell go list)
COMPONENT := $(notdir $(PACKAGE))
PKG_LIST := $(shell go list ./... | grep -v /vendor/)
GO_FILES := $(shell find . -name '*.go' | grep -v /vendor/ | grep -v _test.go)
BUILD_DATE := $(shell date "+%Y%m%d")
BUILD_TIME := $(shell date "+%Y%m%dT%H%M%S")
BUILD_PATH ?= build
DIST_PATH ?= dist
MAIN_FILE ?= ./main.go
CI_COMMIT_REF_NAME ?= ${USER}
CI_COMMIT_SHA ?= 00000000
CI_COMMIT_TAG ?= 0.0.0
COMMIT_ID := $(shell echo ${CI_COMMIT_SHA} | cut -c-8)
DOCKER_REPO_URL ?= ***********
BUILD_IMAGE_PREFIX ?= ${DOCKER_REPO_URL}/postcommit
BUILD_VERSION := $(subst branch-,,${CI_COMMIT_REF_NAME})-${BUILD_DATE}-${COMMIT_ID}
RELEASE_IMAGE_PREFIX ?= ${DOCKER_REPO_URL}/transwarp
RELEASE_VERSION := ${CI_COMMIT_TAG}
DOCKER_LABELS := --label build_name=${PACKAGE} --label build_version=${BUILD_VERSION} --label build_time=${BUILD_TIME} --label commit_id=${CI_COMMIT_SHA}
SHELL=/bin/bash
## Build go executable, this stage will execute on all branch
# $(1): GOARCH
define build
	LDFLAG="-X ${PACKAGE}/version.BuildName=${PACKAGE} -X ${PACKAGE}/version.BuildVersion=${BUILD_VERSION} -X ${PACKAGE}/version.BuildTime=${BUILD_TIME} -X ${PACKAGE}/version.CommitID=${CI_COMMIT_SHA}"; \
	CGO_ENABLED=0 GOOS=linux GOARCH=$(1) go build -ldflags "$${LDFLAG}" -o ${DIST_PATH}/${COMPONENT} ${MAIN_FILE} && \
	([ ! -e etc ] || cp -rf etc ${DIST_PATH}/) &&  \
	([ ! -e public ] || cp -rf public ${DIST_PATH}/) && \
	([ ! -e bin ] || cp -rf bin ${DIST_PATH}/);
endef

## Build and push docker image with latest tag, this stage will execute on [master] and [/^branch-\d\.\d$/] branch
 # this is only for thinger-gateway
 # $(1): ARCH
 # $(2): DOCKER_FILE
 # will build images eg: ***********/postcommit/thinger-gateway:arm64-2.4-20191202-00000000
 # NOTE: This cmd will create a 'build' folder to ignore unrelated files, or else when executing `docker build ..`, the '..' in the command will send too many files to docker context
define build2
	@mkdir -p $(DIST_PATH);
	set -x ; \
	IMAGE=${COMPONENT}:$(1)-build ; \
	rm -rf ../build && \
	mkdir ../build && \
	cp -rf ../$(COMPONENT)/ ../build/ && \
	cp -rf ../vision-std/ ../build/ && \
	cd ../build/${COMPONENT} && \
	docker run --rm --privileged ***********/aip/base/multiarch-qemu-user-static:sophon-0.0 --reset -p yes && \
	docker build -f $(2) ${DOCKER_LABELS} \
		--build-arg PACKAGE=${PACKAGE} \
		--build-arg BUILD_VERSION=${BUILD_VERSION} \
		--build-arg BUILD_TIME=${BUILD_TIME} \
		--build-arg CI_COMMIT_SHA=${CI_COMMIT_SHA} \
		-t $${IMAGE} .. && \
	cd - && rm -rf ../build && \
	(!([ `uname -i` = "x86_64" -a $(1) = "amd64" ] || [ `uname -i` = "aarch64" -a $(1) = "arm64" ]) || ([ ${COMPONENT} != "thinger-gateway" ] || (docker run --rm $${IMAGE} cat /lib/liblicense.so > ${DIST_PATH}/liblicense.so && chmod 755 ${DIST_PATH}/liblicense.so))) && \
	(!([ `uname -i` = "x86_64" -a $(1) = "amd64" ] || [ `uname -i` = "aarch64" -a $(1) = "arm64" ]) || (docker run --rm $${IMAGE} cat ${COMPONENT} > ${DIST_PATH}/${COMPONENT} && chmod 755 ${DIST_PATH}/${COMPONENT})) && \
	([ ! -e etc ] || cp -rf etc ${DIST_PATH}/) && \
	([ ! -e public ] || cp -rf public ${DIST_PATH}/) && \
	([ ! -e bin ] || cp -rf bin ${DIST_PATH}/)
endef

 ## Build and push docker image with latest tag, this stage will execute on [master] and [/^branch-\d\.\d$/] branch
 # $(1): IMAGE_BASE
 # $(2): ARCH
 # $(3): VERSION
 # $(4): DOCKER_FILE
 # $(5): NO_PUSH
 # will build images eg: ***********/postcommit/thinger-faas:arm64-2.4-20191202-00000000
 #                     : ***********/postcommit/thinger-faas:arm64-2.4-latest
 #                     : ***********/aip-arm/thinger-faas:sophon-2.4
define image
	set -x ; \
	IMAGE=$(1):$(2)-$(3) ; \
	IMAGE_LATEST=$(1):$(2)-$${CI_COMMIT_REF_NAME/branch-/}-latest ; \
	IMAGE_BASE=$(1) ; \
	REPO_SOPHON=aip ; \
	[ "$(2)" = "arm64" ] && REPO_SOPHON=aip-arm ; \
	IMAGE_SOPHON=$${IMAGE_BASE/postcommit/$${REPO_SOPHON}}:$${CI_COMMIT_REF_NAME/branch-/sophon-} ; \
	cp $(4) ${DIST_PATH}/Dockerfile && \
	docker build ${DOCKER_LABELS} -t $${IMAGE} -t $${IMAGE_LATEST} -t $${IMAGE_SOPHON} ${DIST_PATH} && \
	export DOCKER_OPTS="--insecure-registry ${DOCKER_REPO_URL}" && \
	([ $(5) ] || (docker push $${IMAGE} && docker push $${IMAGE_LATEST} && docker push $${IMAGE_SOPHON}))
endef

 ## Build and push docker image with release tag, this stage will only execute on git tags
 # $(1): IMAGE_BASE
 # $(2): ARCH
 # $(3): VERSION
 # $(4): DOCKER_FILE
 # $(5): NO_PUSH
 # will build images eg : ***********/transwarp/thinger-faas:sophon-2.4.0-rc2
 #                      : ***********/transwarp/arm64-thinger-faas:sophon-2.4.0-rc2
 #                      : ***********/aip/thinger-faas:sophon-2.4.0-rc2
 #                      : ***********/aip/arm64-thinger-faas:sophon-2.4.0-rc2
 #                      每月会基于当前大版本分支出一个小版本，比如基于branch-2.5出20.05的版本
 #                      : ***********/aip/thinger-faas:20.05
 #                      : ***********/aip/arm64-thinger-faas:20.05
define image-release
	set -x ; \
	IMAGE_RELEASE=$(1):$${CI_COMMIT_REF_NAME} ; \
	IMAGE_BASE=$(1) ; \
	IMAGE_SOPHON=$${IMAGE_BASE/transwarp/aip}:$${CI_COMMIT_REF_NAME} ; \
	IMAGE_SOPHON_MINOR=$${IMAGE_BASE/transwarp/aip}:$${CI_COMMIT_REF_NAME/sophon-/} ; \
	sed -i "4s/cWE6UWExMjM0NTY=/${CI_TOKEN}/" /root/.docker/config.json ; \
	cp $(4) ${DIST_PATH}/Dockerfile ; \
	docker build ${DOCKER_LABELS} -t $${IMAGE_RELEASE} -t $${IMAGE_SOPHON} -t $${IMAGE_SOPHON_MINOR} ${DIST_PATH} ; \
	export DOCKER_OPTS="--insecure-registry ${DOCKER_REPO_URL}" ; \
	[ $(5) ] || (docker push $${IMAGE_RELEASE} && docker push $${IMAGE_SOPHON} && docker push $${IMAGE_SOPHON_MINOR} )
endef


 ## Build and push docker image with latest tag, this stage will execute on [master] and [/^branch-\d\.\d$/] branch
 # this is only for thinger-gateway
 # $(1): IMAGE_BASE
 # $(2): ARCH
 # $(3): VERSION
 # $(4): DOCKER_FILE
 # $(5): NO_PUSH
 # will build images eg: ***********/postcommit/thinger-gateway:arm64-2.4-20191202-00000000
 #                     : ***********/postcommit/thinger-gateway:arm64-2.4-latest
 #                     : ***********/aip-arm/thinger-gateway:sophon-2.4
 # NOTE: This cmd will create a 'build' folder to ignore unrelated files, or else when executing `docker build ..`,
 # the '..' in the command will send too many files to docker context
 #
 # Finally, the build context will looks like:
 # .
 # ├── build
 # │   ├── ${COMPONENT}
 # │   │   ├── common.mk
 # │   │   ├── Dockerfile
 # │   │   └── Makefile
 # │   └── vision-std
 # ├── ${COMPONENT}
 # └── vision-std
 # Docker image build context is build/${COMPONENT}
define build-image
	set -x ; \
	IMAGE=$(1):$(2)-$(3) ; \
	IMAGE_LATEST=$(1):$(2)-$(subst branch-,,${CI_COMMIT_REF_NAME})-latest ; \
	IMAGE_BASE=$(1) ; \
	REPO_SOPHON=aip ; \
	[ "$(2)" = "arm64" ] && REPO_SOPHON=aip-arm ; \
	IMAGE_SOPHON=$${IMAGE_BASE/postcommit/$${REPO_SOPHON}}:$(subst branch-,sophon-,${CI_COMMIT_REF_NAME}) ; \
	rm -rf ../build && \
	mkdir ../build && \
	cp -rf ../$(COMPONENT)/ ../build/ && \
	cp -rf ../vision-std/ ../build/ && \
	cd ../build/${COMPONENT} && \
	docker run --rm --privileged ***********/aip/base/multiarch-qemu-user-static:sophon-0.0 --reset -p yes && \
	docker build -f $(4) ${DOCKER_LABELS} --build-arg PACKAGE=${PACKAGE} --build-arg BUILD_VERSION=${BUILD_VERSION} --build-arg BUILD_TIME=${BUILD_TIME} --build-arg CI_COMMIT_SHA=${CI_COMMIT_SHA} -t $${IMAGE} -t $${IMAGE_SOPHON} -t $${IMAGE_LATEST} .. && \
	export DOCKER_OPTS="--insecure-registry ${DOCKER_REPO_URL}" && \
	([ $(5) ] || (docker push $${IMAGE} && docker push $${IMAGE_LATEST} && docker push $${IMAGE_SOPHON})) && \
	rm -rf ../build
endef

 ## Build and push docker image with release tag, this stage will only execute on git tags
 # this is only for thinger-gateway
 # $(1): IMAGE_BASE
 # $(2): ARCH
 # $(3): VERSION
 # $(4): DOCKER_FILE
 # $(5): NO_PUSH
 # will build images eg : ***********/transwarp/thinger-gateway:sophon-2.4.0-rc2
 #                      : ***********/transwarp/arm64-thinger-gateway:sophon-2.4.0-rc2
 #                      : ***********/aip/thinger-gateway:sophon-2.4.0-rc2
 #                      : ***********/aip/arm64-thinger-gateway:sophon-2.4.0-rc2
 #                      每月会基于当前大版本分支出一个小版本，比如基于branch-2.5出20.05的版本
 #                      : ***********/aip/thinger-gateway:20.05
 #                      : ***********/aip/arm64-thinger-gateway:20.05
define build-image-release
	set -x ; \
	IMAGE_RELEASE=$(1):$${CI_COMMIT_REF_NAME} ; \
	IMAGE_BASE=$(1) ; \
	IMAGE_SOPHON=$${IMAGE_BASE/transwarp/aip}:$${CI_COMMIT_REF_NAME} ; \
	sed -i "4s/cWE6UWExMjM0NTY=/${CI_TOKEN}/" /root/.docker/config.json ; \
	IMAGE_SOPHON_MINOR=$${IMAGE_BASE/transwarp/aip}:$${CI_COMMIT_REF_NAME/sophon-/} ; \
	docker run --rm --privileged ***********/aip/base/multiarch-qemu-user-static:sophon-0.0 --reset -p yes && \
	docker build -f $(4) ${DOCKER_LABELS} --build-arg PACKAGE=${PACKAGE} --build-arg BUILD_VERSION=${BUILD_VERSION} --build-arg BUILD_TIME=${BUILD_TIME} --build-arg CI_COMMIT_SHA=${CI_COMMIT_SHA} -t $${IMAGE_SOPHON_MINOR} -t $${IMAGE_SOPHON} -t $${IMAGE_RELEASE} .. ; \
	export DOCKER_OPTS="--insecure-registry ${DOCKER_REPO_URL}" ; \
	([ $(5) ] || (docker push $${IMAGE_RELEASE} && docker push $${IMAGE_SOPHON} && docker push $${IMAGE_SOPHON_MINOR}))
endef
 ## Deploy latest docker image
 # $(1): COMPONENT
 # $(2): DOPLOY_SERVER
 # $(3): DOCKER_COMPOSE_ROOT
define deploy
	ssh $(2) "cd $(3) ;\
	docker-compose pull $(1); docker-compose up -d $(1)"
endef

 ## Deploy latest docker image
 # $(1): COMPONENT
 # $(2): DOPLOY_SERVER
 # $(3): IMAGE
define run
	ssh $(2) \
	"docker pull $(3) && docker run -d --rm --name $(1) --network=${THINGER_DOCKER_NETWORK} $(3)"
endef


.PHONY: all dep clean test coverage lint deploy \
		build-x86 build-arm \
		image-x86 image-arm \
		image-x86-release image-arm-release

all: build-x86

lint: ## Lint the files
	@golint ${PKG_LIST}

test: ## Run unittests
	@go test -count=1 -short ${PKG_LIST}

race: dep ## Run data race detector
	@go test -race -short ${PKG_LIST}

coverage: ## Generate global code coverage report
	@mkdir -p $(BUILD_PATH);
	@go test -json -covermode=count -coverprofile $(BUILD_PATH)/coverage.out ./... > $(BUILD_PATH)/test.out;
	@go tool cover -func=$(BUILD_PATH)/coverage.out ;
	@go tool cover -html=$(BUILD_PATH)/coverage.out -o $(BUILD_PATH)/coverage.html ;

analyze:
	@git clone http://gitlab-ci-token:${CI_JOB_TOKEN}@***********:10080/InfraTools/base_project.git base_project
	@cp base_project/settings_postcommit.xml /root/.m2/settings.xml
	@rm -rf base_project
	@sonar-scanner -Dsonar.host.url=http://************:30874 \
                   -Dsonar.issuesReport.html.enable=true \
                   -Dsonar.analysis.mode=publish \
                   -Dsonar.gitcoverage.htmllab.commit_sha=${CI_COMMIT_SHA} \
                   -Dsonar.gitlab.ref_name=${CI_COMMIT_REF_NAME} \
                   -Dsonar.gitlab.project_id=${CI_PROJECT_ID} \
                   -Dsonar.sourceEncoding=UTF-8 \
                   -Dsonar.languages=go \
                   -Dsonar.login=sophon \
                   -Dsonar.password=sophon \
                   -Dsonar.projectKey=${COMPONENT} \
                   -Dsonar.projectName=${COMPONENT} \
                   -Dsonar.projectVersion=${BUILD_VERSION} \
                   -Dsonar.sources=. \
                   -Dsonar.inclusions=**/*.go \
                   -Dsonar.tests=. \
                   -Dsonar.test.inclusions=**/*_test.go \
                   -Dsonar.go.tests.reportPaths=${BUILD_PATH}/test.out \
                   -Dsonar.go.coverage.reportPaths=${BUILD_PATH}/coverage.out

dep: ## Get the dependencies
	@go get -v -d ./...
	## @go get -u github.com/golang/lint/golint

clean: ## Remove previous build
	rm -fr ${BUILD_PATH}
	rm -fr $(DIST_PATH)

help: ## Display this help screen
	@grep -h -E '^[a-zA-Z0-9_-]+:.*?## .*$$' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

build-x86: ## Build X86 artifact
	$(call build,amd64)

build-arm: ## Build ARM64 artifact
	$(call build,arm64)

build2-x86: ## Build X86 artifact
	$(call build2,amd64,Dockerfile.x86_64)

build2-arm: ## Build ARM64 artifact
	$(call build2,arm64,Dockerfile.arm_64)

image-x86:: ## Build and push X86 image
	$(call image,${BUILD_IMAGE_PREFIX}/${COMPONENT},amd64,${BUILD_VERSION},Dockerfile.x86_64,${NO_PUSH})

image-arm:: ## Build and push ARM64 image
	$(call image,${BUILD_IMAGE_PREFIX}/${COMPONENT},arm64,${BUILD_VERSION},Dockerfile.arm_64,${NO_PUSH})

image-x86-release:: ## Build and push X86 release image
	$(call image-release,${RELEASE_IMAGE_PREFIX}/${COMPONENT},amd64,${RELEASE_VERSION},Dockerfile.x86_64,${NO_PUSH})

image-arm-release:: ## Build and push ARM64 release image
	$(call image-release,${RELEASE_IMAGE_PREFIX}/arm64-${COMPONENT},arm64,${RELEASE_VERSION},Dockerfile.arm_64,${NO_PUSH})

deploy::
	$(call deploy,${COMPONENT},${DEPLOY_SERVER},${DOCKER_COMPOSE_ROOT})

build-image-x86:: ## Build and push X86 image
	$(call build-image,${BUILD_IMAGE_PREFIX}/${COMPONENT},amd64,${BUILD_VERSION},Dockerfile.x86_64,${NO_PUSH})

build-image-arm:: ## Build and push X86 image
	$(call build-image,${BUILD_IMAGE_PREFIX}/${COMPONENT},arm64,${BUILD_VERSION},Dockerfile.arm_64,${NO_PUSH})

build-image-x86-release:: ## Build and push X86 release image
	$(call build-image-release,${RELEASE_IMAGE_PREFIX}/${COMPONENT},amd64,${RELEASE_VERSION},Dockerfile.x86_64,${NO_PUSH})

build-image-arm-release:: ## Build and push ARM64 release image
	$(call build-image-release,${RELEASE_IMAGE_PREFIX}/arm64-${COMPONENT},arm64,${RELEASE_VERSION},Dockerfile.arm_64,${NO_PUSH})
