package main

import (
	"math/rand"
	"net/http"
	"time"
	"transwarp.io/aip/llmops-common/pkg/crd"

	"transwarp.io/applied-ai/central-auth-service/dao"

	"github.com/emicklei/go-restful/v3"
	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus/promhttp"

	_ "transwarp.io/applied-ai/aiot/vision-std/health"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/api"
	"transwarp.io/applied-ai/central-auth-service/clients"
	"transwarp.io/applied-ai/central-auth-service/conf"
	_ "transwarp.io/applied-ai/central-auth-service/migrations"
	_ "transwarp.io/applied-ai/central-auth-service/service/metrics"

	_ "net/http/pprof"
)

func init() {
	rand.Seed(time.Now().UTC().UnixNano())

	//_ = stdlog.Init(*conf.<PERSON><PERSON>, conf.C.Server.Name)
	//stderr.SetContext(conf.C.Server.Name)
}

func main() {
	dao.Init()
	crd.Init()
	if err := prepare(); err != nil {
		panic(err)
	}
	errs := run(conf.C.Server)
	select {
	case err := <-errs:
		panic(err)
	}
}

func prepare() error {
	clients.Init()
	if err := api.Mount(); err != nil {
		return errors.Wrap(err, "fail to mount APIs")
	}
	return nil
}
func run(cfg *conf.ServerConfig) chan error {
	errs := make(chan error)

	// start an HTTP server
	go func() {
		stdlog.Infof("start an HTTP server on %s", cfg.Addr)
		// add prometheus metrics
		restful.DefaultContainer.Handle("/metrics", promhttp.Handler())

		if err := http.ListenAndServe(cfg.Addr, restful.DefaultContainer); err != nil {
			errs <- errors.Wrap(err, "fail to start an HTTP server")
		}
	}()
	// start an HTTP CAS server
	go func() {
		stdlog.Infof("start an HTTP CAS server on %s", cfg.CASAddr)
		if err := http.ListenAndServeTLS(cfg.CASAddr, cfg.SSLCertPath, cfg.SSLKeyPath, restful.DefaultContainer); err != nil {
			errs <- errors.Wrap(err, "fail to start an HTTPS server")
		}
	}()

	return errs
}
